using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using CSharpFunctionalExtensions;
using CsvHelper;
using Datafeed_v2.Models;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Models.Jim2Api;
using Datafeed_v2.Models.KQM;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Controllers.NopController;

[TestFixture]
public class ProductsFeedTests
{
    private const string ProductSourceSku = "PS-SKU-001";
    private const string PriceBookSku = "PB-SKU-002";
    private const int PriceBookItemId = 1;
    private const int StockCodeColumnIndex = 0;
    private const int QtyAvailableColumnIndex = 1;

    private Mock<EdunetDatafeedsEntities> _mockDbContext;

    private Mock<Func<HttpResponseBase,
            Task<Result<(GetDataModel stockData, int stockCodeColumnIndex, int qtyAvailableColumnIndex)>>>>
        _mockGetStockDataFunc;

    private Mock<HttpResponseBase> _mockResponse;
    private Datafeed_v2.Controllers.NopController _controller;
    private List<ProductSourcesForNopCategory> _productSourcesData;
    private List<MappedPriceBookItemsForNopCategory> _priceBookData;
    private List<PriceBookItemImages> _priceBookImageData;

    [SetUp]
    public void Setup()
    {
        // Initialise mock data
        _productSourcesData =
        [
            new ProductSourcesForNopCategory
            {
                ID = 101, // Assign an ID for consistency if needed elsewhere
                ProductSku = ProductSourceSku,
                ProductTitle = "PS Product Title",
                ProductShortDescription = "PS Short Desc",
                ProductLongDescription = "PS Long Desc",
                QtyAvailable = 10,
                PriceOverride = 100.00m,
                StatusId = (int)ProductSourceStatuses.Published,
                CategoryId = 1 // Assign CategoryId if needed
                // Add other required fields if necessary based on the view definition
            }
        ];

        _priceBookData =
        [
            new MappedPriceBookItemsForNopCategory
            {
                ID = PriceBookItemId, // Assign ID
                ProductSKU = PriceBookSku,
                ProductName = "PB Product Name",
                ProductShortDescription = "PB Short Desc", // Valid short desc
                ProductDescription = "PB Long Desc", // Valid long desc
                StockOnHand = 5,
                ProductPrice = 50.00m, // Valid price > 0
                PriceOverride = null,
                IsPublished = true,
                NopCategoryId = 2 // Assign CategoryId if needed
                // Add other required fields if necessary based on the view definition
            }
        ];

        // Added: Initialise image data for the default valid price book item
        _priceBookImageData =
        [
            new PriceBookItemImages
            {
                ID = 201, // Assign an ID
                PriceBookItemID = PriceBookItemId,
                ImageURL = "http://example.com/image.jpg",
                Image = [1, 2, 3] // Non-empty image bytes
            }
        ];

        // Setup mocks
        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockGetStockDataFunc =
            new Mock<Func<HttpResponseBase,
                Task<Result<(GetDataModel stockData, int stockCodeColumnIndex, int qtyAvailableColumnIndex)>>>>();
        _mockResponse = new Mock<HttpResponseBase>();

        // Setup DbContext mock
        _mockDbContext.Setup(db => db.ProductSourcesForNopCategory)
            .Returns(_productSourcesData.GetAsyncQueryableMockDbSet());
        _mockDbContext.Setup(db => db.MappedPriceBookItemsForNopCategory)
            .Returns(_priceBookData.GetAsyncQueryableMockDbSet());
        // Added: Mock PriceBookItemImages
        _mockDbContext.Setup(db => db.PriceBookItemImages)
            .Returns(_priceBookImageData.GetAsyncQueryableMockDbSet());

        // Setup default GetStockDataFunc response (success with stock for both)
        SetupSuccessStockDataFunc([
            (ProductSourceSku, 20),
            (PriceBookSku, 15)
        ]);

        // Create controller with mocks
        _controller = new Datafeed_v2.Controllers.NopController(
            _mockDbContext.Object,
            _mockGetStockDataFunc.Object);
    }

    // Helper to setup successful stock data function mock
    private void SetupSuccessStockDataFunc(List<(string sku, object qty)> stockLevels)
    {
        var rows = stockLevels.Select(s => new object[] { s.sku, s.qty }).ToArray();
        var stockData = new GetDataModel
        {
            resultSets =
            [
                new ResultSets
                {
                    rows = rows
                }
            ]
        };
        _mockGetStockDataFunc.Setup(f => f(It.IsAny<HttpResponseBase>()))
            .ReturnsAsync(
                Result.Success<(GetDataModel, int, int)>((stockData, StockCodeColumnIndex, QtyAvailableColumnIndex)));
    }

    // Helper to setup failed stock data function mock
    private void SetupFailureStockDataFunc(string errorMessage = "Failed to get stock data")
    {
        _mockGetStockDataFunc.Setup(f => f(It.IsAny<HttpResponseBase>()))
            .ReturnsAsync(Result.Failure<(GetDataModel, int, int)>(errorMessage));
    }

    // Helper to parse CSV content from FileResult
    private static async Task<List<ProductsFeedCsvModel>> ParseCsvResult(ActionResult result)
    {
        Assert.That(result, Is.InstanceOf<FileContentResult>());
        var fileResult = (FileContentResult)result;
        Assert.That(fileResult.ContentType, Is.EqualTo("text/csv"));
        Assert.That(fileResult.FileDownloadName, Is.EqualTo("ProductsFeed.csv"));

        using var memoryStream = new MemoryStream(fileResult.FileContents);
        using var reader = new StreamReader(memoryStream, Encoding.UTF8);
        using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);
        return await csv.GetRecordsAsync<ProductsFeedCsvModel>().ToListAsync();
    }

    [Test]
    public async Task Should_Return_EmptyResult_When_GetStockDataFunc_Fails()
    {
        // Arrange
        SetupFailureStockDataFunc();

        // Act
        var result = await _controller.ProductsFeed();

        // Assert
        Assert.That(result, Is.InstanceOf<EmptyResult>());
        _mockGetStockDataFunc.Verify(f => f(It.IsAny<HttpResponseBase>()), Times.Once);
    }

    [Test]
    public async Task Should_Return_Csv_With_Combined_Stock_When_Successful()
    {
        // Arrange: Setup already configured for success with stock for both items

        // Act
        var result = await _controller.ProductsFeed();

        // Assert
        var csvData = await ParseCsvResult(result);
        Assert.That(csvData.Count, Is.EqualTo(2));

        var psItem = csvData.SingleOrDefault(p => p.Sku == ProductSourceSku);
        Assert.That(psItem, Is.Not.Null);
        Assert.That(psItem.QtyAvailable, Is.EqualTo(10 + 20)); // Base + Jim2
        Assert.That(psItem.SellPriceInc, Is.EqualTo(100.00m));
        Assert.That(psItem.Published, Is.True);

        var pbItem = csvData.SingleOrDefault(p => p.Sku == PriceBookSku);
        Assert.That(pbItem, Is.Not.Null);
        Assert.That(pbItem.QtyAvailable, Is.EqualTo(5 + 15)); // Base + Jim2
        Assert.That(pbItem.SellPriceInc, Is.EqualTo(50.00m));
        Assert.That(pbItem.Published, Is.True);

        _mockGetStockDataFunc.Verify(f => f(It.IsAny<HttpResponseBase>()), Times.Once);
    }

    [Test]
    public async Task Should_Return_Csv_With_Only_ProductSource_When_PriceBook_Is_Empty()
    {
        // Arrange
        _priceBookData.Clear(); // Remove price book items
        SetupSuccessStockDataFunc([(ProductSourceSku, 20)]); // Only PS stock needed

        // Act
        var result = await _controller.ProductsFeed();

        // Assert
        var csvData = await ParseCsvResult(result);
        Assert.That(csvData.Count, Is.EqualTo(1));
        Assert.That(csvData[0].Sku, Is.EqualTo(ProductSourceSku));
        Assert.That(csvData[0].QtyAvailable, Is.EqualTo(10 + 20));
    }

    [Test]
    public async Task Should_Return_Csv_With_Only_PriceBook_When_ProductSource_Is_Empty()
    {
        // Arrange
        _productSourcesData.Clear(); // Remove product source items
        SetupSuccessStockDataFunc([(PriceBookSku, 15)]); // Only PB stock needed

        // Act
        var result = await _controller.ProductsFeed();

        // Assert
        var csvData = await ParseCsvResult(result);
        Assert.That(csvData.Count, Is.EqualTo(1));
        Assert.That(csvData[0].Sku, Is.EqualTo(PriceBookSku));
        Assert.That(csvData[0].QtyAvailable, Is.EqualTo(5 + 15));
    }

    [Test]
    public async Task Should_Use_Base_Quantity_When_No_Jim2_Stock_Found()
    {
        // Arrange
        SetupSuccessStockDataFunc([]); // No matching stock in Jim2

        // Act
        var result = await _controller.ProductsFeed();

        // Assert
        var csvData = await ParseCsvResult(result);
        Assert.That(csvData.Count, Is.EqualTo(2));

        var psItem = csvData.Single(p => p.Sku == ProductSourceSku);
        Assert.That(psItem.QtyAvailable, Is.EqualTo(10)); // Only base quantity

        var pbItem = csvData.Single(p => p.Sku == PriceBookSku);
        Assert.That(pbItem.QtyAvailable, Is.EqualTo(5)); // Only base quantity
    }

    [Test]
    public async Task Should_Use_Base_Quantity_When_Jim2_Quantity_Is_Invalid()
    {
        // Arrange
        SetupSuccessStockDataFunc([
            (ProductSourceSku, "invalid_qty"), // Invalid quantity
            (PriceBookSku, 15)
        ]);

        // Act
        var result = await _controller.ProductsFeed();

        // Assert
        var csvData = await ParseCsvResult(result);
        Assert.That(csvData.Count, Is.EqualTo(2));

        var psItem = csvData.Single(p => p.Sku == ProductSourceSku);
        Assert.That(psItem.QtyAvailable, Is.EqualTo(10)); // Only base quantity due to invalid Jim2 qty

        var pbItem = csvData.Single(p => p.Sku == PriceBookSku);
        Assert.That(pbItem.QtyAvailable, Is.EqualTo(5 + 15)); // Correct quantity
    }

    [Test]
    public async Task Should_Unpublish_PriceBook_Item_With_IsPublished_False()
    {
        // Arrange
        _priceBookData[0].IsPublished = false;

        // Act
        var result = await _controller.ProductsFeed();

        // Assert
        var csvData = await ParseCsvResult(result);
        Assert.That(csvData.Count, Is.EqualTo(2));
        var priceBookItem = csvData.SingleOrDefault(p => p.Sku == PriceBookSku);
        Assert.That(priceBookItem, Is.Not.Null);
        Assert.That(priceBookItem.Published, Is.False);
    }

    [Test]
    public async Task Should_Use_PriceOverride_For_PriceBook_Item_When_Available()
    {
        // Arrange
        const decimal priceOverride = 65.00m;
        _priceBookData[0].PriceOverride = priceOverride;

        // Act
        var result = await _controller.ProductsFeed();

        // Assert
        var csvData = await ParseCsvResult(result);
        var pbItem = csvData.SingleOrDefault(p => p.Sku == PriceBookSku);
        Assert.That(pbItem, Is.Not.Null);
        Assert.That(pbItem.SellPriceInc, Is.EqualTo(priceOverride));
    }

    [Test]
    public async Task Should_Set_Published_False_For_NonPublished_ProductSource()
    {
        // Arrange
        _productSourcesData[0].StatusId = (int)ProductSourceStatuses.NotCompliant; // Set to a non-published status
        _productSourcesData[0].DisabledReason = "Some other reason"; // Ensure it's not "Missing Price" and not null

        // Act
        var result = await _controller.ProductsFeed();

        // Assert
        var csvData = await ParseCsvResult(result);
        var psItem = csvData.SingleOrDefault(p => p.Sku == ProductSourceSku);
        Assert.That(psItem, Is.Not.Null);
        Assert.That(psItem.Published, Is.False);
    }

    [Test]
    public async Task Should_Handle_Null_Jim2_Stock_Value_Gracefully()
    {
        // Arrange
        SetupSuccessStockDataFunc([
            (ProductSourceSku, null), // Null quantity value from Jim2
            (PriceBookSku, 15)
        ]);

        // Act
        var result = await _controller.ProductsFeed();

        // Assert
        var csvData = await ParseCsvResult(result);
        Assert.That(csvData.Count, Is.EqualTo(2));

        var psItem = csvData.Single(p => p.Sku == ProductSourceSku);
        Assert.That(psItem.QtyAvailable, Is.EqualTo(10)); // Should use base quantity

        var pbItem = csvData.Single(p => p.Sku == PriceBookSku);
        Assert.That(pbItem.QtyAvailable, Is.EqualTo(5 + 15)); // Should use combined quantity
    }

    [Test]
    public async Task Should_Handle_Jim2_Row_With_Insufficient_Columns()
    {
        // Arrange
        // Simulate a row returned from Jim2 that doesn't have enough columns for QtyAvailable
        var stockData = new GetDataModel
        {
            resultSets =
            [
                new ResultSets
                {
                    rows =
                    [
                        [ProductSourceSku], // Only SKU column, missing Qty column
                        [PriceBookSku, 15] // Correct row for PB item
                    ]
                }
            ]
        };
        _mockGetStockDataFunc.Setup(f => f(It.IsAny<HttpResponseBase>()))
            .ReturnsAsync(
                Result.Success<(GetDataModel, int, int)>((stockData, StockCodeColumnIndex, QtyAvailableColumnIndex)));


        // Act
        var result = await _controller.ProductsFeed();

        // Assert
        var csvData = await ParseCsvResult(result);
        Assert.That(csvData.Count, Is.EqualTo(2));

        var psItem = csvData.Single(p => p.Sku == ProductSourceSku);
        Assert.That(psItem.QtyAvailable, Is.EqualTo(10)); // Should use base quantity as Jim2 row was incomplete

        var pbItem = csvData.Single(p => p.Sku == PriceBookSku);
        Assert.That(pbItem.QtyAvailable, Is.EqualTo(5 + 15)); // Should use combined quantity
    }
}
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Helpers;
using FandoogleReflectorService;
using FandoogleReflectorService.Models;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Controllers.ManageProductsController;

[TestFixture]
public class RefreshPriceTests
{
    private const int ExpectedProductId = 123;
    private const string ExpectedSku = "TEST-SKU-001";
    private const decimal ExpectedCostPrice = 100.00m;
    private const decimal ExpectedCompetitorPrice = 150.00m;
    private const string ExpectedErrorMessage = "Service error";

    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private Mock<Service> _mockFandoogleService;
    private Mock<HttpResponseBase> _mockResponse;
    private Mock<HttpContextBase> _mockHttpContext;
    private Datafeed_v2.Controllers.ManageProductsController _controller;
    private List<ImportedItemsFromPriceBook> _priceBookData;
    private List<ProductSource> _productSourceData;
    private List<FandoogleReflectorCache> _cacheData;
    private List<ProductsCheaperAtCompetitor> _cheaperProductsData;
    private List<AIGeneratedProductInfo> _aiGeneratedProductInfo;

    [SetUp]
    public void Setup()
    {
        // Initialise mock data
        _priceBookData =
        [
            new ImportedItemsFromPriceBook
            {
                ID = ExpectedProductId,
                ProductSKU = ExpectedSku,
                CostPrice = ExpectedCostPrice
            }
        ];

        _productSourceData =
        [
            new ProductSource
            {
                ID = ExpectedProductId,
                ProductSku = ExpectedSku,
                CostPrice = ExpectedCostPrice
            }
        ];

        _cacheData = [];
        _cheaperProductsData = [];
        _aiGeneratedProductInfo = [];

        // Setup mocks
        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockFandoogleService = new Mock<Service>(new RateLimiter(1, 1), null);
        _mockResponse = new Mock<HttpResponseBase>();
        _mockHttpContext = new Mock<HttpContextBase>();
        _mockHttpContext.Setup(context => context.Response).Returns(_mockResponse.Object);

        // Setup DbContext mock
        _mockDbContext.Setup(db => db.ImportedItemsFromPriceBook).Returns(_priceBookData.GetAsyncQueryableMockDbSet());
        _mockDbContext.Setup(db => db.ProductSource).Returns(_productSourceData.GetAsyncQueryableMockDbSet());
        _mockDbContext.Setup(db => db.FandoogleReflectorCache).Returns(_cacheData.GetAsyncQueryableMockDbSet());
        _mockDbContext.Setup(db => db.ProductsCheaperAtCompetitor)
            .Returns(_cheaperProductsData.GetAsyncQueryableMockDbSet());
        _mockDbContext.Setup(db => db.AIGeneratedProductInfo)
            .Returns(_aiGeneratedProductInfo.GetAsyncQueryableMockDbSet());

        // Setup default Fandoogle service response
        _mockFandoogleService.Setup(service => service.RequestPricing(ExpectedSku))
            .ReturnsAsync(Result.Success(new RequestPricingResponse
            {
                Error = false,
                QueryResult = [ExpectedCompetitorPrice],
                QueriedStockCode = ExpectedSku
            }));

        // Create controller with mocks
        _controller = new Datafeed_v2.Controllers.ManageProductsController(
            _mockDbContext.Object,
            _mockFandoogleService.Object);
        _controller.ControllerContext = new ControllerContext(_mockHttpContext.Object, new RouteData(), _controller);
    }

    #region RefreshPrice Tests

    [Test]
    public async Task Should_Return_Success_When_Refreshing_Price_For_Existing_Pricebook_Product()
    {
        // Arrange
        const string productSource = "pricebook";

        // Act
        var result = await _controller.RefreshPrice(ExpectedProductId, productSource);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = (JsonResult)result;
        Assert.That(jsonResult.Data, Is.Not.Null);

        // Extract the price from the anonymous object
        var priceProperty = jsonResult.Data.GetType().GetProperty("price");
        Assert.That(priceProperty, Is.Not.Null, "JSON result should contain a 'price' property");
        var price = (decimal)priceProperty.GetValue(jsonResult.Data);
        Assert.That(price, Is.EqualTo(ExpectedCompetitorPrice));

        // Verify that the correct methods were called
        _mockFandoogleService.Verify(service => service.RequestPricing(ExpectedSku), Times.Once);
        _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Once);
    }

    [Test]
    public async Task Should_Return_Bad_Request_When_Pricebook_Product_Does_Not_Exist()
    {
        // Arrange
        const string productSource = "pricebook";
        const int nonExistentProductId = 999;

        // Act
        var result = await _controller.RefreshPrice(nonExistentProductId, productSource);

        // Assert
        Assert.That(result, Is.InstanceOf<ContentResult>());
        var contentResult = (ContentResult)result;
        Assert.That(contentResult.Content, Is.EqualTo("Product not found"));

        // Verify response status code was set
        _mockResponse.VerifySet(r => r.StatusCode = (int)HttpStatusCode.BadRequest, Times.Once);
    }

    [Test]
    public async Task Should_Return_Success_When_Refreshing_Price_For_Existing_Product_Source()
    {
        // Arrange
        const string productSource = "productsource";

        // Act
        var result = await _controller.RefreshPrice(ExpectedProductId, productSource);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = (JsonResult)result;
        Assert.That(jsonResult.Data, Is.Not.Null);

        // Extract the price from the anonymous object
        var priceProperty = jsonResult.Data.GetType().GetProperty("price");
        Assert.That(priceProperty, Is.Not.Null, "JSON result should contain a 'price' property");
        var price = (decimal)priceProperty.GetValue(jsonResult.Data);
        Assert.That(price, Is.EqualTo(ExpectedCompetitorPrice));

        // Verify that the correct methods were called
        _mockFandoogleService.Verify(service => service.RequestPricing(ExpectedSku), Times.Once);
        _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Once);
    }

    [Test]
    public async Task Should_Return_Bad_Request_When_Product_Source_Does_Not_Exist()
    {
        // Arrange
        const string productSource = "productsource";
        const int nonExistentProductId = 999;

        // Act
        var result = await _controller.RefreshPrice(nonExistentProductId, productSource);

        // Assert
        Assert.That(result, Is.InstanceOf<ContentResult>());
        var contentResult = (ContentResult)result;
        Assert.That(contentResult.Content, Is.EqualTo("Product source not found"));

        // Verify response status code was set
        _mockResponse.VerifySet(r => r.StatusCode = (int)HttpStatusCode.BadRequest, Times.Once);
    }

    [Test]
    public async Task Should_Return_Bad_Request_When_Product_Source_Is_Invalid()
    {
        // Arrange
        const string productSource = "invalid";

        // Act
        var result = await _controller.RefreshPrice(ExpectedProductId, productSource);

        // Assert
        Assert.That(result, Is.InstanceOf<ContentResult>());
        var contentResult = (ContentResult)result;
        Assert.That(contentResult.Content, Is.EqualTo("Invalid product source"));

        // Verify response status code was set
        _mockResponse.VerifySet(r => r.StatusCode = (int)HttpStatusCode.BadRequest, Times.Once);
    }

    #endregion

    #region RefreshSellPriceForProduct Tests

    [Test]
    public async Task Should_Return_Error_When_Fandoogle_Request_Fails()
    {
        // Arrange
        const string productSource = "pricebook";

        // Setup Fandoogle service to return failure
        _mockFandoogleService.Setup(service => service.RequestPricing(ExpectedSku))
            .ReturnsAsync(Result.Failure<RequestPricingResponse>(ExpectedErrorMessage));

        // Act
        var result = await _controller.RefreshPrice(ExpectedProductId, productSource);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = (JsonResult)result;
        Assert.That(jsonResult.Data, Is.Not.Null);

        // Extract properties from the anonymous object
        var data = jsonResult.Data;
        var successProperty = data.GetType().GetProperty("success");
        var messageProperty = data.GetType().GetProperty("message");
        var reasonProperty = data.GetType().GetProperty("reason");
        
        Assert.That(successProperty, Is.Not.Null);
        Assert.That(messageProperty, Is.Not.Null);
        Assert.That(reasonProperty, Is.Not.Null);
        
        var success = (bool)successProperty.GetValue(data);
        var message = (string)messageProperty.GetValue(data);
        var reason = (string)reasonProperty.GetValue(data);
        
        Assert.That(success, Is.False);
        Assert.That(message, Is.EqualTo(ExpectedErrorMessage));
        Assert.That(reason, Is.EqualTo("service_failure"));
    }

    [Test]
    public async Task Should_Return_Error_When_Fandoogle_Returns_Empty_Results()
    {
        // Arrange
        const string productSource = "pricebook";

        // Setup Fandoogle service to return empty results
        _mockFandoogleService.Setup(service => service.RequestPricing(ExpectedSku))
            .ReturnsAsync(Result.Success(new RequestPricingResponse
            {
                Error = false,
                QueryResult = [],
                QueriedStockCode = ExpectedSku
            }));

        // Act
        var result = await _controller.RefreshPrice(ExpectedProductId, productSource);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = (JsonResult)result;
        Assert.That(jsonResult.Data, Is.Not.Null);

        // Extract properties from the anonymous object
        var data = jsonResult.Data;
        var successProperty = data.GetType().GetProperty("success");
        var messageProperty = data.GetType().GetProperty("message");
        var reasonProperty = data.GetType().GetProperty("reason");
        
        Assert.That(successProperty, Is.Not.Null);
        Assert.That(messageProperty, Is.Not.Null);
        Assert.That(reasonProperty, Is.Not.Null);
        
        var success = (bool)successProperty.GetValue(data);
        var message = (string)messageProperty.GetValue(data);
        var reason = (string)reasonProperty.GetValue(data);
        
        Assert.That(success, Is.False);
        Assert.That(message, Is.EqualTo("No results returned from Fandoogle Reflector"));
        Assert.That(reason, Is.EqualTo("no_results"));
    }

    [Test]
    public async Task Should_Add_Entry_And_Return_Error_When_Competitor_Price_Below_Cost_Price()
    {
        // Arrange
        const string productSource = "pricebook";
        const decimal lowCompetitorPrice = 90.00m; // Lower than cost price (100.00)

        // Setup Fandoogle service to return price below cost
        _mockFandoogleService.Setup(service => service.RequestPricing(ExpectedSku))
            .ReturnsAsync(Result.Success(new RequestPricingResponse
            {
                Error = false,
                QueryResult = [lowCompetitorPrice],
                QueriedStockCode = ExpectedSku
            }));

        // Setup mock to capture added entity
        _mockDbContext.Setup(db => db.ProductsCheaperAtCompetitor.Add(It.IsAny<ProductsCheaperAtCompetitor>()))
            .Callback<ProductsCheaperAtCompetitor>(entity => _cheaperProductsData.Add(entity));

        // Act
        var result = await _controller.RefreshPrice(ExpectedProductId, productSource);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = (JsonResult)result;
        Assert.That(jsonResult.Data, Is.Not.Null);

        // Extract properties from the anonymous object
        var data = jsonResult.Data;
        var successProperty = data.GetType().GetProperty("success");
        var messageProperty = data.GetType().GetProperty("message");
        var reasonProperty = data.GetType().GetProperty("reason");
        var competitorPriceProperty = data.GetType().GetProperty("competitorPrice");
        var costPriceProperty = data.GetType().GetProperty("costPrice");
        
        Assert.That(successProperty, Is.Not.Null);
        Assert.That(messageProperty, Is.Not.Null);
        Assert.That(reasonProperty, Is.Not.Null);
        Assert.That(competitorPriceProperty, Is.Not.Null);
        Assert.That(costPriceProperty, Is.Not.Null);
        
        var success = (bool)successProperty.GetValue(data);
        var message = (string)messageProperty.GetValue(data);
        var reason = (string)reasonProperty.GetValue(data);
        var competitorPrice = (decimal)competitorPriceProperty.GetValue(data);
        var costPrice = (decimal)costPriceProperty.GetValue(data);
        
        Assert.That(success, Is.False);
        Assert.That(message, Is.EqualTo("Competitor price is cheaper than cost price. This product has been flagged for review."));
        Assert.That(reason, Is.EqualTo("cheaper_at_competitor"));
        Assert.That(competitorPrice, Is.EqualTo(lowCompetitorPrice));
        Assert.That(costPrice, Is.EqualTo(ExpectedCostPrice));

        // Verify entity was added
        Assert.That(_cheaperProductsData.Count, Is.EqualTo(1));
        Assert.That(_cheaperProductsData[0].ProductSku, Is.EqualTo(ExpectedSku));
        Assert.That(_cheaperProductsData[0].CompetitorPrice, Is.EqualTo(lowCompetitorPrice));

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Once);
    }

    [Test]
    public async Task Should_Return_Error_When_Competitor_Price_Below_Cost_Price_And_Entry_Exists()
    {
        // Arrange
        const string productSource = "pricebook";
        const decimal lowCompetitorPrice = 90.00m; // Lower than cost price (100.00)

        // Add existing entry
        _cheaperProductsData.Add(new ProductsCheaperAtCompetitor
        {
            ProductSku = ExpectedSku,
            CompetitorPrice = lowCompetitorPrice
        });

        // Setup Fandoogle service to return price below cost
        _mockFandoogleService.Setup(service => service.RequestPricing(ExpectedSku))
            .ReturnsAsync(Result.Success(new RequestPricingResponse
            {
                Error = false,
                QueryResult = [lowCompetitorPrice],
                QueriedStockCode = ExpectedSku
            }));

        // Act
        var result = await _controller.RefreshPrice(ExpectedProductId, productSource);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = (JsonResult)result;
        Assert.That(jsonResult.Data, Is.Not.Null);

        // Extract properties from the anonymous object
        var data = jsonResult.Data;
        var successProperty = data.GetType().GetProperty("success");
        var messageProperty = data.GetType().GetProperty("message");
        var reasonProperty = data.GetType().GetProperty("reason");
        var competitorPriceProperty = data.GetType().GetProperty("competitorPrice");
        var costPriceProperty = data.GetType().GetProperty("costPrice");
        
        Assert.That(successProperty, Is.Not.Null);
        Assert.That(messageProperty, Is.Not.Null);
        Assert.That(reasonProperty, Is.Not.Null);
        Assert.That(competitorPriceProperty, Is.Not.Null);
        Assert.That(costPriceProperty, Is.Not.Null);
        
        var success = (bool)successProperty.GetValue(data);
        var message = (string)messageProperty.GetValue(data);
        var reason = (string)reasonProperty.GetValue(data);
        var competitorPrice = (decimal)competitorPriceProperty.GetValue(data);
        var costPrice = (decimal)costPriceProperty.GetValue(data);
        
        Assert.That(success, Is.False);
        Assert.That(message, Is.EqualTo("Competitor price is cheaper than cost price. This product has been flagged for review."));
        Assert.That(reason, Is.EqualTo("cheaper_at_competitor"));
        Assert.That(competitorPrice, Is.EqualTo(lowCompetitorPrice));
        Assert.That(costPrice, Is.EqualTo(ExpectedCostPrice));

        // Verify no new entity was added
        Assert.That(_cheaperProductsData.Count, Is.EqualTo(1));

        // Verify SaveChangesAsync was not called
        _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Update_Entry_When_Existing_Cache_Entry_Is_Present()
    {
        // Arrange
        const string productSource = "pricebook";

        // Add existing cache entry
        var existingEntry = new FandoogleReflectorCache
        {
            ID = 1,
            ProductSku = ExpectedSku,
            RandomisedPrice = 120.00m,
            OriginalPrice = 125.00m,
            ExpiresAt = DateTime.UtcNow.AddDays(-1) // Expired
        };
        _cacheData.Add(existingEntry);

        _mockDbContext.Setup(db => db.FandoogleReflectorCache)
            .Returns(new List<FandoogleReflectorCache> { existingEntry }.GetAsyncQueryableMockDbSet());

        // Act
        var result = await _controller.RefreshPrice(ExpectedProductId, productSource);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = (JsonResult)result;
        Assert.That(jsonResult.Data, Is.Not.Null);

        // Extract the price from the anonymous object
        var priceProperty = jsonResult.Data.GetType().GetProperty("price");
        Assert.That(priceProperty, Is.Not.Null, "JSON result should contain a 'price' property");
        var price = (decimal)priceProperty.GetValue(jsonResult.Data);
        Assert.That(price, Is.EqualTo(ExpectedCompetitorPrice));

        // Verify cache entry was updated
        Assert.That(existingEntry.OriginalPrice, Is.EqualTo(ExpectedCompetitorPrice));
        Assert.That(existingEntry.ExpiresAt.Date, Is.EqualTo(DateTime.UtcNow.AddDays(30).Date));

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Once);
    }

    [Test]
    public async Task Should_Create_New_Entry_When_No_Cache_Entry_Is_Present()
    {
        // Arrange
        const string productSource = "pricebook";

        _mockDbContext.Setup(db => db.FandoogleReflectorCache)
            .Returns(_cacheData.GetAsyncQueryableMockDbSet());

        // Act
        var result = await _controller.RefreshPrice(ExpectedProductId, productSource);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = (JsonResult)result;
        Assert.That(jsonResult.Data, Is.Not.Null);

        // Extract the price from the anonymous object
        var priceProperty = jsonResult.Data.GetType().GetProperty("price");
        Assert.That(priceProperty, Is.Not.Null, "JSON result should contain a 'price' property");
        var price = (decimal)priceProperty.GetValue(jsonResult.Data);
        Assert.That(price, Is.EqualTo(ExpectedCompetitorPrice));

        // Verify new cache entry was created
        Assert.That(_cacheData.Count, Is.EqualTo(1));
        Assert.That(_cacheData[0].ProductSku, Is.EqualTo(ExpectedSku));
        Assert.That(_cacheData[0].OriginalPrice, Is.EqualTo(ExpectedCompetitorPrice));
        Assert.That(_cacheData[0].ExpiresAt.Date, Is.EqualTo(DateTime.UtcNow.AddDays(30).Date));

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Once);
    }

    [Test]
    public async Task Should_Succeed_When_Cost_Price_Is_Zero()
    {
        // Arrange
        const string productSource = "productsource";

        // Update product source to have zero cost price
        _productSourceData[0].CostPrice = 0;

        // Act
        var result = await _controller.RefreshPrice(ExpectedProductId, productSource);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = (JsonResult)result;
        Assert.That(jsonResult.Data, Is.Not.Null);
        var priceProperty = jsonResult.Data.GetType().GetProperty("price");
        Assert.That(priceProperty, Is.Not.Null, "JSON result should contain a 'price' property");
        var price = (decimal)priceProperty.GetValue(jsonResult.Data);
        Assert.That(price, Is.EqualTo(ExpectedCompetitorPrice));

        // Verify that the correct methods were called
        _mockFandoogleService.Verify(service => service.RequestPricing(ExpectedSku), Times.Once);
        _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Once);
    }

    #endregion
}
using System.Collections.Generic;
using System.Threading.Tasks;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;
using VerifyNUnit;
using VerifyTests;

namespace DatafeedUnitTests.Hangfire.Tasks.GetPriceBookItemImages;

public class RunTests
{
    private static readonly VerifySettings Settings;

    static RunTests()
    {
        Settings = new VerifySettings();
        Settings.UseDirectory("snapshots");
    }

    [Test]
    public Task Verify_Run()
    {
        var priceBookItemImagesRepository = new List<PriceBookItemImages>();
        var dbDatafeed = new Mock<EdunetDatafeedsEntities>();
        dbDatafeed.Setup(db => db.ImportedItemsFromPriceBook)
            .Returns(new List<ImportedItemsFromPriceBook>
            {
                new()
                {
                    ID = 1,
                    PriceBookID = 1,
                    ProductName = "Carrier 20 Cart",
                    ProductSKU = "PCL8-10130",
                    Brand = "PC Locs",
                    ProductDescription = string.Empty,
                    ProductPrice = 2005m,
                    ProductShortDescription =
                        "Top-loading cart. Charge, store, secure and transport 20 devices. Choose Racks or 4 Baskets. 4x 5-way power boards.",
                    ImageURLs =
                        "https://www.pclocs.com.au/hubfs/BRANDFOLDER%20Sync%20Folder/BRANDFOLDER%20Sync%20Folder%20-%20Website%20Assets/Website%20Assets/LNC-US-Website-Accessory-Image_Gallery-980x716-Small%20Basket-2Pack-10527.jpg"
                }
            }.GetAsyncQueryableMockDbSet());
        dbDatafeed.Setup(db => db.PriceBookItemImages)
            .Returns(priceBookItemImagesRepository.GetAsyncQueryableMockDbSet());

        Datafeed_v2.Hangfire.Tasks.GetPriceBookItemImages.Run(null, dbDatafeed.Object);
        return Verifier.Verify(priceBookItemImagesRepository, Settings);
    }
}
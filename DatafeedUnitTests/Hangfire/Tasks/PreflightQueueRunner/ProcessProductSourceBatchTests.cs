using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using AIProductDescription;
using AIProductDescription.Models;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Services;
using DatafeedUnitTests.Functions.ProductSourceFunctions;
using DatafeedUnitTests.Helpers;
using FandoogleReflectorService;
using FandoogleReflectorService.Models;
using Hangfire;
using Hangfire.Common;
using Hangfire.MemoryStorage;
using Hangfire.Server;
using Hangfire.Storage;
using KaseyaAPI;
using KaseyaAPI.Functions;
using KaseyaAPI.Models.ApiModels;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using ProductInfo = Datafeed_v2.Models.Services.IcecatApiService.ProductInfo;
using ProductSpecification = Datafeed_v2.Services.ProductSpecification;
using PreflightRunner = Datafeed_v2.Hangfire.Tasks.PreflightQueueRunner;

namespace DatafeedUnitTests.Hangfire.Tasks.PreflightQueueRunner
{
    [TestFixture]
    public class ProcessProductSourceBatchTests
    {
        private const string TestSku123 = "SKU123";
        private const string TestSku456 = "SKU456";
        private const string TestSku789 = "SKU789";
        private const string TestSkuNoSku = "NO_SKU_SOURCE";
        private const string TestSkuNotNew = "SKU_NOT_NEW";
        private const string IcecatHighPic = "http://example.com/icecat_high.jpg";
        private const string IcecatTitle = "Icecat Product Title";
        private Mock<EdunetDatafeedsEntities> _mockDbContext;
        private Mock<KaseyaApi> _mockKaseyaApi;
        private Mock<IcecatApiService> _mockIcecatService;
        private Mock<Service> _mockFandoogleService;
        private Mock<IKaseyaEstoreScraperService> _mockKaseyaScraper;
        private Mock<Library> _mockAiLibrary;
        private Mock<PerformContext> _mockPerformContext;
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient;

        private Mock<DbSet<ProductSource>> _mockProductSourceSet;
        private Mock<DbSet<AIGeneratedProductInfo>> _mockAiInfoSet;
        private Mock<DbSet<ProductSourcesForNopCategory>> _mockSourceMappingSet;
        private Mock<DbSet<FandoogleReflectorCache>> _mockFandoogleCacheSet;
        private Mock<DbSet<ProductsCheaperAtCompetitor>> _mockCheaperProductsSet;
        private Mock<DbSet<ProductSourceImages>> _mockProductSourceImageSet;
        private Mock<DbSet<PreflightLog>> _mockPreflightLogSet;
        private Mock<DbSet<ImportedItemsFromPriceBook>> _mockImportedItemsFromPriceBookSet;

        private ObservableCollection<AIGeneratedProductInfo> _localAiInfos;
        private ObservableCollection<ProductSourceImages> _localProductImages;

        private List<ProductSource> _productSources;
        private List<AIGeneratedProductInfo> _aiInfos;
        private List<ProductSourcesForNopCategory> _sourceMappings;
        private List<FandoogleReflectorCache> _fandoogleCache;
        private List<ProductsCheaperAtCompetitor> _cheaperProducts;
        private List<ProductSourceImages> _productSourceImages;
        private List<PreflightLog> _preflightLogs;
        private List<ImportedItemsFromPriceBook> _importedItemsFromPriceBookSet;

        [SetUp]
        public void SetUp()
        {
            JobStorage.Current = new MemoryStorage();

            _productSources = [];
            _aiInfos = [];
            _sourceMappings = [];
            _fandoogleCache = [];
            _cheaperProducts = [];
            _productSourceImages = [];
            _preflightLogs = [];
            _importedItemsFromPriceBookSet = [];

            _localAiInfos = [];
            _localProductImages = [];

            _mockDbContext = new Mock<EdunetDatafeedsEntities>();

            _mockProductSourceSet = _productSources.GetAsyncQueryableMockDbSet().AsMock();
            _mockDbContext.Setup(m => m.ProductSource).Returns(_mockProductSourceSet.Object);

            _mockAiInfoSet = _aiInfos.GetAsyncQueryableMockDbSet().AsMock();
            _mockAiInfoSet.Setup(m => m.Local).Returns(_localAiInfos);
            _mockAiInfoSet.Setup(m => m.Add(It.IsAny<AIGeneratedProductInfo>()))
                .Callback<AIGeneratedProductInfo>(entity =>
                {
                    _aiInfos.Add(entity);
                    _localAiInfos.Add(entity);
                });
            _mockDbContext.Setup(m => m.AIGeneratedProductInfo).Returns(_mockAiInfoSet.Object);

            _mockSourceMappingSet = _sourceMappings.GetAsyncQueryableMockDbSet().AsMock();
            _mockDbContext.Setup(m => m.ProductSourcesForNopCategory).Returns(_mockSourceMappingSet.Object);

            _mockFandoogleCacheSet = _fandoogleCache.GetAsyncQueryableMockDbSet().AsMock();
            _mockFandoogleCacheSet.Setup(m => m.Add(It.IsAny<FandoogleReflectorCache>()))
                .Callback<FandoogleReflectorCache>(entity => _fandoogleCache.Add(entity));
            _mockDbContext.Setup(m => m.FandoogleReflectorCache).Returns(_mockFandoogleCacheSet.Object);

            _mockCheaperProductsSet = _cheaperProducts.GetAsyncQueryableMockDbSet().AsMock();
            _mockCheaperProductsSet.Setup(m => m.Add(It.IsAny<ProductsCheaperAtCompetitor>()))
                .Callback<ProductsCheaperAtCompetitor>(entity => _cheaperProducts.Add(entity));
            _mockDbContext.Setup(m => m.ProductsCheaperAtCompetitor).Returns(_mockCheaperProductsSet.Object);

            _mockProductSourceImageSet = _productSourceImages.GetAsyncQueryableMockDbSet().AsMock();
            _mockProductSourceImageSet.Setup(m => m.Local).Returns(_localProductImages);
            _mockProductSourceImageSet.Setup(m => m.Add(It.IsAny<ProductSourceImages>()))
                .Callback<ProductSourceImages>(entity =>
                {
                    _productSourceImages.Add(entity);
                    _localProductImages.Add(entity);
                });
            _mockDbContext.Setup(m => m.ProductSourceImages).Returns(_mockProductSourceImageSet.Object);

            _mockPreflightLogSet = _preflightLogs.GetAsyncQueryableMockDbSet().AsMock();
            _mockPreflightLogSet.Setup(m => m.Add(It.IsAny<PreflightLog>()))
                .Callback<PreflightLog>(entity => _preflightLogs.Add(entity));
            _mockDbContext.Setup(m => m.PreflightLog).Returns(_mockPreflightLogSet.Object);

            _mockImportedItemsFromPriceBookSet = _importedItemsFromPriceBookSet.GetAsyncQueryableMockDbSet().AsMock();
            _mockDbContext.Setup(m => m.ImportedItemsFromPriceBook).Returns(_mockImportedItemsFromPriceBookSet.Object);

            _mockDbContext.Setup(m => m.SaveChangesAsync()).ReturnsAsync(1);

            _mockKaseyaApi = new Mock<KaseyaApi>("https://example.com", "apikey", 25u, Maybe<HttpMethods>.None);
            _mockIcecatService = new Mock<IcecatApiService>(null, null);
            _mockFandoogleService = new Mock<Service>(null, null);
            _mockKaseyaScraper = new Mock<IKaseyaEstoreScraperService>();
            _mockAiLibrary = new Mock<Library>(new ApiConfiguration
            {
                PerplexityApiKey = string.Empty, GeminiApiKey = string.Empty, GeminiRequestsPerMinute = 5,
                PerplexityRequestsPerMinute = 5
            });

            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>()
                )
                .ReturnsAsync((HttpRequestMessage request, CancellationToken _) => new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = request.RequestUri == new Uri(IcecatHighPic)
                        ? new ByteArrayContent([1, 2, 3, 4, 5, 6])
                        : new ByteArrayContent([1, 2, 3, 4, 5])
                });
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri("http://dummy-nop-site.com/")
            };

            var mockStorageConnection = new Mock<IStorageConnection>();
            var dummyJob = Job.FromExpression(() => Console.WriteLine("Dummy Job"));
            var backgroundJob = new BackgroundJob("dummy-job-id", dummyJob, DateTime.UtcNow);
            var mockCancellationToken = new Mock<IJobCancellationToken>();
            _mockPerformContext = new Mock<PerformContext>(mockStorageConnection.Object, backgroundJob,
                mockCancellationToken.Object);

            SetupDefaultServiceMocks(TestSku123, 1);
        }

        private void SetupDefaultServiceMocks(string sku, uint kqmId)
        {
            _mockKaseyaApi.Setup(k => k.GetProduct(It.Is<string>(s => s == sku), It.IsAny<Maybe<DateTime>>()))
                .ReturnsAsync(Result.Success<Product[], GetProductError>([
                    new Product
                    {
                        id = (int)kqmId, brandID = 1, price = 123.45m, title = "KQM Title",
                        description = "KQM Long Desc"
                    }
                ]));

            _mockKaseyaApi
                .Setup(k => k.GetProductSuppliersForProducts(It.Is<uint[]>(ids => ids.Contains(kqmId)),
                    It.IsAny<Maybe<KaseyaApi>>()))
                .ReturnsAsync(Result.Success<ProductSupplier[], GetProductSuppliersForProductsError>([
                    new ProductSupplier { productID = (int)kqmId, quantity = 10, cost = 50.00m }
                ]));

            _mockKaseyaApi.Setup(k => k.GetProductImages(kqmId))
                .ReturnsAsync(Result.Success<ProductImage[], GetProductImageError>([
                    new ProductImage { url = "http://example.com/kqm.jpg" }
                ]));

            _mockIcecatService.Setup(i =>
                    i.GetProductInfoByManufacturerCode(It.IsAny<string>(), sku, It.IsAny<List<string>>()))
                .ReturnsAsync(Result.Success(new ProductInfo
                {
                    Message = "OK",
                    Data = new ProductInfo.ProductData
                    {
                        GeneralInfo = new ProductInfo.GeneralInfo
                        {
                            Title = IcecatTitle,
                            SummaryDescription = new ProductInfo.ProductSummaryDescription
                                { ShortDescription = "Icecat Short", LongDescription = "Icecat Long" }
                        },
                        Gallery = [new ProductInfo.GalleryItem { Pic = "http://example.com/icecat.jpg" }],
                        Image = new ProductInfo.ProductMainImage { HighPic = IcecatHighPic }
                    }
                }));

            _mockFandoogleService.Setup(f => f.RequestPricing(sku))
                .ReturnsAsync(Result.Success(new RequestPricingResponse { QueryResult = [100.00m] }));

            _mockKaseyaScraper.Setup(s => s.FetchProductInformationAsync(sku, It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Success(new ProductInformation
                    { ProductSpecifications = [new ProductSpecification { Kind = "Color", Values = ["Red"] }] }));

            _mockAiLibrary.Setup(a =>
                    a.GetPreflightAttributeExtractionResponseAsync(It.IsAny<string[]>(), It.IsAny<string>(),
                        It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Success<GeminiResponse, Error>(new GeminiResponse
                {
                    Candidates =
                    [
                        new GeminiResponse.Candidate
                        {
                            Content = new GeminiResponse.Content
                            {
                                Parts =
                                [
                                    new GeminiResponse.Part { Text = "{\"product_specifications\":[\"Color: Red\"]}" }
                                ]
                            }
                        }
                    ]
                }));
        }

        [Test]
        public async Task Should_Return_Early_When_No_Sources_Found_For_IDs()
        {
            var batchIds = new List<int> { 999 };

            await PreflightRunner.ProcessProductSourceBatch(
                batchIds,
                _mockPerformContext.Object,
                _mockDbContext.Object,
                _mockKaseyaApi.Object,
                _mockIcecatService.Object,
                _mockFandoogleService.Object,
                _mockAiLibrary.Object,
                _httpClient,
                true);

            _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.AtLeastOnce);
            _mockKaseyaApi.Verify(k => k.GetProduct(It.IsAny<string>(), It.IsAny<Maybe<DateTime>>()), Times.Never);
            _mockIcecatService.Verify(
                i => i.GetProductInfoByManufacturerCode(It.IsAny<string>(), It.IsAny<string>(),
                    It.IsAny<List<string>>()), Times.Never);
            _mockFandoogleService.Verify(f => f.RequestPricing(It.IsAny<string>()), Times.Never);
            Assert.That(_preflightLogs.Any(l => l.Message.Contains("No sources found for the provided IDs")), Is.True);
        }

        [Test]
        public async Task Should_Skip_Source_If_Status_Not_New()
        {
            Assert.Ignore();
            var sourceNew = new ProductSource
            {
                ID = 1, ProductSku = TestSku123, Brand = "TestBrand", Status = (int)ProductSourceStatuses.New,
                SourceTypeIds = "1"
            };
            var sourceNotNew = new ProductSource
            {
                ID = 2, ProductSku = TestSkuNotNew, Brand = "TestBrand", Status = (int)ProductSourceStatuses.Published
            };
            _productSources.Add(sourceNew);
            _productSources.Add(sourceNotNew);
            var batchIds = new List<int> { 1, 2 };

            SetupDefaultServiceMocks(TestSku123, 1);
            SetupDefaultServiceMocks(TestSkuNotNew, 2);

            await PreflightRunner.ProcessProductSourceBatch(
                batchIds,
                _mockPerformContext.Object,
                _mockDbContext.Object,
                _mockKaseyaApi.Object,
                _mockIcecatService.Object,
                _mockFandoogleService.Object,
                _mockAiLibrary.Object,
                _httpClient,
                true);

            Assert.That(sourceNew.Status, Is.EqualTo((int)ProductSourceStatuses.Published));
            _mockFandoogleService.Verify(f => f.RequestPricing(TestSku123), Times.Once);

            Assert.That(sourceNotNew.Status, Is.EqualTo((int)ProductSourceStatuses.Published));
            _mockFandoogleService.Verify(f => f.RequestPricing(TestSkuNotNew), Times.Never);
            Assert.That(
                _preflightLogs.Any(l =>
                    l.Message.Contains($"Skipping source ID: {sourceNotNew.ID}") &&
                    l.Message.Contains($"status is already {sourceNotNew.Status}")), Is.True);
        }

        [Test]
        public async Task Should_Update_To_Published_When_All_Data_Available()
        {
            var source = new ProductSource
            {
                ID = 1, ProductSku = TestSku123, Brand = "TestBrand", Status = (int)ProductSourceStatuses.New,
                SourceTypeIds = "1", CostPrice = 50m, ProductSourceImages = new List<ProductSourceImages>()
            };
            _productSources.Add(source);
            _sourceMappings.Add(new ProductSourcesForNopCategory
                { ID = 1, SourceTypeId = 1, CategoryName = "TestCategory" });
            var batchIds = new List<int> { 1 };

            SetupDefaultServiceMocks(TestSku123, 1);
            _mockFandoogleService.Setup(f => f.RequestPricing(TestSku123))
                .ReturnsAsync(Result.Success(new RequestPricingResponse { QueryResult = [100.00m] }));

            await PreflightRunner.ProcessProductSourceBatch(
                batchIds, _mockPerformContext.Object, _mockDbContext.Object, _mockKaseyaApi.Object,
                _mockIcecatService.Object, _mockFandoogleService.Object, _mockAiLibrary.Object, _httpClient, true);

            Assert.That(source.Status, Is.EqualTo((int)ProductSourceStatuses.Published));
            Assert.That(source.QtyAvailable, Is.EqualTo(10));
            Assert.That(source.CostPrice, Is.EqualTo(50.00m));
            Assert.That(source.ProductTitle, Is.EqualTo("KQM Title"));
            Assert.That(source.ProductShortDescription, Is.EqualTo("Icecat Short"));
            Assert.That(source.ProductLongDescription, Is.EqualTo("Icecat Long"));
            Assert.That(_fandoogleCache.Any(c => c.ProductSku == TestSku123 && c.OriginalPrice == 100.00m), Is.True);
            Assert.That(_productSourceImages.Count(img => img.ProductSourceId == 1), Is.GreaterThan(0));
            Assert.That(_cheaperProducts.Any(p => p.ProductSku == TestSku123), Is.False);
            _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.AtLeast(2));
        }

        [Test]
        public async Task Should_Update_To_NotCompliant_When_Stock_Missing()
        {
            var source = new ProductSource
            {
                ID = 1, ProductSku = TestSku456, Brand = "TestBrand", Status = (int)ProductSourceStatuses.New,
                SourceTypeIds = "1", CostPrice = 50m, ProductSourceImages = new List<ProductSourceImages>()
            };
            _productSources.Add(source);
            _sourceMappings.Add(new ProductSourcesForNopCategory
                { ID = 1, SourceTypeId = 1, CategoryName = "TestCategory" });
            var batchIds = new List<int> { 1 };

            SetupDefaultServiceMocks(TestSku456, 1);

            _mockKaseyaApi.Setup(k =>
                    k.GetProductSuppliersForProducts(It.Is<uint[]>(ids => ids.Contains(1u)),
                        It.IsAny<Maybe<KaseyaApi>>()))
                .ReturnsAsync(Result.Failure<ProductSupplier[], GetProductSuppliersForProductsError>(
                    new GetProductSuppliersForProductsError(
                        GetProductSuppliersForProductsFailure.FailedToGetProductSuppliers, Maybe.None, "Stock fail",
                        Maybe.None)));

            await PreflightRunner.ProcessProductSourceBatch(
                batchIds, _mockPerformContext.Object, _mockDbContext.Object, _mockKaseyaApi.Object,
                _mockIcecatService.Object, _mockFandoogleService.Object, _mockAiLibrary.Object, _httpClient, true);

            Assert.That(source.Status, Is.EqualTo((int)ProductSourceStatuses.NotCompliant));
            Assert.That(source.QtyAvailable, Is.EqualTo(0));
            Assert.That(
                _preflightLogs.Any(l =>
                    l.Message.Contains("[CheckPublishable] Final Result: isPublishable = False") &&
                    l.Message.Contains("Stock:False")), Is.True);
        }

        [Test]
        public async Task Should_Add_To_Cheaper_Table_When_Fandoogle_Price_Lower_Than_Cost()
        {
            var source = new ProductSource
            {
                ID = 1, ProductSku = TestSku789, Brand = "TestBrand", Status = (int)ProductSourceStatuses.New,
                SourceTypeIds = "1", CostPrice = 150m,
                ProductSourceImages = new List<ProductSourceImages>()
            };
            _productSources.Add(source);
            _sourceMappings.Add(new ProductSourcesForNopCategory
                { ID = 1, SourceTypeId = 1, CategoryName = "TestCategory" });
            var batchIds = new List<int> { 1 };

            SetupDefaultServiceMocks(TestSku789, 1);

            _mockKaseyaApi
                .Setup(k => k.GetProductSuppliersForProducts(It.Is<uint[]>(ids => ids.Contains(1u)),
                    It.IsAny<Maybe<KaseyaApi>>()))
                .ReturnsAsync(Result.Success<ProductSupplier[], GetProductSuppliersForProductsError>([
                    new ProductSupplier { productID = 1, quantity = 10, cost = 150.00m }
                ]));

            _mockFandoogleService.Setup(f => f.RequestPricing(TestSku789))
                .ReturnsAsync(Result.Success(new RequestPricingResponse { QueryResult = [100.00m] }));

            _mockCheaperProductsSet = _cheaperProducts.GetAsyncQueryableMockDbSet().AsMock();
            _mockDbContext.Setup(db => db.ProductsCheaperAtCompetitor).Returns(_mockCheaperProductsSet.Object);
            _mockCheaperProductsSet.Setup(m => m.Add(It.IsAny<ProductsCheaperAtCompetitor>()))
                .Callback<ProductsCheaperAtCompetitor>(entity => _cheaperProducts.Add(entity));

            await PreflightRunner.ProcessProductSourceBatch(
                batchIds, _mockPerformContext.Object, _mockDbContext.Object, _mockKaseyaApi.Object,
                _mockIcecatService.Object, _mockFandoogleService.Object, _mockAiLibrary.Object, _httpClient, true);

            Assert.That(source.Status, Is.EqualTo((int)ProductSourceStatuses.Published));
            Assert.That(source.CostPrice, Is.EqualTo(150m));
            Assert.That(_cheaperProducts.Count, Is.EqualTo(1));
            Assert.That(_cheaperProducts.First().ProductSku, Is.EqualTo(TestSku789));
            Assert.That(_cheaperProducts.First().CompetitorPrice, Is.EqualTo(100.00m));
            Assert.That(_fandoogleCache.Any(c => c.ProductSku == TestSku789 && c.OriginalPrice == 100.00m), Is.True);
        }
    }
}
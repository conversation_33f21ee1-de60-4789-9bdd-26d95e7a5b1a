using System;
using System.IO;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models;
using Datafeed_v2.PriceBooks;
using Moq;
using NUnit.Framework;
using VerifyNUnit;
using VerifyTests;

namespace DatafeedUnitTests.PriceBooks.GenericPriceBookParser;

public class ParseExcelFileAsync
{
    private static readonly VerifySettings Settings;

    private readonly Stream _fileStream =
        new FileStream(
            @"C:\Users\<USER>\source\Workspaces\Datafeed v2\DatafeedUnitTests\PriceBooks\GenericPriceBookParser\ecom_databookTemplate_Example.xlsx",
            FileMode.Open);

    static ParseExcelFileAsync()
    {
        Settings = new VerifySettings();
        Settings.UseDirectory("snapshots");
    }

    [Test]
    public Task Verify_ParseExcelFileAsync_Success()
    {
        var result = Datafeed_v2.PriceBooks.GenericPriceBookParser.ParseExcelFileAsync(_fileStream)
            .ConfigureAwait(false).GetAwaiter().GetResult();
        Assert.That(result.IsSuccess, Is.True);
        return Verifier.Verify(result.Value, Settings);
    }

    // Handles null fileStream parameter
    [Test]
    public async Task parse_excel_file_async_returns_failure_with_null_stream()
    {
        // Act
        var result = await Datafeed_v2.PriceBooks.GenericPriceBookParser.ParseExcelFileAsync(null!);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("Invalid or empty file stream."));
    }

    // Handles failure in ParseExcelRowAsync for any row
    [Test]
    public async Task ParseExcelFileAsync_ReturnsFailure_WhenParseExcelRowAsyncFails()
    {
        // Arrange
        var fileStream = new MemoryStream();
        var writer = new StreamWriter(fileStream);
        await writer.WriteAsync("Header1,Header2\nData1,Data2\n");
        await writer.FlushAsync();
        fileStream.Position = 0;

        var mockReader = new Mock<IExcelDataReader>();
        mockReader.SetupSequence(r => r.ReadAsync())
            .ReturnsAsync(true) // Skip header
            .ReturnsAsync(true) // First data row
            .ReturnsAsync(false); // End of data

        var mockParseExcelRowAsync = new Mock<Func<IExcelDataReader, Task<Result<GenericPriceBook>>>>();
        mockParseExcelRowAsync.Setup(m => m(It.IsAny<IExcelDataReader>()))
            .ReturnsAsync(Result.Failure<GenericPriceBook>("Row parsing failed"))
            .Verifiable();

        // Act
        var result =
            await Datafeed_v2.PriceBooks.GenericPriceBookParser.ParseExcelFileAsync(fileStream, mockReader.Object,
                mockParseExcelRowAsync.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to parse Excel file"));
    }
}
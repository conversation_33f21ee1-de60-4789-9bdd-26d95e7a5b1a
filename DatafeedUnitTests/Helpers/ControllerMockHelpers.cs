using System.Collections.Specialized;
using System.Web;
using Moq;

namespace DatafeedUnitTests.Helpers;

public static class ControllerMockHelpers
{
    public static (Mock<HttpResponseBase> responseBaseMock, Mock<NameValueCollection> nvcMock) SetupHttpResponseMock()
    {
        var nvcMock = new Mock<NameValueCollection>();
        nvcMock.Setup(nvc => nvc.Add(It.IsAny<string>(), It.IsAny<string>()))
            .Verifiable();

        var responseBaseMock = new Mock<HttpResponseBase>();
        responseBaseMock.SetupProperty(rsp => rsp.StatusCode);
        responseBaseMock.SetupProperty(rsp => rsp.TrySkipIisCustomErrors);
        responseBaseMock.Setup(rsp => rsp.Headers)
            .Returns(nvcMock.Object)
            .Verifiable();

        return (responseBaseMock, nvcMock);
    }
}
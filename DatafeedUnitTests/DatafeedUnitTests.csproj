<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <Import Project="..\packages\NUnit3TestAdapter.5.1.0-alpha.3\build\net462\NUnit3TestAdapter.props" Condition="Exists('..\packages\NUnit3TestAdapter.5.1.0-alpha.3\build\net462\NUnit3TestAdapter.props')" />
    <Import Project="..\packages\Microsoft.Testing.Extensions.Telemetry.1.7.0\build\netstandard2.0\Microsoft.Testing.Extensions.Telemetry.props" Condition="Exists('..\packages\Microsoft.Testing.Extensions.Telemetry.1.7.0\build\netstandard2.0\Microsoft.Testing.Extensions.Telemetry.props')" />
    <Import Project="..\packages\Microsoft.Testing.Platform.MSBuild.1.7.0\build\Microsoft.Testing.Platform.MSBuild.props" Condition="Exists('..\packages\Microsoft.Testing.Platform.MSBuild.1.7.0\build\Microsoft.Testing.Platform.MSBuild.props')" />
    <Import Project="..\packages\Microsoft.Testing.Platform.1.7.0\build\netstandard2.0\Microsoft.Testing.Platform.props" Condition="Exists('..\packages\Microsoft.Testing.Platform.1.7.0\build\netstandard2.0\Microsoft.Testing.Platform.props')" />
    <Import Project="..\packages\NUnit.4.4.0-beta.1\build\NUnit.props" Condition="Exists('..\packages\NUnit.4.4.0-beta.1\build\NUnit.props')" />
    <Import Project="..\packages\Reqnroll.NUnit.2.4.1\build\Reqnroll.NUnit.props" Condition="Exists('..\packages\Reqnroll.NUnit.2.4.1\build\Reqnroll.NUnit.props')" />
    <Import Project="..\packages\Reqnroll.Tools.MsBuild.Generation.2.4.1\build\Reqnroll.Tools.MsBuild.Generation.props" Condition="Exists('..\packages\Reqnroll.Tools.MsBuild.Generation.2.4.1\build\Reqnroll.Tools.MsBuild.Generation.props')" />
    <Import Project="..\packages\Verify.NUnit.28.13.0\build\Verify.NUnit.props" Condition="Exists('..\packages\Verify.NUnit.28.13.0\build\Verify.NUnit.props')" />
    <Import Project="..\packages\Verify.28.13.0\build\Verify.props" Condition="Exists('..\packages\Verify.28.13.0\build\Verify.props')" />
    <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
    <PropertyGroup>
        <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
        <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
        <ProjectGuid>{74772739-B951-41D5-B35C-BB7E504A69AE}</ProjectGuid>
        <ProjectTypeGuids>{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
        <OutputType>Library</OutputType>
        <AppDesignerFolder>Properties</AppDesignerFolder>
        <RootNamespace>DatafeedUnitTests</RootNamespace>
        <AssemblyName>DatafeedUnitTests</AssemblyName>
        <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
        <FileAlignment>512</FileAlignment>
        <LangVersion>preview</LangVersion>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
        <PlatformTarget>AnyCPU</PlatformTarget>
        <DebugSymbols>true</DebugSymbols>
        <DebugType>full</DebugType>
        <Optimize>false</Optimize>
        <OutputPath>bin\Debug\</OutputPath>
        <DefineConstants>DEBUG;TRACE</DefineConstants>
        <ErrorReport>prompt</ErrorReport>
        <WarningLevel>4</WarningLevel>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
        <PlatformTarget>AnyCPU</PlatformTarget>
        <DebugType>pdbonly</DebugType>
        <Optimize>true</Optimize>
        <OutputPath>bin\Release\</OutputPath>
        <DefineConstants>TRACE</DefineConstants>
        <ErrorReport>prompt</ErrorReport>
        <WarningLevel>4</WarningLevel>
    </PropertyGroup>
    <ItemGroup>
        <Reference Include="AIProductDescription, Version=*******, Culture=neutral, processorArchitecture=MSIL">
            <HintPath>..\packages\AIProductDescription.1.6.6\lib\net48\AIProductDescription.dll</HintPath>
        </Reference>
        <Reference Include="Argon, Version=*******, Culture=neutral, PublicKeyToken=00a55352ff068a54, processorArchitecture=MSIL">
            <HintPath>..\packages\Argon.0.26.0\lib\net48\Argon.dll</HintPath>
        </Reference>
        <Reference Include="BouncyCastle.Cryptography, Version=*******, Culture=neutral, PublicKeyToken=072edcf4a5328938, processorArchitecture=MSIL">
            <HintPath>..\packages\BouncyCastle.Cryptography.2.4.0\lib\net461\BouncyCastle.Cryptography.dll</HintPath>
        </Reference>
        <Reference Include="Castle.Core, Version=*******, Culture=neutral, PublicKeyToken=407dd0808d44fbdc, processorArchitecture=MSIL">
            <HintPath>..\packages\Castle.Core.5.1.1\lib\net462\Castle.Core.dll</HintPath>
        </Reference>
        <Reference Include="CSharpFunctionalExtensions, Version=*******, Culture=neutral, processorArchitecture=MSIL">
            <HintPath>..\packages\FandoogleReflectorService.1.1.5\lib\net48\CSharpFunctionalExtensions.dll</HintPath>
        </Reference>
        <Reference Include="CsvHelper, Version=33.0.0.0, Culture=neutral, PublicKeyToken=8c4959082be5c823, processorArchitecture=MSIL">
            <HintPath>..\packages\CsvHelper.33.0.1\lib\net48\CsvHelper.dll</HintPath>
        </Reference>
        <Reference Include="CucumberExpressions, Version=17.1.0.0, Culture=neutral, PublicKeyToken=86496cfa5b4a5851, processorArchitecture=MSIL">
            <HintPath>..\packages\Cucumber.CucumberExpressions.17.1.0\lib\netstandard2.0\CucumberExpressions.dll</HintPath>
        </Reference>
        <Reference Include="DiffEngine, Version=*******, Culture=neutral, PublicKeyToken=c7a34512ecd69090, processorArchitecture=MSIL">
            <HintPath>..\packages\DiffEngine.15.9.1\lib\net48\DiffEngine.dll</HintPath>
        </Reference>
        <Reference Include="EmptyFiles, Version=*******, Culture=neutral, PublicKeyToken=c7a34512ecd69090, processorArchitecture=MSIL">
            <HintPath>..\packages\EmptyFiles.8.7.1\lib\net48\EmptyFiles.dll</HintPath>
        </Reference>
        <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
            <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
        </Reference>
        <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
            <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
        </Reference>
        <Reference Include="EPPlus, Version=4.0.4.0, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
            <HintPath>..\packages\EPPlus.4.0.4\lib\net20\EPPlus.dll</HintPath>
        </Reference>
        <Reference Include="Esendex.TokenBucket, Version=1.0.7.0, Culture=neutral, processorArchitecture=MSIL">
            <HintPath>..\packages\KaseyaAPI.1.5.0\lib\net48\Esendex.TokenBucket.dll</HintPath>
        </Reference>
        <Reference Include="FandoogleReflectorService, Version=*******, Culture=neutral, processorArchitecture=MSIL">
            <HintPath>..\packages\FandoogleReflectorService.1.1.5\lib\net48\FandoogleReflectorService.dll</HintPath>
        </Reference>
        <Reference Include="Gherkin, Version=30.0.0.0, Culture=neutral, PublicKeyToken=86496cfa5b4a5851, processorArchitecture=MSIL">
            <HintPath>..\packages\Gherkin.30.0.0\lib\netstandard2.0\Gherkin.dll</HintPath>
        </Reference>
        <Reference Include="Hangfire.Core, Version=1.8.14.0, Culture=neutral, processorArchitecture=MSIL">
            <HintPath>..\packages\Hangfire.Core.1.8.14\lib\net46\Hangfire.Core.dll</HintPath>
        </Reference>
        <Reference Include="Hangfire.MemoryStorage, Version=1.8.1.0, Culture=neutral, processorArchitecture=MSIL">
            <HintPath>..\packages\Hangfire.MemoryStorage.1.8.1.1\lib\net40\Hangfire.MemoryStorage.dll</HintPath>
        </Reference>
        <Reference Include="KaseyaAPI, Version=*******, Culture=neutral, processorArchitecture=MSIL">
            <HintPath>..\packages\KaseyaAPI.1.5.0\lib\net48\KaseyaAPI.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.ApplicationInsights, Version=2.23.0.29, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
          <HintPath>..\packages\Microsoft.ApplicationInsights.2.23.0\lib\net46\Microsoft.ApplicationInsights.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.3\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.Bcl.HashCode, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.Bcl.HashCode.1.1.1\lib\net461\Microsoft.Bcl.HashCode.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.CSharp" />
        <Reference Include="Microsoft.Extensions.DependencyInjection, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.8.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.2\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.Extensions.DependencyModel, Version=8.0.0.2, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.Extensions.DependencyModel.8.0.2\lib\net462\Microsoft.Extensions.DependencyModel.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.Extensions.Logging, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.Extensions.Logging.8.0.0\lib\net462\Microsoft.Extensions.Logging.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.9.0.2\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.Extensions.Options, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.Extensions.Options.8.0.0\lib\net462\Microsoft.Extensions.Options.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.Extensions.Primitives, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.Extensions.Primitives.8.0.0\lib\net462\Microsoft.Extensions.Primitives.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.Testing.Extensions.MSBuild, Version=1.7.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\Microsoft.Testing.Platform.MSBuild.1.7.0\lib\netstandard2.0\Microsoft.Testing.Extensions.MSBuild.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.Testing.Extensions.Telemetry, Version=1.7.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\Microsoft.Testing.Extensions.Telemetry.1.7.0\lib\netstandard2.0\Microsoft.Testing.Extensions.Telemetry.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.Testing.Extensions.TrxReport.Abstractions, Version=1.7.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\Microsoft.Testing.Extensions.TrxReport.Abstractions.1.7.0\lib\netstandard2.0\Microsoft.Testing.Extensions.TrxReport.Abstractions.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.Testing.Extensions.VSTestBridge, Version=1.7.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\Microsoft.Testing.Extensions.VSTestBridge.1.7.0\lib\netstandard2.0\Microsoft.Testing.Extensions.VSTestBridge.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.Testing.Platform, Version=1.7.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\Microsoft.Testing.Platform.1.7.0\lib\netstandard2.0\Microsoft.Testing.Platform.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.TestPlatform.AdapterUtilities, Version=1*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\Microsoft.TestPlatform.AdapterUtilities.17.13.0\lib\net462\Microsoft.TestPlatform.AdapterUtilities.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.TestPlatform.CoreUtilities, Version=1*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\Microsoft.TestPlatform.ObjectModel.17.13.0\lib\net462\Microsoft.TestPlatform.CoreUtilities.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.TestPlatform.PlatformAbstractions, Version=1*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\Microsoft.TestPlatform.ObjectModel.17.13.0\lib\net462\Microsoft.TestPlatform.PlatformAbstractions.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.VisualStudio.TestPlatform.ObjectModel, Version=1*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\Microsoft.TestPlatform.ObjectModel.17.13.0\lib\net462\Microsoft.VisualStudio.TestPlatform.ObjectModel.dll</HintPath>
        </Reference>
        <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
        </Reference>
        <Reference Include="Moq, Version=4.20.70.0, Culture=neutral, PublicKeyToken=69f491c39445e920, processorArchitecture=MSIL">
            <HintPath>..\packages\Moq.4.20.70\lib\net462\Moq.dll</HintPath>
        </Reference>
        <Reference Include="mscorlib" />
        <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
            <HintPath>..\packages\FandoogleReflectorService.1.1.5\lib\net48\Newtonsoft.Json.dll</HintPath>
        </Reference>
        <Reference Include="nunit.framework, Version=4.4.0.0, Culture=neutral, PublicKeyToken=2638cd05610744eb, processorArchitecture=MSIL">
          <HintPath>..\packages\NUnit.4.4.0-beta.1\lib\net462\nunit.framework.dll</HintPath>
        </Reference>
        <Reference Include="nunit.framework.legacy, Version=4.4.0.0, Culture=neutral, PublicKeyToken=2638cd05610744eb, processorArchitecture=MSIL">
          <HintPath>..\packages\NUnit.4.4.0-beta.1\lib\net462\nunit.framework.legacy.dll</HintPath>
        </Reference>
        <Reference Include="Owin, Version=*******, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
            <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
        </Reference>
        <Reference Include="PuppeteerSharp, Version=20.1.3.0, Culture=neutral, PublicKeyToken=db12f80f85d8ba23, processorArchitecture=MSIL">
            <HintPath>..\packages\PuppeteerSharp.20.1.3\lib\netstandard2.0\PuppeteerSharp.dll</HintPath>
        </Reference>
        <Reference Include="Renci.SshNet, Version=2024.2.0.1, Culture=neutral, PublicKeyToken=1cee9f8bde3db106, processorArchitecture=MSIL">
            <HintPath>..\packages\SSH.NET.2024.2.0\lib\net462\Renci.SshNet.dll</HintPath>
        </Reference>
        <Reference Include="Reqnroll, Version=*******, Culture=neutral, PublicKeyToken=611ce36403091019, processorArchitecture=MSIL">
          <HintPath>..\packages\Reqnroll.2.4.1\lib\netstandard2.0\Reqnroll.dll</HintPath>
        </Reference>
        <Reference Include="Reqnroll.NUnit.ReqnrollPlugin, Version=*******, Culture=neutral, PublicKeyToken=611ce36403091019, processorArchitecture=MSIL">
          <HintPath>..\packages\Reqnroll.NUnit.2.4.1\lib\netstandard2.0\Reqnroll.NUnit.ReqnrollPlugin.dll</HintPath>
        </Reference>
        <Reference Include="SimpleInfoName, Version=*******, Culture=neutral, PublicKeyToken=c7a34512ecd69090, processorArchitecture=MSIL">
            <HintPath>..\packages\SimpleInfoName.3.1.0\lib\net472\SimpleInfoName.dll</HintPath>
        </Reference>
        <Reference Include="Sylvan.Data.Excel, Version=0.4.25.0, Culture=neutral, processorArchitecture=MSIL">
            <HintPath>..\packages\Sylvan.Data.Excel.0.4.25\lib\netstandard2.0\Sylvan.Data.Excel.dll</HintPath>
        </Reference>
        <Reference Include="System" />
        <Reference Include="System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
            <HintPath>..\packages\System.Buffers.4.6.0\lib\net462\System.Buffers.dll</HintPath>
        </Reference>
        <Reference Include="System.CodeDom, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
            <HintPath>..\packages\System.CodeDom.9.0.2\lib\net462\System.CodeDom.dll</HintPath>
        </Reference>
        <Reference Include="System.Collections.Immutable, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
            <HintPath>..\packages\System.Collections.Immutable.8.0.0\lib\net462\System.Collections.Immutable.dll</HintPath>
        </Reference>
        <Reference Include="System.ComponentModel.DataAnnotations" />
        <Reference Include="System.Configuration" />
        <Reference Include="System.Core" />
        <Reference Include="System.Data" />
        <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
            <HintPath>..\packages\System.Diagnostics.DiagnosticSource.9.0.2\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
        </Reference>
        <Reference Include="System.Formats.Asn1, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
            <HintPath>..\packages\System.Formats.Asn1.8.0.1\lib\net462\System.Formats.Asn1.dll</HintPath>
        </Reference>
        <Reference Include="System.IO.Compression, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
            <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
        </Reference>
        <Reference Include="System.IO.Hashing, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
            <HintPath>..\packages\System.IO.Hashing.9.0.2\lib\net462\System.IO.Hashing.dll</HintPath>
        </Reference>
        <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
            <HintPath>..\packages\System.IO.Pipelines.9.0.3\lib\net462\System.IO.Pipelines.dll</HintPath>
        </Reference>
        <Reference Include="System.Linq.Async, Version=*******, Culture=neutral, PublicKeyToken=94bc3704cddfc263, processorArchitecture=MSIL">
            <HintPath>..\packages\System.Linq.Async.6.0.1\lib\net48\System.Linq.Async.dll</HintPath>
        </Reference>
        <Reference Include="System.Management" />
        <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
            <HintPath>..\packages\System.Memory.4.6.0\lib\net462\System.Memory.dll</HintPath>
        </Reference>
        <Reference Include="System.Net.Http" />
        <Reference Include="System.Numerics" />
        <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
            <HintPath>..\packages\System.Numerics.Vectors.4.6.0\lib\net462\System.Numerics.Vectors.dll</HintPath>
        </Reference>
        <Reference Include="System.Reflection.Metadata, Version=1.4.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
            <HintPath>..\packages\System.Reflection.Metadata.1.6.0\lib\netstandard2.0\System.Reflection.Metadata.dll</HintPath>
        </Reference>
        <Reference Include="System.Runtime" />
        <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
            <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.0\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
        </Reference>
        <Reference Include="System.Runtime.Serialization" />
        <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
            <HintPath>..\packages\System.Text.Encodings.Web.9.0.3\lib\net462\System.Text.Encodings.Web.dll</HintPath>
        </Reference>
        <Reference Include="System.Text.Json, Version=8.0.0.5, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
            <HintPath>..\packages\System.Text.Json.8.0.5\lib\net462\System.Text.Json.dll</HintPath>
        </Reference>
        <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
            <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
        </Reference>
        <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
            <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
        </Reference>
        <Reference Include="System.Web" />
        <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
        </Reference>
        <Reference Include="System.Web.Mvc, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
        </Reference>
        <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
        </Reference>
        <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
        </Reference>
        <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
        </Reference>
        <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
            <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
        </Reference>
        <Reference Include="System.Xml" />
        <Reference Include="Verify, Version=*******, Culture=neutral, PublicKeyToken=c7a34512ecd69090, processorArchitecture=MSIL">
            <HintPath>..\packages\Verify.28.13.0\lib\net48\Verify.dll</HintPath>
        </Reference>
        <Reference Include="Verify.NUnit, Version=*******, Culture=neutral, PublicKeyToken=c7a34512ecd69090, processorArchitecture=MSIL">
            <HintPath>..\packages\Verify.NUnit.28.13.0\lib\net48\Verify.NUnit.dll</HintPath>
        </Reference>
    </ItemGroup>
    <ItemGroup>
        <Compile Include="Controllers\ManageProductsController\RefreshPriceTests.cs" />
        <Compile Include="Controllers\NopController\ProductsFeedTests.cs" />
        <Compile Include="Features\Categories\ArchiveProductSources.cs" />
        <Compile Include="Features\Categories\ArchiveProductSources.feature.cs" />
        <Compile Include="Features\GrabProductInformationFromKqm\GetProductInformationFromKqmServiceSteps.cs" />
        <Compile Include="Features\GrabProductInformationFromKqm\GetProductInformationFromKqmService.feature.cs">
            <DependentUpon>GetProductInformationFromKqmService.feature</DependentUpon>
        </Compile>
        <Compile Include="Features\ManageProducts\AdvancedProductEditScenarios.feature.cs" />
        <Compile Include="Features\ManageProducts\ComprehensiveProductEdit.cs" />
        <Compile Include="Features\ManageProducts\ComprehensiveProductEdit.feature.cs" />
        <Compile Include="Features\ManageProducts\EditBrandForPriceBookItem.cs" />
        <Compile Include="Features\ManageProducts\EditBrandForPriceBookItem.feature.cs">
            <DependentUpon>EditBrandForPriceBookItem.feature</DependentUpon>
        </Compile>
        <Compile Include="Features\ManageProducts\EditBrandForProductSource.cs" />
        <Compile Include="Features\ManageProducts\EditBrandForProductSource.feature.cs">
            <DependentUpon>EditBrandForProductSource.feature</DependentUpon>
        </Compile>
        <Compile Include="Features\ManageProducts\EditLongDescriptionForProductSource.cs" />
        <Compile Include="Features\ManageProducts\EditLongDescriptionForProductSource.feature.cs">
            <DependentUpon>EditLongDescriptionForProductSource.feature</DependentUpon>
        </Compile>
        <Compile Include="Features\ManageProducts\EditShortDescriptionForProductSource.cs" />
        <Compile Include="Features\ManageProducts\EditShortDescriptionForProductSource.feature.cs">
            <DependentUpon>EditShortDescriptionForProductSource.feature</DependentUpon>
        </Compile>
        <Compile Include="Features\ManageProducts\EditTitleForProductSource.cs" />
        <Compile Include="Features\ManageProducts\EditTitleForProductSource.feature.cs">
            <DependentUpon>EditTitleForProductSource.feature</DependentUpon>
        </Compile>
        <Compile Include="Features\ManageProducts\ProductImageManagement.cs" />
        <Compile Include="Features\ManageProducts\ProductImageManagement.feature.cs" />
        <Compile Include="Features\ManageProducts\SetCostPriceOverrideForPriceBookItem.cs" />
        <Compile Include="Features\ManageProducts\SetCostPriceOverrideForPriceBookItem.feature.cs">
            <DependentUpon>SetCostPriceOverrideForPriceBookItem.feature</DependentUpon>
        </Compile>
        <Compile Include="Features\ManageProducts\SetCostPriceOverrideForProductSource.cs" />
        <Compile Include="Features\ManageProducts\SetCostPriceOverrideForProductSource.feature.cs">
            <DependentUpon>SetCostPriceOverrideForProductSource.feature</DependentUpon>
        </Compile>
        <Compile Include="Features\PriceBook\Archive.cs" />
        <Compile Include="Functions\PreflightFunctions\GetFandoogleReflectorPricingTests.cs" />
        <Compile Include="Functions\PreflightFunctions\GetKaseyaEstoreAttributesForSkusTests.cs" />
        <Compile Include="Functions\PreflightFunctions\GetKqmBrandNamesForProductIdsTests.cs" />
        <Compile Include="Functions\PreflightFunctions\GetKqmImagesForProductIdsTests.cs" />
        <Compile Include="Functions\PreflightFunctions\GetKqmProductIdAndRecommendedSellPriceForSkusTests.cs" />
        <Compile Include="Functions\PreflightFunctions\GetKqmSupplierStockCountAndCostPriceForProductIdsTests.cs" />
        <Compile Include="Functions\PreflightFunctions\GetProductDescriptionAndImagesFromIcecatTests.cs" />
        <Compile Include="Functions\PreflightFunctions\GetUnprocessedProductSourcesTests.cs" />
        <Compile Include="Functions\ProductSourceFunctions\CreateNewProductSourceFromSkuTests.cs" />
        <Compile Include="Functions\ProductSourceFunctions\CreateNewProductSourcesFromExcelSheetTests.cs" />
        <Compile Include="Functions\ProductSourceFunctions\CreateNewProductSourceTests.cs" />
        <Compile Include="Functions\ProductSourceFunctions\GetSourcesForNopCategoryTests.cs" />
        <Compile Include="Functions\ProductSourceFunctions\UpdateCostPriceTests.cs" />
        <Compile Include="Functions\ProductSourceListFunctions\CreateNewSourceTypeTests.cs" />
        <Compile Include="Functions\ProductSourceListFunctions\GetSourceTypesTests.cs" />
        <Compile Include="Functions\ProductSourceListFunctions\MapToNopCategoryTests.cs" />
        <Compile Include="Functions\ProductSourceListFunctions\MoveSourceTypeMappingTests.cs" />
        <Compile Include="Functions\ProductSourceListFunctions\RemoveSourceTypeMappingTests.cs" />
        <Compile Include="Hangfire\Tasks\GetPriceBookItemImages\RunTests.cs" />
        <Compile Include="Hangfire\Tasks\GetProductImagesFromIcecat\RunTests.cs" />
        <Compile Include="Hangfire\Tasks\PreflightQueueRunner\ProcessProductSourceBatchTests.cs" />
        <Compile Include="Hangfire\Tasks\PreflightQueueRunner\RunTests.cs" />
        <Compile Include="Helpers\ControllerMockHelpers.cs" />
        <Compile Include="Helpers\DbMockHelpers.cs" />
        <Compile Include="PriceBooks\GenericPriceBookParser\ParseExcelFileAsync.cs" />
        <Compile Include="Properties\AssemblyInfo.cs" />
        <Compile Include="Services\BulkAddSkusToQuoteService\AddSkusToQuoteAsyncTests.cs" />
        <Compile Include="Services\FetchProductInformationAsync\FetchProductInformationAsync.cs" />
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\Datafeed v2\Datafeed v2.csproj">
            <Project>{35f03622-3416-4d7e-a26d-15559472cc17}</Project>
            <Name>Datafeed v2</Name>
        </ProjectReference>
    </ItemGroup>
    <ItemGroup>
        <None Include="App.config" />
        <None Include="packages.config" />
    </ItemGroup>
    <ItemGroup>
        <Content Include="ILLink\ILLink.Descriptors.LibraryBuild.xml" />
        <Content Include="Services\FetchProductInformationAsync\snapshots\FetchProductInformationAsync.Verify_Multiple_Products_Result.verified.txt" />
        <Content Include="Services\FetchProductInformationAsync\snapshots\FetchProductInformationAsync.Verify_Single_Product_Result.verified.txt" />
    </ItemGroup>
    <ItemGroup>
        <Folder Include="Controllers\ProductsAdminController\" />
    </ItemGroup>
    <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
    <Import Project="..\packages\EmptyFiles.8.7.1\build\EmptyFiles.targets" Condition="Exists('..\packages\EmptyFiles.8.7.1\build\EmptyFiles.targets')" />
    <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
        <PropertyGroup>
            <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them. For more information, see http://go.microsoft.com/fwlink/?LinkID=322105.The missing file is {0}.</ErrorText>
        </PropertyGroup>
        <Error Condition="!Exists('..\packages\EmptyFiles.8.7.1\build\EmptyFiles.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EmptyFiles.8.7.1\build\EmptyFiles.targets'))" />
        <Error Condition="!Exists('..\packages\Verify.28.13.0\build\Verify.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Verify.28.13.0\build\Verify.props'))" />
        <Error Condition="!Exists('..\packages\Verify.28.13.0\build\Verify.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Verify.28.13.0\build\Verify.targets'))" />
        <Error Condition="!Exists('..\packages\Verify.NUnit.28.13.0\build\Verify.NUnit.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Verify.NUnit.28.13.0\build\Verify.NUnit.props'))" />
        <Error Condition="!Exists('..\packages\Reqnroll.2.4.1\build\Reqnroll.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Reqnroll.2.4.1\build\Reqnroll.targets'))" />
        <Error Condition="!Exists('..\packages\Reqnroll.Tools.MsBuild.Generation.2.4.1\build\Reqnroll.Tools.MsBuild.Generation.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Reqnroll.Tools.MsBuild.Generation.2.4.1\build\Reqnroll.Tools.MsBuild.Generation.props'))" />
        <Error Condition="!Exists('..\packages\Reqnroll.Tools.MsBuild.Generation.2.4.1\build\Reqnroll.Tools.MsBuild.Generation.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Reqnroll.Tools.MsBuild.Generation.2.4.1\build\Reqnroll.Tools.MsBuild.Generation.targets'))" />
        <Error Condition="!Exists('..\packages\Reqnroll.NUnit.2.4.1\build\Reqnroll.NUnit.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Reqnroll.NUnit.2.4.1\build\Reqnroll.NUnit.props'))" />
        <Error Condition="!Exists('..\packages\Reqnroll.NUnit.2.4.1\build\Reqnroll.NUnit.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Reqnroll.NUnit.2.4.1\build\Reqnroll.NUnit.targets'))" />
        <Error Condition="!Exists('..\packages\NUnit.4.4.0-beta.1\build\NUnit.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NUnit.4.4.0-beta.1\build\NUnit.props'))" />
        <Error Condition="!Exists('..\packages\Microsoft.Testing.Platform.1.7.0\build\netstandard2.0\Microsoft.Testing.Platform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Testing.Platform.1.7.0\build\netstandard2.0\Microsoft.Testing.Platform.props'))" />
        <Error Condition="!Exists('..\packages\Microsoft.Testing.Platform.1.7.0\build\netstandard2.0\Microsoft.Testing.Platform.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Testing.Platform.1.7.0\build\netstandard2.0\Microsoft.Testing.Platform.targets'))" />
        <Error Condition="!Exists('..\packages\Microsoft.Testing.Platform.MSBuild.1.7.0\build\Microsoft.Testing.Platform.MSBuild.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Testing.Platform.MSBuild.1.7.0\build\Microsoft.Testing.Platform.MSBuild.props'))" />
        <Error Condition="!Exists('..\packages\Microsoft.Testing.Platform.MSBuild.1.7.0\build\Microsoft.Testing.Platform.MSBuild.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Testing.Platform.MSBuild.1.7.0\build\Microsoft.Testing.Platform.MSBuild.targets'))" />
        <Error Condition="!Exists('..\packages\Microsoft.Testing.Extensions.Telemetry.1.7.0\build\netstandard2.0\Microsoft.Testing.Extensions.Telemetry.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Testing.Extensions.Telemetry.1.7.0\build\netstandard2.0\Microsoft.Testing.Extensions.Telemetry.props'))" />
        <Error Condition="!Exists('..\packages\NUnit3TestAdapter.5.1.0-alpha.3\build\net462\NUnit3TestAdapter.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NUnit3TestAdapter.5.1.0-alpha.3\build\net462\NUnit3TestAdapter.props'))" />
        <Error Condition="!Exists('..\packages\NUnit3TestAdapter.5.1.0-alpha.3\build\net462\NUnit3TestAdapter.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NUnit3TestAdapter.5.1.0-alpha.3\build\net462\NUnit3TestAdapter.targets'))" />
    </Target>
    <Import Project="..\packages\Verify.28.13.0\build\Verify.targets" Condition="Exists('..\packages\Verify.28.13.0\build\Verify.targets')"/>
    <Import Project="..\packages\Reqnroll.2.4.1\build\Reqnroll.targets" Condition="Exists('..\packages\Reqnroll.2.4.1\build\Reqnroll.targets')" />
    <Import Project="..\packages\Reqnroll.Tools.MsBuild.Generation.2.4.1\build\Reqnroll.Tools.MsBuild.Generation.targets" Condition="Exists('..\packages\Reqnroll.Tools.MsBuild.Generation.2.4.1\build\Reqnroll.Tools.MsBuild.Generation.targets')" />
    <Import Project="..\packages\Reqnroll.NUnit.2.4.1\build\Reqnroll.NUnit.targets" Condition="Exists('..\packages\Reqnroll.NUnit.2.4.1\build\Reqnroll.NUnit.targets')" />
    <Import Project="..\packages\Microsoft.Testing.Platform.1.7.0\build\netstandard2.0\Microsoft.Testing.Platform.targets" Condition="Exists('..\packages\Microsoft.Testing.Platform.1.7.0\build\netstandard2.0\Microsoft.Testing.Platform.targets')" />
    <Import Project="..\packages\Microsoft.Testing.Platform.MSBuild.1.7.0\build\Microsoft.Testing.Platform.MSBuild.targets" Condition="Exists('..\packages\Microsoft.Testing.Platform.MSBuild.1.7.0\build\Microsoft.Testing.Platform.MSBuild.targets')" />
    <Import Project="..\packages\NUnit3TestAdapter.5.1.0-alpha.3\build\net462\NUnit3TestAdapter.targets" Condition="Exists('..\packages\NUnit3TestAdapter.5.1.0-alpha.3\build\net462\NUnit3TestAdapter.targets')" />
    <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
         Other similar extension points exist, see Microsoft.Common.targets.
    <Target Name="BeforeBuild">
    </Target>
    <Target Name="AfterBuild">
    </Target>
    -->

</Project>

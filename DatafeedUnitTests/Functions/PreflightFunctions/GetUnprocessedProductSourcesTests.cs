using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using Datafeed_v2.Models;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Functions.ProductSourceFunctions;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Functions.PreflightFunctions;

[TestFixture]
public class GetUnprocessedProductSourcesTests
{
    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private Mock<DbSet<ProductSourcesForNopCategory>> _mockProductSourcesDbSet;
    private List<ProductSourcesForNopCategory> _productSourcesData;

    [SetUp]
    public void Setup()
    {
        // Initialize test data
        _productSourcesData =
        [
            new ProductSourcesForNopCategory
            {
                ID = 1,
                CategoryId = 10,
                CategoryName = "Test Category 1",
                SourceTypeId = 1,
                SourceTypeName = "Source Type 1",
                ProductSku = "TEST-SKU-1",
                ProductShortDescription = "Short description 1",
                ProductLongDescription = "Long description 1",
                CostPrice = 10.99m,
                SellPrice = 15.99m,
                StatusId = (int)ProductSourceStatuses.New
            },

            new ProductSourcesForNopCategory
            {
                ID = 2,
                CategoryId = 10,
                CategoryName = "Test Category 1",
                SourceTypeId = 2,
                SourceTypeName = "Source Type 2",
                ProductSku = "TEST-SKU-2",
                ProductShortDescription = "Short description 2",
                ProductLongDescription = "Long description 2",
                CostPrice = 20.99m,
                SellPrice = 25.99m,
                StatusId = (int)ProductSourceStatuses.New
            },

            new ProductSourcesForNopCategory
            {
                ID = 3,
                CategoryId = 20,
                CategoryName = "Test Category 2",
                SourceTypeId = 1,
                SourceTypeName = "Source Type 1",
                ProductSku = "TEST-SKU-3",
                ProductShortDescription = "Short description 3",
                ProductLongDescription = "Long description 3",
                CostPrice = 30.99m,
                SellPrice = 35.99m,
                StatusId = (int)ProductSourceStatuses.Published // This one should not be returned
            }
        ];

        // Setup mock DbSet
        _mockProductSourcesDbSet = _productSourcesData.GetAsyncQueryableMockDbSet().AsMock();

        // Setup mock DbContext
        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockDbContext.Setup(db => db.ProductSourcesForNopCategory).Returns(_mockProductSourcesDbSet.Object);
    }

    [Test]
    public async Task Should_Return_Only_New_Product_Sources()
    {
        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetUnprocessedProductSources(_mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Is.Not.Null);
        Assert.That(result.Value.Count, Is.EqualTo(2));
        Assert.That(result.Value.All(ps => ps.StatusId == (int)ProductSourceStatuses.New), Is.True);
        Assert.That(result.Value.Select(ps => ps.ID).OrderBy(id => id).ToList(), Is.EqualTo(new[] { 1, 2 }));
    }

    [Test]
    public async Task Should_Return_Empty_List_When_No_New_Product_Sources_Exist()
    {
        // Arrange
        var emptyData = new List<ProductSourcesForNopCategory>();
        var emptyDbSet = emptyData.GetAsyncQueryableMockDbSet().AsMock();
        _mockDbContext.Setup(db => db.ProductSourcesForNopCategory).Returns(emptyDbSet.Object);

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetUnprocessedProductSources(_mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Is.Not.Null);
        Assert.That(result.Value.Count, Is.EqualTo(0));
    }

    [Test]
    public async Task Should_Return_Failure_When_Exception_Occurs()
    {
        // Arrange
        const string expectedErrorMessage = "Database connection error";
        _mockDbContext.Setup(db => db.ProductSourcesForNopCategory)
            .Throws(new Exception(expectedErrorMessage));

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetUnprocessedProductSources(_mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to retrieve unprocessed product sources"));
        Assert.That(result.Error, Does.Contain(expectedErrorMessage));
    }
}
using System;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using FandoogleReflectorService;
using FandoogleReflectorService.Models;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Functions.PreflightFunctions;

[TestFixture]
public class GetFandoogleReflectorPricingTests
{
    private const string ExpectedSku1 = "SKU001";
    private const string ExpectedSku2 = "SKU002";
    private const string ExpectedSku3 = "SKU003";
    private const decimal ExpectedPrice1 = 100.50m;
    private const decimal ExpectedPrice2 = 200.75m;
    private const string ExpectedErrorMessage = "Service error";
    private const string ExpectedResponseErrorMessage = "No pricing available";

    private Mock<Service> _mockFandoogleService;

    [SetUp]
    public void Setup()
    {
        // Initialise mock Fandoogle Reflector service
        _mockFandoogleService = new Mock<Service>(new RateLimiter(1, 1), null);

        // Setup default responses for valid SKUs
        _mockFandoogleService.Setup(service => service.RequestPricing(ExpectedSku1))
            .ReturnsAsync(Result.Success(new RequestPricingResponse
            {
                Error = false,
                QueryResult = [ExpectedPrice1],
                QueriedStockCode = ExpectedSku1
            }));

        _mockFandoogleService.Setup(service => service.RequestPricing(ExpectedSku2))
            .ReturnsAsync(Result.Success(new RequestPricingResponse
            {
                Error = false,
                QueryResult = [ExpectedPrice2],
                QueriedStockCode = ExpectedSku2
            }));
    }

    [Test]
    public async Task Should_Return_Failure_When_Skus_Are_Null()
    {
        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetFandoogleReflectorPricing(
            null, _mockFandoogleService.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("SKU list cannot be null or empty."));
    }

    [Test]
    public async Task Should_Return_Failure_When_Skus_Are_Empty()
    {
        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetFandoogleReflectorPricing(
            [], _mockFandoogleService.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("SKU list cannot be null or empty."));
    }

    [Test]
    public async Task Should_Return_Success_With_Pricing_For_Valid_Skus()
    {
        // Arrange
        var skus = new[] { ExpectedSku1, ExpectedSku2 };

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetFandoogleReflectorPricing(
            skus, _mockFandoogleService.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(2));

        // Check first SKU
        Assert.That(result.Value[0].sku, Is.EqualTo(ExpectedSku1));
        Assert.That(result.Value[0].sellPrice, Is.EqualTo(ExpectedPrice1));

        // Check second SKU
        Assert.That(result.Value[1].sku, Is.EqualTo(ExpectedSku2));
        Assert.That(result.Value[1].sellPrice, Is.EqualTo(ExpectedPrice2));
    }

    [Test]
    public async Task Should_Skip_Sku_When_Sku_Is_Null_Or_Whitespace()
    {
        // Arrange
        var skus = new[] { ExpectedSku1, null, "", " ", ExpectedSku2 };

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetFandoogleReflectorPricing(
            skus, _mockFandoogleService.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(2));
        Assert.That(result.Value[0].sku, Is.EqualTo(ExpectedSku1));
        Assert.That(result.Value[1].sku, Is.EqualTo(ExpectedSku2));
    }

    [Test]
    public async Task Should_Skip_Sku_When_RequestPricing_Returns_Failure()
    {
        // Arrange
        var skus = new[] { ExpectedSku1, ExpectedSku3 };

        // Setup mock to return failure for ExpectedSku3
        _mockFandoogleService.Setup(service => service.RequestPricing(ExpectedSku3))
            .ReturnsAsync(Result.Failure<RequestPricingResponse>(ExpectedErrorMessage));

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetFandoogleReflectorPricing(
            skus, _mockFandoogleService.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(1));
        Assert.That(result.Value[0].sku, Is.EqualTo(ExpectedSku1));
        Assert.That(result.Value[0].sellPrice, Is.EqualTo(ExpectedPrice1));
    }

    [Test]
    public async Task Should_Skip_Sku_When_Response_Has_Error_Flag()
    {
        // Arrange
        var skus = new[] { ExpectedSku1, ExpectedSku3 };

        // Setup mock to return response with Error=true for ExpectedSku3
        _mockFandoogleService.Setup(service => service.RequestPricing(ExpectedSku3))
            .ReturnsAsync(Result.Success(new RequestPricingResponse
            {
                Error = true,
                ErrorMessage = ExpectedResponseErrorMessage,
                QueriedStockCode = ExpectedSku3
            }));

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetFandoogleReflectorPricing(
            skus, _mockFandoogleService.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(1));
        Assert.That(result.Value[0].sku, Is.EqualTo(ExpectedSku1));
        Assert.That(result.Value[0].sellPrice, Is.EqualTo(ExpectedPrice1));
    }

    [Test]
    public async Task Should_Skip_Sku_When_Response_Has_Null_QueryResult()
    {
        // Arrange
        var skus = new[] { ExpectedSku1, ExpectedSku3 };

        // Setup mock to return response with null QueryResult for ExpectedSku3
        _mockFandoogleService.Setup(service => service.RequestPricing(ExpectedSku3))
            .ReturnsAsync(Result.Success(new RequestPricingResponse
            {
                Error = false,
                QueryResult = null,
                QueriedStockCode = ExpectedSku3
            }));

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetFandoogleReflectorPricing(
            skus, _mockFandoogleService.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(1));
        Assert.That(result.Value[0].sku, Is.EqualTo(ExpectedSku1));
        Assert.That(result.Value[0].sellPrice, Is.EqualTo(ExpectedPrice1));
    }

    [Test]
    public async Task Should_Skip_Sku_When_Response_Has_Empty_QueryResult()
    {
        // Arrange
        var skus = new[] { ExpectedSku1, ExpectedSku3 };

        // Setup mock to return response with empty QueryResult for ExpectedSku3
        _mockFandoogleService.Setup(service => service.RequestPricing(ExpectedSku3))
            .ReturnsAsync(Result.Success(new RequestPricingResponse
            {
                Error = false,
                QueryResult = [],
                QueriedStockCode = ExpectedSku3
            }));

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetFandoogleReflectorPricing(
            skus, _mockFandoogleService.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(1));
        Assert.That(result.Value[0].sku, Is.EqualTo(ExpectedSku1));
        Assert.That(result.Value[0].sellPrice, Is.EqualTo(ExpectedPrice1));
    }

    [Test]
    public async Task Should_Only_Process_Distinct_Skus_When_Duplicates_Exist()
    {
        // Arrange
        var skus = new[] { ExpectedSku1, ExpectedSku1, ExpectedSku2 };

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetFandoogleReflectorPricing(
            skus, _mockFandoogleService.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(2));
        _mockFandoogleService.Verify(service => service.RequestPricing(ExpectedSku1), Times.Once);
    }

    [Test]
    public async Task Should_Return_Failure_When_Service_Throws_Exception()
    {
        // Arrange
        var skus = new[] { ExpectedSku1 };
        var expectedException = new Exception("Service connection error");

        // Setup mock to throw exception
        _mockFandoogleService.Setup(service => service.RequestPricing(ExpectedSku1))
            .ThrowsAsync(expectedException);

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetFandoogleReflectorPricing(
            skus, _mockFandoogleService.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("An unexpected error occurred"));
        Assert.That(result.Error, Does.Contain(expectedException.Message));
    }

    [Test]
    public async Task Should_Use_First_Price_When_Multiple_Prices_Returned()
    {
        // Arrange
        var skus = new[] { ExpectedSku3 };
        const decimal expectedFirstPrice = 150.25m;
        const decimal expectedSecondPrice = 160.75m;

        // Setup mock to return multiple prices
        _mockFandoogleService.Setup(service => service.RequestPricing(ExpectedSku3))
            .ReturnsAsync(Result.Success(new RequestPricingResponse
            {
                Error = false,
                QueryResult = [expectedFirstPrice, expectedSecondPrice],
                QueriedStockCode = ExpectedSku3
            }));

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetFandoogleReflectorPricing(
            skus, _mockFandoogleService.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(1));
        Assert.That(result.Value[0].sku, Is.EqualTo(ExpectedSku3));
        Assert.That(result.Value[0].sellPrice, Is.EqualTo(expectedFirstPrice));
    }
}
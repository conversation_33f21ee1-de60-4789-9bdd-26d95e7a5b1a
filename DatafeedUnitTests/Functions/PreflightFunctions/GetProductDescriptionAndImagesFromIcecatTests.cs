using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models.Services.IcecatApiService;
using Datafeed_v2.Services;
using FandoogleReflectorService;
using Moq;
using Moq.Protected;
using NUnit.Framework;

namespace DatafeedUnitTests.Functions.PreflightFunctions;

[TestFixture]
public class GetProductDescriptionAndImagesFromIcecatTests
{
    private const string ExpectedSku1 = "SKU001";
    private const string ExpectedSku2 = "SKU002";
    private const string ExpectedSku3 = "SKU003";
    private const string ExpectedBrand1 = "Brand1";
    private const string ExpectedBrand2 = "Brand2";
    private const string ExpectedBrand3 = "Brand3";
    private const string ExpectedTitle1 = "Product Title 1";
    private const string ExpectedTitle2 = "Product Title 2";
    private const string ExpectedTitle3 = "Product Title 3";
    private const string ShortDescription1 = "Short description 1";
    private const string LongDescription1 = "Long description 1";
    private const string ShortDescription2 = "Short description 2";
    private const string LongDescription2 = "Long description 2";
    private const string ShortDescription3 = "Short description 3";
    private const string LongDescription3 = "Long description 3";

    private Mock<IcecatApiService> _mockIcecatApiService;
    private Mock<HttpMessageHandler> _mockHttpMessageHandler;
    private HttpClient _httpClient;
    private ProductInfo _productInfo1;
    private ProductInfo _productInfo2;
    private ProductInfo _productInfo3;

    [SetUp]
    public void Setup()
    {
        // Initialise test data
        _productInfo1 = CreateProductInfo(
            ExpectedTitle1,
            ShortDescription1,
            LongDescription1,
            ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
            "https://example.com/main1.jpg");

        _productInfo2 = CreateProductInfo(
            ExpectedTitle2,
            ShortDescription2,
            LongDescription2,
            ["https://example.com/image3.jpg"],
            "https://example.com/main2.jpg");

        _productInfo3 = CreateProductInfo(
            ExpectedTitle3,
            ShortDescription3,
            LongDescription3,
            ["https://example.com/image4.jpg", "https://example.com/image5.jpg", "https://example.com/image6.jpg"],
            "https://example.com/main3.jpg");

        // Setup mock Icecat API service
        _mockIcecatApiService = new Mock<IcecatApiService>(new RateLimiter(10), null);

        // Setup default responses for valid SKUs and brands
        _mockIcecatApiService.Setup(api => api.GetProductInfoByManufacturerCode(
                ExpectedBrand1, ExpectedSku1, It.IsAny<List<string>>()))
            .ReturnsAsync(Result.Success(_productInfo1));

        _mockIcecatApiService.Setup(api => api.GetProductInfoByManufacturerCode(
                ExpectedBrand2, ExpectedSku2, It.IsAny<List<string>>()))
            .ReturnsAsync(Result.Success(_productInfo2));

        _mockIcecatApiService.Setup(api => api.GetProductInfoByManufacturerCode(
                ExpectedBrand3, ExpectedSku3, It.IsAny<List<string>>()))
            .ReturnsAsync(Result.Success(_productInfo3));

        // Setup mock HTTP client
        _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
        _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

        // Setup default HTTP responses for image URLs
        SetupMockHttpResponse("https://example.com/image1.jpg", HttpStatusCode.OK, [1, 2, 3]);
        SetupMockHttpResponse("https://example.com/image2.jpg", HttpStatusCode.OK, [4, 5, 6]);
        SetupMockHttpResponse("https://example.com/image3.jpg", HttpStatusCode.OK, [7, 8, 9]);
        SetupMockHttpResponse("https://example.com/image4.jpg", HttpStatusCode.OK, [10, 11, 12]);
        SetupMockHttpResponse("https://example.com/image5.jpg", HttpStatusCode.OK, [13, 14, 15]);
        SetupMockHttpResponse("https://example.com/image6.jpg", HttpStatusCode.NotFound, []);
        SetupMockHttpResponse("https://example.com/main1.jpg", HttpStatusCode.OK, [16, 17, 18]);
        SetupMockHttpResponse("https://example.com/main2.jpg", HttpStatusCode.OK, [19, 20, 21]);
        SetupMockHttpResponse("https://example.com/main3.jpg", HttpStatusCode.OK, [22, 23, 24]);
    }

    private static ProductInfo CreateProductInfo(string title, string shortDesc, string longDesc, string[] galleryUrls,
        string mainImageUrl)
    {
        var productInfo = new ProductInfo
        {
            Message = "OK",
            Data = new ProductInfo.ProductData
            {
                GeneralInfo = new ProductInfo.GeneralInfo
                {
                    Title = title,
                    SummaryDescription = new ProductInfo.ProductSummaryDescription
                    {
                        ShortDescription = shortDesc,
                        LongDescription = longDesc
                    },
                    Description = new ProductInfo.ProductDescription
                    {
                        LongDescription = longDesc
                    }
                },
                Image = new ProductInfo.ProductMainImage
                {
                    HighPic = mainImageUrl
                },
                Gallery = []
            }
        };

        foreach (var url in galleryUrls)
        {
            productInfo.Data.Gallery.Add(new ProductInfo.GalleryItem { Pic = url });
        }

        return productInfo;
    }

    private void SetupMockHttpResponse(string url, HttpStatusCode statusCode, byte[] content)
    {
        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req => req.RequestUri.ToString() == url),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = statusCode,
                Content = new ByteArrayContent(content)
            });
    }

    [Test]
    public async Task Should_Return_Failure_When_SkusWithBrands_Is_Null()
    {
        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetProductDescriptionAndImagesFromIcecat(
            null, _mockIcecatApiService.Object, _httpClient);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("SKU and Brand list cannot be null or empty."));
    }

    [Test]
    public async Task Should_Return_Failure_When_SkusWithBrands_Is_Empty()
    {
        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetProductDescriptionAndImagesFromIcecat(
            [], _mockIcecatApiService.Object, _httpClient);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("SKU and Brand list cannot be null or empty."));
    }

    [Test]
    public async Task Should_Skip_Item_When_Sku_Is_Null_Or_Empty()
    {
        // Arrange
        var skusWithBrands = new[]
        {
            new KeyValuePair<string, string>(null, ExpectedBrand1),
            new KeyValuePair<string, string>(string.Empty, ExpectedBrand2),
            new KeyValuePair<string, string>(ExpectedSku3, ExpectedBrand3)
        };

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetProductDescriptionAndImagesFromIcecat(
            skusWithBrands, _mockIcecatApiService.Object, _httpClient);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(1));
        Assert.That(result.Value[0].sku, Is.EqualTo(ExpectedSku3));
    }

    [Test]
    public async Task Should_Skip_Item_When_Brand_Is_Null_Or_Empty()
    {
        // Arrange
        var skusWithBrands = new[]
        {
            new KeyValuePair<string, string>(ExpectedSku1, null),
            new KeyValuePair<string, string>(ExpectedSku2, string.Empty),
            new KeyValuePair<string, string>(ExpectedSku3, ExpectedBrand3)
        };

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetProductDescriptionAndImagesFromIcecat(
            skusWithBrands, _mockIcecatApiService.Object, _httpClient);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(1));
        Assert.That(result.Value[0].sku, Is.EqualTo(ExpectedSku3));
    }

    [Test]
    public async Task Should_Skip_Item_When_Icecat_Api_Returns_Failure()
    {
        // Arrange
        var skusWithBrands = new[]
        {
            new KeyValuePair<string, string>(ExpectedSku1, ExpectedBrand1),
            new KeyValuePair<string, string>(ExpectedSku2, ExpectedBrand2)
        };

        // Setup mock to return failure for SKU2
        _mockIcecatApiService.Setup(api => api.GetProductInfoByManufacturerCode(
                ExpectedBrand2, ExpectedSku2, It.IsAny<List<string>>()))
            .ReturnsAsync(Result.Failure<ProductInfo>("API error"));

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetProductDescriptionAndImagesFromIcecat(
            skusWithBrands, _mockIcecatApiService.Object, _httpClient);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(1));
        Assert.That(result.Value[0].sku, Is.EqualTo(ExpectedSku1));
    }

    [Test]
    public async Task Should_Return_Success_With_Product_Info_For_Valid_Skus()
    {
        // Arrange
        var skusWithBrands = new[]
        {
            new KeyValuePair<string, string>(ExpectedSku1, ExpectedBrand1),
            new KeyValuePair<string, string>(ExpectedSku2, ExpectedBrand2)
        };

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetProductDescriptionAndImagesFromIcecat(
            skusWithBrands, _mockIcecatApiService.Object, _httpClient);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(2));

        // Check first product
        Assert.That(result.Value[0].sku, Is.EqualTo(ExpectedSku1));
        Assert.That(result.Value[0].title, Is.EqualTo(ExpectedTitle1));
        Assert.That(result.Value[0].shortDescription, Is.EqualTo(ShortDescription1));
        Assert.That(result.Value[0].longDescription, Is.EqualTo(LongDescription1));
        Assert.That(result.Value[0].images.Count, Is.EqualTo(3)); // 2 gallery + 1 main image

        // Check second product
        Assert.That(result.Value[1].sku, Is.EqualTo(ExpectedSku2));
        Assert.That(result.Value[1].title, Is.EqualTo(ExpectedTitle2));
        Assert.That(result.Value[1].shortDescription, Is.EqualTo(ShortDescription2));
        Assert.That(result.Value[1].longDescription, Is.EqualTo(LongDescription2));
        Assert.That(result.Value[1].images.Count, Is.EqualTo(2)); // 1 gallery + 1 main image
    }

    [Test]
    public async Task Should_Skip_Image_When_Http_Request_Fails()
    {
        // Arrange
        var skusWithBrands = new[]
        {
            new KeyValuePair<string, string>(ExpectedSku3, ExpectedBrand3)
        };

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetProductDescriptionAndImagesFromIcecat(
            skusWithBrands, _mockIcecatApiService.Object, _httpClient);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(1));
        Assert.That(result.Value[0].sku, Is.EqualTo(ExpectedSku3));

        // Should only have 3 images (the fourth one has a 404 status code)
        Assert.That(result.Value[0].images.Count, Is.EqualTo(3));
    }

    [Test]
    public async Task Should_Handle_Multiple_Skus_Correctly()
    {
        // Arrange
        var skusWithBrands = new[]
        {
            new KeyValuePair<string, string>(ExpectedSku1, ExpectedBrand1),
            new KeyValuePair<string, string>(ExpectedSku2, ExpectedBrand2),
            new KeyValuePair<string, string>(ExpectedSku3, ExpectedBrand3)
        };

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetProductDescriptionAndImagesFromIcecat(
            skusWithBrands, _mockIcecatApiService.Object, _httpClient);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(3));
        Assert.That(result.Value.Select(v => v.sku).ToArray(),
            Is.EquivalentTo([ExpectedSku1, ExpectedSku2, ExpectedSku3]));
    }

    [Test]
    public async Task Should_Return_Failure_When_Exception_Is_Thrown()
    {
        // Arrange
        var skusWithBrands = new[]
        {
            new KeyValuePair<string, string>(ExpectedSku1, ExpectedBrand1)
        };

        // Setup mock to throw exception
        const string expectedErrorMessage = "API connection error";
        _mockIcecatApiService.Setup(api => api.GetProductInfoByManufacturerCode(
                ExpectedBrand1, ExpectedSku1, It.IsAny<List<string>>()))
            .ThrowsAsync(new Exception(expectedErrorMessage));

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetProductDescriptionAndImagesFromIcecat(
            skusWithBrands, _mockIcecatApiService.Object, _httpClient);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to retrieve Icecat product info"));
        Assert.That(result.Error, Does.Contain(expectedErrorMessage));
    }
}
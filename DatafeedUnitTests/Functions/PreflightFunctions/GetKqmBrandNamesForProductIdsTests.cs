using System;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using KaseyaAPI;
using KaseyaAPI.Functions;
using KaseyaAPI.Models.ApiModels;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Functions.PreflightFunctions;

[TestFixture]
public class GetKqmBrandNamesForProductIdsTests
{
    private const uint ExpectedProductId1 = 101;
    private const uint ExpectedProductId2 = 102;
    private const uint ExpectedProductId3 = 103;
    private const uint ExpectedBrandId1 = 201;
    private const uint ExpectedBrandId2 = 202;
    private const uint ExpectedBrandId3 = 0; // No brand ID
    private const string ExpectedBrandName1 = "Brand1";
    private const string ExpectedBrandName2 = "Brand2";

    private Mock<KaseyaApi> _mockKaseyaApi;
    private Product _product1;
    private Product _product2;
    private Product _product3;
    private Brand _brand1;
    private Brand _brand2;

    [SetUp]
    public void Setup()
    {
        // Initialize test data
        _product1 = new Product
        {
            id = (int)ExpectedProductId1,
            brandID = (int)ExpectedBrandId1
        };

        _product2 = new Product
        {
            id = (int)ExpectedProductId2,
            brandID = (int)ExpectedBrandId2
        };

        _product3 = new Product
        {
            id = (int)ExpectedProductId3,
            brandID = (int)ExpectedBrandId3 // No brand ID
        };

        _brand1 = new Brand
        {
            id = (int)ExpectedBrandId1,
            name = ExpectedBrandName1
        };

        _brand2 = new Brand
        {
            id = (int)ExpectedBrandId2,
            name = ExpectedBrandName2
        };

        // Setup mock Kaseya API
        _mockKaseyaApi = new Mock<KaseyaApi>("https://example.com", "apikey", 25u, Maybe<HttpMethods>.None);

        // Setup default responses for valid product IDs
        _mockKaseyaApi.Setup(api => api.GetProduct(ExpectedProductId1, It.IsAny<Maybe<DateTime>>()))
            .ReturnsAsync(Result.Success<Product, GetProductError>(_product1));

        _mockKaseyaApi.Setup(api => api.GetProduct(ExpectedProductId2, It.IsAny<Maybe<DateTime>>()))
            .ReturnsAsync(Result.Success<Product, GetProductError>(_product2));

        _mockKaseyaApi.Setup(api => api.GetProduct(ExpectedProductId3, It.IsAny<Maybe<DateTime>>()))
            .ReturnsAsync(Result.Success<Product, GetProductError>(_product3));

        // Setup default responses for valid brand IDs
        _mockKaseyaApi.Setup(api => api.GetBrand(ExpectedBrandId1))
            .ReturnsAsync(Result.Success<Brand, GetBrandError>(_brand1));

        _mockKaseyaApi.Setup(api => api.GetBrand(ExpectedBrandId2))
            .ReturnsAsync(Result.Success<Brand, GetBrandError>(_brand2));
    }

    [Test]
    public async Task Should_Return_Failure_When_ProductIds_Are_Null()
    {
        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetKqmBrandNamesForProductIds(
            null, _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("Product ID list cannot be null or empty."));
    }

    [Test]
    public async Task Should_Return_Failure_When_ProductIds_Are_Empty()
    {
        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetKqmBrandNamesForProductIds(
            [], _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("Product ID list cannot be null or empty."));
    }

    [Test]
    public async Task Should_Return_Success_With_Brand_Names_For_Valid_ProductIds()
    {
        // Arrange
        var productIds = new[] { ExpectedProductId1, ExpectedProductId2 };

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetKqmBrandNamesForProductIds(
            productIds, _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(2));

        // Check first product
        Assert.That(result.Value[0].Key, Is.EqualTo(ExpectedProductId1));
        Assert.That(result.Value[0].Value, Is.EqualTo(ExpectedBrandName1));

        // Check second product
        Assert.That(result.Value[1].Key, Is.EqualTo(ExpectedProductId2));
        Assert.That(result.Value[1].Value, Is.EqualTo(ExpectedBrandName2));
    }

    [Test]
    public async Task Should_Skip_Product_When_GetProduct_Returns_Failure()
    {
        // Arrange
        var productIds = new[] { ExpectedProductId1, ExpectedProductId2 };
        const string expectedErrorMessage = "API error";

        // Setup mock to return failure for ProductId2
        _mockKaseyaApi.Setup(api => api.GetProduct(ExpectedProductId2, It.IsAny<Maybe<DateTime>>()))
            .ReturnsAsync(
                Result.Failure<Product, GetProductError>(new GetProductError(GetProductFailure.GetRequestFailed,
                    expectedErrorMessage)));

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetKqmBrandNamesForProductIds(
            productIds, _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(1));
        Assert.That(result.Value[0].Key, Is.EqualTo(ExpectedProductId1));
        Assert.That(result.Value[0].Value, Is.EqualTo(ExpectedBrandName1));
    }

    [Test]
    public async Task Should_Skip_Product_When_Product_Has_No_BrandId()
    {
        // Arrange
        var productIds = new[] { ExpectedProductId1, ExpectedProductId3 };

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetKqmBrandNamesForProductIds(
            productIds, _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(1));
        Assert.That(result.Value[0].Key, Is.EqualTo(ExpectedProductId1));
        Assert.That(result.Value[0].Value, Is.EqualTo(ExpectedBrandName1));
    }

    [Test]
    public async Task Should_Skip_Product_When_GetBrand_Returns_Failure()
    {
        // Arrange
        var productIds = new[] { ExpectedProductId1, ExpectedProductId2 };
        const string expectedErrorMessage = "API error";

        // Setup mock to return failure for BrandId2
        _mockKaseyaApi.Setup(api => api.GetBrand(ExpectedBrandId2))
            .ReturnsAsync(Result.Failure<Brand, GetBrandError>(new GetBrandError
            {
                Error = expectedErrorMessage,
                Type = GetBrandFailure.GetRequestFailed
            }));

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetKqmBrandNamesForProductIds(
            productIds, _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(1));
        Assert.That(result.Value[0].Key, Is.EqualTo(ExpectedProductId1));
        Assert.That(result.Value[0].Value, Is.EqualTo(ExpectedBrandName1));
    }

    [Test]
    public async Task Should_Only_Process_Distinct_ProductIds_When_Duplicates_Exist()
    {
        // Arrange
        var productIds = new[] { ExpectedProductId1, ExpectedProductId1, ExpectedProductId2 };

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetKqmBrandNamesForProductIds(
            productIds, _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.Count, Is.EqualTo(2));
        _mockKaseyaApi.Verify(api => api.GetProduct(ExpectedProductId1, It.IsAny<Maybe<DateTime>>()), Times.Once);
    }

    [Test]
    public async Task Should_Return_Failure_When_Api_Throws_Exception()
    {
        // Arrange
        var productIds = new[] { ExpectedProductId1 };
        const string expectedErrorMessage = "API connection error";

        // Setup mock to throw exception
        _mockKaseyaApi.Setup(api => api.GetProduct(ExpectedProductId1, It.IsAny<Maybe<DateTime>>()))
            .ThrowsAsync(new Exception(expectedErrorMessage));

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetKqmBrandNamesForProductIds(
            productIds, _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to retrieve KQM brand names"));
        Assert.That(result.Error, Does.Contain(expectedErrorMessage));
    }
}
using System;
using System.Linq;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using KaseyaAPI;
using KaseyaAPI.Functions;
using KaseyaAPI.Models.ApiModels;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Functions.PreflightFunctions;

[TestFixture]
public class GetKqmSupplierStockCountAndCostPriceForProductIdsTests
{
    private const uint ExpectedProductId1 = 101;
    private const uint ExpectedProductId2 = 102;
    private const uint ExpectedProductId3 = 103;
    private Mock<KaseyaApi> _mockKaseyaApi;
    private ProductSupplier[] _suppliers;

    [SetUp]
    public void Setup()
    {
        // Initialise test data with cost prices
        _suppliers =
        [
            // Product 1: Two suppliers, different costs
            new ProductSupplier { productID = (int)ExpectedProductId1, id = 1, quantity = 5, cost = 10.50m },
            new ProductSupplier
                { productID = (int)ExpectedProductId1, id = 2, quantity = 10, cost = 9.99m }, // Cheaper cost
            // Product 2: One supplier
            new ProductSupplier { productID = (int)ExpectedProductId2, id = 3, quantity = 15, cost = 25.00m },
            // Product 3: One supplier, zero quantity, zero cost
            new ProductSupplier { productID = (int)ExpectedProductId3, id = 4, quantity = 0, cost = 0m }
        ];

        // Setup mock KaseyaApi
        _mockKaseyaApi = new Mock<KaseyaApi>("https://example.com", "apikey", 25u, Maybe<HttpMethods>.None);

        // Setup default response for GetProductSuppliersForProducts
        _mockKaseyaApi
            .Setup(api => api.GetProductSuppliersForProducts(It.IsAny<uint[]>(), It.IsAny<Maybe<KaseyaApi>>()))
            .ReturnsAsync((uint[] ids, Maybe<KaseyaApi> _) =>
            {
                var filteredSuppliers = _suppliers.Where(s => ids.Contains((uint)s.productID)).ToArray();
                return Result.Success<ProductSupplier[], GetProductSuppliersForProductsError>(filteredSuppliers);
            });
    }

    [Test]
    public async Task Should_Return_Failure_When_ProductIds_Are_Null()
    {
        // Act
        var result =
            await Datafeed_v2.Functions.PreflightFunctions.GetKqmSupplierStockCountAndCostPriceForProductIds(null,
                _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("Product ID list cannot be null or empty."));
    }

    [Test]
    public async Task Should_Return_Failure_When_ProductIds_Are_Empty()
    {
        // Act
        var result =
            await Datafeed_v2.Functions.PreflightFunctions.GetKqmSupplierStockCountAndCostPriceForProductIds([],
                _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("Product ID list cannot be null or empty."));
    }

    [Test]
    public async Task Should_Return_Failure_When_Api_Returns_Failure()
    {
        // Arrange
        const string expectedErrorMessage = "API error";
        _mockKaseyaApi
            .Setup(api => api.GetProductSuppliersForProducts(It.IsAny<uint[]>(), It.IsAny<Maybe<KaseyaApi>>()))
            .ReturnsAsync(Result.Failure<ProductSupplier[], GetProductSuppliersForProductsError>(
                new GetProductSuppliersForProductsError(
                    GetProductSuppliersForProductsFailure.FailedToGetProductSuppliers, Maybe<ReadAllPagesFailure>.None,
                    expectedErrorMessage, Maybe<ProductSupplier[][]>.None)));

        // Act
        var result = await Datafeed_v2.Functions.PreflightFunctions.GetKqmSupplierStockCountAndCostPriceForProductIds(
            [ExpectedProductId1], _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to get product suppliers from KQM"));
        Assert.That(result.Error, Does.Contain(expectedErrorMessage));
    }

    [Test]
    public async Task Should_Return_Success_With_Correct_Stock_Counts_And_Min_Cost_Prices()
    {
        // Arrange
        var productIds = new[] { ExpectedProductId1, ExpectedProductId2, ExpectedProductId3 };

        // Act
        var result =
            await Datafeed_v2.Functions.PreflightFunctions.GetKqmSupplierStockCountAndCostPriceForProductIds(productIds,
                _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Has.Count.EqualTo(3));

        // Product 1: quantity 15 (5 + 10), min cost 9.99m
        var product1Result = result.Value.FirstOrDefault(kv => kv.Key == ExpectedProductId1);
        Assert.That(product1Result.Key, Is.EqualTo(ExpectedProductId1));
        Assert.That(product1Result.Value.stockCount, Is.EqualTo(15u)); // Check stock count from tuple
        Assert.That(product1Result.Value.costPrice, Is.EqualTo(9.99m)); // Check min cost price from tuple

        // Product 2: quantity 15, cost 25.00m
        var product2Result = result.Value.FirstOrDefault(kv => kv.Key == ExpectedProductId2);
        Assert.That(product2Result.Key, Is.EqualTo(ExpectedProductId2));
        Assert.That(product2Result.Value.stockCount, Is.EqualTo(15u)); // Check stock count from tuple
        Assert.That(product2Result.Value.costPrice, Is.EqualTo(25.00m)); // Check cost price from tuple

        // Product 3: quantity 0, cost 0m
        var product3Result = result.Value.FirstOrDefault(kv => kv.Key == ExpectedProductId3);
        Assert.That(product3Result.Key, Is.EqualTo(ExpectedProductId3));
        Assert.That(product3Result.Value.stockCount, Is.EqualTo(0u)); // Check stock count from tuple
        Assert.That(product3Result.Value.costPrice, Is.EqualTo(0m)); // Check cost price from tuple
    }

    [Test]
    public async Task Should_Return_Success_With_Empty_List_When_No_Suppliers_Found()
    {
        // Arrange
        const uint productIdWithNoSuppliers = 999;
        var productIds = new[] { productIdWithNoSuppliers };

        // Act
        var result =
            await Datafeed_v2.Functions.PreflightFunctions.GetKqmSupplierStockCountAndCostPriceForProductIds(productIds,
                _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Is.Empty,
            "Should return empty list when no suppliers found for any requested product ID");
    }

    [Test]
    public async Task Should_Return_Success_With_Aggregated_Stock_And_Min_Cost_From_Multiple_Suppliers()
    {
        // Arrange
        var productIds = new[] { ExpectedProductId1 };
        var multipleSuppliers = new[]
        {
            new ProductSupplier { productID = (int)ExpectedProductId1, id = 1, quantity = 5, cost = 12.00m },
            new ProductSupplier
                { productID = (int)ExpectedProductId1, id = 2, quantity = 10, cost = 10.50m }, // Min cost
            new ProductSupplier { productID = (int)ExpectedProductId1, id = 3, quantity = 20, cost = 11.00m }
        };

        _mockKaseyaApi
            .Setup(api => api.GetProductSuppliersForProducts(It.IsAny<uint[]>(), It.IsAny<Maybe<KaseyaApi>>()))
            .ReturnsAsync(Result.Success<ProductSupplier[], GetProductSuppliersForProductsError>(multipleSuppliers));

        // Act
        var result =
            await Datafeed_v2.Functions.PreflightFunctions.GetKqmSupplierStockCountAndCostPriceForProductIds(productIds,
                _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Has.Count.EqualTo(1));
        Assert.That(result.Value[0].Key, Is.EqualTo(ExpectedProductId1));
        Assert.That(result.Value[0].Value.stockCount, Is.EqualTo(35u), "Should sum quantities from all suppliers");
        Assert.That(result.Value[0].Value.costPrice, Is.EqualTo(10.50m), "Should return the minimum cost price");
    }

    [Test]
    public async Task Should_Return_Failure_When_Api_Throws_Exception()
    {
        // Arrange
        var productIds = new[] { ExpectedProductId1 };
        const string expectedErrorMessage = "API connection error";

        _mockKaseyaApi
            .Setup(api => api.GetProductSuppliersForProducts(It.IsAny<uint[]>(), It.IsAny<Maybe<KaseyaApi>>()))
            .ThrowsAsync(new Exception(expectedErrorMessage));

        // Act
        var result =
            await Datafeed_v2.Functions.PreflightFunctions.GetKqmSupplierStockCountAndCostPriceForProductIds(productIds,
                _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to retrieve KQM supplier stock counts and cost prices"));
        Assert.That(result.Error, Does.Contain(expectedErrorMessage));
    }

    [Test]
    public async Task Should_Return_Zero_Cost_When_All_Supplier_Costs_Are_Zero()
    {
        // Arrange
        var productIds = new[] { ExpectedProductId1 };
        var zeroCostSuppliers = new[]
        {
            new ProductSupplier { productID = (int)ExpectedProductId1, id = 1, quantity = 5, cost = 0m },
            new ProductSupplier { productID = (int)ExpectedProductId1, id = 2, quantity = 10, cost = 0m }
        };

        _mockKaseyaApi
            .Setup(api => api.GetProductSuppliersForProducts(It.IsAny<uint[]>(), It.IsAny<Maybe<KaseyaApi>>()))
            .ReturnsAsync(Result.Success<ProductSupplier[], GetProductSuppliersForProductsError>(zeroCostSuppliers));

        // Act
        var result =
            await Datafeed_v2.Functions.PreflightFunctions.GetKqmSupplierStockCountAndCostPriceForProductIds(productIds,
                _mockKaseyaApi.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Has.Count.EqualTo(1));
        Assert.That(result.Value[0].Key, Is.EqualTo(ExpectedProductId1));
        Assert.That(result.Value[0].Value.stockCount, Is.EqualTo(15u));
        Assert.That(result.Value[0].Value.costPrice, Is.EqualTo(0m),
            "Should return 0 cost if all supplier costs are 0");
    }
}
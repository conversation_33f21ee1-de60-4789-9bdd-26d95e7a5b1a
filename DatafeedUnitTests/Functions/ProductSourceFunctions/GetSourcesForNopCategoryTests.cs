using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Threading.Tasks;
using Datafeed_v2.Models;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Functions.ProductSourceFunctions;

[TestFixture]
public class GetSourcesForNopCategoryTests
{
    private const string ProductSku1 = "SKU-001";
    private const string ProductSku2 = "SKU-002";
    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private List<NopCategories> _nopCategoriesData;
    private List<ProductSourcesForNopCategory> _productSourcesData;
    private Mock<DbSet<NopCategories>> _mockNopCategoriesDbSet;
    private Mock<DbSet<ProductSourcesForNopCategory>> _mockProductSourcesDbSet;

    [SetUp]
    public void Setup()
    {
        // Initialize test data
        _nopCategoriesData =
        [
            new NopCategories { ID = 1, CategoryId = 10, Name = "Test Category 1" },
            new NopCategories { ID = 2, CategoryId = 20, Name = "Test Category 2" }
        ];

        _productSourcesData =
        [
            new ProductSourcesForNopCategory
            {
                CategoryId = 10,
                CategoryName = "Test Category 1",
                SourceTypeId = 1,
                SourceTypeName = "Source Type 1",
                ProductSku = ProductSku1,
                ProductShortDescription = "Short description 1",
                ProductLongDescription = "Long description 1",
                CostPrice = 10.99m,
                SellPrice = 15.99m,
                StatusId = (int)ProductSourceStatuses.Published
            },

            new ProductSourcesForNopCategory
            {
                CategoryId = 10,
                CategoryName = "Test Category 1",
                SourceTypeId = 2,
                SourceTypeName = "Source Type 2",
                ProductSku = ProductSku2,
                ProductShortDescription = "Short description 2",
                ProductLongDescription = "Long description 2",
                CostPrice = 20.99m,
                SellPrice = 25.99m,
                StatusId = (int)ProductSourceStatuses.New
            },

            new ProductSourcesForNopCategory
            {
                CategoryId = 20,
                CategoryName = "Test Category 2",
                SourceTypeId = 1,
                SourceTypeName = "Source Type 1",
                ProductSku = "SKU-003",
                ProductShortDescription = "Short description 3",
                ProductLongDescription = "Long description 3",
                CostPrice = 30.99m,
                SellPrice = 35.99m,
                StatusId = (int)ProductSourceStatuses.Published
            }
        ];

        // Setup mock DbSets
        _mockNopCategoriesDbSet = _nopCategoriesData.GetAsyncQueryableMockDbSet().AsMock();
        _mockProductSourcesDbSet = _productSourcesData.GetAsyncQueryableMockDbSet().AsMock();

        // Setup mock DbContext
        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockDbContext.Setup(c => c.NopCategories).Returns(_mockNopCategoriesDbSet.Object);
        _mockDbContext.Setup(c => c.ProductSourcesForNopCategory).Returns(_mockProductSourcesDbSet.Object);
    }

    [Test]
    public async Task Should_Return_Sources_When_Category_Exists()
    {
        // Arrange
        const uint nopCategoryId = 10;

        // Act
        var result =
            await Datafeed_v2.Functions.ProductSourceFunctions.GetSourcesForNopCategory(nopCategoryId,
                _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Is.Not.Null);
        Assert.That(result.Value.Count, Is.EqualTo(2));
        Assert.That(result.Value[0].CategoryId, Is.EqualTo(nopCategoryId));
        Assert.That(result.Value[0].ProductSku, Is.EqualTo(ProductSku1));
        Assert.That(result.Value[1].ProductSku, Is.EqualTo(ProductSku2));
    }

    [Test]
    public async Task Should_Return_Failure_When_Category_Does_Not_Exist()
    {
        // Arrange
        const uint nonExistentCategoryId = 99;

        // Act
        var result =
            await Datafeed_v2.Functions.ProductSourceFunctions.GetSourcesForNopCategory(nonExistentCategoryId,
                _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("does not exist"));
    }

    [Test]
    public async Task Should_Return_Empty_List_When_No_Sources_For_Valid_Category()
    {
        // Arrange
        const uint categoryId = 30;

        // Add a category without sources
        _nopCategoriesData.Add(new NopCategories { ID = 3, CategoryId = 30, Name = "Test Category 3" });

        // Refresh the mock DbSet
        _mockNopCategoriesDbSet = _nopCategoriesData.GetAsyncQueryableMockDbSet().AsMock();
        _mockDbContext.Setup(c => c.NopCategories).Returns(_mockNopCategoriesDbSet.Object);

        // Act
        var result =
            await Datafeed_v2.Functions.ProductSourceFunctions.GetSourcesForNopCategory(categoryId,
                _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Is.Not.Null);
        Assert.That(result.Value, Is.Empty);
    }

    [Test]
    public async Task Should_Return_Failure_When_Exception_Occurs()
    {
        // Arrange
        const uint categoryId = 10;

        // Setup mock to throw an exception when querying
        const string expectedErrorMessage = "Test exception";
        _mockDbContext.Setup(c => c.NopCategories)
            .Throws(new Exception(expectedErrorMessage));

        // Act
        var result =
            await Datafeed_v2.Functions.ProductSourceFunctions.GetSourcesForNopCategory(categoryId,
                _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to retrieve product sources"));
        Assert.That(result.Error, Does.Contain(expectedErrorMessage));
    }
}
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using Datafeed_v2.Models;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Functions.ProductSourceFunctions;

[TestFixture]
public class CreateNewProductSourceFromSkuTests
{
    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private Mock<DbSet<ProductSourceList>> _mockProductSourceListDbSet;
    private Mock<DbSet<ProductSource>> _mockProductSourceDbSet;
    private List<ProductSourceList> _sourceListData;
    private List<ProductSource> _productSourceData;

    private const uint ValidSourceTypeId = 1;
    private const uint AnotherValidSourceTypeId = 2;
    private const uint InvalidSourceTypeId = 99;
    private const string ExistingSku = "EXISTING-SKU";
    private const string NewSku = "NEW-SKU";

    [SetUp]
    public void Setup()
    {
        // Initialise data collections
        _sourceListData =
        [
            new ProductSourceList { ID = (int)ValidSourceTypeId, Name = "TestSourceType1" },
            new ProductSourceList { ID = (int)AnotherValidSourceTypeId, Name = "TestSourceType2" }
        ];

        _productSourceData =
        [
            new ProductSource
            {
                ID = 1,
                ProductSku = ExistingSku,
                SourceTypeIds = ValidSourceTypeId.ToString(),
                Brand = "TestBrand",
                ProductTitle = "Existing Product",
                CostPrice = 10.0m,
                Status = 1
            }
        ];

        // Setup mock DbSets
        _mockProductSourceListDbSet = _sourceListData.GetAsyncQueryableMockDbSet().AsMock();
        _mockProductSourceDbSet = _productSourceData.GetAsyncQueryableMockDbSet().AsMock();

        // Setup mock DbContext
        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockDbContext.Setup(db => db.ProductSourceList).Returns(_mockProductSourceListDbSet.Object);
        _mockDbContext.Setup(db => db.ProductSource).Returns(_mockProductSourceDbSet.Object);
        _mockDbContext.Setup(db => db.SaveChangesAsync()).ReturnsAsync(1); // Default success for SaveChangesAsync
    }

    [Test]
    public async Task Should_Return_Failure_When_Sku_Is_Null()
    {
        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourceFromSku(
            ValidSourceTypeId, null, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("SKU cannot be null or empty"));
    }

    [Test]
    public async Task Should_Return_Failure_When_Sku_Is_Empty()
    {
        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourceFromSku(
            ValidSourceTypeId, string.Empty, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("SKU cannot be null or empty"));
    }

    [Test]
    public async Task Should_Return_Failure_When_Sku_Is_Whitespace()
    {
        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourceFromSku(
            ValidSourceTypeId, "   ", _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("SKU cannot be null or empty"));
    }

    [Test]
    public async Task Should_Return_Failure_When_SourceType_Not_Found()
    {
        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourceFromSku(
            InvalidSourceTypeId, NewSku, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo($"Invalid ProductSourceList ID: {InvalidSourceTypeId}"));
        _mockProductSourceDbSet.Verify(m => m.Add(It.IsAny<ProductSource>()), Times.Never);
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Never); // No save should be attempted
    }

    [Test]
    public async Task Should_Return_Success_And_Create_New_Product_When_Sku_Not_Exists()
    {
        // Arrange
        var initialCount = _productSourceData.Count;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourceFromSku(
            ValidSourceTypeId, NewSku, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Is.Not.Null);
        Assert.That(result.Value.ProductSku, Is.EqualTo(NewSku));
        Assert.That(result.Value.SourceTypeIds, Is.EqualTo(ValidSourceTypeId.ToString()));
        Assert.That(result.Value.Brand, Is.EqualTo(string.Empty));
        Assert.That(result.Value.ProductTitle, Is.EqualTo(string.Empty));
        Assert.That(result.Value.CostPrice, Is.EqualTo(0));
        Assert.That(result.Value.Status, Is.EqualTo((int)ProductSourceStatuses.New));

        _mockProductSourceDbSet.Verify(m => m.Add(It.Is<ProductSource>(p => p.ProductSku == NewSku)), Times.Once);
        _mockDbContext.Verify(m => m.SaveChangesAsync(),
            Times.Once); // Only one save expected from the inner CreateNewProductSource call
        Assert.That(_productSourceData.Count,
            Is.EqualTo(initialCount + 1)); // Verify item was added to the underlying list
    }

    [Test]
    public async Task Should_Return_Success_And_Update_Existing_Product_When_Sku_Exists_With_Different_SourceType()
    {
        // Arrange
        var existingProduct = _productSourceData.First(p => p.ProductSku == ExistingSku);
        var initialSourceTypeIds = existingProduct.SourceTypeIds; // Should be "1"
        var expectedSourceTypeIds = $"{initialSourceTypeIds},{AnotherValidSourceTypeId}";

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourceFromSku(
            AnotherValidSourceTypeId, ExistingSku, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Is.Not.Null);
        Assert.That(result.Value.ID, Is.EqualTo(existingProduct.ID)); // Should be the same product
        Assert.That(result.Value.ProductSku, Is.EqualTo(ExistingSku));
        Assert.That(result.Value.SourceTypeIds, Is.EqualTo(expectedSourceTypeIds)); // SourceTypeIds should be updated

        _mockProductSourceDbSet.Verify(m => m.Add(It.IsAny<ProductSource>()), Times.Never); // No new product added
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Once); // Save should be called for the update
    }

    [Test]
    public async Task Should_Return_Success_And_Not_Update_Existing_Product_When_Sku_Exists_With_Same_SourceType()
    {
        // Arrange
        var existingProduct = _productSourceData.First(p => p.ProductSku == ExistingSku);
        var initialSourceTypeIds = existingProduct.SourceTypeIds; // Should be "1"

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourceFromSku(
            ValidSourceTypeId, ExistingSku, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Is.Not.Null);
        Assert.That(result.Value.ID, Is.EqualTo(existingProduct.ID)); // Should be the same product
        Assert.That(result.Value.ProductSku, Is.EqualTo(ExistingSku));
        Assert.That(result.Value.SourceTypeIds,
            Is.EqualTo(initialSourceTypeIds)); // SourceTypeIds should NOT be updated

        _mockProductSourceDbSet.Verify(m => m.Add(It.IsAny<ProductSource>()), Times.Never); // No new product added
        // SaveChanges should NOT be called within the "existing product" block because the ID was already present
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_Db_Exception_During_SourceType_Check()
    {
        // Arrange
        const string exceptionMessage = "Database connection failed";
        _mockDbContext.Setup(db => db.ProductSourceList)
            .Throws(new Exception(exceptionMessage));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourceFromSku(
            ValidSourceTypeId, NewSku, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to create product source from SKU"));
        Assert.That(result.Error, Does.Contain(exceptionMessage));
    }

    [Test]
    public async Task Should_Return_Failure_When_Db_Exception_During_Product_Creation()
    {
        // Arrange
        const string exceptionMessage = "Constraint violation";
        // Setup to pass the SourceType check but fail on SaveChangesAsync during Add
        _mockDbContext.Setup(db => db.SaveChangesAsync())
            .ThrowsAsync(new Exception(exceptionMessage));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourceFromSku(
            ValidSourceTypeId, NewSku, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        // The error comes from the nested CreateNewProductSource call
        Assert.That(result.Error, Does.Contain("Failed to create product source:"));
        Assert.That(result.Error, Does.Contain(exceptionMessage));
        _mockProductSourceDbSet.Verify(m => m.Add(It.Is<ProductSource>(p => p.ProductSku == NewSku)),
            Times.Once); // Add was attempted
    }

    [Test]
    public async Task Should_Return_Failure_When_Db_Exception_During_Product_Update()
    {
        // Arrange
        const string exceptionMessage = "Update conflict";
        // Setup to pass the SourceType check, find existing product, but fail on SaveChangesAsync during update
        _mockDbContext.Setup(db => db.SaveChangesAsync())
            .ThrowsAsync(new Exception(exceptionMessage));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourceFromSku(
            AnotherValidSourceTypeId, ExistingSku,
            _mockDbContext.Object); // Use a different source type to trigger update

        // Assert
        Assert.That(result.IsFailure, Is.True);
        // The error comes from the nested CreateNewProductSource call
        Assert.That(result.Error, Does.Contain("Failed to create product source:"));
        Assert.That(result.Error, Does.Contain(exceptionMessage));
        _mockProductSourceDbSet.Verify(m => m.Add(It.IsAny<ProductSource>()), Times.Never); // Add was not attempted
    }
}
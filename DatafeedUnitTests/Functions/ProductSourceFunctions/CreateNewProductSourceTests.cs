using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using Datafeed_v2.Models;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Functions.ProductSourceFunctions;

// Extension method to convert DbSet to Mock<DbSet>
public static partial class TestExtensions
{
    public static Mock<T> AsMock<T>(this T obj) where T : class
    {
        return Mock.Get(obj);
    }
}

[TestFixture]
public class CreateNewProductSourceTests
{
    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private Mock<DbSet<ProductSourceList>> _mockProductSourceListDbSet;
    private Mock<DbSet<ProductSource>> _mockProductSourceDbSet;
    private List<ProductSourceList> _sourceListData;
    private List<ProductSource> _productSourceData;

    [SetUp]
    public void Setup()
    {
        // Initialize data collections
        _sourceListData = [new ProductSourceList { ID = 1, Name = "Test Source Type" }];

        _productSourceData = [];

        // Setup mock DbSets
        _mockProductSourceListDbSet = new Mock<DbSet<ProductSourceList>>();
        _mockProductSourceDbSet = new Mock<DbSet<ProductSource>>();

        // Configure DbSets with test data using DbMockHelpers
        _mockProductSourceListDbSet = _sourceListData.GetAsyncQueryableMockDbSet().AsMock();
        _mockProductSourceDbSet = _productSourceData.GetAsyncQueryableMockDbSet().AsMock();

        // Setup Add method for ProductSource DbSet
        _mockProductSourceDbSet.Setup(m => m.Add(It.IsAny<ProductSource>()))
            .Callback<ProductSource>(item =>
            {
                item.ID = _productSourceData.Count + 1;
                _productSourceData.Add(item);
            })
            .Returns<ProductSource>(item => item);

        // Setup mock DbContext
        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockDbContext.Setup(c => c.ProductSourceList).Returns(_mockProductSourceListDbSet.Object);
        _mockDbContext.Setup(c => c.ProductSource).Returns(_mockProductSourceDbSet.Object);
        _mockDbContext.Setup(c => c.SaveChangesAsync()).ReturnsAsync(1);
    }

    [Test]
    public async Task Should_Create_New_ProductSource_When_All_Parameters_Valid()
    {
        // Arrange
        const uint sourceType = 1;
        const string brand = "Test Brand";
        const string sku = "TEST-SKU-123";
        const string title = "Test Product";
        const string longDesc = "This is a long description";
        const string shortDesc = "Short description";
        const decimal costPrice = 10.99m;
        const decimal sellPrice = 19.99m;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSource(
            sourceType, brand, sku, title, longDesc, shortDesc, costPrice, sellPrice, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Is.Not.Null);
        Assert.That(result.Value.ProductSku, Is.EqualTo(sku));
        Assert.That(result.Value.ProductTitle, Is.EqualTo(title));
        Assert.That(result.Value.ProductLongDescription, Is.EqualTo(longDesc));
        Assert.That(result.Value.ProductShortDescription, Is.EqualTo(shortDesc));
        Assert.That(result.Value.CostPrice, Is.EqualTo(costPrice));
        Assert.That(result.Value.SellPrice, Is.EqualTo(sellPrice));
        Assert.That(result.Value.SourceTypeIds, Is.EqualTo(sourceType.ToString()));
        Assert.That(result.Value.Status, Is.EqualTo((int)ProductSourceStatuses.New));

        _mockProductSourceDbSet.Verify(m => m.Add(It.IsAny<ProductSource>()), Times.Once);
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Once);
    }

    [Test]
    public async Task Should_Fail_When_Sku_Is_Null()
    {
        // Arrange
        const uint sourceType = 1;
        const string brand = "Test Brand";
        const string sku = null;
        const string title = "Test Product";
        const decimal costPrice = 10.99m;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSource(
            sourceType, brand, sku, title, null, null, costPrice, null, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.False);
        Assert.That(result.Error, Does.Contain("SKU cannot be null"));
        _mockProductSourceDbSet.Verify(m => m.Add(It.IsAny<ProductSource>()), Times.Never);
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Fail_When_Sku_Is_Empty()
    {
        // Arrange
        const uint sourceType = 1;
        const string brand = "Test Brand";
        const string sku = "";
        const string title = "Test Product";
        const decimal costPrice = 10.99m;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSource(
            sourceType, brand, sku, title, null, null, costPrice, null, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.False);
        Assert.That(result.Error, Does.Contain("SKU cannot be null"));
        _mockProductSourceDbSet.Verify(m => m.Add(It.IsAny<ProductSource>()), Times.Never);
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Fail_When_CostPrice_Is_Negative()
    {
        // Arrange
        const uint sourceType = 1;
        const string brand = "Test Brand";
        const string sku = "TEST-SKU-123";
        const string title = "Test Product";
        const decimal costPrice = -10.99m;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSource(
            sourceType, brand, sku, title, null, null, costPrice, null, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.False);
        Assert.That(result.Error, Does.Contain("Cost price cannot be negative"));
        _mockProductSourceDbSet.Verify(m => m.Add(It.IsAny<ProductSource>()), Times.Never);
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Fail_When_SourceType_Does_Not_Exist()
    {
        // Arrange
        const uint sourceType = 999; // Non-existent source type
        const string brand = "Test Brand";
        const string sku = "TEST-SKU-123";
        const string title = "Test Product";
        const decimal costPrice = 10.99m;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSource(
            sourceType, brand, sku, title, null, null, costPrice, null, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.False);
        Assert.That(result.Error, Does.Contain("Invalid ProductSourceList ID"));
        _mockProductSourceDbSet.Verify(m => m.Add(It.IsAny<ProductSource>()), Times.Never);
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Fail_When_Exception_Occurs()
    {
        // Arrange
        const uint sourceType = 1;
        const string brand = "Test Brand";
        const string sku = "TEST-SKU-123";
        const string title = "Test Product";
        const decimal costPrice = 10.99m;

        // Setup exception on SaveChangesAsync
        const string expectedErrorMessage = "Test exception";
        _mockDbContext.Setup(c => c.SaveChangesAsync()).ThrowsAsync(new Exception(expectedErrorMessage));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSource(
            sourceType, brand, sku, title, null, null, costPrice, null, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.False);
        Assert.That(result.Error, Does.Contain("Failed to create product source"));
        Assert.That(result.Error, Does.Contain(expectedErrorMessage));
    }

    [Test]
    public async Task Should_Trim_String_Values()
    {
        // Arrange
        const uint sourceType = 1;
        const string brand = "Test Brand";
        const string sku = "  TEST-SKU-123  ";
        const string title = "  Test Product  ";
        const string longDesc = "  This is a long description  ";
        const string shortDesc = "  Short description  ";
        const decimal costPrice = 10.99m;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSource(
            sourceType, brand, sku, title, longDesc, shortDesc, costPrice, null, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.ProductSku, Is.EqualTo("TEST-SKU-123"));
        Assert.That(result.Value.ProductTitle, Is.EqualTo("Test Product"));
        Assert.That(result.Value.ProductLongDescription, Is.EqualTo("This is a long description"));
        Assert.That(result.Value.ProductShortDescription, Is.EqualTo("Short description"));
    }

    [Test]
    public async Task
        Should_Return_Existing_Product_And_Add_New_SourceTypeId_When_Sku_Exists_With_Different_SourceType()
    {
        // Arrange
        const uint existingSourceType = 1;
        const uint newSourceType = 2;
        const string sku = "EXISTING-SKU-456";

        // Add another source type to the list
        _sourceListData.Add(new ProductSourceList { ID = (int)newSourceType, Name = "Another Test Source" });
        _mockProductSourceListDbSet = _sourceListData.GetAsyncQueryableMockDbSet().AsMock(); // Re-mock with new data
        _mockDbContext.Setup(c => c.ProductSourceList).Returns(_mockProductSourceListDbSet.Object);

        // Pre-populate with an existing product having only the first source type ID
        var existingProduct = new ProductSource
        {
            ID = 99,
            SourceTypeIds = existingSourceType.ToString(), // Initial source type
            Brand = "Existing Brand",
            ProductSku = sku,
            ProductTitle = "Existing Product",
            CostPrice = 50.00m,
            Status = (int)ProductSourceStatuses.New
        };
        _productSourceData.Add(existingProduct);

        // Re-setup the mock DbSet with the added data
        _mockProductSourceDbSet = _productSourceData.GetAsyncQueryableMockDbSet().AsMock();
        _mockDbContext.Setup(c => c.ProductSource).Returns(_mockProductSourceDbSet.Object);

        // Act: Attempt to create a product with the same SKU but a new source type
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSource(
            newSourceType, "New Brand", sku, "New Title", null, null, 100.00m, null, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True, "Result should be success.");
        Assert.That(result.Value, Is.Not.Null, "Returned value should not be null.");
        Assert.That(result.Value.ID, Is.EqualTo(existingProduct.ID), "Should return the existing product's ID.");
        Assert.That(result.Value.ProductSku, Is.EqualTo(sku), "Should return the existing product's SKU.");
        Assert.That(result.Value.SourceTypeIds, Does.Contain(existingSourceType.ToString()),
            "Should still contain the original SourceTypeId.");
        Assert.That(result.Value.SourceTypeIds, Does.Contain(newSourceType.ToString()),
            "Should now contain the new SourceTypeId.");
        Assert.That(result.Value.SourceTypeIds.Split(',').Length, Is.EqualTo(2),
            "Should have exactly two SourceTypeIds.");

        // Verify Add was NOT called, but SaveChanges WAS called
        _mockProductSourceDbSet.Verify(m => m.Add(It.IsAny<ProductSource>()), Times.Never, "Add should not be called.");
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Once,
            "SaveChangesAsync should be called once to update SourceTypeIds.");
    }

    [Test]
    public async Task Should_Return_Existing_Product_And_Not_Update_When_Sku_Exists_With_Same_SourceType()
    {
        // Arrange
        const uint sourceType = 1;
        const string sku = "EXISTING-SKU-789";
        const string existingSourceTypeIds = "1,2"; // Already contains the source type '1'

        // Add another source type to the list if needed for the existing product setup
        if (_sourceListData.All(s => s.ID != 2))
        {
            _sourceListData.Add(new ProductSourceList { ID = 2, Name = "Another Test Source" });
            _mockProductSourceListDbSet = _sourceListData.GetAsyncQueryableMockDbSet().AsMock(); // Re-mock
            _mockDbContext.Setup(c => c.ProductSourceList).Returns(_mockProductSourceListDbSet.Object);
        }


        // Pre-populate with an existing product having the source type ID already
        var existingProduct = new ProductSource
        {
            ID = 100,
            SourceTypeIds = existingSourceTypeIds, // Contains '1'
            Brand = "Existing Brand",
            ProductSku = sku,
            ProductTitle = "Existing Product",
            CostPrice = 60.00m,
            Status = (int)ProductSourceStatuses.New
        };
        _productSourceData.Add(existingProduct);

        // Re-setup the mock DbSet with the added data
        _mockProductSourceDbSet = _productSourceData.GetAsyncQueryableMockDbSet().AsMock();
        _mockDbContext.Setup(c => c.ProductSource).Returns(_mockProductSourceDbSet.Object);

        // Act: Attempt to create a product with the same SKU and an existing source type
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSource(
            sourceType, "New Brand", sku, "New Title", null, null, 110.00m, null, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True, "Result should be success.");
        Assert.That(result.Value, Is.Not.Null, "Returned value should not be null.");
        Assert.That(result.Value.ID, Is.EqualTo(existingProduct.ID), "Should return the existing product's ID.");
        Assert.That(result.Value.ProductSku, Is.EqualTo(sku), "Should return the existing product's SKU.");
        Assert.That(result.Value.SourceTypeIds, Is.EqualTo(existingSourceTypeIds),
            "SourceTypeIds should remain unchanged.");

        // Verify Add and SaveChanges were NOT called
        _mockProductSourceDbSet.Verify(m => m.Add(It.IsAny<ProductSource>()), Times.Never, "Add should not be called.");
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Never, "SaveChangesAsync should not be called.");
    }

    [Test]
    public async Task Should_Return_Existing_Product_And_Add_SourceTypeId_When_Sku_Exists_With_Null_SourceTypeIds()
    {
        // Arrange
        const uint newSourceType = 1;
        const string sku = "EXISTING-SKU-NULL-IDS";

        // Pre-populate with an existing product having null SourceTypeIds
        var existingProduct = new ProductSource
        {
            ID = 101,
            SourceTypeIds = null, // Null SourceTypeIds
            Brand = "Existing Brand Null",
            ProductSku = sku,
            ProductTitle = "Existing Product Null",
            CostPrice = 70.00m,
            Status = (int)ProductSourceStatuses.New
        };
        _productSourceData.Add(existingProduct);

        // Re-setup the mock DbSet with the added data
        _mockProductSourceDbSet = _productSourceData.GetAsyncQueryableMockDbSet().AsMock();
        _mockDbContext.Setup(c => c.ProductSource).Returns(_mockProductSourceDbSet.Object);

        // Act: Attempt to create a product with the same SKU and a new source type
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSource(
            newSourceType, "New Brand", sku, "New Title", null, null, 120.00m, null, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True, "Result should be success.");
        Assert.That(result.Value, Is.Not.Null, "Returned value should not be null.");
        Assert.That(result.Value.ID, Is.EqualTo(existingProduct.ID), "Should return the existing product's ID.");
        Assert.That(result.Value.ProductSku, Is.EqualTo(sku), "Should return the existing product's SKU.");
        Assert.That(result.Value.SourceTypeIds, Is.EqualTo(newSourceType.ToString()),
            "Should now contain only the new SourceTypeId.");

        // Verify Add was NOT called, but SaveChanges WAS called
        _mockProductSourceDbSet.Verify(m => m.Add(It.IsAny<ProductSource>()), Times.Never, "Add should not be called.");
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Once,
            "SaveChangesAsync should be called once to update SourceTypeIds.");
    }

    [Test]
    public async Task Should_Return_Existing_Product_And_Add_SourceTypeId_When_Sku_Matches_With_Different_Spacing()
    {
        // Arrange
        const uint existingSourceType = 1;
        const uint newSourceType = 2;
        const string existingSku = "SPACED-SKU-789"; // SKU stored in DB
        const string inputSku = "  SPACED-SKU-789  "; // SKU provided as input with spaces

        // Add another source type to the list
        _sourceListData.Add(new ProductSourceList { ID = (int)newSourceType, Name = "Another Test Source" });
        _mockProductSourceListDbSet = _sourceListData.GetAsyncQueryableMockDbSet().AsMock(); // Re-mock with new data
        _mockDbContext.Setup(c => c.ProductSourceList).Returns(_mockProductSourceListDbSet.Object);

        // Pre-populate with an existing product having only the first source type ID
        var existingProduct = new ProductSource
        {
            ID = 102,
            SourceTypeIds = existingSourceType.ToString(), // Initial source type
            Brand = "Spaced Brand",
            ProductSku = existingSku, // Stored without spaces
            ProductTitle = "Spaced Product",
            CostPrice = 75.50m,
            Status = (int)ProductSourceStatuses.New
        };
        _productSourceData.Add(existingProduct);

        // Re-setup the mock DbSet with the added data
        _mockProductSourceDbSet = _productSourceData.GetAsyncQueryableMockDbSet().AsMock();
        _mockDbContext.Setup(c => c.ProductSource).Returns(_mockProductSourceDbSet.Object);

        // Act: Attempt to create a product with the same SKU (different spacing) and a new source type
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSource(
            newSourceType, "New Spaced Brand", inputSku, "New Spaced Title", null, null, 120.00m, null,
            _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True, "Result should be success.");
        Assert.That(result.Value, Is.Not.Null, "Returned value should not be null.");
        Assert.That(result.Value.ID, Is.EqualTo(existingProduct.ID), "Should return the existing product's ID.");
        Assert.That(result.Value.ProductSku, Is.EqualTo(existingSku),
            "Should return the existing product's SKU (trimmed).");
        Assert.That(result.Value.SourceTypeIds, Does.Contain(existingSourceType.ToString()),
            "Should still contain the original SourceTypeId.");
        Assert.That(result.Value.SourceTypeIds, Does.Contain(newSourceType.ToString()),
            "Should now contain the new SourceTypeId.");
        Assert.That(result.Value.SourceTypeIds.Split(',').Length, Is.EqualTo(2),
            "Should have exactly two SourceTypeIds.");

        // Verify Add was NOT called, but SaveChanges WAS called
        _mockProductSourceDbSet.Verify(m => m.Add(It.IsAny<ProductSource>()), Times.Never, "Add should not be called.");
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Once,
            "SaveChangesAsync should be called once to update SourceTypeIds.");
    }
}
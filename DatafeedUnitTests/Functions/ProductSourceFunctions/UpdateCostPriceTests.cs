using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Threading.Tasks;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Functions.ProductSourceFunctions;

[TestFixture]
public class UpdateCostPriceTests
{
    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private Mock<DbSet<ProductSource>> _mockProductSourceDbSet;
    private List<ProductSource> _productSourceData;

    [SetUp]
    public void Setup()
    {
        // Initialize data collections
        _productSourceData =
        [
            new ProductSource
            {
                ID = 1,
                SourceTypeIds = 1.ToString(),
                ProductSku = "TEST-SKU-1",
                ProductTitle = "Test Product 1",
                CostPrice = 10.99m,
                SellPrice = 19.99m,
                Status = 1
            }
        ];

        // Setup mock DbSets
        _mockProductSourceDbSet = _productSourceData.GetAsyncQueryableMockDbSet().AsMock();

        // Setup mock DbContext
        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockDbContext.Setup(db => db.ProductSource).Returns(_mockProductSourceDbSet.Object);
        _mockDbContext.Setup(db => db.SaveChangesAsync()).ReturnsAsync(1);
    }

    [Test]
    public async Task Should_Return_Failure_When_CostPrice_Is_Negative()
    {
        // Arrange
        const uint sourceId = 1;
        const decimal costPrice = -10.99m;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.UpdateCostPrice(
            sourceId, costPrice, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("Cost price cannot be negative"));
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_ProductSource_Not_Found()
    {
        // Arrange
        const uint sourceId = 999; // Non-existent source ID
        const decimal costPrice = 15.99m;

        // Setup FindAsync to return null for non-existent ID
        _mockDbContext.Setup(db => db.ProductSource.FindAsync(999)).ReturnsAsync((ProductSource)null);

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.UpdateCostPrice(
            sourceId, costPrice, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("ProductSource with ID 999 not found."));
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_Exception_Is_Thrown()
    {
        // Arrange
        const uint sourceId = 1;
        const decimal costPrice = 15.99m;
        const string expectedErrorMessage = "Database error";

        // Setup SaveChangesAsync to throw an exception
        _mockDbContext.Setup(db => db.SaveChangesAsync()).ThrowsAsync(new Exception(expectedErrorMessage));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.UpdateCostPrice(
            sourceId, costPrice, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to update product source cost price"));
        Assert.That(result.Error, Does.Contain(expectedErrorMessage));
    }

    [Test]
    public async Task Should_Update_CostPrice_Successfully()
    {
        // Arrange
        const uint sourceId = 1;
        const decimal newCostPrice = 15.99m;

        // Setup FindAsync to return the product source
        _mockDbContext.Setup(db => db.ProductSource.FindAsync(1)).ReturnsAsync(_productSourceData[0]);

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.UpdateCostPrice(
            sourceId, newCostPrice, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value.CostPrice, Is.EqualTo(newCostPrice));
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Once);
    }
}
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Threading.Tasks;
using System.Web;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Properties;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Functions.ProductSourceFunctions;

[TestFixture]
public class CreateNewProductSourcesFromExcelSheetTests
{
    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private Mock<DbSet<ProductSourceList>> _mockProductSourceListDbSet;
    private Mock<DbSet<ProductSource>> _mockProductSourceDbSet;
    private List<ProductSourceList> _sourceListData;
    private List<ProductSource> _productSourceData;
    private Mock<HttpPostedFileBase> _mockCsvFile;

    [SetUp]
    public void Setup()
    {
        // Initialize data collections
        _sourceListData = [new ProductSourceList { ID = 1, Name = "GenericCsv" }];

        _productSourceData = [];

        // Setup mock DbSets
        _mockProductSourceListDbSet = _sourceListData.GetAsyncQueryableMockDbSet().AsMock();
        _mockProductSourceDbSet = _productSourceData.GetAsyncQueryableMockDbSet().AsMock();

        // Setup mock DbContext
        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockDbContext.Setup(db => db.ProductSourceList).Returns(_mockProductSourceListDbSet.Object);
        _mockDbContext.Setup(db => db.ProductSource).Returns(_mockProductSourceDbSet.Object);
        _mockDbContext.Setup(db => db.SaveChangesAsync()).ReturnsAsync(1);

        // Setup mock CSV file
        _mockCsvFile = new Mock<HttpPostedFileBase>();
        _mockCsvFile.Setup(f => f.ContentLength).Returns(1000);
        _mockCsvFile.Setup(f => f.ContentType).Returns(Resources.CsvMimeType);
    }

    [Test]
    public async Task Should_Return_Failure_When_ExcelFile_Is_Null()
    {
        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourcesFromExcelSheet(
            1, null, null, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("Excel file is required"));
    }

    [Test]
    public async Task Should_Return_Failure_When_ExcelFile_Is_Empty()
    {
        // Arrange
        _mockCsvFile.Setup(f => f.ContentLength).Returns(0);

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourcesFromExcelSheet(
            1, _mockCsvFile.Object, null, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("Excel file is required"));
    }

    [Test]
    public async Task Should_Return_Failure_When_ExcelFile_Has_Invalid_ContentType()
    {
        // Arrange
        _mockCsvFile.Setup(f => f.ContentType).Returns("application/pdf");

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourcesFromExcelSheet(
            1, _mockCsvFile.Object, null, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("Invalid file type. Please upload an Excel file"));
    }

    [Test]
    public async Task Should_Return_Failure_When_ExcelParser_Returns_Failure()
    {
        // Arrange
        var mockCsvParser = new Mock<Func<string, HttpPostedFileBase, Task<Result<List<GenericPriceBook>>>>>();
        mockCsvParser
            .Setup(p => p(It.IsAny<string>(), It.IsAny<HttpPostedFileBase>()))
            .ReturnsAsync(Result.Failure<List<GenericPriceBook>>("Failed to parse CSV"));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourcesFromExcelSheet(
            1, _mockCsvFile.Object, mockCsvParser.Object, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("Failed to parse CSV"));
    }

    [Test]
    public async Task Should_Return_Failure_When_SourceType_Not_Found()
    {
        // Arrange
        _sourceListData.Clear(); // Remove the GenericCsv source type

        var mockCsvParser = new Mock<Func<string, HttpPostedFileBase, Task<Result<List<GenericPriceBook>>>>>();
        mockCsvParser
            .Setup(p => p(It.IsAny<string>(), It.IsAny<HttpPostedFileBase>()))
            .ReturnsAsync(Result.Success(new List<GenericPriceBook>
            {
                new()
                {
                    ProductSku = "TEST-SKU-1",
                    ProductTitle = "Test Product 1",
                    BuyPrice = 10.99m
                }
            }));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourcesFromExcelSheet(
            1, _mockCsvFile.Object, mockCsvParser.Object, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Is.EqualTo("Product source type not found"));
    }

    [Test]
    public async Task Should_Return_Failure_When_CreateNewProductSource_Returns_Failure()
    {
        // Arrange
        var mockCsvParser = new Mock<Func<string, HttpPostedFileBase, Task<Result<List<GenericPriceBook>>>>>();
        mockCsvParser
            .Setup(p => p(It.IsAny<string>(), It.IsAny<HttpPostedFileBase>()))
            .ReturnsAsync(Result.Success(new List<GenericPriceBook>
            {
                new()
                {
                    ProductSku = "", // Empty SKU will cause CreateNewProductSource to fail
                    ProductTitle = "Test Product 1",
                    BuyPrice = 10.99m
                }
            }));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourcesFromExcelSheet(
            1, _mockCsvFile.Object, mockCsvParser.Object, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to create product"));
    }

    // [Test]
    // public async Task Should_Return_Failure_When_Product_Title_Is_Empty()
    // {
    //     // Arrange
    //     var mockCsvParser = new Mock<Func<string, HttpPostedFileBase, Task<Result<List<GenericPriceBook>>>>>();
    //     mockCsvParser
    //         .Setup(p => p(It.IsAny<string>(), It.IsAny<HttpPostedFileBase>()))
    //         .ReturnsAsync(Result.Success(new List<GenericPriceBook>
    //         {
    //             new()
    //             {
    //                 ProductSku = "TEST-SKU-1",
    //                 ProductTitle = "", // Empty title will cause CreateNewProductSource to fail
    //                 BuyPrice = 10.99m
    //             }
    //         }));
    //
    //     // Act
    //     var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourcesFromExcelSheet(
    //         1, _mockCsvFile.Object, mockCsvParser.Object, _mockDbContext.Object);
    //
    //     // Assert
    //     Assert.That(result.IsFailure, Is.True);
    //     Assert.That(result.Error, Does.Contain("Failed to create product"));
    //     Assert.That(result.Error, Does.Contain("Title cannot be null or empty"));
    // }

    // [Test]
    // public async Task Should_Return_Failure_When_Product_Title_Is_Whitespace()
    // {
    //     // Arrange
    //     var mockCsvParser = new Mock<Func<string, HttpPostedFileBase, Task<Result<List<GenericPriceBook>>>>>();
    //     mockCsvParser
    //         .Setup(p => p(It.IsAny<string>(), It.IsAny<HttpPostedFileBase>()))
    //         .ReturnsAsync(Result.Success(new List<GenericPriceBook>
    //         {
    //             new()
    //             {
    //                 ProductSku = "TEST-SKU-1",
    //                 ProductTitle = "   ", // Whitespace title will cause CreateNewProductSource to fail
    //                 BuyPrice = 10.99m
    //             }
    //         }));
    //
    //     // Act
    //     var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourcesFromExcelSheet(
    //         1, _mockCsvFile.Object, mockCsvParser.Object, _mockDbContext.Object);
    //
    //     // Assert
    //     Assert.That(result.IsFailure, Is.True);
    //     Assert.That(result.Error, Does.Contain("Failed to create product"));
    //     Assert.That(result.Error, Does.Contain("Title cannot be null or empty"));
    // }

    [Test]
    public async Task Should_Return_Failure_When_Cost_Price_Is_Negative()
    {
        // Arrange
        var mockCsvParser = new Mock<Func<string, HttpPostedFileBase, Task<Result<List<GenericPriceBook>>>>>();
        mockCsvParser
            .Setup(p => p(It.IsAny<string>(), It.IsAny<HttpPostedFileBase>()))
            .ReturnsAsync(Result.Success(new List<GenericPriceBook>
            {
                new()
                {
                    ProductSku = "TEST-SKU-1",
                    ProductTitle = "Test Product 1",
                    BuyPrice = -10.99m // Negative cost price will cause CreateNewProductSource to fail
                }
            }));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourcesFromExcelSheet(
            1, _mockCsvFile.Object, mockCsvParser.Object, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to create product"));
        Assert.That(result.Error, Does.Contain("Cost price cannot be negative"));
    }

    [Test]
    public async Task Should_Return_Failure_When_Exception_Is_Thrown()
    {
        // Arrange
        const string expectedErrorMessage = "Database connection error";
        _mockDbContext.Setup(db => db.ProductSourceList)
            .Throws(new Exception(expectedErrorMessage));

        var mockCsvParser = new Mock<Func<string, HttpPostedFileBase, Task<Result<List<GenericPriceBook>>>>>();
        mockCsvParser
            .Setup(p => p(It.IsAny<string>(), It.IsAny<HttpPostedFileBase>()))
            .ReturnsAsync(Result.Success(new List<GenericPriceBook>
            {
                new()
                {
                    ProductSku = "TEST-SKU-1",
                    ProductTitle = "Test Product 1",
                    BuyPrice = 10.99m
                }
            }));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourcesFromExcelSheet(
            1, _mockCsvFile.Object, mockCsvParser.Object, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to create products"));
        Assert.That(result.Error, Does.Contain(expectedErrorMessage));
    }

    [Test]
    public async Task Should_Return_Failure_When_Exception_Is_Thrown_During_Product_Creation()
    {
        // Arrange
        const string expectedErrorMessage = "Database constraint violation";

        // Setup normal ProductSourceList behaviour
        var mockCsvParser = new Mock<Func<string, HttpPostedFileBase, Task<Result<List<GenericPriceBook>>>>>();
        mockCsvParser
            .Setup(p => p(It.IsAny<string>(), It.IsAny<HttpPostedFileBase>()))
            .ReturnsAsync(Result.Success(new List<GenericPriceBook>
            {
                new()
                {
                    ProductSku = "TEST-SKU-1",
                    ProductTitle = "Test Product 1",
                    BuyPrice = 10.99m
                }
            }));

        // Make SaveChangesAsync throw an exception during product creation
        _mockDbContext.Setup(db => db.SaveChangesAsync())
            .ThrowsAsync(new Exception(expectedErrorMessage));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourcesFromExcelSheet(
            1, _mockCsvFile.Object, mockCsvParser.Object, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to create product"));
        Assert.That(result.Error, Does.Contain(expectedErrorMessage));
    }

    [Test]
    public async Task Should_Return_Success_With_Created_Products()
    {
        // Arrange
        var priceBooks = new List<GenericPriceBook>
        {
            new()
            {
                ProductSku = "TEST-SKU-1",
                ProductTitle = "Test Product 1",
                ProductDescription = "Short description 1",
                LongDescription = "Long description 1",
                BuyPrice = 10.99m
            },
            new()
            {
                ProductSku = "TEST-SKU-2",
                ProductTitle = "Test Product 2",
                ProductDescription = "Short description 2",
                LongDescription = "Long description 2",
                BuyPrice = 20.99m
            }
        };

        var mockCsvParser = new Mock<Func<string, HttpPostedFileBase, Task<Result<List<GenericPriceBook>>>>>();
        mockCsvParser
            .Setup(p => p(It.IsAny<string>(), It.IsAny<HttpPostedFileBase>()))
            .ReturnsAsync(Result.Success(priceBooks));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceFunctions.CreateNewProductSourcesFromExcelSheet(
            1, _mockCsvFile.Object, mockCsvParser.Object, _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Has.Count.EqualTo(2));
        Assert.That(result.Value[0].ProductSku, Is.EqualTo("TEST-SKU-1"));
        Assert.That(result.Value[1].ProductSku, Is.EqualTo("TEST-SKU-2"));

        // Verify that products were added to the database
        _mockProductSourceDbSet.Verify(m => m.Add(It.IsAny<ProductSource>()), Times.Exactly(2));
        // SaveChangesAsync is called once for each product creation and once at the end
        _mockDbContext.Verify(m => m.SaveChangesAsync(), Times.Exactly(3));
    }
}
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Threading.Tasks;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Functions.ProductSourceFunctions;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Functions.ProductSourceListFunctions;

[TestFixture]
public class MoveSourceTypeMappingTests
{
    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private List<NopCategories> _nopCategoriesData;
    private List<ProductSourceTypeToNopCategoryMapping> _mappingData;
    private Mock<DbSet<NopCategories>> _mockNopCategoriesDbSet;
    private Mock<DbSet<ProductSourceTypeToNopCategoryMapping>> _mockMappingDbSet;

    [SetUp]
    public void Setup()
    {
        // Initialise test data
        _nopCategoriesData =
        [
            new NopCategories { ID = 1, CategoryId = 10, Name = "Category 1" },
            new NopCategories { ID = 2, CategoryId = 20, Name = "Category 2" }
        ];

        _mappingData =
        [
            new ProductSourceTypeToNopCategoryMapping { ID = 1, SourceTypeId = 1, NopCategoryId = 10 }
        ];

        // Set up mock DbSets
        _mockNopCategoriesDbSet = _nopCategoriesData.GetAsyncQueryableMockDbSet().AsMock();
        _mockMappingDbSet = _mappingData.GetAsyncQueryableMockDbSet().AsMock();

        // Set up mock DbContext
        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockDbContext.Setup(c => c.NopCategories).Returns(_mockNopCategoriesDbSet.Object);
        _mockDbContext.Setup(c => c.ProductSourceTypeToNopCategoryMapping).Returns(_mockMappingDbSet.Object);
        _mockDbContext.Setup(c => c.SaveChangesAsync()).ReturnsAsync(1);
    }

    [Test]
    public async Task Should_Move_Mapping_Successfully()
    {
        // Arrange
        const uint sourceTypeId = 1;
        const uint currentNopCategoryId = 10;
        const uint targetNopCategoryId = 20;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.MoveSourceTypeMapping(
            sourceTypeId,
            currentNopCategoryId,
            targetNopCategoryId,
            _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Is.Not.Null);
        Assert.That(result.Value.SourceTypeId, Is.EqualTo(sourceTypeId));
        Assert.That(result.Value.NopCategoryId, Is.EqualTo(targetNopCategoryId));
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Once);
    }

    [Test]
    public async Task Should_Return_Failure_When_SourceTypeId_Is_Zero()
    {
        // Arrange
        const uint sourceTypeId = 0;
        const uint currentNopCategoryId = 10;
        const uint targetNopCategoryId = 20;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.MoveSourceTypeMapping(
            sourceTypeId,
            currentNopCategoryId,
            targetNopCategoryId,
            _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Source type ID must be greater than 0"));
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_CurrentNopCategoryId_Is_Zero()
    {
        // Arrange
        const uint sourceTypeId = 1;
        const uint currentNopCategoryId = 0;
        const uint targetNopCategoryId = 20;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.MoveSourceTypeMapping(
            sourceTypeId,
            currentNopCategoryId,
            targetNopCategoryId,
            _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Current category ID must be greater than 0"));
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_TargetNopCategoryId_Is_Zero()
    {
        // Arrange
        const uint sourceTypeId = 1;
        const uint currentNopCategoryId = 10;
        const uint targetNopCategoryId = 0;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.MoveSourceTypeMapping(
            sourceTypeId,
            currentNopCategoryId,
            targetNopCategoryId,
            _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Target category ID must be greater than 0"));
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_TargetCategory_Does_Not_Exist()
    {
        // Arrange
        const uint sourceTypeId = 1;
        const uint currentNopCategoryId = 10;
        const uint targetNopCategoryId = 99; // Non-existent category ID

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.MoveSourceTypeMapping(
            sourceTypeId,
            currentNopCategoryId,
            targetNopCategoryId,
            _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Invalid target NopCommerce category ID"));
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_Mapping_Does_Not_Exist()
    {
        // Arrange
        const uint sourceTypeId = 2; // Non-existent mapping source type ID
        const uint currentNopCategoryId = 10;
        const uint targetNopCategoryId = 20;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.MoveSourceTypeMapping(
            sourceTypeId,
            currentNopCategoryId,
            targetNopCategoryId,
            _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("No mapping found for source type ID"));
        Assert.That(result.Error, Does.Contain(sourceTypeId.ToString()));
        Assert.That(result.Error, Does.Contain(currentNopCategoryId.ToString()));
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_Exception_Occurs()
    {
        // Arrange
        const uint sourceTypeId = 1;
        const uint currentNopCategoryId = 10;
        const uint targetNopCategoryId = 20;
        const string expectedErrorMessage = "Database error";
        _mockDbContext.Setup(c => c.SaveChangesAsync()).ThrowsAsync(new Exception(expectedErrorMessage));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.MoveSourceTypeMapping(
            sourceTypeId,
            currentNopCategoryId,
            targetNopCategoryId,
            _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to move mapping"));
        Assert.That(result.Error, Does.Contain(expectedErrorMessage));
    }
}
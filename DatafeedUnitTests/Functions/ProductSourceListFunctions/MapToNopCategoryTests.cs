using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Threading.Tasks;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Functions.ProductSourceFunctions;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Functions.ProductSourceListFunctions;

[TestFixture]
public class MapToNopCategoryTests
{
    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private List<ProductSourceList> _sourceListData;
    private List<NopCategories> _nopCategoriesData;
    private List<ProductSourceTypeToNopCategoryMapping> _mappingData;
    private Mock<DbSet<ProductSourceList>> _mockProductSourceListDbSet;
    private Mock<DbSet<NopCategories>> _mockNopCategoriesDbSet;
    private Mock<DbSet<ProductSourceTypeToNopCategoryMapping>> _mockMappingDbSet;

    [SetUp]
    public void Setup()
    {
        // Initialize test data
        _sourceListData =
        [
            new ProductSourceList { ID = 1, Name = "Source Type 1", FileName = "file1.csv" },
            new ProductSourceList { ID = 2, Name = "Source Type 2", FileName = "file2.csv" }
        ];

        _nopCategoriesData =
        [
            new NopCategories { ID = 1, CategoryId = 10, Name = "Category 1" },
            new NopCategories { ID = 2, CategoryId = 20, Name = "Category 2" }
        ];

        _mappingData = [];

        // Set up mock DbSets
        _mockProductSourceListDbSet = _sourceListData.GetAsyncQueryableMockDbSet().AsMock();
        _mockNopCategoriesDbSet = _nopCategoriesData.GetAsyncQueryableMockDbSet().AsMock();
        _mockMappingDbSet = _mappingData.GetAsyncQueryableMockDbSet().AsMock();

        // Setup Add method for mapping DbSet
        _mockMappingDbSet.Setup(m => m.Add(It.IsAny<ProductSourceTypeToNopCategoryMapping>()))
            .Callback<ProductSourceTypeToNopCategoryMapping>(item =>
            {
                item.ID = _mappingData.Count + 1;
                _mappingData.Add(item);
            })
            .Returns<ProductSourceTypeToNopCategoryMapping>(item => item);

        // Set up mock DbContext
        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockDbContext.Setup(c => c.ProductSourceList).Returns(_mockProductSourceListDbSet.Object);
        _mockDbContext.Setup(c => c.NopCategories).Returns(_mockNopCategoriesDbSet.Object);
        _mockDbContext.Setup(c => c.ProductSourceTypeToNopCategoryMapping).Returns(_mockMappingDbSet.Object);
        _mockDbContext.Setup(c => c.SaveChangesAsync()).ReturnsAsync(1);
    }

    [Test]
    public async Task Should_Create_Mapping_Successfully()
    {
        // Arrange
        const uint sourceTypeId = 1;
        const uint nopCategoryId = 10;

        // Act
        var result =
            await Datafeed_v2.Functions.ProductSourceListFunctions.MapToNopCategory(sourceTypeId, nopCategoryId,
                _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Is.Not.Null);
        Assert.That(result.Value.SourceTypeId, Is.EqualTo(sourceTypeId));
        Assert.That(result.Value.NopCategoryId, Is.EqualTo(nopCategoryId));
        Assert.That(_mappingData.Count, Is.EqualTo(1));
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Once);
    }

    [Test]
    public async Task Should_Return_Failure_When_SourceTypeId_Is_Zero()
    {
        // Arrange
        const uint sourceTypeId = 0;
        const uint nopCategoryId = 10;

        // Act
        var result =
            await Datafeed_v2.Functions.ProductSourceListFunctions.MapToNopCategory(sourceTypeId, nopCategoryId,
                _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Source type ID must be greater than 0"));
        Assert.That(_mappingData.Count, Is.EqualTo(0));
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_NopCategoryId_Is_Zero()
    {
        // Arrange
        const uint sourceTypeId = 1;
        const uint nopCategoryId = 0;

        // Act
        var result =
            await Datafeed_v2.Functions.ProductSourceListFunctions.MapToNopCategory(sourceTypeId, nopCategoryId,
                _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Category ID must be greater than 0"));
        Assert.That(_mappingData.Count, Is.EqualTo(0));
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_SourceType_Does_Not_Exist()
    {
        // Arrange
        const uint sourceTypeId = 99; // Non-existent source type ID
        const uint nopCategoryId = 10;

        // Act
        var result =
            await Datafeed_v2.Functions.ProductSourceListFunctions.MapToNopCategory(sourceTypeId, nopCategoryId,
                _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Invalid product source type ID"));
        Assert.That(_mappingData.Count, Is.EqualTo(0));
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_NopCategory_Does_Not_Exist()
    {
        // Arrange
        const uint sourceTypeId = 1;
        const uint nopCategoryId = 99; // Non-existent category ID

        // Act
        var result =
            await Datafeed_v2.Functions.ProductSourceListFunctions.MapToNopCategory(sourceTypeId, nopCategoryId,
                _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Invalid NopCommerce category ID"));
        Assert.That(_mappingData.Count, Is.EqualTo(0));
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_Exception_Occurs()
    {
        // Arrange
        const uint sourceTypeId = 1;
        const uint nopCategoryId = 10;
        const string expectedErrorMessage = "Database error";
        _mockDbContext.Setup(c => c.SaveChangesAsync()).ThrowsAsync(new Exception(expectedErrorMessage));

        // Act
        var result =
            await Datafeed_v2.Functions.ProductSourceListFunctions.MapToNopCategory(sourceTypeId, nopCategoryId,
                _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to create mapping"));
        Assert.That(result.Error, Does.Contain(expectedErrorMessage));
    }
}
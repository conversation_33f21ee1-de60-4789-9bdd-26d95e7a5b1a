using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.Threading.Tasks;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Functions.ProductSourceListFunctions;

[TestFixture]
public class GetSourceTypesTests
{
    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private Mock<DbSet<ProductSourceList>> _mockProductSourceListDbSet;
    private List<ProductSourceList> _sourceListData;
    private const string SourceType1 = "Source Type 1";
    private const string SourceType2 = "Source Type 2";
    private const string SourceType3 = "Source Type 3";

    [SetUp]
    public void Setup()
    {
        // Initialize test data
        _sourceListData =
        [
            new ProductSourceList { ID = 1, Name = SourceType1, FileName = "file1.csv" },
            new ProductSourceList { ID = 2, Name = SourceType2, FileName = "file2.csv" },
            new ProductSourceList { ID = 3, Name = SourceType3, FileName = null }
        ];

        // Set up mock DbSet
        _mockProductSourceListDbSet = new Mock<DbSet<ProductSourceList>>();
        _mockProductSourceListDbSet.As<IDbAsyncEnumerable<ProductSourceList>>()
            .Setup(m => m.GetAsyncEnumerator())
            .Returns(new TestDbAsyncEnumerator<ProductSourceList>(_sourceListData.GetEnumerator()));

        _mockProductSourceListDbSet.As<IQueryable<ProductSourceList>>()
            .Setup(m => m.Provider)
            .Returns(new TestDbAsyncQueryProvider<ProductSourceList>(_sourceListData.AsQueryable().Provider));

        _mockProductSourceListDbSet.As<IQueryable<ProductSourceList>>().Setup(m => m.Expression)
            .Returns(_sourceListData.AsQueryable().Expression);
        _mockProductSourceListDbSet.As<IQueryable<ProductSourceList>>().Setup(m => m.ElementType)
            .Returns(_sourceListData.AsQueryable().ElementType);
        _mockProductSourceListDbSet.As<IQueryable<ProductSourceList>>().Setup(m => m.GetEnumerator())
            .Returns(_sourceListData.GetEnumerator());

        // Set up mock DbContext
        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockDbContext.Setup(c => c.ProductSourceList).Returns(_mockProductSourceListDbSet.Object);
    }

    [Test]
    public async Task Should_Return_All_Source_Types_With_External_Context()
    {
        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.GetSourceTypes(_mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Is.Not.Null);
        Assert.That(result.Value.Count, Is.EqualTo(3));
        Assert.That(result.Value[0].Name, Is.EqualTo(SourceType1));
        Assert.That(result.Value[1].Name, Is.EqualTo(SourceType2));
        Assert.That(result.Value[2].Name, Is.EqualTo(SourceType3));
    }

    [Test]
    public async Task Should_Return_Failure_When_Exception_Occurs()
    {
        // Arrange
        const string expectedErrorMessage = "Database connection error";
        _mockDbContext.Setup(c => c.ProductSourceList)
            .Throws(new Exception(expectedErrorMessage));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.GetSourceTypes(_mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Error retrieving source types"));
        Assert.That(result.Error, Does.Contain(expectedErrorMessage));
    }

    [Test]
    public async Task Should_Return_Empty_List_When_No_Source_Types_Exist()
    {
        // Arrange
        var emptySourceList = new List<ProductSourceList>();
        var emptyDbSet = emptySourceList.GetAsyncQueryableMockDbSet();
        _mockDbContext.Setup(c => c.ProductSourceList).Returns(emptyDbSet);

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.GetSourceTypes(_mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Is.Not.Null);
        Assert.That(result.Value, Is.Empty);
    }
}
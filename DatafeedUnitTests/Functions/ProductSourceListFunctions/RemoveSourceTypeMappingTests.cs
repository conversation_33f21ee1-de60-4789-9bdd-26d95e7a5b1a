using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Threading.Tasks;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Functions.ProductSourceFunctions;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;

namespace DatafeedUnitTests.Functions.ProductSourceListFunctions;

[TestFixture]
public class RemoveSourceTypeMappingTests
{
    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private List<ProductSourceTypeToNopCategoryMapping> _mappingData;
    private Mock<DbSet<ProductSourceTypeToNopCategoryMapping>> _mockMappingDbSet;

    [SetUp]
    public void Setup()
    {
        // Initialise test data
        _mappingData =
        [
            new ProductSourceTypeToNopCategoryMapping { ID = 1, SourceTypeId = 1, NopCategoryId = 10 }
        ];

        // Set up mock DbSets
        _mockMappingDbSet = _mappingData.GetAsyncQueryableMockDbSet().AsMock();

        // Set up mock DbContext
        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockDbContext.Setup(c => c.ProductSourceTypeToNopCategoryMapping).Returns(_mockMappingDbSet.Object);
        _mockDbContext.Setup(c => c.SaveChangesAsync()).ReturnsAsync(1);
    }

    [Test]
    public async Task Should_Remove_Mapping_Successfully()
    {
        // Arrange
        const uint sourceTypeId = 1;
        const uint nopCategoryId = 10;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.RemoveSourceTypeMapping(
            sourceTypeId,
            nopCategoryId,
            _mockDbContext.Object);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        _mockMappingDbSet.Verify(
            m => m.Remove(It.Is<ProductSourceTypeToNopCategoryMapping>(map =>
                map.SourceTypeId == sourceTypeId && map.NopCategoryId == nopCategoryId)), Times.Once);
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Once);
    }

    [Test]
    public async Task Should_Return_Failure_When_SourceTypeId_Is_Zero()
    {
        // Arrange
        const uint sourceTypeId = 0;
        const uint nopCategoryId = 10;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.RemoveSourceTypeMapping(
            sourceTypeId,
            nopCategoryId,
            _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Source type ID must be greater than 0"));
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Never);
        _mockMappingDbSet.Verify(m => m.Remove(It.IsAny<ProductSourceTypeToNopCategoryMapping>()), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_NopCategoryId_Is_Zero()
    {
        // Arrange
        const uint sourceTypeId = 1;
        const uint nopCategoryId = 0;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.RemoveSourceTypeMapping(
            sourceTypeId,
            nopCategoryId,
            _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Category ID must be greater than 0"));
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Never);
        _mockMappingDbSet.Verify(m => m.Remove(It.IsAny<ProductSourceTypeToNopCategoryMapping>()), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_Mapping_Does_Not_Exist()
    {
        // Arrange
        const uint sourceTypeId = 2; // Non-existent mapping source type ID
        const uint nopCategoryId = 10;

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.RemoveSourceTypeMapping(
            sourceTypeId,
            nopCategoryId,
            _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("No mapping found for source type ID"));
        Assert.That(result.Error, Does.Contain(sourceTypeId.ToString()));
        Assert.That(result.Error, Does.Contain(nopCategoryId.ToString()));
        _mockDbContext.Verify(c => c.SaveChangesAsync(), Times.Never);
        _mockMappingDbSet.Verify(m => m.Remove(It.IsAny<ProductSourceTypeToNopCategoryMapping>()), Times.Never);
    }

    [Test]
    public async Task Should_Return_Failure_When_Exception_Occurs()
    {
        // Arrange
        const uint sourceTypeId = 1;
        const uint nopCategoryId = 10;
        const string expectedErrorMessage = "Database error";
        _mockDbContext.Setup(c => c.SaveChangesAsync()).ThrowsAsync(new Exception(expectedErrorMessage));

        // Act
        var result = await Datafeed_v2.Functions.ProductSourceListFunctions.RemoveSourceTypeMapping(
            sourceTypeId,
            nopCategoryId,
            _mockDbContext.Object);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error, Does.Contain("Failed to remove mapping"));
        Assert.That(result.Error, Does.Contain(expectedErrorMessage));
        _mockMappingDbSet.Verify(m => m.Remove(It.IsAny<ProductSourceTypeToNopCategoryMapping>()),
            Times.Once); // Remove is called before SaveChanges
    }
}
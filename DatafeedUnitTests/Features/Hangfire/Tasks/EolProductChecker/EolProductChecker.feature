@EolProductChecker
Feature: End of Life Product Checker
    As a system administrator
    I want to automatically check if products are marked as End of Life in KQM
    So that I can identify products that are no longer active

    Background:
        Given the EOL Product Checker task is configured
        And the database contains product sources and price book items

    Scenario: Check products and identify EOL products
        Given there are ProductSource entries with SKUs "ACTIVE-SKU-001", "EOL-SKU-002"
        And there are ImportedItemsFromPriceBook entries with SKUs "ACTIVE-SKU-003", "EOL-SKU-004"
        And KQM returns isActive=true for SKU "ACTIVE-SKU-001"
        And KQM returns isActive=false for SKU "EOL-SKU-002"
        And KQM returns isActive=true for SKU "ACTIVE-SKU-003"
        And KQM returns isActive=false for SKU "EOL-SKU-004"
        When the EOL Product Checker task runs
        Then the task should complete successfully
        And EOL log should show "ACTIVE-SKU-001" as not EOL
        And EOL log should show "EOL-SKU-002" as EOL
        And EOL log should show "ACTIVE-SKU-003" as not EOL
        And EOL log should show "EOL-SKU-004" as EOL

    Scenario: Handle products not found in KQM
        Given there are ProductSource entries with SKUs "UNKNOWN-SKU-001"
        And KQM returns no results for SKU "UNKNOWN-SKU-001"
        When the EOL Product Checker task runs
        Then the task should complete successfully
        And EOL log should show "UNKNOWN-SKU-001" with error "Product not found in KQM"

    Scenario: Handle KQM API failures
        Given there are ProductSource entries with SKUs "API-FAIL-SKU"
        And KQM API fails for SKU "API-FAIL-SKU" with error "API connection failed"
        When the EOL Product Checker task runs
        Then the task should complete successfully
        And EOL log should show "API-FAIL-SKU" with error "API connection failed"

    Scenario: Handle duplicate SKUs across sources
        Given there are ProductSource entries with SKUs "DUPLICATE-SKU"
        And there are ImportedItemsFromPriceBook entries with SKUs "DUPLICATE-SKU"
        And KQM returns isActive=false for SKU "DUPLICATE-SKU"
        When the EOL Product Checker task runs
        Then the task should complete successfully
        And EOL log should show 2 entries for "DUPLICATE-SKU" both marked as EOL

    Scenario: Handle empty database
        Given there are no ProductSource entries
        And there are no ImportedItemsFromPriceBook entries
        When the EOL Product Checker task runs
        Then the task should complete successfully
        And no EOL log entries should be created

    Scenario: Handle archived ProductSource entries
        Given there are ProductSource entries with SKUs "ACTIVE-SKU" marked as not archived
        And there are ProductSource entries with SKUs "ARCHIVED-SKU" marked as archived
        And KQM returns isActive=true for SKU "ACTIVE-SKU"
        When the EOL Product Checker task runs
        Then the task should complete successfully
        And EOL log should show "ACTIVE-SKU" as not EOL
        And EOL log should not contain "ARCHIVED-SKU"

    Scenario: Log includes source information
        Given there are ProductSource entries with SKUs "PS-SKU" with ID 100
        And there are ImportedItemsFromPriceBook entries with SKUs "PB-SKU" with ID 200
        And KQM returns isActive=true for SKU "PS-SKU"
        And KQM returns isActive=false for SKU "PB-SKU"
        When the EOL Product Checker task runs
        Then the task should complete successfully
        And EOL log should show "PS-SKU" from ProductSource with ID 100
        And EOL log should show "PB-SKU" from ImportedItemsFromPriceBook with ID 200

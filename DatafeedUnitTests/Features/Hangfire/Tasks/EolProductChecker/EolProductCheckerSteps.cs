using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Functions.ProductSourceFunctions;
using DatafeedUnitTests.Helpers;
using Hangfire.Server;
using KaseyaAPI;
using KaseyaAPI.Functions;
using KaseyaAPI.Models.ApiModels;
using Moq;
using NUnit.Framework;
using Reqnroll;

namespace DatafeedUnitTests.Features.Hangfire.Tasks.EolProductChecker
{
    [Binding, Scope(Tag = "EolProductChecker")]
    public class EolProductCheckerSteps
    {
        private Mock<EdunetDatafeedsEntities> _mockDbContext;
        private Mock<KaseyaApi> _mockKaseyaApi;
        private Mock<PerformContext> _mockPerformContext;
        
        private List<ProductSource> _productSources;
        private List<ImportedItemsFromPriceBook> _importedItems;
        private List<EolProductLog> _eolLogs;
        
        private Mock<DbSet<ProductSource>> _mockProductSourceSet;
        private Mock<DbSet<ImportedItemsFromPriceBook>> _mockImportedItemsSet;
        private Mock<DbSet<EolProductLog>> _mockEolLogSet;
        
        private Exception _taskException;

        [BeforeScenario]
        public void Setup()
        {
            _productSources = new List<ProductSource>();
            _importedItems = new List<ImportedItemsFromPriceBook>();
            _eolLogs = new List<EolProductLog>();
            
            _mockDbContext = new Mock<EdunetDatafeedsEntities>();
            _mockKaseyaApi = new Mock<KaseyaApi>("https://example.com", "apikey", 25u, Maybe<HttpMethods>.None);
            _mockPerformContext = new Mock<PerformContext>();
            
            _mockProductSourceSet = _productSources.GetAsyncQueryableMockDbSet().AsMock();
            _mockImportedItemsSet = _importedItems.GetAsyncQueryableMockDbSet().AsMock();
            _mockEolLogSet = _eolLogs.GetAsyncQueryableMockDbSet().AsMock();
            
            _mockDbContext.Setup(m => m.ProductSource).Returns(_mockProductSourceSet.Object);
            _mockDbContext.Setup(m => m.ImportedItemsFromPriceBook).Returns(_mockImportedItemsSet.Object);
            _mockDbContext.Setup(m => m.EolProductLog).Returns(_mockEolLogSet.Object);
            _mockDbContext.Setup(m => m.SaveChangesAsync()).ReturnsAsync(1);
            
            _taskException = null;
        }

        [Given("the EOL Product Checker task is configured")]
        public void GivenTheEolProductCheckerTaskIsConfigured()
        {
            // Setup is already done in BeforeScenario
        }

        [Given("the database contains product sources and price book items")]
        public void GivenTheDatabaseContainsProductSourcesAndPriceBookItems()
        {
            // This is a general setup step - specific data will be added in other steps
        }

        [Given("there are ProductSource entries with SKUs {string}")]
        public void GivenThereAreProductSourceEntriesWithSkus(string skus)
        {
            var skuList = skus.Split(',').Select(s => s.Trim().Trim('"')).ToArray();
            foreach (var sku in skuList)
            {
                _productSources.Add(new ProductSource
                {
                    ID = _productSources.Count + 1,
                    ProductSku = sku,
                    ProductTitle = $"Product {sku}",
                    Archived = false,
                    Status = 1
                });
            }
            _mockProductSourceSet = _productSources.GetAsyncQueryableMockDbSet().AsMock();
            _mockDbContext.Setup(m => m.ProductSource).Returns(_mockProductSourceSet.Object);
        }

        [Given("there are ImportedItemsFromPriceBook entries with SKUs {string}")]
        public void GivenThereAreImportedItemsFromPriceBookEntriesWithSkus(string skus)
        {
            var skuList = skus.Split(',').Select(s => s.Trim().Trim('"')).ToArray();
            foreach (var sku in skuList)
            {
                _importedItems.Add(new ImportedItemsFromPriceBook
                {
                    ID = _importedItems.Count + 1,
                    ProductSKU = sku,
                    ProductName = $"Product {sku}",
                    PriceBookID = 1
                });
            }
            _mockImportedItemsSet = _importedItems.GetAsyncQueryableMockDbSet().AsMock();
            _mockDbContext.Setup(m => m.ImportedItemsFromPriceBook).Returns(_mockImportedItemsSet.Object);
        }

        [Given("KQM returns isActive={word} for SKU {string}")]
        public void GivenKqmReturnsIsActiveForSku(string isActiveValue, string sku)
        {
            var isActive = bool.Parse(isActiveValue);
            var product = new Product
            {
                id = 1,
                productNumber = sku,
                isActive = isActive,
                title = $"KQM Product {sku}"
            };
            
            _mockKaseyaApi.Setup(k => k.GetProduct(sku, It.IsAny<Maybe<DateTime>>()))
                .ReturnsAsync(Result.Success<Product[], GetProductError>(new[] { product }));
        }

        [Given("KQM returns no results for SKU {string}")]
        public void GivenKqmReturnsNoResultsForSku(string sku)
        {
            _mockKaseyaApi.Setup(k => k.GetProduct(sku, It.IsAny<Maybe<DateTime>>()))
                .ReturnsAsync(Result.Success<Product[], GetProductError>(new Product[0]));
        }

        [Given("KQM API fails for SKU {string} with error {string}")]
        public void GivenKqmApiFailsForSkuWithError(string sku, string errorMessage)
        {
            _mockKaseyaApi.Setup(k => k.GetProduct(sku, It.IsAny<Maybe<DateTime>>()))
                .ReturnsAsync(Result.Failure<Product[], GetProductError>(
                    new GetProductError(GetProductFailure.GetRequestFailed, errorMessage)));
        }

        [Given("there are ProductSource entries with SKUs {string} marked as not archived")]
        public void GivenThereAreProductSourceEntriesWithSkusMarkedAsNotArchived(string skus)
        {
            GivenThereAreProductSourceEntriesWithSkus(skus);
            // Already set Archived = false in the previous method
        }

        [Given("there are ProductSource entries with SKUs {string} marked as archived")]
        public void GivenThereAreProductSourceEntriesWithSkusMarkedAsArchived(string skus)
        {
            var skuList = skus.Split(',').Select(s => s.Trim().Trim('"')).ToArray();
            foreach (var sku in skuList)
            {
                _productSources.Add(new ProductSource
                {
                    ID = _productSources.Count + 1,
                    ProductSku = sku,
                    ProductTitle = $"Product {sku}",
                    Archived = true,
                    Status = 1
                });
            }
            _mockProductSourceSet = _productSources.GetAsyncQueryableMockDbSet().AsMock();
            _mockDbContext.Setup(m => m.ProductSource).Returns(_mockProductSourceSet.Object);
        }

        [Given("there are ProductSource entries with SKUs {string} with ID {int}")]
        public void GivenThereAreProductSourceEntriesWithSkusWithId(string skus, int id)
        {
            var skuList = skus.Split(',').Select(s => s.Trim().Trim('"')).ToArray();
            foreach (var sku in skuList)
            {
                _productSources.Add(new ProductSource
                {
                    ID = id,
                    ProductSku = sku,
                    ProductTitle = $"Product {sku}",
                    Archived = false,
                    Status = 1
                });
            }
            _mockProductSourceSet = _productSources.GetAsyncQueryableMockDbSet().AsMock();
            _mockDbContext.Setup(m => m.ProductSource).Returns(_mockProductSourceSet.Object);
        }

        [Given("there are ImportedItemsFromPriceBook entries with SKUs {string} with ID {int}")]
        public void GivenThereAreImportedItemsFromPriceBookEntriesWithSkusWithId(string skus, int id)
        {
            var skuList = skus.Split(',').Select(s => s.Trim().Trim('"')).ToArray();
            foreach (var sku in skuList)
            {
                _importedItems.Add(new ImportedItemsFromPriceBook
                {
                    ID = id,
                    ProductSKU = sku,
                    ProductName = $"Product {sku}",
                    PriceBookID = 1
                });
            }
            _mockImportedItemsSet = _importedItems.GetAsyncQueryableMockDbSet().AsMock();
            _mockDbContext.Setup(m => m.ImportedItemsFromPriceBook).Returns(_mockImportedItemsSet.Object);
        }

        [Given("there are no ProductSource entries")]
        public void GivenThereAreNoProductSourceEntries()
        {
            _productSources.Clear();
            _mockProductSourceSet = _productSources.GetAsyncQueryableMockDbSet().AsMock();
            _mockDbContext.Setup(m => m.ProductSource).Returns(_mockProductSourceSet.Object);
        }

        [Given("there are no ImportedItemsFromPriceBook entries")]
        public void GivenThereAreNoImportedItemsFromPriceBookEntries()
        {
            _importedItems.Clear();
            _mockImportedItemsSet = _importedItems.GetAsyncQueryableMockDbSet().AsMock();
            _mockDbContext.Setup(m => m.ImportedItemsFromPriceBook).Returns(_mockImportedItemsSet.Object);
        }

        [When("the EOL Product Checker task runs")]
        public async Task WhenTheEolProductCheckerTaskRuns()
        {
            try
            {
                await Datafeed_v2.Hangfire.Tasks.EolProductChecker.Run(
                    _mockPerformContext.Object,
                    _mockDbContext.Object,
                    _mockKaseyaApi.Object,
                    true);
            }
            catch (Exception ex)
            {
                _taskException = ex;
            }
        }

        [Then("the task should complete successfully")]
        public void ThenTheTaskShouldCompleteSuccessfully()
        {
            Assert.That(_taskException, Is.Null, $"Task should complete without exceptions. Exception: {_taskException?.Message}");
        }

        [Then("EOL log should show {string} as not EOL")]
        public void ThenEolLogShouldShowAsNotEol(string sku)
        {
            var logEntry = _eolLogs.FirstOrDefault(l => l.SKU == sku);
            Assert.That(logEntry, Is.Not.Null, $"EOL log should contain entry for SKU {sku}");
            Assert.That(logEntry.IsEol, Is.False, $"SKU {sku} should not be marked as EOL");
        }

        [Then("EOL log should show {string} as EOL")]
        public void ThenEolLogShouldShowAsEol(string sku)
        {
            var logEntry = _eolLogs.FirstOrDefault(l => l.SKU == sku);
            Assert.That(logEntry, Is.Not.Null, $"EOL log should contain entry for SKU {sku}");
            Assert.That(logEntry.IsEol, Is.True, $"SKU {sku} should be marked as EOL");
        }

        [Then("EOL log should show {string} with error {string}")]
        public void ThenEolLogShouldShowWithError(string sku, string expectedError)
        {
            var logEntry = _eolLogs.FirstOrDefault(l => l.SKU == sku);
            Assert.That(logEntry, Is.Not.Null, $"EOL log should contain entry for SKU {sku}");
            Assert.That(logEntry.ErrorMessage, Does.Contain(expectedError), 
                $"SKU {sku} should have error message containing '{expectedError}'");
        }

        [Then("EOL log should show {int} entries for {string} both marked as EOL")]
        public void ThenEolLogShouldShowEntriesForBothMarkedAsEol(int expectedCount, string sku)
        {
            var logEntries = _eolLogs.Where(l => l.SKU == sku).ToList();
            Assert.That(logEntries.Count, Is.EqualTo(expectedCount), 
                $"Should have {expectedCount} entries for SKU {sku}");
            Assert.That(logEntries.All(l => l.IsEol), Is.True, 
                $"All entries for SKU {sku} should be marked as EOL");
        }

        [Then("no EOL log entries should be created")]
        public void ThenNoEolLogEntriesShouldBeCreated()
        {
            Assert.That(_eolLogs.Count, Is.EqualTo(0), "No EOL log entries should be created");
        }

        [Then("EOL log should not contain {string}")]
        public void ThenEolLogShouldNotContain(string sku)
        {
            var logEntry = _eolLogs.FirstOrDefault(l => l.SKU == sku);
            Assert.That(logEntry, Is.Null, $"EOL log should not contain entry for SKU {sku}");
        }

        [Then("EOL log should show {string} from ProductSource with ID {int}")]
        public void ThenEolLogShouldShowFromProductSourceWithId(string sku, int expectedId)
        {
            var logEntry = _eolLogs.FirstOrDefault(l => l.SKU == sku && l.ProductSource == "ProductSource");
            Assert.That(logEntry, Is.Not.Null, $"EOL log should contain ProductSource entry for SKU {sku}");
            Assert.That(logEntry.ProductSourceId, Is.EqualTo(expectedId), 
                $"ProductSource entry for SKU {sku} should have ID {expectedId}");
        }

        [Then("EOL log should show {string} from ImportedItemsFromPriceBook with ID {int}")]
        public void ThenEolLogShouldShowFromImportedItemsFromPriceBookWithId(string sku, int expectedId)
        {
            var logEntry = _eolLogs.FirstOrDefault(l => l.SKU == sku && l.ProductSource == "ImportedItemsFromPriceBook");
            Assert.That(logEntry, Is.Not.Null, $"EOL log should contain ImportedItemsFromPriceBook entry for SKU {sku}");
            Assert.That(logEntry.ProductSourceId, Is.EqualTo(expectedId), 
                $"ImportedItemsFromPriceBook entry for SKU {sku} should have ID {expectedId}");
        }
    }
}

@GetProductInformationFromKqmService
Feature: New service to get product information from the KQM admin backend

    Scenario Outline: Specified product SKU returned no search results
        Given a product whose SKU is '<sku>'
        When the Kaseya admin backend is searched using PuppeteerSharp
        And no results are returned
        Then the product should be skipped

        Examples:
          | sku          |
          | NONEXISTENT1 |
          | INVALID-SKU  |
          | ""           |

    Scenario Outline: Specified product SKU returned multiple results
        Given a product whose SKU is '<sku>'
        When the Kaseya admin backend is searched using PuppeteerSharp
        And multiple results are returned
        Then the product information from the first result should be used
        And a new Dictionary object entry with a key set to the SKU and the value set to an object containing the product's ID, Title, Long Description should be returned

        Examples:
          | sku           |
          | MULTI-SKU-001 |
          | COMMON-PART   |

    Scenario Outline: Specified product SKU returned a single result
        Given a product whose SKU is '<sku>'
        When the Kaseya admin backend is searched using PuppeteerSharp
        And only one result is returned
        Then the product information from the first result should be used
        And a new Dictionary object entry with a key set to the SKU and the value set to an object containing the product's ID, Title, Long Description should be returned

        Examples:
          | sku           |
          | SINGLE-SKU-01 |
          | UNIQUE-PART   |
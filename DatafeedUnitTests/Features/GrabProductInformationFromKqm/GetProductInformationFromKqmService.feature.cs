// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:*******
//      Reqnroll Generator Version:*******
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace DatafeedUnitTests.Features.GrabProductInformationFromKqm
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [NUnit.Framework.TestFixtureAttribute()]
    [NUnit.Framework.DescriptionAttribute("New service to get product information from the KQM admin backend")]
    [NUnit.Framework.FixtureLifeCycleAttribute(NUnit.Framework.LifeCycle.InstancePerTestCase)]
    [NUnit.Framework.CategoryAttribute("GetProductInformationFromKqmService")]
    public partial class NewServiceToGetProductInformationFromTheKQMAdminBackendFeature
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "GetProductInformationFromKqmService"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features/GrabProductInformationFromKqm", "New service to get product information from the KQM admin backend", null, global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
#line 1 "GetProductInformationFromKqmService.feature"
#line hidden
        
        [NUnit.Framework.OneTimeSetUpAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        [NUnit.Framework.OneTimeTearDownAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        [NUnit.Framework.SetUpAttribute()]
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        [NUnit.Framework.TearDownAttribute()]
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<NUnit.Framework.TestContext>(NUnit.Framework.TestContext.CurrentContext);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Specified product SKU returned no search results")]
        [NUnit.Framework.TestCaseAttribute("NONEXISTENT1", null)]
        [NUnit.Framework.TestCaseAttribute("INVALID-SKU", null)]
        [NUnit.Framework.TestCaseAttribute("\"\"", null)]
        public async global::System.Threading.Tasks.Task SpecifiedProductSKUReturnedNoSearchResults(string sku, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("sku", sku);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Specified product SKU returned no search results", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 4
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 5
        await testRunner.GivenAsync(string.Format("a product whose SKU is \'{0}\'", sku), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 6
        await testRunner.WhenAsync("the Kaseya admin backend is searched using PuppeteerSharp", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 7
        await testRunner.AndAsync("no results are returned", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 8
        await testRunner.ThenAsync("the product should be skipped", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Specified product SKU returned multiple results")]
        [NUnit.Framework.TestCaseAttribute("MULTI-SKU-001", null)]
        [NUnit.Framework.TestCaseAttribute("COMMON-PART", null)]
        public async global::System.Threading.Tasks.Task SpecifiedProductSKUReturnedMultipleResults(string sku, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("sku", sku);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Specified product SKU returned multiple results", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 16
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 17
        await testRunner.GivenAsync(string.Format("a product whose SKU is \'{0}\'", sku), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 18
        await testRunner.WhenAsync("the Kaseya admin backend is searched using PuppeteerSharp", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 19
        await testRunner.AndAsync("multiple results are returned", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 20
        await testRunner.ThenAsync("the product information from the first result should be used", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 21
        await testRunner.AndAsync("a new Dictionary object entry with a key set to the SKU and the value set to an o" +
                        "bject containing the product\'s ID, Title, Long Description should be returned", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Specified product SKU returned a single result")]
        [NUnit.Framework.TestCaseAttribute("SINGLE-SKU-01", null)]
        [NUnit.Framework.TestCaseAttribute("UNIQUE-PART", null)]
        public async global::System.Threading.Tasks.Task SpecifiedProductSKUReturnedASingleResult(string sku, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("sku", sku);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Specified product SKU returned a single result", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 28
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 29
        await testRunner.GivenAsync(string.Format("a product whose SKU is \'{0}\'", sku), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 30
        await testRunner.WhenAsync("the Kaseya admin backend is searched using PuppeteerSharp", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 31
        await testRunner.AndAsync("only one result is returned", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 32
        await testRunner.ThenAsync("the product information from the first result should be used", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 33
        await testRunner.AndAsync("a new Dictionary object entry with a key set to the SKU and the value set to an o" +
                        "bject containing the product\'s ID, Title, Long Description should be returned", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
    }
}
#pragma warning restore
#endregion

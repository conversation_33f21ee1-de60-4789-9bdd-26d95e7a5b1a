using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using Datafeed_v2.Controllers;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Functions.ProductSourceFunctions;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;
using Reqnroll;

namespace DatafeedUnitTests.Features.PriceBook;

[Binding, Scope(Tag = "Archive")]
public class ArchiveFeature
{
    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private List<NopCategories> _nopCategories;
    private List<ImportedPriceBooks> _priceBooks;
    private List<ImportedItemsFromPriceBook> _importedItemsFromPriceBook;
    private List<PriceBookToNopCategoryMapping> _priceBookToNopCategoryMappings;
    private List<PriceBookImportedItemToNopCategoryMapping> _priceBookImportedItemToNopCategoryMappings;
    private Mock<DbSet<NopCategories>> _mockNopCategories;
    private Mock<DbSet<ImportedPriceBooks>> _mockPriceBooks;
    private Mock<DbSet<ImportedItemsFromPriceBook>> _mockImportedItemsFromPriceBook;
    private Mock<DbSet<PriceBookToNopCategoryMapping>> _mockPriceBookToNopCategoryMappings;
    private Mock<DbSet<PriceBookImportedItemToNopCategoryMapping>> _mockPriceBookImportedItemToNopCategoryMappings;
    private PriceBookController _priceBookController;

    [Given("a price book with products synced to Nop that don't exist in other sources")]
    public void GivenAPriceBookWithProductsSyncedToNopThatDontExistInOtherSources()
    {
        _nopCategories =
        [
            new NopCategories { ID = 1, Name = "Test Category" }
        ];
        _priceBooks =
        [
            new ImportedPriceBooks
            {
                ID = 1,
                Archived = false
            }
        ];
        _importedItemsFromPriceBook =
        [
            new ImportedItemsFromPriceBook
            {
                ID = 1,
                ProductSKU = "test-sku-001",
                PriceBookID = _priceBooks.First().ID
            }
        ];
        _priceBookToNopCategoryMappings =
        [
            new PriceBookToNopCategoryMapping
            {
                ID = 1,
                PriceBookId = _priceBooks.First().ID,
                NopCategoryId = _nopCategories.First().ID,
            }
        ];
        _priceBookImportedItemToNopCategoryMappings =
        [
            new PriceBookImportedItemToNopCategoryMapping
            {
                ID = 1,
                PriceBookItemId = _importedItemsFromPriceBook.First().ID,
                NopCategoryId = _nopCategories.First().ID,
                NopCategories = _nopCategories.First()
            }
        ];

        _mockNopCategories = _nopCategories.GetAsyncQueryableMockDbSet().AsMock();
        _mockPriceBooks = _priceBooks.GetAsyncQueryableMockDbSet().AsMock();
        _mockImportedItemsFromPriceBook = _importedItemsFromPriceBook.GetAsyncQueryableMockDbSet().AsMock();

        _mockPriceBookToNopCategoryMappings = _priceBookToNopCategoryMappings.GetAsyncQueryableMockDbSet().AsMock();
        _mockPriceBookToNopCategoryMappings.Setup(m => m.Remove(It.IsAny<PriceBookToNopCategoryMapping>()))
            .Callback<PriceBookToNopCategoryMapping>(mapping => _priceBookToNopCategoryMappings.Remove(mapping));

        _mockPriceBookImportedItemToNopCategoryMappings =
            _priceBookImportedItemToNopCategoryMappings.GetAsyncQueryableMockDbSet().AsMock();
        _mockPriceBookImportedItemToNopCategoryMappings.Setup(m =>
                m.RemoveRange(It.IsAny<IEnumerable<PriceBookImportedItemToNopCategoryMapping>>()))
            .Callback<IEnumerable<PriceBookImportedItemToNopCategoryMapping>>(mappings =>
            {
                foreach (var mapping in mappings.ToList())
                {
                    _priceBookImportedItemToNopCategoryMappings.Remove(mapping);
                }
            });

        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockDbContext.Setup(db => db.NopCategories).Returns(_mockNopCategories.Object);
        _mockDbContext.Setup(db => db.ImportedPriceBooks).Returns(_mockPriceBooks.Object);
        _mockDbContext.Setup(db => db.ImportedItemsFromPriceBook).Returns(_mockImportedItemsFromPriceBook.Object);
        _mockDbContext.Setup(db => db.PriceBookToNopCategoryMapping)
            .Returns(_mockPriceBookToNopCategoryMappings.Object);
        _mockDbContext.Setup(db => db.PriceBookImportedItemToNopCategoryMapping)
            .Returns(_mockPriceBookImportedItemToNopCategoryMappings.Object);
        _mockDbContext.Setup(db => db.SaveChangesAsync()).ReturnsAsync(1).Verifiable();

        _priceBookController = new PriceBookController();
    }

    [When("I manually archive the price book")]
    public async Task WhenIManuallyArchiveThePriceBook()
    {
        await _priceBookController.ArchivePriceBook((uint)_priceBooks.First().ID, _mockDbContext.Object);
    }

    [Then("the price book items should be archived")]
    public void ThenThePriceBookItemsShouldBeArchived()
    {
        Assert.That(_priceBooks.First().Archived, Is.True);
    }

    [Then("the products should be removed from Nop")]
    public void ThenTheProductsShouldBeRemovedFromNop()
    {
        // Verify PriceBook-level mappings were removed
        _mockPriceBookToNopCategoryMappings.Verify(
            m => m.Remove(It.Is<PriceBookToNopCategoryMapping>(map => map.ID == 1)), Times.Once);
        Assert.That(_priceBookToNopCategoryMappings, Is.Empty,
            "Price book to category mappings should have been removed.");

        // Verify Item-level mappings were removed
        Assert.That(_priceBookImportedItemToNopCategoryMappings, Is.Empty,
            "Item to category mappings should have been removed.");

        // Verify SaveChangesAsync was called (once in ArchiveExistingPriceBookAsync, once in ArchivePriceBook action)
        _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Exactly(2));
    }
}
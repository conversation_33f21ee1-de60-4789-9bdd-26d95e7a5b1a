Feature: Archiving price books

    Scenario: Archive a price book where products only exist in Nop
        Given a price book with products synced to Nop that don't exist in other sources
        When I manually archive the price book
        Then the price book items should be archived
        And the products should be removed from Nop

    Scenario: Replace price book where products only exist in Nop
        Given an existing price book with products synced to Nop that don't exist in other sources
        When I upload a new price book to replace the existing one
        Then the old price book should be archived
        And the products should be removed from Nop
        And all products from the updated price book should push through to Nop with special pricing

    Scenario: Archive a price book where products exist in multiple sources
        Given a price book with products synced to Nop that also exist in other sources
        When I manually archive the price book
        Then the price book should be archived
        And product pricing should default to KQM/fandoogle for other instances
        And the sku (existing elsewhere) should remain in Nop

    Scenario: Replace price book where products exist in multiple sources
        Given an existing price book with products synced to Nop that also exist in other sources
        When I upload a new price book to replace the existing one
        Then the old price book should be archived
        And the products should be removed from Nop
        And product pricing should default to KQM/fandoogle for other instances
        And all products from the updated price book should push through to Nop with the special pricing
@EditLongDescriptionForProductSource
Feature: Ability to edit a product sources' long description on the Manage Products page

    Scenario: A user has tried to access the Edit Long Description For Product Source endpoint directly for a product ID that doesn't exist
        Given a user is trying to access the Edit Long Description For Product Source endpoint directly for product 1
        When their request is received
        Then the error message 'Invalid product ID.' should be returned

    Scenario Outline: User has entered a blank long description and clicked the save button
        Given the user has edited the long description to '<newLongDescription>' for product <productId> whose original long description is '<originalLongDescription>'
        When they click the save button
        Then the products' long description should NOT be updated
        And the error message '<errorMessage>' should be returned

        Examples:
          | newLongDescription | productId | originalLongDescription   | errorMessage                             |
          |                    | 1         | original long description | New product long description is invalid. |

    Scenario Outline: User has entered a new long description and clicked the save button
        Given the user has edited the long description to '<newLongDescription>' for product <productId> whose original long description is '<originalLongDescription>'
        When they click the save button
        Then the products' long description should be updated

        Examples:
          | productId | newLongDescription   | originalLongDescription   |
          | 1         | new long description | original long description |
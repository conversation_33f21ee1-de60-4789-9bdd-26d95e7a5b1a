// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:*******
//      Reqnroll Generator Version:*******
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace DatafeedUnitTests.Features.ManageProducts
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [NUnit.Framework.TestFixtureAttribute()]
    [NUnit.Framework.DescriptionAttribute("Product Image Management in Comprehensive Edit Modal")]
    [NUnit.Framework.FixtureLifeCycleAttribute(NUnit.Framework.LifeCycle.InstancePerTestCase)]
    [NUnit.Framework.CategoryAttribute("ProductImageManagement")]
    public partial class ProductImageManagementInComprehensiveEditModalFeature
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "ProductImageManagement"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features/ManageProducts", "Product Image Management in Comprehensive Edit Modal", "As a user managing products\r\nI want to upload, view, and manage product images wi" +
                "thin the comprehensive edit modal\r\nSo that I can efficiently handle all product " +
                "visual content in one place", global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
#line 1 "ProductImageManagement.feature"
#line hidden
        
        [NUnit.Framework.OneTimeSetUpAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        [NUnit.Framework.OneTimeTearDownAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        [NUnit.Framework.SetUpAttribute()]
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        [NUnit.Framework.TearDownAttribute()]
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<NUnit.Framework.TestContext>(NUnit.Framework.TestContext.CurrentContext);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        public virtual async global::System.Threading.Tasks.Task FeatureBackgroundAsync()
        {
#line 7
    #line hidden
#line 8
        await testRunner.GivenAsync("the manage products page is loaded", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 9
        await testRunner.AndAsync("the products table contains test data", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User opens modal for ProductSource with existing images")]
        public async global::System.Threading.Tasks.Task UserOpensModalForProductSourceWithExistingImages()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User opens modal for ProductSource with existing images", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 13
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 14
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 15
        await testRunner.AndAsync("the ProductSource has 3 existing images", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 16
        await testRunner.WhenAsync("the user opens the comprehensive edit modal for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 17
        await testRunner.ThenAsync("the modal should display the image management section", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 18
        await testRunner.AndAsync("all 3 existing images should be displayed as thumbnails", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 19
        await testRunner.AndAsync("each image should have a \"Remove\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 20
        await testRunner.AndAsync("an \"Upload Images\" section should be visible", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User opens modal for PriceBookItem with existing images")]
        public async global::System.Threading.Tasks.Task UserOpensModalForPriceBookItemWithExistingImages()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User opens modal for PriceBookItem with existing images", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 22
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 23
        await testRunner.GivenAsync("a PriceBookItem with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 24
        await testRunner.AndAsync("the PriceBookItem has 2 existing images", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 25
        await testRunner.WhenAsync("the user opens the comprehensive edit modal for PriceBookItem 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 26
        await testRunner.ThenAsync("the modal should display the image management section", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 27
        await testRunner.AndAsync("all 2 existing images should be displayed as thumbnails", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 28
        await testRunner.AndAsync("each image should have a \"Remove\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 29
        await testRunner.AndAsync("an \"Upload Images\" section should be visible", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User opens modal for product with no images")]
        public async global::System.Threading.Tasks.Task UserOpensModalForProductWithNoImages()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User opens modal for product with no images", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 31
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 32
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 33
        await testRunner.AndAsync("the ProductSource has no existing images", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 34
        await testRunner.WhenAsync("the user opens the comprehensive edit modal for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 35
        await testRunner.ThenAsync("the modal should display the image management section", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 36
        await testRunner.AndAsync("a message \"No images uploaded\" should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 37
        await testRunner.AndAsync("an \"Upload Images\" section should be visible", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User uploads single image for ProductSource")]
        public async global::System.Threading.Tasks.Task UserUploadsSingleImageForProductSource()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User uploads single image for ProductSource", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 41
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 42
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 43
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 44
        await testRunner.WhenAsync("the user selects a valid image file \"test-image.jpg\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 45
        await testRunner.AndAsync("the user clicks the \"Upload\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 46
        await testRunner.ThenAsync("the image should be uploaded successfully", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 47
        await testRunner.AndAsync("the new image should appear in the thumbnails section", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 48
        await testRunner.AndAsync("a success message \"Image uploaded successfully\" should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User uploads multiple images for ProductSource")]
        public async global::System.Threading.Tasks.Task UserUploadsMultipleImagesForProductSource()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User uploads multiple images for ProductSource", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 50
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 51
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 52
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 53
        await testRunner.WhenAsync("the user selects multiple valid image files \"image1.jpg, image2.png, image3.gif\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 54
        await testRunner.AndAsync("the user clicks the \"Upload\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 55
        await testRunner.ThenAsync("all 3 images should be uploaded successfully", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 56
        await testRunner.AndAsync("all new images should appear in the thumbnails section", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 57
        await testRunner.AndAsync("a success message \"3 images uploaded successfully\" should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User uploads single image for PriceBookItem")]
        public async global::System.Threading.Tasks.Task UserUploadsSingleImageForPriceBookItem()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User uploads single image for PriceBookItem", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 59
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 60
        await testRunner.GivenAsync("a PriceBookItem with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 61
        await testRunner.AndAsync("the comprehensive edit modal is open for PriceBookItem 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 62
        await testRunner.WhenAsync("the user selects a valid image file \"product-image.png\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 63
        await testRunner.AndAsync("the user clicks the \"Upload\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 64
        await testRunner.ThenAsync("the image should be uploaded successfully", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 65
        await testRunner.AndAsync("the new image should appear in the thumbnails section", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 66
        await testRunner.AndAsync("a success message \"Image uploaded successfully\" should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User drags and drops images onto upload area")]
        public async global::System.Threading.Tasks.Task UserDragsAndDropsImagesOntoUploadArea()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User drags and drops images onto upload area", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 70
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 71
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 72
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 73
        await testRunner.WhenAsync("the user drags and drops 2 image files onto the upload area", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 74
        await testRunner.ThenAsync("both images should be uploaded automatically", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 75
        await testRunner.AndAsync("both new images should appear in the thumbnails section", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 76
        await testRunner.AndAsync("a success message \"2 images uploaded successfully\" should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User drags non-image files onto upload area")]
        public async global::System.Threading.Tasks.Task UserDragsNon_ImageFilesOntoUploadArea()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User drags non-image files onto upload area", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 78
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 79
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 80
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 81
        await testRunner.WhenAsync("the user drags and drops a non-image file \"document.pdf\" onto the upload area", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 82
        await testRunner.ThenAsync("an error message \"Only image files are allowed\" should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 83
        await testRunner.AndAsync("the file should not be uploaded", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User attempts to upload invalid image files")]
        [NUnit.Framework.TestCaseAttribute("document.pdf", "Only image files are allowed", null)]
        [NUnit.Framework.TestCaseAttribute("large-image.jpg", "Image file size must be less than 5MB", null)]
        [NUnit.Framework.TestCaseAttribute("invalid.txt", "Only image files are allowed", null)]
        public async global::System.Threading.Tasks.Task UserAttemptsToUploadInvalidImageFiles(string filename, string errorMessage, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("filename", filename);
            argumentsOfScenario.Add("errorMessage", errorMessage);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User attempts to upload invalid image files", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 87
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 88
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 89
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 90
        await testRunner.WhenAsync(string.Format("the user selects an invalid file \"{0}\"", filename), ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 91
        await testRunner.AndAsync("the user clicks the \"Upload\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 92
        await testRunner.ThenAsync(string.Format("an error message \"{0}\" should be displayed", errorMessage), ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 93
        await testRunner.AndAsync("the file should not be uploaded", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User attempts to upload too many images")]
        public async global::System.Threading.Tasks.Task UserAttemptsToUploadTooManyImages()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User attempts to upload too many images", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 101
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 102
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 103
        await testRunner.AndAsync("the ProductSource already has 8 existing images", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 104
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 105
        await testRunner.WhenAsync("the user selects 5 additional image files", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 106
        await testRunner.AndAsync("the user clicks the \"Upload\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 107
        await testRunner.ThenAsync("an error message \"Maximum 10 images allowed per product\" should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 108
        await testRunner.AndAsync("the files should not be uploaded", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User removes single existing image")]
        public async global::System.Threading.Tasks.Task UserRemovesSingleExistingImage()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User removes single existing image", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 112
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 113
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 114
        await testRunner.AndAsync("the ProductSource has 3 existing images", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 115
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 116
        await testRunner.WhenAsync("the user clicks the \"Remove\" button for the second image", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 117
        await testRunner.ThenAsync("a confirmation dialog should appear asking \"Are you sure you want to remove this " +
                        "image?\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 118
        await testRunner.WhenAsync("the user confirms the removal", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 119
        await testRunner.ThenAsync("the image should be removed from the thumbnails", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 120
        await testRunner.AndAsync("a success message \"Image removed successfully\" should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User cancels image removal")]
        public async global::System.Threading.Tasks.Task UserCancelsImageRemoval()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User cancels image removal", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 122
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 123
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 124
        await testRunner.AndAsync("the ProductSource has 3 existing images", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 125
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 126
        await testRunner.WhenAsync("the user clicks the \"Remove\" button for the first image", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 127
        await testRunner.ThenAsync("a confirmation dialog should appear", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 128
        await testRunner.WhenAsync("the user cancels the removal", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 129
        await testRunner.ThenAsync("the image should remain in the thumbnails", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 130
        await testRunner.AndAsync("no changes should be made", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User removes multiple images")]
        public async global::System.Threading.Tasks.Task UserRemovesMultipleImages()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User removes multiple images", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 132
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 133
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 134
        await testRunner.AndAsync("the ProductSource has 5 existing images", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 135
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 136
        await testRunner.WhenAsync("the user removes the first image", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 137
        await testRunner.AndAsync("the user removes the third image", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 138
        await testRunner.ThenAsync("both images should be removed from the thumbnails", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 139
        await testRunner.AndAsync("the remaining 3 images should still be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User clicks on image thumbnail to view full size")]
        public async global::System.Threading.Tasks.Task UserClicksOnImageThumbnailToViewFullSize()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User clicks on image thumbnail to view full size", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 143
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 144
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 145
        await testRunner.AndAsync("the ProductSource has existing images", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 146
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 147
        await testRunner.WhenAsync("the user clicks on the first image thumbnail", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 148
        await testRunner.ThenAsync("a full-size image preview should open", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 149
        await testRunner.AndAsync("navigation arrows should be available to view other images", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 150
        await testRunner.AndAsync("a close button should be available", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User navigates through image previews")]
        public async global::System.Threading.Tasks.Task UserNavigatesThroughImagePreviews()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User navigates through image previews", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 152
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 153
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 154
        await testRunner.AndAsync("the ProductSource has 4 existing images", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 155
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 156
        await testRunner.AndAsync("the full-size image preview is open showing the first image", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 157
        await testRunner.WhenAsync("the user clicks the \"Next\" arrow", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 158
        await testRunner.ThenAsync("the second image should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 159
        await testRunner.WhenAsync("the user clicks the \"Previous\" arrow", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 160
        await testRunner.ThenAsync("the first image should be displayed again", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User reorders images by drag and drop")]
        public async global::System.Threading.Tasks.Task UserReordersImagesByDragAndDrop()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User reorders images by drag and drop", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 164
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 165
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 166
        await testRunner.AndAsync("the ProductSource has 4 existing images in order \"A, B, C, D\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 167
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 168
        await testRunner.WhenAsync("the user drags image \"B\" to the position after image \"C\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 169
        await testRunner.ThenAsync("the images should be reordered to \"A, C, B, D\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 170
        await testRunner.AndAsync("a success message \"Images reordered successfully\" should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User saves product after uploading new images")]
        public async global::System.Threading.Tasks.Task UserSavesProductAfterUploadingNewImages()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User saves product after uploading new images", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 174
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 175
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 176
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 177
        await testRunner.WhenAsync("the user uploads 2 new images", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 178
        await testRunner.AndAsync("the user changes the product title to \"Updated Title\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 179
        await testRunner.AndAsync("the user clicks the \"Save\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 180
        await testRunner.ThenAsync("both the product changes and new images should be saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 181
        await testRunner.AndAsync("the modal should close", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 182
        await testRunner.AndAsync("a success message should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User cancels editing after uploading images")]
        public async global::System.Threading.Tasks.Task UserCancelsEditingAfterUploadingImages()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User cancels editing after uploading images", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 184
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 185
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 186
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 187
        await testRunner.WhenAsync("the user uploads 2 new images", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 188
        await testRunner.AndAsync("the user changes the product title to \"Updated Title\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 189
        await testRunner.AndAsync("the user clicks the \"Cancel\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 190
        await testRunner.ThenAsync("a confirmation dialog should appear asking \"You have unsaved changes including up" +
                        "loaded images. Are you sure you want to cancel?\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 191
        await testRunner.WhenAsync("the user confirms cancellation", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 192
        await testRunner.ThenAsync("the modal should close", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 193
        await testRunner.AndAsync("no changes should be saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 194
        await testRunner.AndAsync("the uploaded images should be discarded", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Image upload fails due to server error")]
        public async global::System.Threading.Tasks.Task ImageUploadFailsDueToServerError()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Image upload fails due to server error", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 198
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 199
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 200
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 201
        await testRunner.AndAsync("the server is experiencing issues", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 202
        await testRunner.WhenAsync("the user selects a valid image file", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 203
        await testRunner.AndAsync("the user clicks the \"Upload\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 204
        await testRunner.ThenAsync("an error message \"Failed to upload image. Please try again.\" should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 205
        await testRunner.AndAsync("the image should not appear in the thumbnails", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Image removal fails due to server error")]
        public async global::System.Threading.Tasks.Task ImageRemovalFailsDueToServerError()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Image removal fails due to server error", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 207
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 208
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 209
        await testRunner.AndAsync("the ProductSource has existing images", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 210
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 211
        await testRunner.AndAsync("the server is experiencing issues", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 212
        await testRunner.WhenAsync("the user attempts to remove an image", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 213
        await testRunner.ThenAsync("an error message \"Failed to remove image. Please try again.\" should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 214
        await testRunner.AndAsync("the image should remain in the thumbnails", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
    }
}
#pragma warning restore
#endregion

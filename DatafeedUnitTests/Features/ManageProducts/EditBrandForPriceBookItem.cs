using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;
using Datafeed_v2.Controllers;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;
using Reqnroll;

namespace DatafeedUnitTests.Features.ManageProducts;

[Binding, Scope(Tag = "EditBrandForPriceBookItem")]
public class EditBrandForPriceBookItem
{
    private int _priceBookItemId;
    private string _newBrand;
    private string _originalBrand;

    private List<ImportedItemsFromPriceBook> _priceBookItems;

    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private Mock<HttpResponseBase> _mockResponse;
    private Mock<HttpContextBase> _mockHttpContext;
    private ManageProductsController _controller;
    private ActionResult _result;

    [BeforeScenario]
    public void Setup()
    {
        _priceBookItems = []; // Changed from _productSources

        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockResponse = new Mock<HttpResponseBase>();
        _mockResponse.SetupProperty(res => res.StatusCode);
        _mockResponse.SetupProperty(res => res.TrySkipIisCustomErrors);

        _mockHttpContext = new Mock<HttpContextBase>();
        _mockHttpContext.Setup(ctx => ctx.Response).Returns(_mockResponse.Object);
    }

    private void SetupController()
    {
        // Setup mock for ImportedItemsFromPriceBook instead of ProductSource
        _mockDbContext.Setup(db => db.ImportedItemsFromPriceBook)
            .Returns(_priceBookItems.GetAsyncQueryableMockDbSet());

        _mockDbContext.Setup(db => db.SaveChangesAsync()).ReturnsAsync(1);

        _controller = new ManageProductsController(_mockDbContext.Object);
        _controller.ControllerContext = new ControllerContext(_mockHttpContext.Object, new RouteData(), _controller);
    }

    [Given("a user is trying to access the endpoint directly for price book item {int}")]
    public void GivenAUserIsTryingToAccessTheEndpointDirectlyForProduct(int priceBookItemId)
    {
        _priceBookItemId = priceBookItemId;
        // No item added to _priceBookItems for this scenario to simulate not found
        SetupController();
    }

    [When("their request is received")]
    public async Task WhenTheirRequestIsReceived()
    {
        _result = await _controller.EditBrandForPriceBookItem(_priceBookItemId, "some brand");
    }

    [Then("the error message {string} should be returned")]
    public void ThenTheErrorMessageShouldBeReturned(string errorMessage)
    {
        var contentResult = _result as ContentResult;
        Assert.That(contentResult, Is.Not.Null, "Result should be a ContentResult.");
        Assert.That(contentResult.Content,
            Is.EqualTo(errorMessage)); // This needs to match what the new controller method will return

        Assert.That(_mockResponse.Object.StatusCode, Is.EqualTo((int)HttpStatusCode.BadRequest));
        Assert.That(_mockResponse.Object.TrySkipIisCustomErrors, Is.True, "TrySkipIisCustomErrors should be true.");
    }

    [Given("the user has edited the brand to {string} for price book item {int} whose original brand is {string}")]
    public void GivenTheUserHasEditedTheBrandToForProductWhoseOriginalBrandIs(string brand, int priceBookItemId,
        string originalBrand)
    {
        _newBrand = brand;
        _priceBookItemId = priceBookItemId;
        _originalBrand = originalBrand;

        // Add an ImportedItemsFromPriceBook item
        _priceBookItems.Add(new ImportedItemsFromPriceBook { ID = _priceBookItemId, Brand = _originalBrand });
        SetupController();
    }

    [Given("the brand is invalid")]
    public void GivenTheBrandIsInvalid()
    {
        Assert.That(string.IsNullOrWhiteSpace(_newBrand), Is.True,
            "The brand should be null or whitespace for this step.");
    }

    [Given("the brand is valid")]
    public void GivenTheBrandIsValid()
    {
        Assert.That(string.IsNullOrWhiteSpace(_newBrand), Is.False,
            "The brand should not be null or whitespace for this step.");
    }

    [When("they click the save button")]
    public async Task WhenTheyClickTheSaveButton()
    {
        // Call the EditBrandForPriceBookItem method
        _result = await _controller.EditBrandForPriceBookItem(_priceBookItemId, _newBrand);
    }

    [Then("the price book item's brand should not be updated")]
    public void ThenThePriceBookItemsBrandShouldNotBeUpdated()
    {
        var item = _priceBookItems.FirstOrDefault(p => p.ID == _priceBookItemId); // Check _priceBookItems
        Assert.That(item, Is.Not.Null, "PriceBookItem should exist in the mock list.");
        Assert.That(item.Brand, Is.EqualTo(_originalBrand), "Brand should not have changed.");
        _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Never,
            "SaveChangesAsync should not have been called.");
    }

    [Then("the price book item's brand should be updated")]
    public void ThenThePriceBookItemsBrandShouldBeUpdated()
    {
        var contentResult = _result as ContentResult;
        Assert.That(contentResult, Is.Not.Null, "Result should be a ContentResult for success.");
        Assert.That(contentResult.Content, Is.EqualTo(_newBrand.Trim())); // Controller should trim the brand

        var item = _priceBookItems.FirstOrDefault(p => p.ID == _priceBookItemId); // Check _priceBookItems
        Assert.That(item, Is.Not.Null, "PriceBookItem should exist for update verification.");
        Assert.That(item.Brand, Is.EqualTo(_newBrand.Trim()));

        _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Once,
            "SaveChangesAsync should have been called once.");
    }
}
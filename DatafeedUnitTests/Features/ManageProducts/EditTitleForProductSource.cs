using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;
using Datafeed_v2.Controllers;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;
using Reqnroll;

namespace DatafeedUnitTests.Features.ManageProducts;

[Binding, Scope(Tag = "EditTitleForProductSource")]
public class EditTitle
{
    private string _newTitle;
    private string _originalTitle;
    private int _productId;
    private List<ProductSource> _productSources;
    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private Mock<HttpResponseBase> _mockResponse;
    private Mock<HttpContextBase> _mockHttpContext;
    private ManageProductsController _controller;
    private ActionResult _result;

    [BeforeScenario]
    public void Setup()
    {
        _productSources = [];
        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockResponse = new Mock<HttpResponseBase>();
        _mockResponse.SetupProperty(res => res.StatusCode);
        _mockResponse.SetupProperty(res => res.TrySkipIisCustomErrors);
        _mockHttpContext = new Mock<HttpContextBase>();
        _mockHttpContext.Setup(ctx => ctx.Response).Returns(_mockResponse.Object);
    }

    private void SetupController()
    {
        _mockDbContext.Setup(db => db.ProductSource)
            .Returns(_productSources.GetAsyncQueryableMockDbSet());
        _mockDbContext.Setup(db => db.SaveChangesAsync())
            .ReturnsAsync(1);

        _controller = new ManageProductsController(_mockDbContext.Object);
        _controller.ControllerContext = new ControllerContext(_mockHttpContext.Object, new RouteData(), _controller);
    }

    [Given("a user is trying to access the Edit Title For Product Source endpoint directly for product 1")]
    public void GivenAUserIsTryingToAccessTheEditTitleForProductSourceEndpointDirectlyForProduct()
    {
        _productId = 1;
        SetupController();
    }

    [When("their request is received")]
    public async Task WhenTheirRequestIsReceived()
    {
        _result = await _controller.EditTitleForProductSource(_productId, string.Empty);
    }

    [Then("the error message {string} should be returned")]
    public void ThenTheErrorMessageStringShouldBeReturned(string errorMessage)
    {
        var contentResult = _result as ContentResult;
        Assert.That(contentResult, Is.Not.Null, "The result should be a ContentResult.");
        Assert.That(contentResult.Content, Is.EqualTo(errorMessage),
            "The content of the result should be the error message.");
        Assert.That(_mockResponse.Object.TrySkipIisCustomErrors, Is.True, "TrySkipIisCustomErrors should be true.");
        Assert.That(_mockResponse.Object.StatusCode, Is.EqualTo((int)HttpStatusCode.BadRequest),
            "The response status code should be 400 (Bad Request).");
        ;
    }

    [Given("the user has edited the title to {string} for product {int} whose original title is {string}")]
    public void GivenTheUserHasEditedTheTitleToStringForProductIntWhoseOriginalTitleIsString(string newTitle,
        int productId, string originalTitle)
    {
        _newTitle = newTitle;
        _originalTitle = originalTitle;
        _productId = productId;
        _productSources.Add(new ProductSource { ID = productId, ProductTitle = originalTitle });
        SetupController();
    }

    [When("they click the save button")]
    public async Task WhenTheyClickTheSaveButton()
    {
        _result = await _controller.EditTitleForProductSource(_productId, _newTitle);
    }

    [Then("the products' title should be updated")]
    public void ThenTheProductsTitleShouldBeUpdated()
    {
        var contentResult = _result as ContentResult;
        Assert.That(contentResult, Is.Not.Null, "The result should be a ContentResult.");
        Assert.That(contentResult.Content, Is.EqualTo(_newTitle), "The content of the result should be the new title.");

        // Find the product source in our mock list
        var productSource = _productSources.SingleOrDefault(p => p.ID == _productId);

        // Assert that the product source was found and its title updated in the mock list
        Assert.That(productSource, Is.Not.Null, $"Product source with ID {_productId} should exist in the mock list.");
        Assert.That(productSource.ProductTitle, Is.EqualTo(_newTitle),
            "The ProductTitle property in the mock list should be updated to the new title.");

        // Verify that SaveChangesAsync was called exactly once
        _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Once,
            "SaveChangesAsync should have been called exactly once on the DbContext.");
    }

    [Then("the products' title should NOT be updated")]
    public void ThenTheProductsTitleShouldNotBeUpdated()
    {
        // Find the product source in our mock list
        var productSource = _productSources.SingleOrDefault(p => p.ID == _productId);

        // Assert that the product source was found and its title updated in the mock list
        Assert.That(productSource, Is.Not.Null, $"Product source with ID {_productId} should exist in the mock list.");
        Assert.That(productSource.ProductTitle, Is.EqualTo(_originalTitle),
            "The ProductTitle property in the mock list should NOT be updated to the new title.");

        // Verify that SaveChangesAsync was never called
        _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Never,
            "SaveChangesAsync should not have been called on the DbContext.");
    }
}
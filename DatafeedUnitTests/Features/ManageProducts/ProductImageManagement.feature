@ProductImageManagement
Feature: Product Image Management in Comprehensive Edit Modal
As a user managing products
I want to upload, view, and manage product images within the comprehensive edit modal
So that I can efficiently handle all product visual content in one place

    Background:
        Given the manage products page is loaded
        And the products table contains test data

# Image Display Scenarios

    Scenario: User opens modal for ProductSource with existing images
        Given a ProductSource with ID 1 exists
        And the ProductSource has 3 existing images
        When the user opens the comprehensive edit modal for ProductSource 1
        Then the modal should display the image management section
        And all 3 existing images should be displayed as thumbnails
        And each image should have a "Remove" button
        And an "Upload Images" section should be visible

    Scenario: User opens modal for PriceBookItem with existing images
        Given a PriceBookItem with ID 1 exists
        And the PriceBookItem has 2 existing images
        When the user opens the comprehensive edit modal for PriceBookItem 1
        Then the modal should display the image management section
        And all 2 existing images should be displayed as thumbnails
        And each image should have a "Remove" button
        And an "Upload Images" section should be visible

    Scenario: User opens modal for product with no images
        Given a ProductSource with ID 1 exists
        And the ProductSource has no existing images
        When the user opens the comprehensive edit modal for ProductSource 1
        Then the modal should display the image management section
        And a message "No images uploaded" should be displayed
        And an "Upload Images" section should be visible

# Image Upload Scenarios

    Scenario: User uploads single image for ProductSource
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user selects a valid image file "test-image.jpg"
        And the user clicks the "Upload" button
        Then the image should be uploaded successfully
        And the new image should appear in the thumbnails section
        And a success message "Image uploaded successfully" should be displayed

    Scenario: User uploads multiple images for ProductSource
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user selects multiple valid image files "image1.jpg, image2.png, image3.gif"
        And the user clicks the "Upload" button
        Then all 3 images should be uploaded successfully
        And all new images should appear in the thumbnails section
        And a success message "3 images uploaded successfully" should be displayed

    Scenario: User uploads single image for PriceBookItem
        Given a PriceBookItem with ID 1 exists
        And the comprehensive edit modal is open for PriceBookItem 1
        When the user selects a valid image file "product-image.png"
        And the user clicks the "Upload" button
        Then the image should be uploaded successfully
        And the new image should appear in the thumbnails section
        And a success message "Image uploaded successfully" should be displayed

# Drag and Drop Upload Scenarios

    Scenario: User drags and drops images onto upload area
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user drags and drops 2 image files onto the upload area
        Then both images should be uploaded automatically
        And both new images should appear in the thumbnails section
        And a success message "2 images uploaded successfully" should be displayed

    Scenario: User drags non-image files onto upload area
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user drags and drops a non-image file "document.pdf" onto the upload area
        Then an error message "Only image files are allowed" should be displayed
        And the file should not be uploaded

# Image Validation Scenarios

    Scenario Outline: User attempts to upload invalid image files
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user selects an invalid file "<filename>"
        And the user clicks the "Upload" button
        Then an error message "<errorMessage>" should be displayed
        And the file should not be uploaded

        Examples:
          | filename        | errorMessage                          |
          | document.pdf    | Only image files are allowed          |
          | large-image.jpg | Image file size must be less than 5MB |
          | invalid.txt     | Only image files are allowed          |

    Scenario: User attempts to upload too many images
        Given a ProductSource with ID 1 exists
        And the ProductSource already has 8 existing images
        And the comprehensive edit modal is open for ProductSource 1
        When the user selects 5 additional image files
        And the user clicks the "Upload" button
        Then an error message "Maximum 10 images allowed per product" should be displayed
        And the files should not be uploaded

# Image Removal Scenarios

    Scenario: User removes single existing image
        Given a ProductSource with ID 1 exists
        And the ProductSource has 3 existing images
        And the comprehensive edit modal is open for ProductSource 1
        When the user clicks the "Remove" button for the second image
        Then a confirmation dialog should appear asking "Are you sure you want to remove this image?"
        When the user confirms the removal
        Then the image should be removed from the thumbnails
        And a success message "Image removed successfully" should be displayed

    Scenario: User cancels image removal
        Given a ProductSource with ID 1 exists
        And the ProductSource has 3 existing images
        And the comprehensive edit modal is open for ProductSource 1
        When the user clicks the "Remove" button for the first image
        Then a confirmation dialog should appear
        When the user cancels the removal
        Then the image should remain in the thumbnails
        And no changes should be made

    Scenario: User removes multiple images
        Given a ProductSource with ID 1 exists
        And the ProductSource has 5 existing images
        And the comprehensive edit modal is open for ProductSource 1
        When the user removes the first image
        And the user removes the third image
        Then both images should be removed from the thumbnails
        And the remaining 3 images should still be displayed

# Image Preview Scenarios

    Scenario: User clicks on image thumbnail to view full size
        Given a ProductSource with ID 1 exists
        And the ProductSource has existing images
        And the comprehensive edit modal is open for ProductSource 1
        When the user clicks on the first image thumbnail
        Then a full-size image preview should open
        And navigation arrows should be available to view other images
        And a close button should be available

    Scenario: User navigates through image previews
        Given a ProductSource with ID 1 exists
        And the ProductSource has 4 existing images
        And the comprehensive edit modal is open for ProductSource 1
        And the full-size image preview is open showing the first image
        When the user clicks the "Next" arrow
        Then the second image should be displayed
        When the user clicks the "Previous" arrow
        Then the first image should be displayed again

# Image Reordering Scenarios

    Scenario: User reorders images by drag and drop
        Given a ProductSource with ID 1 exists
        And the ProductSource has 4 existing images in order "A, B, C, D"
        And the comprehensive edit modal is open for ProductSource 1
        When the user drags image "B" to the position after image "C"
        Then the images should be reordered to "A, C, B, D"
        And a success message "Images reordered successfully" should be displayed

# Save Behaviour with Images

    Scenario: User saves product after uploading new images
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user uploads 2 new images
        And the user changes the product title to "Updated Title"
        And the user clicks the "Save" button
        Then both the product changes and new images should be saved
        And the modal should close
        And a success message should be displayed

    Scenario: User cancels editing after uploading images
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user uploads 2 new images
        And the user changes the product title to "Updated Title"
        And the user clicks the "Cancel" button
        Then a confirmation dialog should appear asking "You have unsaved changes including uploaded images. Are you sure you want to cancel?"
        When the user confirms cancellation
        Then the modal should close
        And no changes should be saved
        And the uploaded images should be discarded

# Error Handling Scenarios

    Scenario: Image upload fails due to server error
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        And the server is experiencing issues
        When the user selects a valid image file
        And the user clicks the "Upload" button
        Then an error message "Failed to upload image. Please try again." should be displayed
        And the image should not appear in the thumbnails

    Scenario: Image removal fails due to server error
        Given a ProductSource with ID 1 exists
        And the ProductSource has existing images
        And the comprehensive edit modal is open for ProductSource 1
        And the server is experiencing issues
        When the user attempts to remove an image
        Then an error message "Failed to remove image. Please try again." should be displayed
        And the image should remain in the thumbnails
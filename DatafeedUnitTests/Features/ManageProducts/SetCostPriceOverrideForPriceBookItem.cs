using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;
using Datafeed_v2.Controllers;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;
using Reqnroll;
// For ImportedItemsFromPriceBook and PriceBookItemCostPriceOverride

namespace DatafeedUnitTests.Features.ManageProducts
{
    [Binding, Scope(Tag = "SetCostPriceOverrideForPriceBookItem")]
    public class SetCostPriceOverrideForPriceBookItem
    {
        private int _priceBookItemId;
        private decimal _costPrice;
        private DateTime? _expiryDate;
        private string _note;

        // For existing override tests
        private decimal _existingCostPrice;
        private DateTime _existingExpiryDate;
        private string _existingNote;
        private const int _existingOverrideId = 1; // Default ID for existing override

        private List<ImportedItemsFromPriceBook> _priceBookItems;
        private List<PriceBookItemCostPriceOverride> _priceBookItemCostPriceOverrides;

        private Mock<EdunetDatafeedsEntities> _mockDbContext;
        private Mock<HttpResponseBase> _mockResponse;
        private Mock<HttpContextBase> _mockHttpContext;
        private ManageProductsController _controller;
        private ActionResult _result;
        private ClaimsPrincipal _user;

        [BeforeScenario]
        public void Setup()
        {
            _priceBookItems = [];
            _priceBookItemCostPriceOverrides = [];

            _mockDbContext = new Mock<EdunetDatafeedsEntities>();
            _mockResponse = new Mock<HttpResponseBase>();
            _mockResponse.SetupProperty(res => res.StatusCode);
            _mockResponse.SetupProperty(res => res.TrySkipIisCustomErrors);

            _mockHttpContext = new Mock<HttpContextBase>();
            _mockHttpContext.Setup(ctx => ctx.Response).Returns(_mockResponse.Object);

            var claims = new List<Claim> { new(ClaimTypes.Name, "testuser") };
            var identity = new ClaimsIdentity(claims, "TestAuthType");
            _user = new ClaimsPrincipal(identity);
            _mockHttpContext.Setup(ctx => ctx.User).Returns(_user);
        }

        private void SetupController()
        {
            _mockDbContext.Setup(db => db.ImportedItemsFromPriceBook)
                .Returns(_priceBookItems.GetAsyncQueryableMockDbSet());

            var mockCostPriceOverrideDbSet = _priceBookItemCostPriceOverrides.GetAsyncQueryableMockDbSet();
            _mockDbContext.Setup(db => db.PriceBookItemCostPriceOverride)
                .Returns(mockCostPriceOverrideDbSet);

            _mockDbContext
                .Setup(db => db.PriceBookItemCostPriceOverride.Add(It.IsAny<PriceBookItemCostPriceOverride>()))
                .Callback<PriceBookItemCostPriceOverride>(cpo =>
                {
                    // Simulate DB assigning an ID
                    cpo.ID = _priceBookItemCostPriceOverrides.Count + 1;
                    _priceBookItemCostPriceOverrides.Add(cpo);
                });

            _mockDbContext.Setup(db => db.SaveChangesAsync()).ReturnsAsync(1);

            _controller = new ManageProductsController(_mockDbContext.Object);
            _controller.ControllerContext = new ControllerContext(_mockHttpContext.Object, new RouteData(), _controller)
            {
                HttpContext =
                {
                    User = _user
                }
            };
        }

        [Given("a user is trying to add a cost price override for price book item {int}")]
        public void GivenAUserIsTryingToAddACostPriceOverrideForPriceBookItem(int priceBookItemId)
        {
            _priceBookItemId = priceBookItemId;
            // No item added to _priceBookItems for this scenario to simulate not found
            SetupController();
        }

        [When("their request is received")]
        public async Task WhenTheirRequestIsReceived()
        {
            // Passing valid dummy values for other params as price book item ID check should be first
            _result = await _controller.SetCostPriceOverrideForPriceBookItem(_priceBookItemId, 10.0m,
                DateTime.Now.AddDays(30), "Test Note");
        }

        [Then("the error message {string} should be returned")]
        public void ThenTheErrorMessageShouldBeReturned(string errorMessage)
        {
            var contentResult = _result as ContentResult;
            Assert.That(contentResult, Is.Not.Null, "Result should be a ContentResult.");
            Assert.That(contentResult.Content, Is.EqualTo(errorMessage));

            Assert.That(_mockResponse.Object.StatusCode, Is.EqualTo((int)HttpStatusCode.BadRequest));
            Assert.That(_mockResponse.Object.TrySkipIisCustomErrors, Is.True, "TrySkipIisCustomErrors should be true.");
        }

        [Given("the user is trying to save a cost price override with the note {string}")]
        public void GivenUserIsTryingToSaveWithNote(string note)
        {
            _note = note;
            _priceBookItemId = 1; // Assume a valid item for this test
            _priceBookItems.Add(new ImportedItemsFromPriceBook
                { ID = _priceBookItemId, ProductName = "Test Price Book Item" });
            _costPrice = 100m; // Valid cost price
            _expiryDate = DateTime.Now.AddYears(1); // Valid date
            SetupController();
        }

        [When("they click the save button")]
        public async Task WhenTheyClickTheSaveButton()
        {
            _result = await _controller.SetCostPriceOverrideForPriceBookItem(_priceBookItemId, _costPrice, _expiryDate,
                _note);
        }

        [Then("a cost price override entry should NOT be added")]
        public void ThenACostPriceOverrideEntryShouldNotBeAdded()
        {
            Assert.That(_priceBookItemCostPriceOverrides, Is.Empty, "No cost price override should have been added.");
            _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Never,
                "SaveChangesAsync should not have been called.");
        }

        [Given("the user is trying to save a cost price override with the date {string}")]
        public void GivenUserIsTryingToSaveWithDate(string dateString)
        {
            _priceBookItemId = 1; // Assume a valid item
            _priceBookItems.Add(new ImportedItemsFromPriceBook
                { ID = _priceBookItemId, ProductName = "Test Price Book Item" });
            _costPrice = 100m; // Valid cost price
            _note = "Valid Note"; // Valid note

            if (string.IsNullOrWhiteSpace(dateString) ||
                !DateTime.TryParseExact(dateString, "dd-MM-yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None,
                    out var parsedDate))
            {
                _expiryDate = null;
            }
            else
            {
                _expiryDate = parsedDate;
            }

            SetupController();
        }

        [Given(
            "the user is trying to save a cost price override for price book item with ID {int}, cost price {decimal}, date {string} and note {string}")]
        public void GivenUserIsTryingToSaveACostPriceOverrideForPriceBookItemWithIDCostPriceDateAndNote(
            int priceBookItemId, decimal costPrice, string dateString, string note)
        {
            _priceBookItemId = priceBookItemId;
            _costPrice = costPrice;
            _note = note;

            if (!DateTime.TryParseExact(dateString, "dd-MM-yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None,
                    out var parsedNewDate))
            {
                _expiryDate = null;
            }
            else
            {
                _expiryDate = parsedNewDate;
            }

            _priceBookItems.Add(new ImportedItemsFromPriceBook
                { ID = _priceBookItemId, ProductName = "Test Price Book Item " + priceBookItemId });

            SetupController();
        }

        [Given("a valid cost price override already exists for price book item {int}")]
        public void GivenAValidCostPriceOverrideAlreadyExistsForPriceBookItem(int priceBookItemId)
        {
            _existingCostPrice = 50m;
            _existingExpiryDate = DateTime.Now.AddMonths(6);
            _existingNote = "Old test note";

            _priceBookItemCostPriceOverrides.Add(new PriceBookItemCostPriceOverride
            {
                ID = _existingOverrideId,
                PriceBookItemId = priceBookItemId,
                CostPrice = _existingCostPrice,
                ExpiryDate = _existingExpiryDate,
                Note = _existingNote,
                ActionedBy = "anotheruser"
            });

            SetupController();
        }

        [Then("a new cost price override entry should be added")]
        public void ThenANewCostPriceOverrideEntryShouldBeAdded()
        {
            Assert.That(_result, Is.InstanceOf<JsonResult>(), "Result should be a JsonResult for success.");
            var jsonResult = (JsonResult)_result;
            dynamic jsonData = jsonResult.Data;
            Assert.That(Convert.ToDecimal(jsonData.GetType().GetProperty("costPrice").GetValue(jsonData, null)),
                Is.EqualTo(_costPrice));

            Assert.That(_priceBookItemCostPriceOverrides, Has.Count.EqualTo(1),
                "One cost price override should have been added.");
            var newEntry = _priceBookItemCostPriceOverrides.First();
            Assert.That(newEntry.PriceBookItemId, Is.EqualTo(_priceBookItemId));
            Assert.That(newEntry.CostPrice, Is.EqualTo(_costPrice));
            Assert.That(newEntry.ExpiryDate,
                Is.EqualTo(_expiryDate.Value)); // .Value is safe here due to valid data scenario
            Assert.That(newEntry.Note, Is.EqualTo(_note));
            Assert.That(newEntry.ActionedBy, Is.EqualTo("testuser"));

            _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Once,
                "SaveChangesAsync should have been called once.");
        }

        [Then(
            "the valid existing cost price override should have its 'CostPrice' property updated to {decimal}, 'ExpiryDate' property updated to {string} and 'Note' property updated to {string}")]
        public void
            ThenTheValidExistingCostPriceOverrideShouldHaveItsCostPricePropertyUpdatedToExpiryDatePropertyUpdatedToAndNotePropertyUpdatedTo(
                decimal expectedCostPrice, string expectedDateString, string expectedNote)
        {
            Assert.That(_result, Is.InstanceOf<JsonResult>(), "Result should be a JsonResult for success.");
            var jsonResult = (JsonResult)_result;
            dynamic jsonData = jsonResult.Data;
            Assert.That(Convert.ToDecimal(jsonData.GetType().GetProperty("costPrice").GetValue(jsonData, null)),
                Is.EqualTo(expectedCostPrice));

            Assert.That(_priceBookItemCostPriceOverrides, Has.Count.EqualTo(1),
                "Exactly one cost price override record should exist.");
            var updatedEntry = _priceBookItemCostPriceOverrides.First(o => o.ID == _existingOverrideId);

            Assert.That(updatedEntry.PriceBookItemId, Is.EqualTo(_priceBookItemId));
            Assert.That(updatedEntry.CostPrice, Is.EqualTo(expectedCostPrice));

            if (!DateTime.TryParseExact(expectedDateString, "dd-MM-yyyy", CultureInfo.InvariantCulture,
                    DateTimeStyles.None, out var parsedExpectedDate))
            {
                throw new ArgumentException(
                    $"Invalid expected date format in Gherkin step: {expectedDateString}. Expected dd-MM-yyyy.");
            }

            Assert.That(updatedEntry.ExpiryDate, Is.EqualTo(parsedExpectedDate));
            Assert.That(updatedEntry.Note, Is.EqualTo(expectedNote));
            Assert.That(updatedEntry.ActionedBy, Is.EqualTo("testuser"));

            _mockDbContext.Verify(
                db => db.PriceBookItemCostPriceOverride.Add(It.IsAny<PriceBookItemCostPriceOverride>()), Times.Never);
            _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Once,
                "SaveChangesAsync should have been called once for the update.");
        }
    }
}
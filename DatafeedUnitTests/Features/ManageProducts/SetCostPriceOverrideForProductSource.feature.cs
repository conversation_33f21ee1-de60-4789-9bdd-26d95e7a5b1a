// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:*******
//      Reqnroll Generator Version:*******
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace DatafeedUnitTests.Features.ManageProducts
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [NUnit.Framework.TestFixtureAttribute()]
    [NUnit.Framework.DescriptionAttribute("Ability to add a cost price override for a product source with an expiry date")]
    [NUnit.Framework.FixtureLifeCycleAttribute(NUnit.Framework.LifeCycle.InstancePerTestCase)]
    [NUnit.Framework.CategoryAttribute("SetCostPriceOverrideForProductSource")]
    public partial class AbilityToAddACostPriceOverrideForAProductSourceWithAnExpiryDateFeature
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "SetCostPriceOverrideForProductSource"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features/ManageProducts", "Ability to add a cost price override for a product source with an expiry date", null, global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
#line 1 "SetCostPriceOverrideForProductSource.feature"
#line hidden
        
        [NUnit.Framework.OneTimeSetUpAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        [NUnit.Framework.OneTimeTearDownAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        [NUnit.Framework.SetUpAttribute()]
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        [NUnit.Framework.TearDownAttribute()]
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<NUnit.Framework.TestContext>(NUnit.Framework.TestContext.CurrentContext);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("A user has tried to access the endpoint directly for a product ID that doesn\'t ex" +
            "ist")]
        public async global::System.Threading.Tasks.Task AUserHasTriedToAccessTheEndpointDirectlyForAProductIDThatDoesntExist()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("A user has tried to access the endpoint directly for a product ID that doesn\'t ex" +
                    "ist", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 4
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 5
        await testRunner.GivenAsync("a user is trying to add a cost price override for product 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 6
        await testRunner.WhenAsync("their request is received", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 7
        await testRunner.ThenAsync("the error message \'Invalid product ID.\' should be returned", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User has entered an invalid note and clicked the save button")]
        [NUnit.Framework.TestCaseAttribute("", "Note is invalid.", null)]
        public async global::System.Threading.Tasks.Task UserHasEnteredAnInvalidNoteAndClickedTheSaveButton(string note, string errorMessage, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("note", note);
            argumentsOfScenario.Add("errorMessage", errorMessage);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User has entered an invalid note and clicked the save button", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 9
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 10
        await testRunner.GivenAsync(string.Format("the user is trying to save a cost price override with the note \'{0}\'", note), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 11
        await testRunner.WhenAsync("they click the save button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 12
        await testRunner.ThenAsync("a cost price override entry should NOT be added", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 13
        await testRunner.AndAsync(string.Format("the error message \'{0}\' should be returned", errorMessage), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User has entered an invalid date and clicked the save button")]
        [NUnit.Framework.TestCaseAttribute("", "Date is invalid.", null)]
        [NUnit.Framework.TestCaseAttribute("lksjdflkjdsf", "Date is invalid.", null)]
        [NUnit.Framework.TestCaseAttribute("ojsoiu093904590", "Date is invalid.", null)]
        public async global::System.Threading.Tasks.Task UserHasEnteredAnInvalidDateAndClickedTheSaveButton(string date, string errorMessage, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("date", date);
            argumentsOfScenario.Add("errorMessage", errorMessage);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User has entered an invalid date and clicked the save button", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 19
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 20
        await testRunner.GivenAsync(string.Format("the user is trying to save a cost price override with the date \'{0}\'", date), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 21
        await testRunner.WhenAsync("they click the save button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 22
        await testRunner.ThenAsync("a cost price override entry should NOT be added", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 23
        await testRunner.AndAsync(string.Format("the error message \'{0}\' should be returned", errorMessage), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User has entered valid data but a valid (\'ExpiryDate\' > Now) cost price override " +
            "already exists for the specified product source")]
        [NUnit.Framework.TestCaseAttribute("1", "100", "16-05-2025", "Test cost price override", null)]
        public async global::System.Threading.Tasks.Task UserHasEnteredValidDataButAValidExpiryDateNowCostPriceOverrideAlreadyExistsForTheSpecifiedProductSource(string productId, string costPrice, string expiryDate, string note, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("productId", productId);
            argumentsOfScenario.Add("costPrice", costPrice);
            argumentsOfScenario.Add("expiryDate", expiryDate);
            argumentsOfScenario.Add("note", note);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User has entered valid data but a valid (\'ExpiryDate\' > Now) cost price override " +
                    "already exists for the specified product source", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 31
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 32
        await testRunner.GivenAsync(string.Format("the user is trying to save a cost price override for product with ID {0}, cost pr" +
                            "ice {1}, date \'{2}\' and note \'{3}\'", productId, costPrice, expiryDate, note), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 33
        await testRunner.AndAsync(string.Format("a valid cost price override already exists for product {0}", productId), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 34
        await testRunner.WhenAsync("they click the save button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 35
        await testRunner.ThenAsync(string.Format("the valid existing cost price override should have its \'CostPrice\' property updat" +
                            "ed to {0}, \'ExpiryDate\' property updated to \'{1}\' and \'Note\' property updated to" +
                            " \'{2}\'", costPrice, expiryDate, note), ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User has entered valid data and clicked the save button")]
        [NUnit.Framework.TestCaseAttribute("1", "100", "15-05-2025", "Test cost price override", null)]
        public async global::System.Threading.Tasks.Task UserHasEnteredValidDataAndClickedTheSaveButton(string productId, string costPrice, string expiryDate, string note, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("productId", productId);
            argumentsOfScenario.Add("costPrice", costPrice);
            argumentsOfScenario.Add("expiryDate", expiryDate);
            argumentsOfScenario.Add("note", note);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User has entered valid data and clicked the save button", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 41
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 42
        await testRunner.GivenAsync(string.Format("the user is trying to save a cost price override for product with ID {0}, cost pr" +
                            "ice {1}, date \'{2}\' and note \'{3}\'", productId, costPrice, expiryDate, note), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 43
        await testRunner.WhenAsync("they click the save button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 44
        await testRunner.ThenAsync("a new cost price override entry should be added", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
    }
}
#pragma warning restore
#endregion

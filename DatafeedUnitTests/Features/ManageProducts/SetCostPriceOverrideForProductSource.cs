using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;
using Datafeed_v2.Controllers;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Helpers;
using Moq;
using NUnit.Framework;
using Reqnroll;

// For ProductSource

namespace DatafeedUnitTests.Features.ManageProducts
{
    [Binding, Scope(Tag = "SetCostPriceOverrideForProductSource")]
    public class SetCostPriceOverride
    {
        private int _productId;
        private decimal _costPrice;
        private DateTime? _expiryDate;
        private string _note;

        // For existing override tests
        private decimal _existingCostPrice;
        private DateTime _existingExpiryDate;
        private string _existingNote;
        private readonly int _existingOverrideId = 1; // Default ID for existing override

        private List<ProductSource> _productSources;
        private List<ProductSourceCostPriceOverride> _productSourceCostPriceOverrides;

        private Mock<EdunetDatafeedsEntities> _mockDbContext;
        private Mock<HttpResponseBase> _mockResponse;
        private Mock<HttpContextBase> _mockHttpContext;
        private ManageProductsController _controller;
        private ActionResult _result;
        private ClaimsPrincipal _user;

        [BeforeScenario]
        public void Setup()
        {
            _productSources = [];
            _productSourceCostPriceOverrides = [];

            _mockDbContext = new Mock<EdunetDatafeedsEntities>();
            _mockResponse = new Mock<HttpResponseBase>();
            _mockResponse.SetupProperty(res => res.StatusCode);
            _mockResponse.SetupProperty(res => res.TrySkipIisCustomErrors);

            _mockHttpContext = new Mock<HttpContextBase>();
            _mockHttpContext.Setup(ctx => ctx.Response).Returns(_mockResponse.Object);

            var claims = new List<Claim> { new(ClaimTypes.Name, "testuser") };
            var identity = new ClaimsIdentity(claims, "TestAuthType");
            _user = new ClaimsPrincipal(identity);
            _mockHttpContext.Setup(ctx => ctx.User).Returns(_user);
        }

        private void SetupController()
        {
            _mockDbContext.Setup(db => db.ProductSource)
                .Returns(_productSources.GetAsyncQueryableMockDbSet());

            var mockCostPriceOverrideDbSet = _productSourceCostPriceOverrides.GetAsyncQueryableMockDbSet();
            _mockDbContext.Setup(db => db.ProductSourceCostPriceOverride)
                .Returns(mockCostPriceOverrideDbSet);

            _mockDbContext
                .Setup(db => db.ProductSourceCostPriceOverride.Add(It.IsAny<ProductSourceCostPriceOverride>()))
                .Callback<ProductSourceCostPriceOverride>(cpo =>
                {
                    // Simulate DB assigning an ID
                    cpo.ID = _productSourceCostPriceOverrides.Count + 1;
                    _productSourceCostPriceOverrides.Add(cpo);
                });

            // Setup FirstOrDefault for finding existing overrides
            // _mockDbContext.Setup(db => db.ProductSourceCostPriceOverride.FirstOrDefault(
            //     It.IsAny<System.Linq.Expressions.Expression<Func<ProductSourceCostPriceOverride, bool>>>()))
            //     .Returns<System.Linq.Expressions.Expression<Func<ProductSourceCostPriceOverride, bool>>>(
            //         expr => _productSourceCostPriceOverrides.AsQueryable().FirstOrDefault(expr.Compile()));

            _mockDbContext.Setup(db => db.SaveChangesAsync()).ReturnsAsync(1);

            _controller = new ManageProductsController(_mockDbContext.Object);
            _controller.ControllerContext = new ControllerContext(_mockHttpContext.Object, new RouteData(), _controller)
            {
                HttpContext =
                {
                    User = _user
                }
            };
        }

        [Given("a user is trying to add a cost price override for product {int}")]
        public void GivenAUserIsTryingToAddACostPriceOverrideForProduct(int productId)
        {
            _productId = productId;
            // No product added to _productSources for this scenario to simulate not found
            SetupController();
        }

        [When("their request is received")]
        public async Task WhenTheirRequestIsReceived()
        {
            // Passing valid dummy values for other params as product ID check should be first
            _result = await _controller.SetCostPriceOverrideForProductSource(_productId, 10.0m,
                DateTime.Now.AddDays(30), "Test Note");
        }

        [Then("the error message {string} should be returned")]
        public void ThenTheErrorMessageShouldBeReturned(string errorMessage)
        {
            var contentResult = _result as ContentResult;
            Assert.That(contentResult, Is.Not.Null, "Result should be a ContentResult.");
            Assert.That(contentResult.Content, Is.EqualTo(errorMessage));

            Assert.That(_mockResponse.Object.StatusCode, Is.EqualTo((int)HttpStatusCode.BadRequest));
            Assert.That(_mockResponse.Object.TrySkipIisCustomErrors, Is.True, "TrySkipIisCustomErrors should be true.");
        }

        [Given("the user is trying to save a cost price override with the note {string}")]
        public void GivenUserIsTryingToSaveWithNote(string note)
        {
            _note = note;
            _productId = 1; // Assume a valid product for this test
            _productSources.Add(new ProductSource { ID = _productId, ProductTitle = "Test Product" });
            _costPrice = 100m; // Valid cost price
            _expiryDate = DateTime.Now.AddYears(1); // Valid date
            SetupController();
        }

        [When("they click the save button")]
        public async Task WhenTheyClickTheSaveButton()
        {
            _result = await _controller.SetCostPriceOverrideForProductSource(_productId, _costPrice, _expiryDate,
                _note);
        }

        [Then("a cost price override entry should NOT be added")]
        public void ThenACostPriceOverrideEntryShouldNotBeAdded()
        {
            Assert.That(_productSourceCostPriceOverrides, Is.Empty, "No cost price override should have been added.");
            _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Never,
                "SaveChangesAsync should not have been called.");
        }

        [Given("the user is trying to save a cost price override with the date {string}")]
        public void GivenUserIsTryingToSaveWithDate(string dateString)
        {
            _productId = 1; // Assume a valid product
            _productSources.Add(new ProductSource { ID = _productId, ProductTitle = "Test Product" });
            _costPrice = 100m; // Valid cost price
            _note = "Valid Note"; // Valid note

            // Simulate how the controller's model binding might interpret invalid date strings
            // for a DateTime? parameter. "" or unparseable strings usually result in null.
            if (string.IsNullOrWhiteSpace(dateString) ||
                !DateTime.TryParseExact(dateString, "dd-MM-yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None,
                    out var parsedDate))
            {
                _expiryDate = null; // Simulate invalid date resulting in null for DateTime?
            }
            else
            {
                // This path should ideally not be hit by "invalid date" examples if they are truly unparseable or empty
                _expiryDate = parsedDate;
            }

            SetupController();
        }

        [Given("a valid cost price override already exists for product {int}")]
        public void GivenAValidCostPriceOverrideAlreadyExistsForProduct(int productId)
        {
            // Setup existing valid override
            _existingCostPrice = 50m; // Or any other distinct value for testing
            _existingExpiryDate = DateTime.Now.AddMonths(6); // Ensure it's valid and different from new date
            _existingNote = "Old test note"; // Or any other distinct value

            _productSourceCostPriceOverrides.Add(new ProductSourceCostPriceOverride
            {
                ID = _existingOverrideId, // Uses the class field _existingOverrideId
                ProductSourceId = productId, // Use the productId from the step
                CostPrice = _existingCostPrice,
                ExpiryDate = _existingExpiryDate,
                Note = _existingNote,
                ActionedBy = "anotheruser" // Simulate it was actioned by someone else previously
            });

            SetupController(); // Refresh the DbContext mock with the new override
        }

        [Given(
            "the user is trying to save a cost price override for product with ID {int}, cost price {decimal}, date {string} and note {string}")]
        public void GivenUserIsTryingToSaveValidData(int productId, decimal costPrice, string dateString, string note)
        {
            _productId = productId;
            _costPrice = costPrice;
            _note = note;

            _productSources.Add(new ProductSource { ID = _productId, ProductTitle = "Test Product " + productId });

            if (!DateTime.TryParseExact(dateString, "dd-MM-yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None,
                    out var parsedDate))
            {
                throw new ArgumentException($"Invalid date format in Gherkin step: {dateString}. Expected dd-MM-yyyy.");
            }

            _expiryDate = parsedDate;
            SetupController();
        }

        [Then("a new cost price override entry should be added")]
        public void ThenANewCostPriceOverrideEntryShouldBeAdded()
        {
            Assert.That(_result, Is.InstanceOf<JsonResult>(), "Result should be a JsonResult for success.");
            var jsonResult = (JsonResult)_result;
            dynamic jsonData = jsonResult.Data;
            Assert.That(Convert.ToDecimal(jsonData.GetType().GetProperty("costPrice").GetValue(jsonData, null)),
                Is.EqualTo(_costPrice));


            Assert.That(_productSourceCostPriceOverrides, Has.Count.EqualTo(1),
                "One cost price override should have been added.");
            var newEntry = _productSourceCostPriceOverrides.First();
            Assert.That(newEntry.ProductSourceId, Is.EqualTo(_productId));
            Assert.That(newEntry.CostPrice, Is.EqualTo(_costPrice));
            Assert.That(newEntry.ExpiryDate,
                Is.EqualTo(_expiryDate.Value)); // .Value is safe here due to valid data scenario
            Assert.That(newEntry.Note, Is.EqualTo(_note));
            Assert.That(newEntry.ActionedBy, Is.EqualTo("testuser"));

            _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Once,
                "SaveChangesAsync should have been called once.");
        }

        [Then(
            "the valid existing cost price override should have its 'CostPrice' property updated to {decimal}, 'ExpiryDate' property updated to {string} and 'Note' property updated to {string}")]
        public void ThenTheExistingOverrideShouldBeUpdated(decimal expectedCostPrice, string expectedDateString,
            string expectedNote)
        {
            Assert.That(_result, Is.InstanceOf<JsonResult>(), "Result should be a JsonResult for success.");
            var jsonResult = (JsonResult)_result;
            dynamic jsonData = jsonResult.Data;
            Assert.That(Convert.ToDecimal(jsonData.GetType().GetProperty("costPrice").GetValue(jsonData, null)),
                Is.EqualTo(expectedCostPrice));

            Assert.That(_productSourceCostPriceOverrides, Has.Count.EqualTo(1),
                "Exactly one cost price override record should exist.");
            var updatedEntry = _productSourceCostPriceOverrides.First(o => o.ID == _existingOverrideId);

            Assert.That(updatedEntry.ProductSourceId, Is.EqualTo(_productId));
            Assert.That(updatedEntry.CostPrice, Is.EqualTo(expectedCostPrice));

            if (!DateTime.TryParseExact(expectedDateString, "dd-MM-yyyy", CultureInfo.InvariantCulture,
                    DateTimeStyles.None, out var parsedExpectedDate))
            {
                throw new ArgumentException(
                    $"Invalid expected date format in Gherkin step: {expectedDateString}. Expected dd-MM-yyyy.");
            }

            Assert.That(updatedEntry.ExpiryDate, Is.EqualTo(parsedExpectedDate));
            Assert.That(updatedEntry.Note, Is.EqualTo(expectedNote));
            Assert.That(updatedEntry.ActionedBy, Is.EqualTo("testuser")); // Should be updated by the current user

            // Verify Add was not called, as we are updating
            _mockDbContext.Verify(
                db => db.Set<ProductSourceCostPriceOverride>().Add(It.IsAny<ProductSourceCostPriceOverride>()),
                Times.Never);
            _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Once,
                "SaveChangesAsync should have been called once for the update.");
        }
    }
}
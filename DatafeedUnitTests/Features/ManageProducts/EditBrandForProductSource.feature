@EditBrandForProductSource
Feature: Ability to edit a product sources' brand on the Manage Products page

    Scenario: A user has tried to access the endpoint directly for a product ID that doesn't exist
        Given a user is trying to access the endpoint directly for product 1
        When their request is received
        Then the error message 'Invalid product ID.' should be returned

    Scenario Outline: User has entered a blank brand and clicked the save button
        Given the user has edited the brand to '<brand>' for product <productId> whose original brand is '<originalBrand>'
        And the brand is invalid
        When they click the save button
        Then the products' brand should not be updated
        And the error message '<errorMessage>' should be returned

        Examples:
          | brand | productId | originalBrand  | errorMessage          |
          |       | 1         | original brand | New brand is invalid. |

    Scenario Outline: User has entered a valid new brand and clicked the save button
        Given the user has edited the brand to '<brand>' for product <productId> whose original brand is '<originalBrand>'
        And the brand is valid
        When they click the save button
        Then the products' brand should be updated

        Examples:
          | brand     | productId | originalBrand  |
          | new brand | 1         | original brand |
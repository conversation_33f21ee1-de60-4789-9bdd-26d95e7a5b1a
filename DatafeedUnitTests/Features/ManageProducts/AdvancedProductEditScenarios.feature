@AdvancedProductEditScenarios
Feature: Advanced Product Editing Scenarios and Edge Cases
As a user managing products
I want the comprehensive edit modal to handle complex scenarios and edge cases
So that I can reliably manage products in all situations

    Background:
        Given the manage products page is loaded
        And the products table contains test data

# Concurrent Editing Scenarios

    Scenario: Two users attempt to edit the same product simultaneously
        Given a ProductSource with ID 1 exists
        And user A opens the comprehensive edit modal for ProductSource 1
        And user B opens the comprehensive edit modal for ProductSource 1
        When user A changes the title to "Title A" and saves
        And user B changes the title to "Title B" and attempts to save
        Then user B should receive a conflict error message
        And the error should indicate "This product has been modified by another user"
        And user B should be prompted to refresh and try again

    Scenario: User edits product that was deleted by another user
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        And another user deletes ProductSource 1
        When the user changes the title and clicks "Save"
        Then an error message "Product no longer exists" should be displayed
        And the user should be redirected back to the products table

# Complex Price Override Scenarios

    Scenario: User updates existing sell price override
        Given a ProductSource with ID 1 exists
        And the ProductSource has an existing sell price override of 100.00 expiring on "2025-06-01"
        And the comprehensive edit modal is open for ProductSource 1
        When the user changes the sell price override to 120.00
        And the user changes the expiry date to "2025-12-01"
        And the user changes the note to "Updated pricing"
        And the user clicks "Save"
        Then the existing override should be updated with the new values
        And the modal should close
        And a success message should be displayed

    Scenario: User removes existing price override
        Given a ProductSource with ID 1 exists
        And the ProductSource has an existing sell price override
        And the comprehensive edit modal is open for ProductSource 1
        When the user clears the sell price override field
        And the user clicks "Save"
        Then the existing price override should be removed
        And the product should use its base price
        And the modal should close
        And a success message should be displayed

    Scenario: User sets multiple price overrides simultaneously
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user sets a sell price override of 150.00 with expiry "2025-12-31"
        And the user sets a cost price override of 80.00 with expiry "2025-11-30"
        And the user clicks "Save"
        Then both price overrides should be created
        And the modal should close
        And a success message should be displayed

# Data Integrity Scenarios

    Scenario: User enters extremely long text values
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user enters a title with 1000 characters
        And the user enters a long description with 10000 characters
        And the user clicks "Save"
        Then appropriate validation should occur based on field limits
        And if limits are exceeded, appropriate error messages should be displayed

    Scenario: User enters special characters and HTML in text fields
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user enters a title with special characters "Product <script>alert('test')</script> & Co."
        And the user enters a description with HTML tags "<b>Bold</b> and <i>italic</i> text"
        And the user clicks "Save"
        Then the special characters should be properly escaped/sanitised
        And the product should be saved with safe content
        And the modal should close

# Performance and Large Data Scenarios

    Scenario: User edits product with many existing price overrides
        Given a ProductSource with ID 1 exists
        And the ProductSource has 50 expired price overrides in history
        When the user opens the comprehensive edit modal for ProductSource 1
        Then the modal should load within reasonable time
        And only current/active overrides should be displayed for editing
        And historical overrides should not impact performance

    Scenario: User works with product having many images
        Given a ProductSource with ID 1 exists
        And the ProductSource has 10 existing images (maximum allowed)
        When the user opens the comprehensive edit modal for ProductSource 1
        Then all images should load and display properly
        And the upload section should indicate "Maximum images reached"
        And the user should not be able to upload additional images

# Network and Connectivity Scenarios

    Scenario: User loses internet connection while editing
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        And the user makes several changes
        When the internet connection is lost
        And the user clicks "Save"
        Then an appropriate error message should be displayed
        And the user's changes should be preserved in the modal
        And the user should be able to retry saving when connection is restored

    Scenario: Server responds slowly during save operation
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        And the server response time is very slow
        When the user makes changes and clicks "Save"
        Then a loading indicator should be displayed
        And the save button should be disabled to prevent double-submission
        And the user should receive feedback about the ongoing operation

# Browser and Client-side Scenarios

    Scenario: User refreshes browser with unsaved changes
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user makes changes to the product
        And the user attempts to refresh the browser
        Then a browser confirmation should appear warning about unsaved changes
        And the user should be able to choose whether to stay or leave

    Scenario: User navigates away with unsaved changes
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user makes changes to the product
        And the user attempts to navigate to another page
        Then a confirmation dialog should appear
        And the dialog should warn about losing unsaved changes
        And the user should be able to choose whether to stay or leave

# Accessibility Scenarios

    Scenario: User navigates modal using keyboard only
        Given a ProductSource with ID 1 exists
        When the user opens the comprehensive edit modal using keyboard navigation
        Then all form fields should be accessible via Tab key
        And the user should be able to save using keyboard shortcuts
        And the user should be able to close the modal using Escape key
        And all interactive elements should have proper focus indicators

    Scenario: Screen reader user interacts with modal
        Given a ProductSource with ID 1 exists
        And a screen reader is active
        When the user opens the comprehensive edit modal
        Then all form labels should be properly associated with inputs
        And error messages should be announced when validation fails
        And success messages should be announced when save completes
        And the modal should have appropriate ARIA attributes

# Integration with Existing Features Scenarios

    Scenario: User edits product and table filters are active
        Given the products table has active filters for "Brand = TestBrand"
        And a ProductSource with ID 1 matching the filter exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user changes the brand to "DifferentBrand"
        And the user saves the changes
        Then the modal should close
        And the products table should refresh
        And the product should no longer appear in the filtered results
        And the table should maintain its current filter settings

    Scenario: User edits product while table is sorted
        Given the products table is sorted by "Title" in ascending order
        And a ProductSource with ID 1 exists with title "B Product"
        And the comprehensive edit modal is open for ProductSource 1
        When the user changes the title to "Z Product"
        And the user saves the changes
        Then the modal should close
        And the products table should refresh
        And the product should appear in the correct sorted position
        And the table should maintain its current sort order

# Bulk Operations Integration

    Scenario: User edits product that was part of a bulk operation
        Given multiple products were recently updated via bulk operation
        And a ProductSource with ID 1 was included in the bulk update
        And the comprehensive edit modal is open for ProductSource 1
        When the user makes additional changes
        And the user saves the changes
        Then the individual changes should be applied successfully
        And any bulk operation history should be preserved
        And the modal should close normally

# Error Recovery Scenarios

    Scenario: User encounters validation error and corrects it
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user clears the required title field
        And the user clicks "Save"
        Then a validation error should be displayed
        When the user enters a valid title
        And the user clicks "Save" again
        Then the validation error should clear
        And the product should be saved successfully
        And the modal should close

    Scenario: User encounters server error and retries
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        And the server will return an error on first save attempt
        When the user makes changes and clicks "Save"
        Then a server error message should be displayed
        And the modal should remain open with user's changes intact
        When the server issue is resolved
        And the user clicks "Save" again
        Then the product should be saved successfully
        And the modal should close
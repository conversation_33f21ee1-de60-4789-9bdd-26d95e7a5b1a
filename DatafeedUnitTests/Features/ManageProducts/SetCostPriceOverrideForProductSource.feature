@SetCostPriceOverrideForProductSource
Feature: Ability to add a cost price override for a product source with an expiry date

    Scenario: A user has tried to access the endpoint directly for a product ID that doesn't exist
        Given a user is trying to add a cost price override for product 1
        When their request is received
        Then the error message 'Invalid product ID.' should be returned

    Scenario Outline: User has entered an invalid note and clicked the save button
        Given the user is trying to save a cost price override with the note '<note>'
        When they click the save button
        Then a cost price override entry should NOT be added
        And the error message '<errorMessage>' should be returned

        Examples:
          | note | errorMessage     |
          |      | Note is invalid. |

    Scenario Outline: User has entered an invalid date and clicked the save button
        Given the user is trying to save a cost price override with the date '<date>'
        When they click the save button
        Then a cost price override entry should NOT be added
        And the error message '<errorMessage>' should be returned

        Examples:
          | date            | errorMessage     |
          |                 | Date is invalid. |
          | lksjdflkjdsf    | Date is invalid. |
          | ojsoiu093904590 | Date is invalid. |

    Scenario Outline: User has entered valid data but a valid ('ExpiryDate' > Now) cost price override already exists for the specified product source
        Given the user is trying to save a cost price override for product with ID <productId>, cost price <costPrice>, date '<expiryDate>' and note '<note>'
        And a valid cost price override already exists for product <productId>
        When they click the save button
        Then the valid existing cost price override should have its 'CostPrice' property updated to <costPrice>, 'ExpiryDate' property updated to '<expiryDate>' and 'Note' property updated to '<note>'

        Examples:
          | productId | costPrice | expiryDate | note                     |
          | 1         | 100       | 16-05-2025 | Test cost price override |

    Scenario Outline: User has entered valid data and clicked the save button
        Given the user is trying to save a cost price override for product with ID <productId>, cost price <costPrice>, date '<expiryDate>' and note '<note>'
        When they click the save button
        Then a new cost price override entry should be added

        Examples:
          | productId | costPrice | expiryDate | note                     |
          | 1         | 100       | 15-05-2025 | Test cost price override |
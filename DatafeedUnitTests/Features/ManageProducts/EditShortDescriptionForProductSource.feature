@EditShortDescriptionForProductSource
Feature: Ability to edit a product sources' short description on the Manage Products page

    Scenario: A user has tried to access the Edit Short Description For Product Source endpoint directly for a product ID that doesn't exist
        Given a user is trying to access the Edit Short Description For Product Source endpoint directly for product 1
        When their request is received
        Then the error message 'Invalid product ID.' should be returned

    Scenario Outline: User has entered a blank short description and clicked the save button
        Given the user has edited the short description to '<newShortDescription>' for product <productId> whose original short description is '<originalShortDescription>'
        When they click the save button
        Then the products' short description should NOT be updated
        And the error message '<errorMessage>' should be returned

        Examples:
          | newShortDescription | productId | originalShortDescription   | errorMessage                              |
          |                     | 1         | original short description | New product short description is invalid. |

    Scenario Outline: User has entered a new short description and clicked the save button
        Given the user has edited the short description to '<newShortDescription>' for product <productId> whose original short description is '<originalShortDescription>'
        When they click the save button
        Then the products' short description should be updated

        Examples:
          | productId | newShortDescription   | originalShortDescription   |
          | 1         | new short description | original short description |
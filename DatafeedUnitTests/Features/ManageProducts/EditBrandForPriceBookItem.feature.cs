// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:*******
//      Reqnroll Generator Version:*******
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace DatafeedUnitTests.Features.ManageProducts
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [NUnit.Framework.TestFixtureAttribute()]
    [NUnit.Framework.DescriptionAttribute("Ability to edit a price book item\'s brand on the Manage Products page")]
    [NUnit.Framework.FixtureLifeCycleAttribute(NUnit.Framework.LifeCycle.InstancePerTestCase)]
    [NUnit.Framework.CategoryAttribute("EditBrandForPriceBookItem")]
    public partial class AbilityToEditAPriceBookItemsBrandOnTheManageProductsPageFeature
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "EditBrandForPriceBookItem"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features/ManageProducts", "Ability to edit a price book item\'s brand on the Manage Products page", null, global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
#line 1 "EditBrandForPriceBookItem.feature"
#line hidden
        
        [NUnit.Framework.OneTimeSetUpAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        [NUnit.Framework.OneTimeTearDownAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        [NUnit.Framework.SetUpAttribute()]
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        [NUnit.Framework.TearDownAttribute()]
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<NUnit.Framework.TestContext>(NUnit.Framework.TestContext.CurrentContext);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("A user has tries to access the endpoint directly for a price book item ID that do" +
            "esn\'t exist")]
        public async global::System.Threading.Tasks.Task AUserHasTriesToAccessTheEndpointDirectlyForAPriceBookItemIDThatDoesntExist()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("A user has tries to access the endpoint directly for a price book item ID that do" +
                    "esn\'t exist", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 4
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 5
        await testRunner.GivenAsync("a user is trying to access the endpoint directly for price book item 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 6
        await testRunner.WhenAsync("their request is received", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 7
        await testRunner.ThenAsync("the error message \'Invalid price book item ID.\' should be returned", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User has entered a blank brand and clicked the save button")]
        [NUnit.Framework.TestCaseAttribute("", "1", "original brand", "New brand is invalid.", null)]
        public async global::System.Threading.Tasks.Task UserHasEnteredABlankBrandAndClickedTheSaveButton(string brand, string priceBookItemId, string originalBrand, string errorMessage, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("brand", brand);
            argumentsOfScenario.Add("priceBookItemId", priceBookItemId);
            argumentsOfScenario.Add("originalBrand", originalBrand);
            argumentsOfScenario.Add("errorMessage", errorMessage);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User has entered a blank brand and clicked the save button", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 9
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 10
        await testRunner.GivenAsync(string.Format("the user has edited the brand to \'{0}\' for price book item {1} whose original bra" +
                            "nd is \'{2}\'", brand, priceBookItemId, originalBrand), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 11
        await testRunner.AndAsync("the brand is invalid", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 12
        await testRunner.WhenAsync("they click the save button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 13
        await testRunner.ThenAsync("the price book item\'s brand should not be updated", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 14
        await testRunner.AndAsync(string.Format("the error message \'{0}\' should be returned", errorMessage), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User has entered a valid new brand and clicked the save button")]
        [NUnit.Framework.TestCaseAttribute("new brand", "1", "original brand", null)]
        public async global::System.Threading.Tasks.Task UserHasEnteredAValidNewBrandAndClickedTheSaveButton(string brand, string priceBookItemId, string originalBrand, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("brand", brand);
            argumentsOfScenario.Add("priceBookItemId", priceBookItemId);
            argumentsOfScenario.Add("originalBrand", originalBrand);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User has entered a valid new brand and clicked the save button", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 20
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 21
        await testRunner.GivenAsync(string.Format("the user has edited the brand to \'{0}\' for price book item {1} whose original bra" +
                            "nd is \'{2}\'", brand, priceBookItemId, originalBrand), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 22
        await testRunner.AndAsync("the brand is valid", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 23
        await testRunner.WhenAsync("they click the save button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 24
        await testRunner.ThenAsync("the price book item\'s brand should be updated", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
    }
}
#pragma warning restore
#endregion

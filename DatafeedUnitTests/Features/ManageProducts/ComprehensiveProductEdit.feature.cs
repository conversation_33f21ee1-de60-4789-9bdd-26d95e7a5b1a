// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:*******
//      Reqnroll Generator Version:*******
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace DatafeedUnitTests.Features.ManageProducts
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [NUnit.Framework.TestFixtureAttribute()]
    [NUnit.Framework.DescriptionAttribute("Comprehensive Product Editing Modal")]
    [NUnit.Framework.FixtureLifeCycleAttribute(NUnit.Framework.LifeCycle.InstancePerTestCase)]
    [NUnit.Framework.CategoryAttribute("ComprehensiveProductEdit")]
    public partial class ComprehensiveProductEditingModalFeature
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "ComprehensiveProductEdit"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features/ManageProducts", "Comprehensive Product Editing Modal", "As a user managing products\r\nI want to edit all product properties in a single co" +
                "mprehensive modal\r\nSo that I can efficiently manage product information without " +
                "multiple inline edits", global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
#line 1 "ComprehensiveProductEdit.feature"
#line hidden
        
        [NUnit.Framework.OneTimeSetUpAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        [NUnit.Framework.OneTimeTearDownAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        [NUnit.Framework.SetUpAttribute()]
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        [NUnit.Framework.TearDownAttribute()]
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<NUnit.Framework.TestContext>(NUnit.Framework.TestContext.CurrentContext);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        public virtual async global::System.Threading.Tasks.Task FeatureBackgroundAsync()
        {
#line 7
    #line hidden
#line 8
        await testRunner.GivenAsync("the manage products page is loaded", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 9
        await testRunner.AndAsync("the products table contains test data", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User clicks Edit button for a ProductSource")]
        public async global::System.Threading.Tasks.Task UserClicksEditButtonForAProductSource()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User clicks Edit button for a ProductSource", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 13
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 14
        await testRunner.GivenAsync("a ProductSource with ID 1 exists in the table", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 15
        await testRunner.WhenAsync("the user clicks the \"Edit\" button for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 16
        await testRunner.ThenAsync("the comprehensive edit modal should open", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 17
        await testRunner.AndAsync("the modal title should be \"Edit Product\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 18
        await testRunner.AndAsync("all ProductSource properties should be displayed in the modal", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 19
        await testRunner.AndAsync("the modal should show the correct product type as \"ProductSource\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User clicks Edit button for a PriceBookItem")]
        public async global::System.Threading.Tasks.Task UserClicksEditButtonForAPriceBookItem()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User clicks Edit button for a PriceBookItem", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 21
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 22
        await testRunner.GivenAsync("a PriceBookItem with ID 1 exists in the table", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 23
        await testRunner.WhenAsync("the user clicks the \"Edit\" button for PriceBookItem 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 24
        await testRunner.ThenAsync("the comprehensive edit modal should open", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 25
        await testRunner.AndAsync("the modal title should be \"Edit Product\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 26
        await testRunner.AndAsync("all PriceBookItem properties should be displayed in the modal", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 27
        await testRunner.AndAsync("the modal should show the correct product type as \"PriceBookItem\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User tries to edit a non-existent product")]
        public async global::System.Threading.Tasks.Task UserTriesToEditANon_ExistentProduct()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User tries to edit a non-existent product", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 29
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 30
        await testRunner.GivenAsync("no product with ID 999 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 31
        await testRunner.WhenAsync("the user attempts to open the edit modal for product 999", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 32
        await testRunner.ThenAsync("an error message \"Product not found\" should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 33
        await testRunner.AndAsync("the modal should not open", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User edits basic ProductSource properties")]
        [NUnit.Framework.TestCaseAttribute("Old Title", "New Title", "NEW-SKU-1", "NewBrand", null)]
        [NUnit.Framework.TestCaseAttribute("Test Product", "Updated Title", "UPD-SKU-2", "TestCorp", null)]
        public async global::System.Threading.Tasks.Task UserEditsBasicProductSourceProperties(string originalTitle, string newTitle, string newSku, string newBrand, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("originalTitle", originalTitle);
            argumentsOfScenario.Add("newTitle", newTitle);
            argumentsOfScenario.Add("newSku", newSku);
            argumentsOfScenario.Add("newBrand", newBrand);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User edits basic ProductSource properties", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 37
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 38
        await testRunner.GivenAsync(string.Format("a ProductSource with ID 1 exists with title \"{0}\"", originalTitle), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 39
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 40
        await testRunner.WhenAsync(string.Format("the user changes the title to \"{0}\"", newTitle), ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 41
        await testRunner.AndAsync(string.Format("the user changes the SKU to \"{0}\"", newSku), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 42
        await testRunner.AndAsync(string.Format("the user changes the brand to \"{0}\"", newBrand), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 43
        await testRunner.AndAsync("the user clicks the \"Save\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 44
        await testRunner.ThenAsync("the product should be updated with the new values", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 45
        await testRunner.AndAsync("the modal should close", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 46
        await testRunner.AndAsync("the products table should refresh", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 47
        await testRunner.AndAsync("a success message should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User edits basic PriceBookItem properties")]
        [NUnit.Framework.TestCaseAttribute("Old Product", "New Product", "NEW-SKU-3", "NewBrand", null)]
        [NUnit.Framework.TestCaseAttribute("Test Item", "Updated Item", "UPD-SKU-4", "TestCorp", null)]
        public async global::System.Threading.Tasks.Task UserEditsBasicPriceBookItemProperties(string originalName, string newName, string newSku, string newBrand, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("originalName", originalName);
            argumentsOfScenario.Add("newName", newName);
            argumentsOfScenario.Add("newSku", newSku);
            argumentsOfScenario.Add("newBrand", newBrand);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User edits basic PriceBookItem properties", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 54
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 55
        await testRunner.GivenAsync(string.Format("a PriceBookItem with ID 1 exists with name \"{0}\"", originalName), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 56
        await testRunner.AndAsync("the comprehensive edit modal is open for PriceBookItem 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 57
        await testRunner.WhenAsync(string.Format("the user changes the name to \"{0}\"", newName), ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 58
        await testRunner.AndAsync(string.Format("the user changes the SKU to \"{0}\"", newSku), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 59
        await testRunner.AndAsync(string.Format("the user changes the brand to \"{0}\"", newBrand), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 60
        await testRunner.AndAsync("the user clicks the \"Save\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 61
        await testRunner.ThenAsync("the product should be updated with the new values", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 62
        await testRunner.AndAsync("the modal should close", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 63
        await testRunner.AndAsync("the products table should refresh", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 64
        await testRunner.AndAsync("a success message should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User edits ProductSource descriptions")]
        public async global::System.Threading.Tasks.Task UserEditsProductSourceDescriptions()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User edits ProductSource descriptions", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 73
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 74
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 75
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 76
        await testRunner.WhenAsync("the user changes the short description to \"New short description\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 77
        await testRunner.AndAsync("the user changes the long description to \"New detailed long description with more" +
                        " information\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 78
        await testRunner.AndAsync("the user clicks the \"Save\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 79
        await testRunner.ThenAsync("the ProductSource should be updated with the new descriptions", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 80
        await testRunner.AndAsync("the modal should close", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 81
        await testRunner.AndAsync("a success message should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User edits PriceBookItem description")]
        public async global::System.Threading.Tasks.Task UserEditsPriceBookItemDescription()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User edits PriceBookItem description", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 83
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 84
        await testRunner.GivenAsync("a PriceBookItem with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 85
        await testRunner.AndAsync("the comprehensive edit modal is open for PriceBookItem 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 86
        await testRunner.WhenAsync("the user changes the description to \"New product description\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 87
        await testRunner.AndAsync("the user changes the short description to \"New short description\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 88
        await testRunner.AndAsync("the user clicks the \"Save\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 89
        await testRunner.ThenAsync("the PriceBookItem should be updated with the new descriptions", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 90
        await testRunner.AndAsync("the modal should close", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 91
        await testRunner.AndAsync("a success message should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User sets sell price override for ProductSource")]
        public async global::System.Threading.Tasks.Task UserSetsSellPriceOverrideForProductSource()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User sets sell price override for ProductSource", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 95
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 96
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 97
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 98
        await testRunner.WhenAsync("the user sets a sell price override of 150.00", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 99
        await testRunner.AndAsync("the user sets the override expiry date to \"2025-12-31\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 100
        await testRunner.AndAsync("the user sets the override note to \"Holiday pricing\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 101
        await testRunner.AndAsync("the user clicks the \"Save\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 102
        await testRunner.ThenAsync("a sell price override should be created for the ProductSource", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 103
        await testRunner.AndAsync("the override should have the correct price, expiry date, and note", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 104
        await testRunner.AndAsync("the modal should close", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 105
        await testRunner.AndAsync("a success message should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User sets cost price override for ProductSource")]
        public async global::System.Threading.Tasks.Task UserSetsCostPriceOverrideForProductSource()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User sets cost price override for ProductSource", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 107
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 108
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 109
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 110
        await testRunner.WhenAsync("the user sets a cost price override of 75.00", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 111
        await testRunner.AndAsync("the user sets the cost override expiry date to \"2025-12-31\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 112
        await testRunner.AndAsync("the user sets the cost override note to \"Supplier discount\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 113
        await testRunner.AndAsync("the user clicks the \"Save\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 114
        await testRunner.ThenAsync("a cost price override should be created for the ProductSource", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 115
        await testRunner.AndAsync("the override should have the correct price, expiry date, and note", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 116
        await testRunner.AndAsync("the modal should close", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 117
        await testRunner.AndAsync("a success message should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User sets sell price override for PriceBookItem")]
        public async global::System.Threading.Tasks.Task UserSetsSellPriceOverrideForPriceBookItem()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User sets sell price override for PriceBookItem", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 119
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 120
        await testRunner.GivenAsync("a PriceBookItem with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 121
        await testRunner.AndAsync("the comprehensive edit modal is open for PriceBookItem 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 122
        await testRunner.WhenAsync("the user sets a sell price override of 200.00", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 123
        await testRunner.AndAsync("the user clicks the \"Save\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 124
        await testRunner.ThenAsync("a sell price override should be created for the PriceBookItem", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 125
        await testRunner.AndAsync("the override should have the correct price", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 126
        await testRunner.AndAsync("the modal should close", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 127
        await testRunner.AndAsync("a success message should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User sets cost price override for PriceBookItem")]
        public async global::System.Threading.Tasks.Task UserSetsCostPriceOverrideForPriceBookItem()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User sets cost price override for PriceBookItem", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 129
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 130
        await testRunner.GivenAsync("a PriceBookItem with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 131
        await testRunner.AndAsync("the comprehensive edit modal is open for PriceBookItem 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 132
        await testRunner.WhenAsync("the user sets a cost price override of 100.00", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 133
        await testRunner.AndAsync("the user sets the cost override expiry date to \"2025-09-15\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 134
        await testRunner.AndAsync("the user sets the cost override note to \"Bulk purchase discount\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 135
        await testRunner.AndAsync("the user clicks the \"Save\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 136
        await testRunner.ThenAsync("a cost price override should be created for the PriceBookItem", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 137
        await testRunner.AndAsync("the override should have the correct price, expiry date, and note", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 138
        await testRunner.AndAsync("the modal should close", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 139
        await testRunner.AndAsync("a success message should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User sets status override for ProductSource")]
        public async global::System.Threading.Tasks.Task UserSetsStatusOverrideForProductSource()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User sets status override for ProductSource", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 143
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 144
        await testRunner.GivenAsync("a ProductSource with ID 1 exists with status \"Active\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 145
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 146
        await testRunner.WhenAsync("the user sets a status override to \"Disabled\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 147
        await testRunner.AndAsync("the user clicks the \"Save\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 148
        await testRunner.ThenAsync("a status override should be created for the ProductSource", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 149
        await testRunner.AndAsync("the override should set the status to \"Disabled\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 150
        await testRunner.AndAsync("the modal should close", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 151
        await testRunner.AndAsync("a success message should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User enters invalid data for required fields")]
        [NUnit.Framework.TestCaseAttribute("title", "Product title is required", null)]
        [NUnit.Framework.TestCaseAttribute("sku", "Product SKU is required", null)]
        [NUnit.Framework.TestCaseAttribute("brand", "Product brand is required", null)]
        public async global::System.Threading.Tasks.Task UserEntersInvalidDataForRequiredFields(string field, string errorMessage, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("field", field);
            argumentsOfScenario.Add("errorMessage", errorMessage);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User enters invalid data for required fields", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 155
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 156
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 157
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 158
        await testRunner.WhenAsync(string.Format("the user clears the \"{0}\" field", field), ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 159
        await testRunner.AndAsync("the user clicks the \"Save\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 160
        await testRunner.ThenAsync(string.Format("a validation error should be displayed for the \"{0}\" field", field), ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 161
        await testRunner.AndAsync(string.Format("the error message should be \"{0}\"", errorMessage), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 162
        await testRunner.AndAsync("the modal should remain open", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 163
        await testRunner.AndAsync("the product should not be saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User tries to save from Product Information tab when sell price override is requi" +
            "red")]
        public async global::System.Threading.Tasks.Task UserTriesToSaveFromProductInformationTabWhenSellPriceOverrideIsRequired()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User tries to save from Product Information tab when sell price override is requi" +
                    "red", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 171
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 172
        await testRunner.GivenAsync("a ProductSource with ID 1 exists with no sell price and no existing override", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 173
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 174
        await testRunner.AndAsync("the user is on the \"Product Information\" tab", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 175
        await testRunner.WhenAsync("the user clicks the \"Save\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 176
        await testRunner.ThenAsync("the modal should automatically switch to the \"Pricing & Overrides\" tab", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 177
        await testRunner.AndAsync("the sell price override field should be focused", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 178
        await testRunner.AndAsync("a validation error should be displayed for the sell price override field", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 179
        await testRunner.AndAsync("the error message should be \"Sell price override is required as the product has n" +
                        "o existing sell price\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 180
        await testRunner.AndAsync("the modal should remain open", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 181
        await testRunner.AndAsync("the product should not be saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User enters invalid price override values")]
        public async global::System.Threading.Tasks.Task UserEntersInvalidPriceOverrideValues()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User enters invalid price override values", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 183
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 184
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 185
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 186
        await testRunner.WhenAsync("the user sets a sell price override of -10.00", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 187
        await testRunner.AndAsync("the user clicks the \"Save\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 188
        await testRunner.ThenAsync("a validation error should be displayed for the sell price override", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 189
        await testRunner.AndAsync("the error message should be \"Price must be greater than zero\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 190
        await testRunner.AndAsync("the modal should remain open", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 191
        await testRunner.AndAsync("the product should not be saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User enters invalid expiry date for price override")]
        public async global::System.Threading.Tasks.Task UserEntersInvalidExpiryDateForPriceOverride()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User enters invalid expiry date for price override", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 193
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 194
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 195
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 196
        await testRunner.WhenAsync("the user sets a sell price override of 100.00", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 197
        await testRunner.AndAsync("the user sets the override expiry date to \"2020-01-01\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 198
        await testRunner.AndAsync("the user clicks the \"Save\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 199
        await testRunner.ThenAsync("a validation error should be displayed for the expiry date", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 200
        await testRunner.AndAsync("the error message should be \"Expiry date must be in the future\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 201
        await testRunner.AndAsync("the modal should remain open", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 202
        await testRunner.AndAsync("the product should not be saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User cancels editing without saving")]
        public async global::System.Threading.Tasks.Task UserCancelsEditingWithoutSaving()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User cancels editing without saving", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 206
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 207
        await testRunner.GivenAsync("a ProductSource with ID 1 exists with title \"Original Title\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 208
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 209
        await testRunner.WhenAsync("the user changes the title to \"Modified Title\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 210
        await testRunner.AndAsync("the user clicks the \"Cancel\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 211
        await testRunner.ThenAsync("the modal should close", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 212
        await testRunner.AndAsync("the product should not be updated", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 213
        await testRunner.AndAsync("the title should remain \"Original Title\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User closes modal using X button")]
        public async global::System.Threading.Tasks.Task UserClosesModalUsingXButton()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User closes modal using X button", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 215
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 216
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 217
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 218
        await testRunner.WhenAsync("the user changes the title to \"Modified Title\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 219
        await testRunner.AndAsync("the user clicks the modal close button (X)", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 220
        await testRunner.ThenAsync("the modal should close", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 221
        await testRunner.AndAsync("the product should not be updated", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User closes modal by clicking outside")]
        public async global::System.Threading.Tasks.Task UserClosesModalByClickingOutside()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User closes modal by clicking outside", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 223
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
    await this.FeatureBackgroundAsync();
#line hidden
#line 224
        await testRunner.GivenAsync("a ProductSource with ID 1 exists", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 225
        await testRunner.AndAsync("the comprehensive edit modal is open for ProductSource 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 226
        await testRunner.WhenAsync("the user changes the title to \"Modified Title\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 227
        await testRunner.AndAsync("the user clicks outside the modal", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 228
        await testRunner.ThenAsync("the modal should close", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 229
        await testRunner.AndAsync("the product should not be updated", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
    }
}
#pragma warning restore
#endregion

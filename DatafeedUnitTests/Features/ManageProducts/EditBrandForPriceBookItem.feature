@EditBrandForPriceBookItem
Feature: Ability to edit a price book item's brand on the Manage Products page

    Scenario: A user has tries to access the endpoint directly for a price book item ID that doesn't exist
        Given a user is trying to access the endpoint directly for price book item 1
        When their request is received
        Then the error message 'Invalid price book item ID.' should be returned

    Scenario Outline: User has entered a blank brand and clicked the save button
        Given the user has edited the brand to '<brand>' for price book item <priceBookItemId> whose original brand is '<originalBrand>'
        And the brand is invalid
        When they click the save button
        Then the price book item's brand should not be updated
        And the error message '<errorMessage>' should be returned

        Examples:
          | brand | priceBookItemId | originalBrand  | errorMessage          |
          |       | 1               | original brand | New brand is invalid. |

    Scenario Outline: User has entered a valid new brand and clicked the save button
        Given the user has edited the brand to '<brand>' for price book item <priceBookItemId> whose original brand is '<originalBrand>'
        And the brand is valid
        When they click the save button
        Then the price book item's brand should be updated

        Examples:
          | brand     | priceBookItemId | originalBrand  |
          | new brand | 1               | original brand |
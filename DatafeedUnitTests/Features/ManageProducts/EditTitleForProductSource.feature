@EditTitleForProductSource
Feature: Ability to edit a product sources' title on the Manage Products page

    Scenario: A user has tried to access the Edit Title For Product Source endpoint directly for a product ID that doesn't exist
        Given a user is trying to access the Edit Title For Product Source endpoint directly for product 1
        When their request is received
        Then the error message 'Invalid product ID.' should be returned

    Scenario Outline: User has entered a blank title and clicked the save button
        Given the user has edited the title to '<newTitle>' for product <productId> whose original title is '<originalTitle>'
        When they click the save button
        Then the products' title should NOT be updated
        And the error message '<errorMessage>' should be returned

        Examples:
          | newTitle | productId | originalTitle  | errorMessage                  |
          |          | 1         | original title | New product title is invalid. |

    Scenario Outline: User has entered a new title and clicked the save button
        Given the user has edited the title to '<newTitle>' for product <productId> whose original title is '<originalTitle>'
        When they click the save button
        Then the products' title should be updated

        Examples:
          | productId | newTitle  | originalTitle  |
          | 1         | new title | original title |
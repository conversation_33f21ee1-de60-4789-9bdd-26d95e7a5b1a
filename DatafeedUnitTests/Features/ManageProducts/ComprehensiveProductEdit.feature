@ComprehensiveProductEdit
Feature: Comprehensive Product Editing Modal
As a user managing products
I want to edit all product properties in a single comprehensive modal
So that I can efficiently manage product information without multiple inline edits

    Background:
        Given the manage products page is loaded
        And the products table contains test data

# Modal Opening Scenarios

    Scenario: User clicks Edit button for a ProductSource
        Given a ProductSource with ID 1 exists in the table
        When the user clicks the "Edit" button for ProductSource 1
        Then the comprehensive edit modal should open
        And the modal title should be "Edit Product"
        And all ProductSource properties should be displayed in the modal
        And the modal should show the correct product type as "ProductSource"

    Scenario: User clicks Edit button for a PriceBookItem
        Given a PriceBookItem with ID 1 exists in the table
        When the user clicks the "Edit" button for PriceBookItem 1
        Then the comprehensive edit modal should open
        And the modal title should be "Edit Product"
        And all PriceBookItem properties should be displayed in the modal
        And the modal should show the correct product type as "PriceBookItem"

    Scenario: User tries to edit a non-existent product
        Given no product with ID 999 exists
        When the user attempts to open the edit modal for product 999
        Then an error message "Product not found" should be displayed
        And the modal should not open

# Basic Property Editing Scenarios

    Scenario Outline: User edits basic ProductSource properties
        Given a ProductSource with ID 1 exists with title "<originalTitle>"
        And the comprehensive edit modal is open for ProductSource 1
        When the user changes the title to "<newTitle>"
        And the user changes the SKU to "<newSku>"
        And the user changes the brand to "<newBrand>"
        And the user clicks the "Save" button
        Then the product should be updated with the new values
        And the modal should close
        And the products table should refresh
        And a success message should be displayed

        Examples:
          | originalTitle | newTitle      | newSku    | newBrand |
          | Old Title     | New Title     | NEW-SKU-1 | NewBrand |
          | Test Product  | Updated Title | UPD-SKU-2 | TestCorp |

    Scenario Outline: User edits basic PriceBookItem properties
        Given a PriceBookItem with ID 1 exists with name "<originalName>"
        And the comprehensive edit modal is open for PriceBookItem 1
        When the user changes the name to "<newName>"
        And the user changes the SKU to "<newSku>"
        And the user changes the brand to "<newBrand>"
        And the user clicks the "Save" button
        Then the product should be updated with the new values
        And the modal should close
        And the products table should refresh
        And a success message should be displayed

        Examples:
          | originalName | newName      | newSku    | newBrand |
          | Old Product  | New Product  | NEW-SKU-3 | NewBrand |
          | Test Item    | Updated Item | UPD-SKU-4 | TestCorp |

          # Description Editing Scenarios

    Scenario: User edits ProductSource descriptions
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user changes the short description to "New short description"
        And the user changes the long description to "New detailed long description with more information"
        And the user clicks the "Save" button
        Then the ProductSource should be updated with the new descriptions
        And the modal should close
        And a success message should be displayed

    Scenario: User edits PriceBookItem description
        Given a PriceBookItem with ID 1 exists
        And the comprehensive edit modal is open for PriceBookItem 1
        When the user changes the description to "New product description"
        And the user changes the short description to "New short description"
        And the user clicks the "Save" button
        Then the PriceBookItem should be updated with the new descriptions
        And the modal should close
        And a success message should be displayed

# Price Override Scenarios

    Scenario: User sets sell price override for ProductSource
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user sets a sell price override of 150.00
        And the user sets the override expiry date to "2025-12-31"
        And the user sets the override note to "Holiday pricing"
        And the user clicks the "Save" button
        Then a sell price override should be created for the ProductSource
        And the override should have the correct price, expiry date, and note
        And the modal should close
        And a success message should be displayed

    Scenario: User sets cost price override for ProductSource
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user sets a cost price override of 75.00
        And the user sets the cost override expiry date to "2025-12-31"
        And the user sets the cost override note to "Supplier discount"
        And the user clicks the "Save" button
        Then a cost price override should be created for the ProductSource
        And the override should have the correct price, expiry date, and note
        And the modal should close
        And a success message should be displayed

    Scenario: User sets sell price override for PriceBookItem
        Given a PriceBookItem with ID 1 exists
        And the comprehensive edit modal is open for PriceBookItem 1
        When the user sets a sell price override of 200.00
        And the user clicks the "Save" button
        Then a sell price override should be created for the PriceBookItem
        And the override should have the correct price
        And the modal should close
        And a success message should be displayed

    Scenario: User sets cost price override for PriceBookItem
        Given a PriceBookItem with ID 1 exists
        And the comprehensive edit modal is open for PriceBookItem 1
        When the user sets a cost price override of 100.00
        And the user sets the cost override expiry date to "2025-09-15"
        And the user sets the cost override note to "Bulk purchase discount"
        And the user clicks the "Save" button
        Then a cost price override should be created for the PriceBookItem
        And the override should have the correct price, expiry date, and note
        And the modal should close
        And a success message should be displayed

# Status Override Scenarios

    Scenario: User sets status override for ProductSource
        Given a ProductSource with ID 1 exists with status "Active"
        And the comprehensive edit modal is open for ProductSource 1
        When the user sets a status override to "Disabled"
        And the user clicks the "Save" button
        Then a status override should be created for the ProductSource
        And the override should set the status to "Disabled"
        And the modal should close
        And a success message should be displayed

# Validation Scenarios

    Scenario Outline: User enters invalid data for required fields
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user clears the "<field>" field
        And the user clicks the "Save" button
        Then a validation error should be displayed for the "<field>" field
        And the error message should be "<errorMessage>"
        And the modal should remain open
        And the product should not be saved

        Examples:
          | field | errorMessage              |
          | title | Product title is required |
          | sku   | Product SKU is required   |
          | brand | Product brand is required |

    Scenario: User tries to save from Product Information tab when sell price override is required
        Given a ProductSource with ID 1 exists with no sell price and no existing override
        And the comprehensive edit modal is open for ProductSource 1
        And the user is on the "Product Information" tab
        When the user clicks the "Save" button
        Then the modal should automatically switch to the "Pricing & Overrides" tab
        And the sell price override field should be focused
        And a validation error should be displayed for the sell price override field
        And the error message should be "Sell price override is required as the product has no existing sell price"
        And the modal should remain open
        And the product should not be saved

    Scenario: User enters invalid price override values
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user sets a sell price override of -10.00
        And the user clicks the "Save" button
        Then a validation error should be displayed for the sell price override
        And the error message should be "Price must be greater than zero"
        And the modal should remain open
        And the product should not be saved

    Scenario: User enters invalid expiry date for price override
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user sets a sell price override of 100.00
        And the user sets the override expiry date to "2020-01-01"
        And the user clicks the "Save" button
        Then a validation error should be displayed for the expiry date
        And the error message should be "Expiry date must be in the future"
        And the modal should remain open
        And the product should not be saved

# Modal Interaction Scenarios

    Scenario: User cancels editing without saving
        Given a ProductSource with ID 1 exists with title "Original Title"
        And the comprehensive edit modal is open for ProductSource 1
        When the user changes the title to "Modified Title"
        And the user clicks the "Cancel" button
        Then the modal should close
        And the product should not be updated
        And the title should remain "Original Title"

    Scenario: User closes modal using X button
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user changes the title to "Modified Title"
        And the user clicks the modal close button (X)
        Then the modal should close
        And the product should not be updated

    Scenario: User closes modal by clicking outside
        Given a ProductSource with ID 1 exists
        And the comprehensive edit modal is open for ProductSource 1
        When the user changes the title to "Modified Title"
        And the user clicks outside the modal
        Then the modal should close
        And the product should not be updated
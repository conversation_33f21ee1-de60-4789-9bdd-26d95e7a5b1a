// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:*******
//      Reqnroll Generator Version:*******
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace DatafeedUnitTests.Features.ManageProducts
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [NUnit.Framework.TestFixtureAttribute()]
    [NUnit.Framework.DescriptionAttribute("Ability to edit a product sources\' long description on the Manage Products page")]
    [NUnit.Framework.FixtureLifeCycleAttribute(NUnit.Framework.LifeCycle.InstancePerTestCase)]
    [NUnit.Framework.CategoryAttribute("EditLongDescriptionForProductSource")]
    public partial class AbilityToEditAProductSourcesLongDescriptionOnTheManageProductsPageFeature
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "EditLongDescriptionForProductSource"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features/ManageProducts", "Ability to edit a product sources\' long description on the Manage Products page", null, global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
#line 1 "EditLongDescriptionForProductSource.feature"
#line hidden
        
        [NUnit.Framework.OneTimeSetUpAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        [NUnit.Framework.OneTimeTearDownAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        [NUnit.Framework.SetUpAttribute()]
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        [NUnit.Framework.TearDownAttribute()]
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<NUnit.Framework.TestContext>(NUnit.Framework.TestContext.CurrentContext);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("A user has tried to access the Edit Long Description For Product Source endpoint " +
            "directly for a product ID that doesn\'t exist")]
        public async global::System.Threading.Tasks.Task AUserHasTriedToAccessTheEditLongDescriptionForProductSourceEndpointDirectlyForAProductIDThatDoesntExist()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("A user has tried to access the Edit Long Description For Product Source endpoint " +
                    "directly for a product ID that doesn\'t exist", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 4
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 5
        await testRunner.GivenAsync("a user is trying to access the Edit Long Description For Product Source endpoint " +
                        "directly for product 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 6
        await testRunner.WhenAsync("their request is received", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 7
        await testRunner.ThenAsync("the error message \'Invalid product ID.\' should be returned", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User has entered a blank long description and clicked the save button")]
        [NUnit.Framework.TestCaseAttribute("", "1", "original long description", "New product long description is invalid.", null)]
        public async global::System.Threading.Tasks.Task UserHasEnteredABlankLongDescriptionAndClickedTheSaveButton(string newLongDescription, string productId, string originalLongDescription, string errorMessage, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("newLongDescription", newLongDescription);
            argumentsOfScenario.Add("productId", productId);
            argumentsOfScenario.Add("originalLongDescription", originalLongDescription);
            argumentsOfScenario.Add("errorMessage", errorMessage);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User has entered a blank long description and clicked the save button", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 9
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 10
        await testRunner.GivenAsync(string.Format("the user has edited the long description to \'{0}\' for product {1} whose original " +
                            "long description is \'{2}\'", newLongDescription, productId, originalLongDescription), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 11
        await testRunner.WhenAsync("they click the save button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 12
        await testRunner.ThenAsync("the products\' long description should NOT be updated", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 13
        await testRunner.AndAsync(string.Format("the error message \'{0}\' should be returned", errorMessage), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User has entered a new long description and clicked the save button")]
        [NUnit.Framework.TestCaseAttribute("1", "new long description", "original long description", null)]
        public async global::System.Threading.Tasks.Task UserHasEnteredANewLongDescriptionAndClickedTheSaveButton(string productId, string newLongDescription, string originalLongDescription, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("productId", productId);
            argumentsOfScenario.Add("newLongDescription", newLongDescription);
            argumentsOfScenario.Add("originalLongDescription", originalLongDescription);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User has entered a new long description and clicked the save button", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 19
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 20
        await testRunner.GivenAsync(string.Format("the user has edited the long description to \'{0}\' for product {1} whose original " +
                            "long description is \'{2}\'", newLongDescription, productId, originalLongDescription), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 21
        await testRunner.WhenAsync("they click the save button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 22
        await testRunner.ThenAsync("the products\' long description should be updated", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
    }
}
#pragma warning restore
#endregion

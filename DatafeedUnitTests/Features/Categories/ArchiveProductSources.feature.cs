// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:*******
//      Reqnroll Generator Version:*******
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace DatafeedUnitTests.Features.Categories
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [NUnit.Framework.TestFixtureAttribute()]
    [NUnit.Framework.DescriptionAttribute("Archive Product Sources in Categories")]
    [NUnit.Framework.FixtureLifeCycleAttribute(NUnit.Framework.LifeCycle.InstancePerTestCase)]
    [NUnit.Framework.CategoryAttribute("ArchiveProductSources")]
    public partial class ArchiveProductSourcesInCategoriesFeature
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "ArchiveProductSources"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features/Categories", "Archive Product Sources in Categories", null, global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
#line 1 "ArchiveProductSources.feature"
#line hidden
        
        [NUnit.Framework.OneTimeSetUpAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        [NUnit.Framework.OneTimeTearDownAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        [NUnit.Framework.SetUpAttribute()]
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        [NUnit.Framework.TearDownAttribute()]
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<NUnit.Framework.TestContext>(NUnit.Framework.TestContext.CurrentContext);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Successfully archive a single product source")]
        public async global::System.Threading.Tasks.Task SuccessfullyArchiveASingleProductSource()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Successfully archive a single product source", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 4
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 5
        await testRunner.GivenAsync("a product source with ID 1 exists in category 1 and is not archived", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 6
        await testRunner.WhenAsync("I archive the product source with ID 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 7
        await testRunner.ThenAsync("the product source should be marked as archived", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 8
        await testRunner.AndAsync("the database should be updated", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Successfully archive multiple product sources")]
        public async global::System.Threading.Tasks.Task SuccessfullyArchiveMultipleProductSources()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Successfully archive multiple product sources", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 10
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 11
        await testRunner.GivenAsync("product sources with IDs \"1,2,3\" exist in category 1 and are not archived", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 12
        await testRunner.WhenAsync("I archive the product sources with IDs \"1,2,3\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 13
        await testRunner.ThenAsync("all product sources should be marked as archived", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 14
        await testRunner.AndAsync("the database should be updated", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Archived products should not appear in category views")]
        public async global::System.Threading.Tasks.Task ArchivedProductsShouldNotAppearInCategoryViews()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Archived products should not appear in category views", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 16
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 17
        await testRunner.GivenAsync("a product source with ID 1 exists in category 1 and is archived", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 18
        await testRunner.WhenAsync("I check the archived status of the product", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 19
        await testRunner.ThenAsync("the product should be marked as archived in the database", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Error when trying to archive non-existent product source")]
        public async global::System.Threading.Tasks.Task ErrorWhenTryingToArchiveNon_ExistentProductSource()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Error when trying to archive non-existent product source", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 21
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 22
        await testRunner.GivenAsync("no product sources exist", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 23
        await testRunner.WhenAsync("I try to archive the product source with ID 999", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 24
        await testRunner.ThenAsync("the error message \'One or more product IDs are invalid.\' should be returned", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Error when trying to archive with empty product IDs")]
        public async global::System.Threading.Tasks.Task ErrorWhenTryingToArchiveWithEmptyProductIDs()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Error when trying to archive with empty product IDs", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 26
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 27
        await testRunner.GivenAsync("product sources exist in the system", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 28
        await testRunner.WhenAsync("I try to archive with empty product IDs", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 29
        await testRunner.ThenAsync("the error message \'No product IDs provided.\' should be returned", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Error when trying to archive with invalid JSON")]
        public async global::System.Threading.Tasks.Task ErrorWhenTryingToArchiveWithInvalidJSON()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Error when trying to archive with invalid JSON", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 31
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 32
        await testRunner.GivenAsync("product sources exist in the system", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 33
        await testRunner.WhenAsync("I try to archive with invalid JSON \"invalid-json\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 34
        await testRunner.ThenAsync("the error message \'Invalid product IDs format.\' should be returned", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
    }
}
#pragma warning restore
#endregion

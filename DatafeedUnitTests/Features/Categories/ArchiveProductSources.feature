@ArchiveProductSources
Feature: Archive Product Sources in Categories

    Scenario: Successfully archive a single product source
        Given a product source with ID 1 exists in category 1 and is not archived
        When I archive the product source with ID 1
        Then the product source should be marked as archived
        And the database should be updated

    Scenario: Successfully archive multiple product sources
        Given product sources with IDs "1,2,3" exist in category 1 and are not archived
        When I archive the product sources with IDs "1,2,3"
        Then all product sources should be marked as archived
        And the database should be updated

    Scenario: Archived products should not appear in category views
        Given a product source with ID 1 exists in category 1 and is archived
        When I check the archived status of the product
        Then the product should be marked as archived in the database

    Scenario: Error when trying to archive non-existent product source
        Given no product sources exist
        When I try to archive the product source with ID 999
        Then the error message 'One or more product IDs are invalid.' should be returned

    Sc<PERSON>rio: Error when trying to archive with empty product IDs
        Given product sources exist in the system
        When I try to archive with empty product IDs
        Then the error message 'No product IDs provided.' should be returned

    Scenario: Error when trying to archive with invalid JSON
        Given product sources exist in the system
        When I try to archive with invalid JSON "invalid-json"
        Then the error message 'Invalid product IDs format.' should be returned
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;
using Datafeed_v2.Controllers;
using Datafeed_v2.Models.DbModels.Datafeed;
using DatafeedUnitTests.Helpers;
using Moq;
using Newtonsoft.Json;
using NUnit.Framework;
using Reqnroll;

namespace DatafeedUnitTests.Features.Categories;

[Binding, Scope(Tag = "ArchiveProductSources")]
public class ArchiveProductSources
{
    private List<ProductSource> _productSources;
    private List<NopCategories> _nopCategories;
    private Mock<EdunetDatafeedsEntities> _mockDbContext;
    private Mock<HttpResponseBase> _mockResponse;
    private Mock<HttpContextBase> _mockHttpContext;
    private CategoriesController _controller;
    private ActionResult _result;
    private int _selectedCategoryId;
    private List<int> _productIdsToArchive;
    private string _productIdsJson;

    [BeforeScenario]
    public void Setup()
    {
        _productSources = [];
        _nopCategories = [];
        _selectedCategoryId = 1;
        _productIdsToArchive = [];
        _productIdsJson = string.Empty;

        _mockDbContext = new Mock<EdunetDatafeedsEntities>();
        _mockResponse = new Mock<HttpResponseBase>();
        _mockResponse.SetupProperty(res => res.StatusCode);
        _mockResponse.SetupProperty(res => res.TrySkipIisCustomErrors);
        _mockHttpContext = new Mock<HttpContextBase>();
        _mockHttpContext.Setup(ctx => ctx.Response).Returns(_mockResponse.Object);
    }

    private void SetupController()
    {
        _mockDbContext.Setup(db => db.ProductSource)
            .Returns(_productSources.GetAsyncQueryableMockDbSet());
        _mockDbContext.Setup(db => db.NopCategories)
            .Returns(_nopCategories.GetAsyncQueryableMockDbSet());
        _mockDbContext.Setup(db => db.SaveChangesAsync())
            .ReturnsAsync(1);

        // Mock additional dependencies needed for GetMappedProductSourcesForNopCategory
        var emptyProductSourcesForNopCategory = new List<ProductSourcesForNopCategory>();
        var emptyProductSourceList = new List<ProductSourceList>();
        var emptyTransformationPackages = new List<CommerceTransformationPackages>();
        var emptyPriceBookMappings = new List<PriceBookToNopCategoryMapping>();
        var emptyPriceBooks = new List<ImportedPriceBooks>();
        var emptyMappedPriceBookItems = new List<MappedPriceBookItemsForNopCategory>();

        _mockDbContext.Setup(db => db.ProductSourcesForNopCategory)
            .Returns(emptyProductSourcesForNopCategory.GetAsyncQueryableMockDbSet());
        _mockDbContext.Setup(db => db.ProductSourceList)
            .Returns(emptyProductSourceList.GetAsyncQueryableMockDbSet());
        _mockDbContext.Setup(db => db.CommerceTransformationPackages)
            .Returns(emptyTransformationPackages.GetAsyncQueryableMockDbSet());
        _mockDbContext.Setup(db => db.PriceBookToNopCategoryMapping)
            .Returns(emptyPriceBookMappings.GetAsyncQueryableMockDbSet());
        _mockDbContext.Setup(db => db.ImportedPriceBooks)
            .Returns(emptyPriceBooks.GetAsyncQueryableMockDbSet());
        _mockDbContext.Setup(db => db.MappedPriceBookItemsForNopCategory)
            .Returns(emptyMappedPriceBookItems.GetAsyncQueryableMockDbSet());

        _controller = new CategoriesController(_mockDbContext.Object);
        _controller.ControllerContext = new ControllerContext(_mockHttpContext.Object, new RouteData(), _controller);
    }

    [Given("a product source with ID {int} exists in category {int} and is not archived")]
    public void GivenAProductSourceWithIdExistsInCategoryAndIsNotArchived(int productId, int categoryId)
    {
        _selectedCategoryId = categoryId;
        _nopCategories.Add(new NopCategories { ID = categoryId, Name = $"Test Category {categoryId}" });
        _productSources.Add(new ProductSource
        {
            ID = productId,
            ProductSku = $"SKU-{productId}",
            ProductTitle = $"Test Product {productId}",
            Archived = false
        });
        SetupController();
    }

    [Given("product sources with IDs {string} exist in category {int} and are not archived")]
    public void GivenProductSourcesWithIdsExistInCategoryAndAreNotArchived(string productIds, int categoryId)
    {
        _selectedCategoryId = categoryId;
        _nopCategories.Add(new NopCategories { ID = categoryId, Name = $"Test Category {categoryId}" });

        var ids = productIds.Split(',').Select(int.Parse).ToList();
        foreach (var id in ids)
        {
            _productSources.Add(new ProductSource
            {
                ID = id,
                ProductSku = $"SKU-{id}",
                ProductTitle = $"Test Product {id}",
                Archived = false
            });
        }

        SetupController();
    }

    [Given("a product source with ID {int} exists in category {int} and is archived")]
    public void GivenAProductSourceWithIdExistsInCategoryAndIsArchived(int productId, int categoryId)
    {
        _selectedCategoryId = categoryId;
        _nopCategories.Add(new NopCategories { ID = categoryId, Name = $"Test Category {categoryId}" });
        _productSources.Add(new ProductSource
        {
            ID = productId,
            ProductSku = $"SKU-{productId}",
            ProductTitle = $"Test Product {productId}",
            Archived = true
        });
        SetupController();
    }

    [Given("no product sources exist")]
    public void GivenNoProductSourcesExist()
    {
        _nopCategories.Add(new NopCategories { ID = 1, Name = "Test Category 1" });
        SetupController();
    }

    [Given("product sources exist in the system")]
    public void GivenProductSourcesExistInTheSystem()
    {
        _nopCategories.Add(new NopCategories { ID = 1, Name = "Test Category 1" });
        _productSources.Add(new ProductSource
        {
            ID = 1,
            ProductSku = "SKU-1",
            ProductTitle = "Test Product 1",
            Archived = false
        });
        SetupController();
    }

    [When("I archive the product source with ID {int}")]
    public async Task WhenIArchiveTheProductSourceWithId(int productId)
    {
        _productIdsToArchive = [productId];
        _productIdsJson = JsonConvert.SerializeObject(_productIdsToArchive);
        _result = await _controller.ArchiveSelectedProductSources(_productIdsJson, _mockResponse.Object);
    }

    [When("I archive the product sources with IDs {string}")]
    public async Task WhenIArchiveTheProductSourcesWithIds(string productIds)
    {
        _productIdsToArchive = productIds.Split(',').Select(int.Parse).ToList();
        _productIdsJson = JsonConvert.SerializeObject(_productIdsToArchive);
        _result = await _controller.ArchiveSelectedProductSources(_productIdsJson, _mockResponse.Object);
    }

    [When("I try to archive the product source with ID {int}")]
    public async Task WhenITryToArchiveTheProductSourceWithId(int productId)
    {
        _productIdsToArchive = [productId];
        _productIdsJson = JsonConvert.SerializeObject(_productIdsToArchive);
        _result = await _controller.ArchiveSelectedProductSources(_productIdsJson, _mockResponse.Object);
    }

    [When("I try to archive with empty product IDs")]
    public async Task WhenITryToArchiveWithEmptyProductIds()
    {
        _productIdsJson = "[]";
        _result = await _controller.ArchiveSelectedProductSources(_productIdsJson, _mockResponse.Object);
    }

    [When("I try to archive with invalid JSON {string}")]
    public async Task WhenITryToArchiveWithInvalidJson(string invalidJson)
    {
        _productIdsJson = invalidJson;
        _result = await _controller.ArchiveSelectedProductSources(_productIdsJson, _mockResponse.Object);
    }

    [When("I check the archived status of the product")]
    public void WhenICheckTheArchivedStatusOfTheProduct()
    {
        // This step is just a placeholder to transition to the validation step
        // We don't need to perform any action here since we're just checking the state
    }

    [Then("the product source should be marked as archived")]
    public void ThenTheProductSourceShouldBeMarkedAsArchived()
    {
        var jsonResult = _result as JsonResult;
        Assert.That(jsonResult, Is.Not.Null, "The result should be a JsonResult.");

        // Use reflection to safely access properties of the anonymous object
        var successProperty = jsonResult.Data.GetType().GetProperty("success");
        Assert.That(successProperty, Is.Not.Null, "JSON result should contain a 'success' property");
        var success = (bool)successProperty.GetValue(jsonResult.Data);
        Assert.That(success, Is.True, "The operation should be successful.");

        var productSource = _productSources.First(p => _productIdsToArchive.Contains(p.ID));
        Assert.That(productSource.Archived, Is.True, "The product source should be marked as archived.");
    }

    [Then("all product sources should be marked as archived")]
    public void ThenAllProductSourcesShouldBeMarkedAsArchived()
    {
        var jsonResult = _result as JsonResult;
        Assert.That(jsonResult, Is.Not.Null, "The result should be a JsonResult.");

        // Use reflection to safely access properties of the anonymous object
        var successProperty = jsonResult.Data.GetType().GetProperty("success");
        Assert.That(successProperty, Is.Not.Null, "JSON result should contain a 'success' property");
        var success = (bool)successProperty.GetValue(jsonResult.Data);
        Assert.That(success, Is.True, "The operation should be successful.");

        foreach (var productId in _productIdsToArchive)
        {
            var productSource = _productSources.First(p => p.ID == productId);
            Assert.That(productSource.Archived, Is.True, $"Product source {productId} should be marked as archived.");
        }
    }

    [Then("the database should be updated")]
    public void ThenTheDatabaseShouldBeUpdated()
    {
        _mockDbContext.Verify(db => db.SaveChangesAsync(), Times.Once,
            "SaveChangesAsync should have been called exactly once on the DbContext.");
    }

    [Then("the product should be marked as archived in the database")]
    public void ThenTheProductShouldBeMarkedAsArchivedInTheDatabase()
    {
        // Find the archived product in our test data
        var archivedProduct = _productSources.FirstOrDefault(p => p.ID == 1);
        Assert.That(archivedProduct, Is.Not.Null, "The product source should exist in the test data.");
        Assert.That(archivedProduct.Archived, Is.True, "The product source should be marked as archived.");
    }

    [Then("the error message {string} should be returned")]
    public void ThenTheErrorMessageShouldBeReturned(string expectedErrorMessage)
    {
        var jsonResult = _result as JsonResult;
        Assert.That(jsonResult, Is.Not.Null, "The result should be a JsonResult.");

        // Use reflection to safely access properties of the anonymous object
        var successProperty = jsonResult.Data.GetType().GetProperty("success");
        Assert.That(successProperty, Is.Not.Null, "JSON result should contain a 'success' property");
        var success = (bool)successProperty.GetValue(jsonResult.Data);
        Assert.That(success, Is.False, "The operation should not be successful.");

        var messageProperty = jsonResult.Data.GetType().GetProperty("message");
        Assert.That(messageProperty, Is.Not.Null, "JSON result should contain a 'message' property");
        var message = (string)messageProperty.GetValue(jsonResult.Data);
        Assert.That(message, Is.EqualTo(expectedErrorMessage),
            "The error message should match the expected message.");

        Assert.That(_mockResponse.Object.StatusCode, Is.EqualTo((int)HttpStatusCode.BadRequest),
            "The response status code should be 400 (Bad Request).");
    }
}
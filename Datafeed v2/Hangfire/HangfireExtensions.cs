using System;
using System.Collections.Generic;
using Hangfire.Common;
using Hangfire.Server;

namespace Datafeed_v2.Hangfire;

public static class HangfireExtensions
{
    public static void SendHeartbeat(this PerformContext context)
    {
        context.Connection.SetRangeInHash($"job:{context.BackgroundJob.Id}", [
            new KeyValuePair<string, string>("Fetched", JobHelper.SerializeDateTime(DateTime.UtcNow))
        ]);
    }
}
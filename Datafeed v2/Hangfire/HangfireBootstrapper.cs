using System;
using System.Web.Hosting;
using Hangfire;
using Hangfire.SqlServer;

namespace Datafeed_v2.Hangfire;

public class HangfireBootstrapper : IRegisteredObject
{
    public static readonly HangfireBootstrapper Instance = new();

    private readonly object _lock = new();
    private bool _started;

    private BackgroundJobServer _backgroundJobServer;

    public void Start()
    {
        lock (_lock)
        {
            if (_started)
            {
                return;
            }

            _started = true;
            HostingEnvironment.RegisterObject(this);
            var options = new SqlServerStorageOptions
            {
                QueuePollInterval = TimeSpan.FromSeconds(15),
                InvisibilityTimeout = TimeSpan.FromHours(3)
            };
            GlobalConfiguration.Configuration.UseSqlServerStorage("DefaultConnection", options);
            _backgroundJobServer = new BackgroundJobServer();
        }
    }

    public void Stop(bool immediate)
    {
        lock (_lock)
        {
            _backgroundJobServer?.Dispose();

            HostingEnvironment.UnregisterObject(this);
        }
    }
}
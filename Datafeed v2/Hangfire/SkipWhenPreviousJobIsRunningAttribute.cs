using System;
using System.Collections.Generic;
using Hangfire.Client;
using Hangfire.Common;
using Hangfire.States;
using Hangfire.Storage;

namespace Datafeed_v2.Hangfire;

/// <summary>
/// A Hangfire job filter attribute that prevents recurring jobs from running if the previous instance is still executing.
/// This filter manages a "Running" state in the job storage to coordinate job execution across multiple workers.
/// </summary>
/// <remarks>
/// This filter implements both IClientFilter and IApplyStateFilter to:
/// 1. Check if a previous job instance is running before creating a new job (OnCreating)
/// 2. Update the running state when jobs start and finish (OnStateApplied)
///
/// Thread Safety: This filter uses distributed locks when available in the storage provider
/// to prevent race conditions during state changes.
/// </remarks>
public class SkipWhenPreviousJobIsRunningAttribute : JobFilterAttribute, IClientFilter, IApplyStateFilter
{
    #region Constants

    private const string RunningStateYes = "yes";
    private const string RunningStateNo = "no";
    private const string RunningHashKey = "Running";
    private const string JobHashKey = "Job";
    private const string RecurringJobParameter = "RecurringJobId";
    private const int DistributedLockTimeoutSeconds = 5;

    #endregion

    #region IClientFilter Implementation

    /// <summary>
    /// Called when a background job is being created. Checks if a previous instance of the recurring job is still running.
    /// </summary>
    /// <param name="context">The context containing job creation information</param>
    public void OnCreating(CreatingContext context)
    {
        try
        {
            // We can't handle old storages that don't support JobStorageConnection
            if (context.Connection is not JobStorageConnection connection)
            {
                return;
            }

            // This filter only applies to background jobs based on recurring ones
            var recurringJobId = GetRecurringJobId(context.Parameters);
            if (string.IsNullOrWhiteSpace(recurringJobId))
            {
                return;
            }

            // Check if the previous instance is still running
            var runningState = connection.GetValueFromHash(GetRecurringJobHashKey(recurringJobId), RunningHashKey);
            if (IsRunningStateActive(runningState))
            {
                context.Canceled = true;
                // Note: In a production environment, you might want to add logging here
                // However, following the existing codebase pattern, we keep this lightweight
            }
        }
        catch (Exception)
        {
            // Gracefully handle any storage exceptions to prevent job creation failures
            // In case of errors, we allow the job to proceed rather than blocking it
        }
    }

    /// <summary>
    /// Called after a background job has been created. No action required for this filter.
    /// </summary>
    /// <param name="context">The context containing job creation information</param>
    public void OnCreated(CreatedContext context)
    {
        // No action required
    }

    #endregion

    #region IApplyStateFilter Implementation

    /// <summary>
    /// Called when a job state is being applied. Updates the running state based on the new job state.
    /// </summary>
    /// <param name="context">The context containing state change information</param>
    /// <param name="transaction">The storage transaction</param>
    public void OnStateApplied(ApplyStateContext context, IWriteOnlyTransaction transaction)
    {
        try
        {
            if (context.NewState is EnqueuedState)
            {
                // Job is starting - mark as running
                ChangeRunningState(context, RunningStateYes);
            }
            else if (ShouldResetRunningState(context))
            {
                // Job is finishing - mark as not running
                ChangeRunningState(context, RunningStateNo);
            }
        }
        catch (Exception)
        {
            // Gracefully handle any storage exceptions to prevent state change failures
            // The job will continue to execute even if we can't update the running state
        }
    }

    /// <summary>
    /// Called when a job state is being unapplied. No action required for this filter.
    /// </summary>
    /// <param name="context">The context containing state change information</param>
    /// <param name="transaction">The storage transaction</param>
    public void OnStateUnapplied(ApplyStateContext context, IWriteOnlyTransaction transaction)
    {
        // No action required
    }

    #endregion

    #region Private Helper Methods

    /// <summary>
    /// Extracts the recurring job ID from the job parameters dictionary.
    /// </summary>
    /// <param name="parameters">The job parameters dictionary</param>
    /// <returns>The recurring job ID if found, otherwise null</returns>
    private static string GetRecurringJobId(IDictionary<string, object> parameters)
    {
        if (!parameters.TryGetValue(RecurringJobParameter, out var parameter))
        {
            return null;
        }

        return parameter as string;
    }

    /// <summary>
    /// Extracts the recurring job ID from the apply state context.
    /// </summary>
    /// <param name="context">The apply state context</param>
    /// <returns>The recurring job ID if found, otherwise null</returns>
    private static string GetRecurringJobId(ApplyStateContext context)
    {
        return context.GetJobParameter<string>(RecurringJobParameter, allowStale: true);
    }

    /// <summary>
    /// Generates the hash key for a recurring job in storage.
    /// </summary>
    /// <param name="recurringJobId">The recurring job ID</param>
    /// <returns>The formatted hash key</returns>
    private static string GetRecurringJobHashKey(string recurringJobId)
    {
        return $"recurring-job:{recurringJobId}";
    }

    /// <summary>
    /// Determines if the running state indicates an active job.
    /// </summary>
    /// <param name="runningState">The running state value from storage</param>
    /// <returns>True if the job is considered running, false otherwise</returns>
    private static bool IsRunningStateActive(string runningState)
    {
        return string.Equals(runningState?.Trim(), RunningStateYes, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Determines if the running state should be reset to "no" based on the job state transition.
    /// </summary>
    /// <param name="context">The apply state context</param>
    /// <returns>True if the running state should be reset, false otherwise</returns>
    private static bool ShouldResetRunningState(ApplyStateContext context)
    {
        // Reset running state when:
        // 1. Job reaches a final state (except when transitioning from Failed state)
        // 2. Job explicitly fails
        var isTransitioningToFinalState = context.NewState.IsFinal &&
                                          !string.Equals(FailedState.StateName?.Trim(), context.OldStateName?.Trim(),
                                              StringComparison.OrdinalIgnoreCase);

        var isExplicitlyFailing = context.NewState is FailedState;

        return isTransitioningToFinalState || isExplicitlyFailing;
    }

    /// <summary>
    /// Updates the running state for a recurring job in storage.
    /// </summary>
    /// <param name="context">The apply state context containing job and storage information</param>
    /// <param name="state">The new running state value to set</param>
    private static void ChangeRunningState(ApplyStateContext context, string state)
    {
        try
        {
            // We can't handle old storages that don't support JobStorageConnection
            if (context.Connection is not JobStorageConnection connection)
            {
                return;
            }

            // Obtain the recurring job identifier
            var recurringJobId = GetRecurringJobId(context);
            if (string.IsNullOrWhiteSpace(recurringJobId))
            {
                return;
            }

            // Acquire a distributed lock if supported to avoid race conditions
            if (context.Storage.HasFeature(JobStorageFeatures.Transaction.AcquireDistributedLock))
            {
                ((JobStorageTransaction)context.Transaction).AcquireDistributedLock(
                    $"lock:recurring-job:{recurringJobId}",
                    TimeSpan.FromSeconds(DistributedLockTimeoutSeconds));
            }

            // Verify that the recurring job still exists before updating its state
            var recurringJobHashKey = GetRecurringJobHashKey(recurringJobId);
            var recurringJob = connection.GetValueFromHash(recurringJobHashKey, JobHashKey);
            if (string.IsNullOrWhiteSpace(recurringJob))
            {
                return;
            }

            // Update the running state
            context.Transaction.SetRangeInHash(recurringJobHashKey, [
                new KeyValuePair<string, string>(RunningHashKey, state)
            ]);
        }
        catch (Exception)
        {
            // Gracefully handle any storage exceptions to prevent job state change failures
            // The job will continue to execute even if we can't update the running state
        }
    }

    #endregion
}
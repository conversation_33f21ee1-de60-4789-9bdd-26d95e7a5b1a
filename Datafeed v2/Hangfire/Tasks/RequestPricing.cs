using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Functions;
using Datafeed_v2.Helpers;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Singletons;
using FandoogleReflectorService;
using Hangfire;
using Hangfire.Server;

namespace Datafeed_v2.Hangfire.Tasks;

/// <summary>
/// Manages product fandoogle pricing updates and competitor price comparisons.
/// This service fetches prices from fandoogle reflector, applies randomised pricing adjustments,
/// and maintains a cache to minimise API calls while staying competitive.
/// </summary>
public class RequestPricing
{
    // API and error handling constants
    private const string RateLimitError = "Rate limit exceeded";
    private const int CacheExpiryDays = 30;

    // Email notification configuration
    private const string EmailActionName = "DatafeedRequestPricingExpiredCacheUpdated";
    private const string SalesEdunetComAu = "<EMAIL>";

    // Randomised pricing adjustments
    private static readonly decimal[] PriceDecrements = [0.25m, 0.50m, 0.75m, 1.00m];

    /// <summary>
    /// Generates a random expiry date between 14 and 28 days in the future.
    /// </summary>
    /// <param name="random">Random number generator instance</param>
    /// <returns>DateTime between 14-28 days from now</returns>
    private static DateTime GetRandomExpiryDate(Random random)
    {
        var randomDays = random.Next(14, 29); // 14 to 28 days inclusive
        return DateTime.UtcNow.AddDays(randomDays);
    }

    /// <summary>
    /// Generates a random cutoff time between 5:00 PM and 6:00 PM for the current day.
    /// </summary>
    /// <param name="random">Random number generator instance</param>
    /// <returns>DateTime representing the random cutoff time between 5-6 PM today</returns>
    private static DateTime GenerateRandomCutoffTime(Random random)
    {
        var now = DateTime.Now;
        var randomMinutes = random.Next(0, 60); // 0 to 59 minutes
        return new DateTime(now.Year, now.Month, now.Day, 17, randomMinutes, 0); // 5 PM + random minutes
    }

    /// <summary>
    /// Checks if the current time has exceeded the execution cutoff time.
    /// </summary>
    /// <param name="cutoffTime">The randomly generated cutoff time for this execution</param>
    /// <returns>True if execution should stop, false if it can continue</returns>
    private static bool ShouldStopExecution(DateTime cutoffTime)
    {
        return DateTime.Now >= cutoffTime;
    }

    /// <summary>
    /// Main entry point for processing fandoogle pricing updates. Handles three key operations:
    /// 1. Updates expired fandoogle cache entries
    /// 2. Processes unpriced products from ProductSource
    /// 3. Processes unpriced items from price books
    /// Uses random delays and retry logic to avoid detection and handle rate limits.
    /// </summary>
    /// <param name="context">Hangfire execution context</param>
    /// <param name="dataFeedsContext">Database context (injectable for testing)</param>
    /// <param name="diEmailEngine">Email engine (injectable for testing)</param>
    [DisableConcurrentExecution(60 * 60 * 2)] // Prevent overlapping executions for 2 hours
    public static void Run(PerformContext context, EdunetDatafeedsEntities dataFeedsContext = null,
        EmailEngine diEmailEngine = null)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var emailEngine = diEmailEngine ?? new EmailEngine(EmailActionName);
        var service = FandoogleReflectorSingleton.Instance;
        var random = new Random();

        // Generate random cutoff time between 5-6 PM for this run
        var cutoffTime = GenerateRandomCutoffTime(random);
        LogMessageAsync(dbDataFeedsContext, $"Execution will stop at randomly selected cutoff time: {cutoffTime:HH:mm}", context).Wait();

        // Get counts for logging
        var expiredCount = dbDataFeedsContext.FandoogleReflectorCache
            .Count(q => q.ExpiresAt < DateTime.UtcNow);

        var pricedSkus = GetCurrentlyPricedSkus(dbDataFeedsContext);

        var unpricedProductSourcesCount = dbDataFeedsContext.ProductSource
            .Count(q => !pricedSkus.Contains(q.ProductSku.Trim()));

        var unpricedPriceBookItemsCount = dbDataFeedsContext.ImportedItemsFromPriceBook
            .Count(q => !pricedSkus.Contains(q.ProductSKU.Trim()));

        // Log execution start with summary counts
        LogMessageAsync(dbDataFeedsContext,
            $"Starting execution - Expired: {expiredCount}, ProductSources: {unpricedProductSourcesCount}, PriceBook: {unpricedPriceBookItemsCount}",
            context).Wait();

        // Step 1: Update expired pricing cache entries
        if (ShouldStopExecution(cutoffTime))
        {
            LogMessageAsync(dbDataFeedsContext, "Execution stopped due to time limit before processing expired entries", context).Wait();
            return;
        }
        UpdateExpiredCacheEntries(dbDataFeedsContext, emailEngine, service, random, cutoffTime, context);
        LogMessageAsync(dbDataFeedsContext, $"Completed expired entries processing - {expiredCount} processed", context)
            .Wait();

        // Step 2: Get list of all currently priced SKUs to avoid duplicate processing (refresh after step 1)
        if (ShouldStopExecution(cutoffTime))
        {
            LogMessageAsync(dbDataFeedsContext, "Execution stopped due to time limit before processing ProductSource items", context).Wait();
            return;
        }
        pricedSkus = GetCurrentlyPricedSkus(dbDataFeedsContext);

        // Step 3: Process unpriced products from ProductSource
        ProcessUnpricedProductSources(dbDataFeedsContext, service, random, pricedSkus, cutoffTime, context);
        LogMessageAsync(dbDataFeedsContext,
            $"Completed ProductSource processing - {unpricedProductSourcesCount} processed", context).Wait();

        // Step 4: Process unpriced items from price books
        if (ShouldStopExecution(cutoffTime))
        {
            LogMessageAsync(dbDataFeedsContext, "Execution stopped due to time limit before processing PriceBook items", context).Wait();
            return;
        }
        ProcessUnpricedPriceBookItems(dbDataFeedsContext, service, random, pricedSkus, cutoffTime, context);
        LogMessageAsync(dbDataFeedsContext, $"Completed PriceBook processing - {unpricedPriceBookItemsCount} processed",
            context).Wait();

        // Log execution completion
        var totalProcessed = expiredCount + unpricedProductSourcesCount + unpricedPriceBookItemsCount;
        LogMessageAsync(dbDataFeedsContext, $"Execution completed - Total processed: {totalProcessed}", context).Wait();
    }

    /// <summary>
    /// Updates all expired pricing cache entries with fresh competitor prices.
    /// Sends email notifications to sales team when prices are updated.
    /// </summary>
    /// <param name="dbDataFeedsContext">Database context</param>
    /// <param name="emailEngine">Email notification service</param>
    /// <param name="service">FandoogleReflector pricing service</param>
    /// <param name="random">Random number generator for delays and price adjustments</param>
    /// <param name="context">Hangfire context for logging</param>
    private static void UpdateExpiredCacheEntries(
        EdunetDatafeedsEntities dbDataFeedsContext,
        EmailEngine emailEngine,
        Service service,
        Random random,
        DateTime cutoffTime,
        PerformContext context = null)
    {
        // Find all cache entries that have expired and need fresh pricing data
        var expiredEntries = dbDataFeedsContext.FandoogleReflectorCache
            .Where(q => q.ExpiresAt < DateTime.UtcNow)
            .ToList();

        var totalCount = expiredEntries.Count;
        var currentIndex = 0;

        foreach (var entry in expiredEntries)
        {
            if (ShouldStopExecution(cutoffTime))
            {
                LogMessageAsync(dbDataFeedsContext, $"Execution stopped due to time limit while processing expired entries - {currentIndex} of {totalCount} completed", context).Wait();
                break;
            }

            currentIndex++;
            LogMessageAsync(dbDataFeedsContext,
                $"Processing expired entry {currentIndex} of {totalCount}: {entry.ProductSku.Trim()}", context).Wait();

            // Request fresh pricing data from fandoogle
            var (price, error) = RequestProductPrice(service, entry.ProductSku.Trim());
            if (error != null)
            {
                LogMessageAsync(dbDataFeedsContext,
                    $"Error processing expired entry {entry.ProductSku.Trim()}: {error}", context).Wait();
                continue;
            }

            // Skip if no valid price received
            if (price is null or <= 0)
            {
                LogMessageAsync(dbDataFeedsContext,
                    $"No valid price received for expired entry: {entry.ProductSku.Trim()}", context).Wait();
                continue;
            }

            // Apply randomised pricing
            var oldPrice = entry.OriginalPrice;
            entry.RandomisedPrice = price.Value - PriceDecrements[random.Next(PriceDecrements.Length)];
            entry.OriginalPrice = price.Value;
            entry.ExpiresAt = DateTime.UtcNow.AddDays(CacheExpiryDays);

            // Notify sales team that fandoogle pricing has been automatically updated
            NotifySalesOfPriceUpdate(emailEngine, entry.ProductSku.Trim(), oldPrice, entry.RandomisedPrice);

            LogMessageAsync(dbDataFeedsContext,
                    $"Updated expired entry {entry.ProductSku.Trim()}: {oldPrice:C} -> {entry.RandomisedPrice:C}",
                    context)
                .Wait();

            // Random delay between requests to avoid detection
            Thread.Sleep(random.Next(1000, 5001));
        }

        dbDataFeedsContext.SaveChanges();
    }

    /// <summary>
    /// Gets list of all product SKUs that currently have fandoogle pricing data in the cache.
    /// This prevents duplicate processing of already-priced products.
    /// </summary>
    /// <param name="dbDataFeedsContext">Database context</param>
    /// <returns>List of SKUs that already have pricing data</returns>
    private static List<string> GetCurrentlyPricedSkus(EdunetDatafeedsEntities dbDataFeedsContext)
    {
        return dbDataFeedsContext.FandoogleReflectorCache
            .Where(q => q.ExpiresAt > DateTime.UtcNow)
            .Select(q => q.ProductSku.Trim())
            .ToList();
    }

    /// <summary>
    /// Processes all unpriced products from the ProductSource table.
    /// </summary>
    /// <param name="dbDataFeedsContext">Database context</param>
    /// <param name="service">FandoogleReflector pricing service</param>
    /// <param name="random">Random number generator for delays and price adjustments</param>
    /// <param name="pricedSkus">List of SKUs that already have pricing data</param>
    /// <param name="context">Hangfire context for logging</param>
    private static void ProcessUnpricedProductSources(
        EdunetDatafeedsEntities dbDataFeedsContext,
        Service service,
        Random random,
        List<string> pricedSkus,
        DateTime cutoffTime,
        PerformContext context = null)
    {
        // Find all ProductSource items that don't have pricing data yet
        var unpricedProductSources = dbDataFeedsContext.ProductSource
            .Where(q => !pricedSkus.Contains(q.ProductSku.Trim()))
            .ToList();

        var totalCount = unpricedProductSources.Count;
        var currentIndex = 0;

        foreach (var product in unpricedProductSources)
        {
            if (ShouldStopExecution(cutoffTime))
            {
                LogMessageAsync(dbDataFeedsContext, $"Execution stopped due to time limit while processing ProductSource items - {currentIndex} of {totalCount} completed", context).Wait();
                break;
            }

            currentIndex++;
            LogMessageAsync(dbDataFeedsContext,
                    $"Processing ProductSource {currentIndex} of {totalCount}: {product.ProductSku.Trim()}", context)
                .Wait();

            // Random delay between requests to avoid detection
            Thread.Sleep(random.Next(1000, 5001));

            ProcessUnpricedProduct(
                dbDataFeedsContext,
                service,
                random,
                product.ProductSku.Trim(),
                product.CostPrice,
                context
            );

            // Save after each product to avoid losing work if process fails
            dbDataFeedsContext.SaveChanges();
        }
    }

    /// <summary>
    /// Processes all unpriced items from imported price books.
    /// </summary>
    /// <param name="dbDataFeedsContext">Database context</param>
    /// <param name="service">FandoogleReflector pricing service</param>
    /// <param name="random">Random number generator for delays and price adjustments</param>
    /// <param name="pricedSkus">List of SKUs that already have pricing data</param>
    /// <param name="context">Hangfire context for logging</param>
    private static void ProcessUnpricedPriceBookItems(
        EdunetDatafeedsEntities dbDataFeedsContext,
        Service service,
        Random random,
        List<string> pricedSkus,
        DateTime cutoffTime,
        PerformContext context = null)
    {
        // Find all price book items that don't have pricing data yet
        var unpricedPriceBookItems = dbDataFeedsContext.ImportedItemsFromPriceBook
            .Where(q => !pricedSkus.Contains(q.ProductSKU.Trim()))
            .ToList();

        var totalCount = unpricedPriceBookItems.Count;
        var currentIndex = 0;

        foreach (var item in unpricedPriceBookItems)
        {
            if (ShouldStopExecution(cutoffTime))
            {
                LogMessageAsync(dbDataFeedsContext, $"Execution stopped due to time limit while processing PriceBook items - {currentIndex} of {totalCount} completed", context).Wait();
                break;
            }

            currentIndex++;
            LogMessageAsync(dbDataFeedsContext,
                $"Processing PriceBook item {currentIndex} of {totalCount}: {item.ProductSKU.Trim()}", context).Wait();

            // Random delay between requests to avoid detection
            Thread.Sleep(random.Next(1000, 5001));

            ProcessUnpricedProduct(
                dbDataFeedsContext,
                service,
                random,
                item.ProductSKU.Trim(),
                item.CostPrice ?? 0,
                context
            );

            // Save after each product to avoid losing work if process fails
            dbDataFeedsContext.SaveChanges();
        }
    }

    /// <summary>
    /// Sends email notification to sales team when fandoogle pricing is updated.
    /// </summary>
    /// <param name="emailEngine">Email notification service</param>
    /// <param name="productSku">Product SKU that was updated</param>
    /// <param name="oldPrice">Previous price</param>
    /// <param name="newPrice">New randomised fandoogle price</param>
    private static void NotifySalesOfPriceUpdate(EmailEngine emailEngine, string productSku, decimal oldPrice,
        decimal newPrice)
    {
        emailEngine.SetRecipient(SalesEdunetComAu);
        emailEngine.AddMergeField("ProductSku", productSku);
        emailEngine.AddMergeField("OldPrice", oldPrice.ToString(CultureInfo.CurrentCulture));
        emailEngine.AddMergeField("NewPrice", newPrice.ToString(CultureInfo.CurrentCulture));
        // emailEngine.SendEmail();
    }

    /// <summary>
    /// Processes a single product's pricing with retry logic and competitor price comparison.
    /// </summary>
    private static void ProcessUnpricedProduct(
        EdunetDatafeedsEntities context,
        Service service,
        Random random,
        string sku,
        decimal costPrice,
        PerformContext performContext = null)
    {
        var (price, error) = RequestProductPrice(service, sku);
        if (error != null)
        {
            // If rate limit exceeded, log and continue to next product
            if (error.Trim().ToLower().Contains("Max retry attempts exceeded".Trim().ToLower()))
            {
                LogMessageAsync(context, $"Rate limit exceeded for SKU: {sku}, skipping", performContext).Wait();
                return;
            }

            // Log other errors and continue
            LogMessageAsync(context, $"Error processing SKU {sku}: {error}", performContext).Wait();
            return; // swallow the exception and continue
        }

        if (!price.HasValue)
        {
            LogMessageAsync(context, $"No price received for SKU: {sku}", performContext).Wait();
            return;
        }

        var competitorPrice = price.Value;
        var randomisedPrice = competitorPrice - PriceDecrements[random.Next(PriceDecrements.Length)];

        // Check if price is below cost and flag if necessary
        if (randomisedPrice < costPrice)
        {
            var exists = context.ProductsCheaperAtCompetitor
                .Any(q => q.ProductSku.Trim().ToLower() == sku.ToLower());

            if (!exists)
            {
                context.ProductsCheaperAtCompetitor.Add(new ProductsCheaperAtCompetitor
                {
                    ProductSku = sku,
                    CompetitorPrice = competitorPrice
                });

                LogMessageAsync(context,
                        $"SKU {sku} flagged as cheaper at competitor: {randomisedPrice:C} < {costPrice:C}",
                        performContext)
                    .Wait();
            }

            return;
        }

        // Add to cache with new price
        context.FandoogleReflectorCache.Add(new FandoogleReflectorCache
        {
            ProductSku = sku,
            RandomisedPrice = randomisedPrice,
            OriginalPrice = competitorPrice,
            ExpiresAt = GetRandomExpiryDate(random)
        });

        LogMessageAsync(context, $"Added pricing for SKU {sku}: {randomisedPrice:C} (competitor: {competitorPrice:C})",
            performContext).Wait();
    }

    /// <summary>
    /// Makes a fandoogle pricing request with exponential backoff retry logic
    /// </summary>
    private static (decimal? price, string error) RequestProductPrice(
        Service service,
        string sku)
    {
        var result = RetryHelper.ExecuteWithRetry(
            () => service.RequestPricing(sku)
                .ConfigureAwait(false)
                .GetAwaiter()
                .GetResult(),
            r => r.IsFailure && r.Error?.Contains(RateLimitError) == true
        );

        var (_, requestFailed, response, requestError) = result;

        switch (requestFailed)
        {
            case false when !response.Error:
                return (response.QueryResult.FirstOrDefault(), null);
            case true when !requestError.Contains(RateLimitError):
                return (null, requestError);
        }

        if (response?.Error ?? false)
        {
            return (null, response.ErrorMessage ?? "Unknown error");
        }

        return (null, "Max retry attempts exceeded");
    }

    /// <summary>
    /// Logs a message to the PreflightLog table with [RequestPricing] prefix
    /// </summary>
    /// <param name="db">Database context</param>
    /// <param name="message">Message to log</param>
    /// <param name="context">Hangfire context for heartbeat management</param>
    private static async Task LogMessageAsync(EdunetDatafeedsEntities db, string message, PerformContext context = null)
    {
        db.PreflightLog.Add(new PreflightLog
        {
            LogDateTime = DateTime.Now,
            Message = $"[RequestPricing] {message}"
        });

        try
        {
            await db.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            // Best effort logging - don't fail the job if logging fails
        }

        // Keep Hangfire job alive during long operations
        context?.SetJobParameter("heartbeat", DateTime.UtcNow);
    }
}
using System;
using System.Data.Entity;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models.DbModels.Datafeed;
using Microsoft.Ajax.Utilities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OfficeOpenXml;

namespace Datafeed_v2.Hangfire.Tasks;

public class ScanForCheaperCompetitorPricing
{
    private static uint debugCount = 0;

    //todo: move these to a separate file?
    private static async Task<Result<object>> _postAsync(string resourceUrl, string stockCode, string guid)
    {
        if (string.IsNullOrWhiteSpace(resourceUrl))
        {
            return Result.Failure<object>("resourceUrl is null or whitespace");
        }

        if (string.IsNullOrWhiteSpace(stockCode))
        {
            return Result.Failure<object>("stockCode is null or whitespace");
        }

        if (string.IsNullOrWhiteSpace(guid))
        {
            return Result.Failure<object>("guid is null or whitespace");
        }

        var postContent = JsonConvert.SerializeObject(new
        {
            stockCode,
            guid
        });

        HttpResponseMessage response;
        try
        {
            response = await new HttpClient().PostAsync(resourceUrl,
                new StringContent(postContent, Encoding.UTF8, "application/json"));
        }
        catch (HttpRequestException e)
        {
            return Result.Failure<object>($"POST request to {resourceUrl} failed with exception {e.Message}");
        }

        var responseContent = await response.Content.ReadAsStringAsync();
        Debug.Assert(string.IsNullOrWhiteSpace(responseContent) == false);
        return Result.Success(responseContent)
            .Ensure(_ => response.IsSuccessStatusCode,
                $"POST request to {resourceUrl} failed with status code {response.StatusCode} and reason phrase \"{response.ReasonPhrase}\"");
    }

    private static async Task<Result<object>> _queryFandoogleReflector(string stockCode, string guid)
    {
        if (string.IsNullOrWhiteSpace(stockCode))
        {
            return Result.Failure<object>("stockCode is null or whitespace");
        }

        if (string.IsNullOrWhiteSpace(guid))
        {
            return Result.Failure<object>("guid is null or whitespace");
        }

        var postResult = await _postAsync("https://fandooglereflector.edunet.com.au/PriceBook/RequestPricing",
                stockCode, guid)
            .ConfigureAwait(false);
        if (Debugger.IsAttached && debugCount++ == 1)
        {
            postResult =
                Result.Success("{\"Error\":false,\"QueryResult\":[12.00],\"QueriedStockCode\":\"C13T344492\"}");
        }

        return postResult.IsSuccess
            ? Result.Success(postResult.Value)
                .Ensure(content => JObject.Parse(content.ToString()).GetValue("Error")?.Value<bool>() ?? false
                    ? Result.Failure<object>("Request response contains an error")
                    : content)
            : Result.Failure<object>($"Failed to get competitor pricing: {postResult.Error}");
    }

    public static async Task Run(EdunetDatafeedsEntities dataFeedsContext = null)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var kqmProducts = await dbDataFeedsContext.KQMProducts.ToArrayAsync();
        var fandoogleTasks = kqmProducts.Select(kqmProduct =>
                _queryFandoogleReflector(kqmProduct.manufacturerPartNumber.Trim(),
                    "0910a0c1-0123-48c0-9609-77497c028523")) // todo: move guid out to a project property setting
            .ToList();
        var fandoogleResults = await Task.WhenAll(fandoogleTasks).ConfigureAwait(false);
        var successfulFandoogleResults = fandoogleResults.Where(result => result.IsSuccess)
            .Select(q =>
            {
                var jobj = JObject.Parse(q.Value.ToString());
                return (jobj.GetValue("QueriedStockCode")?.Value<string>() ?? "error",
                    jobj.Value<JArray>("QueryResult").First?.Value<decimal>() ?? -1);
            })
            .ToList();

        var productsDelta = kqmProducts.Length - successfulFandoogleResults.Count;
        if (productsDelta != 0)
        {
            throw new Exception($"Failed to get competitor pricing for {productsDelta} products");
        }

        var productsThatAreCheaperAtACompetitor = kqmProducts.Where(ourProduct => successfulFandoogleResults.Any(
                theirProduct =>
                    string.Equals(ourProduct.manufacturerPartNumber.Trim(), theirProduct.Item1.Trim(),
                        StringComparison.CurrentCultureIgnoreCase) && theirProduct.Item2 < ourProduct.price))
            .DistinctBy(q => q.manufacturerPartNumber.Trim().ToLower())
            .ToArray();

        if (productsThatAreCheaperAtACompetitor.Length ==
            0) // don't need to do anything if there are no cheaper products
        {
            return;
        }

        // Create in-memory excel sheet and populate it with the data
        using var memoryStream = new MemoryStream();
        using var excelPackage = new ExcelPackage();
        var worksheet = excelPackage.Workbook.Worksheets.Add("Competitor Pricing Report");
        worksheet.Cells["A1"].Value = "Competitor Product SKU";
        worksheet.Cells["B1"].Value = "Competitor Product Price";
        worksheet.Cells["C1"].Value = "Price Difference";
        worksheet.Cells["D1"].Value = "Price Difference %";
        for (var i = 0; i <= productsThatAreCheaperAtACompetitor.Length - 1; i++)
        {
            var productCheaperAtCompetitor = productsThatAreCheaperAtACompetitor.ElementAt(i);
            var (_, competitorPrice) = successfulFandoogleResults.First(q =>
                string.Equals(q.Item1.Trim(), productCheaperAtCompetitor.manufacturerPartNumber.Trim(),
                    StringComparison.CurrentCultureIgnoreCase) && q.Item2 < productCheaperAtCompetitor.price);

            worksheet.Cells[i + 2, 1].Value = productCheaperAtCompetitor.manufacturerPartNumber.Trim();
            worksheet.Cells[i + 2, 2].Value = competitorPrice;
            worksheet.Cells[i + 2, 3].Value = productCheaperAtCompetitor.price - competitorPrice;
            worksheet.Cells[i + 2, 4].Value =
                Math.Round((competitorPrice / productCheaperAtCompetitor.price) - 1, 2).ToString("P");
        }

        excelPackage.SaveAs(memoryStream);

        // send email
        memoryStream.Position = 0;
        var mail = new MailMessage
        {
            From = new MailAddress("<EMAIL>", "Edunet"),
            Subject = "Competitor Pricing Report",
            IsBodyHtml = true,
            Body =
                $"Good evening,<br>There are currently {productsThatAreCheaperAtACompetitor.Length} products that are available for a cheaper price at a competitor.",
            Attachments =
            {
                new Attachment(memoryStream, "Competitor Pricing Report.xlsx",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            }
        };
        mail.To.Add(Debugger.IsAttached == false
            ? new MailAddress("<EMAIL>")
            : new MailAddress("<EMAIL>"));

        using var smtpClient = new SmtpClient();
        smtpClient.Host = "saferoute.solution-one.com.au";
        smtpClient.Port = 25;
        // await smtpClient.SendMailAsync(mail);
    }
}
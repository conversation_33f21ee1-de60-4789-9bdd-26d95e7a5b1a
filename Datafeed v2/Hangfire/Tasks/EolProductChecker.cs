using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Properties;
using Datafeed_v2.Singletons;
using Hangfire;
using Hangfire.Server;
using KaseyaAPI;

namespace Datafeed_v2.Hangfire.Tasks
{
    /// <summary>
    /// Hangfire task that checks for End of Life (EOL) products by querying KQM for product status.
    /// Fetches all ProductSource and ImportedItemsFromPriceBook entries and checks each SKU
    /// in KQM to determine if the product is still active (isActive property).
    /// </summary>
    public static class EolProductChecker
    {
        /// <summary>
        /// Main entry point for the EOL Product Checker task.
        /// Fetches all products from ProductSource and ImportedItemsFromPriceBook,
        /// checks their EOL status in KQM, and logs the results.
        /// </summary>
        /// <param name="context">Hangfire execution context for logging and heartbeat</param>
        /// <param name="diDbDatafeed">Database context (injectable for testing)</param>
        /// <param name="di<PERSON>ase<PERSON><PERSON><PERSON>">KaseyaApi instance (injectable for testing)</param>
        /// <param name="isUnderTest">Flag indicating if running in test mode</param>
        [DisableConcurrentExecution(60 * 60 * 4)] // Prevent overlapping executions for 4 hours
        [SkipWhenPreviousJobIsRunning]
        public static async Task Run(
            PerformContext context,
            EdunetDatafeedsEntities diDbDatafeed = null,
            KaseyaApi diKaseyaApi = null,
            bool isUnderTest = false)
        {
            using var timer = new Timer(_ => context?.SendHeartbeat(), null, TimeSpan.Zero, TimeSpan.FromMinutes(15));
            
            var dbDatafeed = diDbDatafeed ?? new EdunetDatafeedsEntities();
            var kaseyaApi = diKaseyaApi ?? KaseyaApiSingleton.Instance;
            
            try
            {
                context?.WriteLine("Starting EOL Product Checker task...");
                
                // Fetch all products to check
                var productsToCheck = await GetProductsToCheck(dbDatafeed);
                context?.WriteLine($"Found {productsToCheck.Count} products to check for EOL status");
                
                if (!productsToCheck.Any())
                {
                    context?.WriteLine("No products found to check. Task completed.");
                    return;
                }
                
                // Check each product's EOL status
                var eolResults = new List<EolProductLog>();
                var processedCount = 0;
                
                foreach (var product in productsToCheck)
                {
                    try
                    {
                        var eolResult = await CheckProductEolStatus(product, kaseyaApi);
                        eolResults.Add(eolResult);
                        
                        processedCount++;
                        if (processedCount % 10 == 0)
                        {
                            context?.WriteLine($"Processed {processedCount}/{productsToCheck.Count} products");
                        }
                        
                        // Add small delay to avoid overwhelming the API
                        if (!isUnderTest)
                        {
                            await Task.Delay(100);
                        }
                    }
                    catch (Exception ex)
                    {
                        context?.WriteLine($"Error checking product {product.SKU}: {ex.Message}");
                        
                        // Create error log entry
                        eolResults.Add(new EolProductLog
                        {
                            SKU = product.SKU,
                            ProductSource = product.Source,
                            ProductSourceId = product.SourceId,
                            IsEol = false,
                            CheckedDateTime = DateTime.UtcNow,
                            KqmProductId = null,
                            ErrorMessage = $"Exception during check: {ex.Message}"
                        });
                    }
                }
                
                // Save results to database
                await SaveEolResults(dbDatafeed, eolResults);
                
                var eolCount = eolResults.Count(r => r.IsEol);
                var errorCount = eolResults.Count(r => !string.IsNullOrEmpty(r.ErrorMessage));
                
                context?.WriteLine($"EOL Product Checker completed. Checked: {eolResults.Count}, EOL: {eolCount}, Errors: {errorCount}");
            }
            catch (Exception ex)
            {
                context?.WriteLine($"Fatal error in EOL Product Checker: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// Fetches all products that need to be checked for EOL status.
        /// Includes ProductSource entries (not archived) and ImportedItemsFromPriceBook entries.
        /// </summary>
        /// <param name="dbContext">Database context</param>
        /// <returns>List of products to check with their source information</returns>
        private static async Task<List<ProductToCheck>> GetProductsToCheck(EdunetDatafeedsEntities dbContext)
        {
            var productsToCheck = new List<ProductToCheck>();
            
            // Get ProductSource entries (not archived)
            var productSources = await dbContext.ProductSource
                .Where(ps => !ps.Archived && !string.IsNullOrEmpty(ps.ProductSku))
                .Select(ps => new ProductToCheck
                {
                    SKU = ps.ProductSku,
                    Source = "ProductSource",
                    SourceId = ps.ID
                })
                .ToListAsync();
            
            productsToCheck.AddRange(productSources);
            
            // Get ImportedItemsFromPriceBook entries
            var priceBookItems = await dbContext.ImportedItemsFromPriceBook
                .Where(pb => !string.IsNullOrEmpty(pb.ProductSKU))
                .Select(pb => new ProductToCheck
                {
                    SKU = pb.ProductSKU,
                    Source = "ImportedItemsFromPriceBook",
                    SourceId = pb.ID
                })
                .ToListAsync();
            
            productsToCheck.AddRange(priceBookItems);
            
            return productsToCheck;
        }
        
        /// <summary>
        /// Checks a single product's EOL status by querying KQM.
        /// </summary>
        /// <param name="product">Product to check</param>
        /// <param name="kaseyaApi">KaseyaApi instance</param>
        /// <returns>EOL check result</returns>
        private static async Task<EolProductLog> CheckProductEolStatus(ProductToCheck product, KaseyaApi kaseyaApi)
        {
            var logEntry = new EolProductLog
            {
                SKU = product.SKU,
                ProductSource = product.Source,
                ProductSourceId = product.SourceId,
                CheckedDateTime = DateTime.UtcNow,
                IsEol = false,
                KqmProductId = null,
                ErrorMessage = null
            };
            
            try
            {
                var productResult = await kaseyaApi.GetProduct(product.SKU);
                
                if (productResult.IsFailure)
                {
                    logEntry.ErrorMessage = $"KQM API error: {productResult.Error.Error}";
                    return logEntry;
                }
                
                if (!productResult.Value.Any())
                {
                    logEntry.ErrorMessage = "Product not found in KQM";
                    return logEntry;
                }
                
                var kqmProduct = productResult.Value.First();
                logEntry.KqmProductId = kqmProduct.id;
                logEntry.IsEol = !kqmProduct.isActive; // EOL if not active
                
                return logEntry;
            }
            catch (Exception ex)
            {
                logEntry.ErrorMessage = $"Exception during KQM check: {ex.Message}";
                return logEntry;
            }
        }
        
        /// <summary>
        /// Saves EOL check results to the database.
        /// </summary>
        /// <param name="dbContext">Database context</param>
        /// <param name="results">EOL check results to save</param>
        private static async Task SaveEolResults(EdunetDatafeedsEntities dbContext, List<EolProductLog> results)
        {
            foreach (var result in results)
            {
                dbContext.EolProductLog.Add(result);
            }
            
            await dbContext.SaveChangesAsync();
        }
        
        /// <summary>
        /// Represents a product that needs to be checked for EOL status.
        /// </summary>
        private class ProductToCheck
        {
            public string SKU { get; set; }
            public string Source { get; set; }
            public int SourceId { get; set; }
        }
    }
}

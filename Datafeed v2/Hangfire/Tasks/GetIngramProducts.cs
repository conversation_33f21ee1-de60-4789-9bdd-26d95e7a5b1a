using System;
using System.Collections.Generic;
using System.Linq;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Properties;
using Datafeed_v2.Services;
using Hangfire.Server;

namespace Datafeed_v2.Hangfire.Tasks;

public class GetIngramProducts
{
    public static void Run(string path, string categoriesPath, PerformContext context,
        EdunetDatafeedsEntities dataFeedsContext = null)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();

        var ingram = new IngramSftpService(Settings.Default.IngramSftpHostname,
            Settings.Default.IngramCategorySftpUsername, Settings.Default.IngramCategorySftpPassword);
        var (_, connectFailed, _, connectError) = ingram.Connect();
        if (connectFailed)
        {
            throw new Exception(connectError);
        }

        var (_, fetchFailed, categories, fetchError) = ingram.FetchCategories("ddc/download", categoriesPath);
        if (fetchFailed)
        {
            throw new Exception(fetchError);
        }

        ingram = new IngramSftpService(Settings.Default.IngramSftpHostname, Settings.Default.IngramSftpUsername,
            Settings.Default.IngramSftpPassword);
        var (_, connectFailed1, _, connectError1) = ingram.Connect();
        if (connectFailed1)
        {
            throw new Exception(connectError1);
        }

        var (_, fetchFailed1, feed, fetchError1) = ingram.FetchFeed(path);
        if (fetchFailed1)
        {
            throw new Exception(fetchError1);
        }

        var productsToAddToDb = new List<IngramProducts>();
        foreach (var product in
                 feed)
        {
            // todo: need to figure out which category to use if a product is listed under multiple categories
            var productCategory = categories.FirstOrDefault(q =>
                string.Equals(q.IngramPartNumber.TrimStart('0').Trim(), product.IngramPartNumber.TrimStart('0').Trim(),
                    StringComparison.CurrentCultureIgnoreCase));
            var existingProduct =
                dbDataFeedsContext.IngramProducts.SingleOrDefault(q =>
                    q.IngramPartNumber.Trim().ToLower() == product.IngramPartNumber.Trim().ToLower());
            if (existingProduct != null)
            {
                existingProduct.CustomerPrice = product.CustomerPrice;
                existingProduct.RetailPrice = product.RetailPrice;
                existingProduct.AvailableQuantity = product.AvailableQuantity;
                existingProduct.CategoryName = productCategory?.Category;
                existingProduct.SubCategoryName = productCategory?.SubCategory;
                continue;
            }

            var ingramProduct = new IngramProducts
            {
                IngramPartNumber = product.IngramPartNumber,
                IngramPartDescription = product.IngramPartDescription,
                CustomerPartNumber = product.CustomerPartNumber,
                VendorPartNumber = product.VendorPartNumber,
                EANUPCCode = product.EANUPCCode,
                Plant = product.Plant,
                VendorNumber = product.VendorNumber,
                VendorName = product.VendorName,
                Size = product.Size,
                Weight = product.Weight,
                Volume = product.Volume,
                Unit = product.Unit,
                CategoryID = product.CategoryID,
                CustomerPrice = product.CustomerPrice,
                RetailPrice = product.RetailPrice,
                AvailabilityFlag = product.AvailabilityFlag,
                AvailableQuantity = product.AvailableQuantity,
                BacklogInformation = product.BacklogInformation,
                BacklogETA = product.BacklogETA,
                LicenseFlag = product.LicenseFlag,
                BOMFlag = product.BOMFlag,
                WarrantyFlag = product.WarrantyFlag,
                BulkFreightFlag = product.BulkFreightFlag,
                MaterialLongDescription = product.MaterialLongDescription,
                Length = product.Length,
                Width = product.Width,
                Height = product.Height,
                DimensionUnit = product.DimensionUnit,
                WeightUnit = product.WeightUnit,
                VolumeUnit = product.VolumeUnit,
                Category = product.Category,
                MaterialCreationReasonCode = product.MaterialCreationReasonCode,
                MediaCode = product.MediaCode,
                MaterialLanguageCode = product.MaterialLanguageCode,
                SubstituteMaterial = product.SubstituteMaterial,
                SupersededMaterial = product.SupersededMaterial,
                ManufacturerVendorNumber = product.ManufacturerVendorNumber,
                SubCategory = product.SubCategory,
                ProductFamily = product.ProductFamily,
                PurchasingVendor = product.PurchasingVendor,
                MaterialChangeCode = product.MaterialChangeCode,
                ActionCode = product.ActionCode,
                PriceStatus = product.PriceStatus,
                NewMaterialFlag = product.NewMaterialFlag,
                VendorSubrange = product.VendorSubrange,
                CaseQty = product.CaseQty,
                PalletQty = product.PalletQty,
                DirectOrderIdentifier = product.DirectOrderIdentifier,
                MaterialStatus = product.MaterialStatus,
                Discontinued = product.Discontinued,
                ReleaseDate = product.ReleaseDate,
                FulfilmentType = product.FulfilmentType,
                MusicCopyrightFees = product.MusicCopyrightFees,
                RecyclingFees = product.RecyclingFees,
                DocumentCopyrightFees = product.DocumentCopyrightFees,
                BatteryFees = product.BatteryFees,
                CustomerPriceWithTax = product.CustomerPriceWithTax,
                RetailPriceWithTax = product.RetailPriceWithTax,
                TaxPercent = product.TaxPercent,
                DiscountInPercent = product.DiscountInPercent,
                CustomerReservationNumber = product.CustomerReservationNumber,
                CustomerReservationQty = product.CustomerReservationQty,
                AgreementID = product.AgreementID,
                LevelID = product.LevelID,
                Period = product.Period,
                Points = product.Points,
                CompanyCode = product.CompanyCode,
                CompanyCodeCurrency = product.CompanyCodeCurrency,
                CustomerCurrencyCode = product.CustomerCurrencyCode,
                CustomerPriceChangeFlag = product.CustomerPriceChangeFlag,
                SubstituteFlag = product.SubstituteFlag,
                CreationReasonType = product.CreationReasonType,
                CreationReasonValue = product.CreationReasonValue,
                Plant01AvailableQuantity = product.Plant01AvailableQuantity,
                Plant02AvailableQuantity = product.Plant02AvailableQuantity,
                CategoryName = productCategory?.Category,
                SubCategoryName = productCategory?.SubCategory
            };
            productsToAddToDb.Add(ingramProduct);
        }

        productsToAddToDb = productsToAddToDb.Where(q => q is { CategoryName: not null, SubCategoryName: not null })
            .Where(q => new string[]
            {
                "Audio/Visual", "Cables", "Cameras & Scanners", "Components", "Computers", "Displays", "Input Devices",
                "Networking", "Office Equipment", "Power & Rack Equipment", "Printers", "Storage", "Supplies & Media"
            }.Select(q => q.Trim().ToLower()).Contains(q.CategoryName.Trim().ToLower()))
            .Where(q => new string[]
            {
                "A/V & Music Accessories", "Projection Screens", "Projector Accessories", "Projectors",
                "Whiteboards & Presentation Devices", "A/V Cables & Adapters", "Cabling Components & Tools",
                "Computer Cables & Adapters", "I/O Device Cables", "Network Cables", "Power Cables", "Storage Cables",
                "Camera Accessories", "Cameras", "Scanner Accessories", "Scanners", "Desktops",
                "Notebook/Tablet Accessories", "Notebooks & Tablets", "Commercial/Signage Displays",
                "Computer Displays", "Monitor/TV Accessories", "Monitors", "Keyboards & Keypads",
                "Mice/Pointers & Graphic Tables"
            }.Select(q => q.Trim().ToLower()).Contains(q.SubCategoryName.Trim().ToLower()))
            .ToList();
        dbDataFeedsContext.IngramProducts.AddRange(productsToAddToDb);
        dbDataFeedsContext.SaveChanges();
    }
}
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using Datafeed_v2.Models;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Services;
using Hangfire;
using Hangfire.Server;

namespace Datafeed_v2.Hangfire.Tasks
{
    /// <summary>
    /// Hangfire background task that processes non-compliant ProductSource records
    /// by fetching comprehensive product information from the KQM service and updating
    /// the ProductSource records with the retrieved data.
    /// </summary>
    public abstract class UpdateProductInformationFromKqm
    {
        private const int BatchSize = 20; // Process in batches to manage memory and performance

        /// <summary>
        /// Logs a message to the PreflightLog table for tracking task execution
        /// </summary>
        /// <param name="db">Database context</param>
        /// <param name="message">Message to log</param>
        /// <param name="context">Optional Hangfire context for console logging</param>
        private static async Task LogMessageAsync(EdunetDatafeedsEntities db, string message,
            PerformContext context = null)
        {
            db.PreflightLog.Add(new PreflightLog
                { LogDateTime = DateTime.Now, Message = $"[UpdateProductInformationFromKqm] {message}" });
            await db.SaveChangesAsync();
        }

        /// <summary>
        /// Main task execution method that processes non-compliant ProductSource records
        /// by fetching product information from KQM and updating the records
        /// </summary>
        /// <param name="context">Hangfire execution context</param>
        /// <param name="diDbDatafeed">Optional injected database context</param>
        /// <param name="diKqmService">Optional injected KQM service</param>
        [DisableConcurrentExecution(60 * 60 * 6)] // Prevent concurrent execution for 6 hours
        [SkipWhenPreviousJobIsRunning]
        public static async Task Run(PerformContext context,
            EdunetDatafeedsEntities diDbDatafeed = null,
            IGetProductInformationFromKqmService diKqmService = null)
        {
            using var dbDatafeed = diDbDatafeed ?? new EdunetDatafeedsEntities();
            using var kqmService = diKqmService ?? new GetProductInformationFromKqmService();

            await LogMessageAsync(dbDatafeed, "[Run] Starting UpdateProductInformationFromKqm task.", context);

            var totalProcessed = 0;
            var totalUpdated = 0;
            var totalErrors = 0;

            try
            {
                var batchNumber = 0;
                List<ProductSource> currentBatch;

                do
                {
                    context?.SendHeartbeat(); // Send heartbeat before processing each batch

                    // Fetch the next batch of non-compliant sources
                    currentBatch = await dbDatafeed.ProductSource
                        .Where(ps => ps.Status == (int)ProductSourceStatuses.NotCompliant)
                        .OrderBy(ps => ps.ID)
                        .Skip(batchNumber * BatchSize)
                        .Take(BatchSize)
                        .Include(ps => ps.ProductSourceImages) // Include images for publishable check
                        .ToListAsync();

                    if (!currentBatch.Any())
                    {
                        await LogMessageAsync(dbDatafeed,
                            "[Run] No more non-compliant sources found. Exiting batch processing.", context);
                        break;
                    }

                    await LogMessageAsync(dbDatafeed,
                        $"[Run] Processing batch {batchNumber + 1} with {currentBatch.Count} sources.", context);

                    // Extract unique SKUs from the batch (avoid duplicates and empty SKUs)
                    var skusToProcess = currentBatch
                        .Where(source => !string.IsNullOrWhiteSpace(source.ProductSku))
                        .Select(source => source.ProductSku)
                        .Distinct()
                        .ToList();

                    if (!skusToProcess.Any())
                    {
                        await LogMessageAsync(dbDatafeed,
                            $"[Run][Batch {batchNumber + 1}] No valid SKUs found in batch. Skipping.", context);
                        batchNumber++;
                        continue;
                    }

                    await LogMessageAsync(dbDatafeed,
                        $"[Run][Batch {batchNumber + 1}] Fetching product information for {skusToProcess.Count} unique SKUs.",
                        context);

                    // Fetch product information from KQM service
                    var fetchResult = await kqmService.FetchProductInformationAsync(skusToProcess);
                    if (fetchResult.IsFailure)
                    {
                        await LogMessageAsync(dbDatafeed,
                            $"[Run][Batch {batchNumber + 1}] Failed to fetch product information: {fetchResult.Error}",
                            context);
                        totalErrors++;
                        batchNumber++;
                        continue;
                    }

                    var fetchedProductInfo = fetchResult.Value.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                    await LogMessageAsync(dbDatafeed,
                        $"[Run][Batch {batchNumber + 1}] Successfully fetched information for {fetchedProductInfo.Count} SKUs.",
                        context);

                    // Process results for the batch
                    foreach (var source in currentBatch.Where(source => !string.IsNullOrWhiteSpace(source.ProductSku)))
                    {
                        totalProcessed++;

                        if (fetchedProductInfo.TryGetValue(source.ProductSku, out var productInfo))
                        {
                            try
                            {
                                await LogMessageAsync(dbDatafeed,
                                    $"[Run][Batch {batchNumber + 1}] Updating product information for Source ID: {source.ID}, SKU: {source.ProductSku}.",
                                    context);

                                // Update ProductSource with KQM data
                                UpdateProductSourceFromKqmData(source, productInfo);

                                // Check if the source is now publishable
                                if (IsSourcePublishable(source, dbDatafeed))
                                {
                                    await LogMessageAsync(dbDatafeed,
                                        $"[Run][Batch {batchNumber + 1}] Source ID: {source.ID} is now publishable. Setting status to Published.",
                                        context);
                                    source.Status = (int)ProductSourceStatuses.Published;
                                }

                                totalUpdated++;
                            }
                            catch (Exception ex)
                            {
                                await LogMessageAsync(dbDatafeed,
                                    $"[Run][Batch {batchNumber + 1}] Error updating Source ID: {source.ID}, SKU: {source.ProductSku}: {ex.Message}",
                                    context);
                                totalErrors++;
                            }
                        }
                        else
                        {
                            await LogMessageAsync(dbDatafeed,
                                $"[Run][Batch {batchNumber + 1}] No product information fetched for Source ID: {source.ID}, SKU: {source.ProductSku}.",
                                context);
                        }
                    }

                    // Save changes for this batch
                    try
                    {
                        await dbDatafeed.SaveChangesAsync();
                        await LogMessageAsync(dbDatafeed,
                            $"[Run][Batch {batchNumber + 1}] Successfully saved changes for batch.", context);
                    }
                    catch (Exception ex)
                    {
                        await LogMessageAsync(dbDatafeed,
                            $"[Run][Batch {batchNumber + 1}] Error saving changes: {ex.Message}", context);
                        totalErrors++;
                    }

                    batchNumber++;
                } while (currentBatch.Count == BatchSize); // Continue while we have full batches

                // Log final statistics
                await LogMessageAsync(dbDatafeed,
                    $"[Run] Task completed. Processed: {totalProcessed}, Updated: {totalUpdated}, Errors: {totalErrors}",
                    context);
            }
            catch (Exception ex)
            {
                await LogMessageAsync(dbDatafeed,
                    $"[Run] Unhandled exception in UpdateProductInformationFromKqm: {ex.Message} --- StackTrace: {ex.StackTrace}",
                    context);
                throw; // Re-throw so Hangfire knows the job failed
            }
            finally
            {
                await LogMessageAsync(dbDatafeed, "[Run] Exiting UpdateProductInformationFromKqm task.", context);
            }
        }

        /// <summary>
        /// Updates a ProductSource record with data from KQM service
        /// </summary>
        /// <param name="source">ProductSource to update</param>
        /// <param name="kqmData">KQM product information</param>
        private static void UpdateProductSourceFromKqmData(ProductSource source, KqmProductInformation kqmData)
        {
            if (source == null || kqmData == null)
            {
                return;
            }

            // Update title if available and not empty
            if (!string.IsNullOrWhiteSpace(kqmData.Title))
            {
                source.ProductTitle = kqmData.Title.Trim();
            }

            // Update long description if available and not empty
            if (!string.IsNullOrWhiteSpace(kqmData.LongDescription))
            {
                source.ProductLongDescription = kqmData.LongDescription.Trim();
            }

            // Update cost price if available and positive
            if (kqmData.CostPrice is > 0)
            {
                source.CostPrice = kqmData.CostPrice.Value;
            }

            // Update sell price (recommended sell price) if available and positive
            if (kqmData.RecommendedSellPrice is > 0)
            {
                source.SellPrice = kqmData.RecommendedSellPrice.Value;
            }

            // Update stock count if available and non-negative
            if (kqmData.TotalStockCount is >= 0)
            {
                source.QtyAvailable = kqmData.TotalStockCount.Value;
            }
        }

        /// <summary>
        /// Checks if a ProductSource meets the basic requirements to be published.
        /// Adapted from the existing IsSourcePublishable logic in GetDescriptionsFromKqm.
        /// </summary>
        /// <param name="source">ProductSource to check</param>
        /// <param name="db">Database context for additional checks</param>
        /// <returns>True if the source meets publishing requirements</returns>
        private static bool IsSourcePublishable(ProductSource source, EdunetDatafeedsEntities db)
        {
            if (source == null)
            {
                return false;
            }

            // Basic checks for essential fields
            var hasTitle = !string.IsNullOrWhiteSpace(source.ProductTitle);
            var hasShortDescription = !string.IsNullOrWhiteSpace(source.ProductShortDescription);
            var hasLongDescription = !string.IsNullOrWhiteSpace(source.ProductLongDescription);
            var hasStockCount = source.QtyAvailable >= 0; // Allow zero stock
            var hasCostPrice = source.CostPrice > 0; // Cost must be positive

            // Check for Sell Price: Either directly set or via Fandoogle cache
            var hasSellPrice = source.SellPrice is > 0;
            if (!hasSellPrice)
            {
                // Check Fandoogle cache for pricing
                var cachedPrice = db.FandoogleReflectorCache
                    .FirstOrDefault(c => c.ProductSku == source.ProductSku && c.ExpiresAt > DateTime.UtcNow);
                hasSellPrice = cachedPrice != null;
            }

            // Check for images (either loaded in context or already in DB)
            var hasImages = source.ProductSourceImages.Any() ||
                            db.ProductSourceImages.Any(psi => psi.ProductSourceId == source.ID);

            return hasTitle
                   && hasShortDescription
                   && hasLongDescription
                   && hasStockCount
                   && hasCostPrice
                   && hasSellPrice
                   && hasImages;
        }
    }
}
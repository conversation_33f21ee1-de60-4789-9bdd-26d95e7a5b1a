using System;
using System.Collections.Concurrent;
using System.Data.Entity;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Datafeed_v2.Helpers;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Services;
using Datafeed_v2.Singletons;
using Hangfire;
using Hangfire.Server;

namespace Datafeed_v2.Hangfire.Tasks
{
    public class GetProductImagesFromIcecat
    {
        [DisableConcurrentExecution(60 * 60 * 2)]
        public static async Task Run(PerformContext context, EdunetDatafeedsEntities dataFeedsContext = null,
            IcecatApiService icecatApiService = null, IHttpClient diHttpClient = null)
        {
            ThreadPool.SetMaxThreads(10, 2);

            var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
            var icecatService = icecatApiService ?? IcecatApiServiceSingleton.Instance;
            var httpClient = diHttpClient ?? new MockableHttpClient();
            var random = new Random();

            var products = await dbDataFeedsContext.KQMProducts
                .Join(dbDataFeedsContext.KQMBrands,
                    product => product.brandID,
                    brand => brand.brandID,
                    (product, brand) => new { Product = product, Brand = brand })
                .ToListAsync();

            // New Price Book products query
            var priceBookProducts = await dbDataFeedsContext.ImportedItemsFromPriceBook
                .Select(p => new { Product = p, BrandName = p.Brand })
                .ToListAsync();

            var imagesToAdd = new ConcurrentBag<KqmProductImages>();
            var priceBookImagesToAdd = new ConcurrentBag<PriceBookItemImages>();

            Parallel.ForEach(products, new ParallelOptions { MaxDegreeOfParallelism = 2 }, item =>
            {
                try
                {
                    var product = item.Product;
                    var brand = item.Brand;

                    if (string.IsNullOrWhiteSpace(product.manufacturerPartNumber))
                    {
                        return;
                    }

                    // Get product info with both image types
                    var result = icecatService.GetProductInfoByManufacturerCode(
                        brand.name,
                        product.manufacturerPartNumber,
                        [IcecatApiContentTypes.Image, IcecatApiContentTypes.Gallery]
                    ).ConfigureAwait(false).GetAwaiter().GetResult();

                    if (result.IsFailure)
                    {
                        return;
                    }

                    // First handle main image
                    if (result.Value.Data?.Image != null)
                    {
                        var mainImageUrl = result.Value.Data.Image.HighPic ?? result.Value.Data.Image.LowPic;

                        if (!string.IsNullOrEmpty(mainImageUrl))
                        {
                            // Check if main image exists
                            var existingMainImage = dbDataFeedsContext.KqmProductImages
                                .Any(i => i.KqmProductId == product.productID && i.ImageUrl == mainImageUrl);

                            if (!existingMainImage)
                            {
                                // Download and add main image
                                var response = httpClient.GetAsync(mainImageUrl).ConfigureAwait(false).GetAwaiter()
                                    .GetResult();
                                if (response.IsSuccessStatusCode)
                                {
                                    var imageBytes = response.Content.ReadAsByteArrayAsync().ConfigureAwait(false)
                                        .GetAwaiter().GetResult();
                                    imagesToAdd.Add(new KqmProductImages
                                    {
                                        KqmProductId = product.productID,
                                        Image = imageBytes,
                                        ImageUrl = mainImageUrl
                                    });
                                    Thread.Sleep(random.Next(1000, 3000));
                                }
                            }
                        }
                    }

                    // Then handle gallery images
                    if (result.Value.Data?.Gallery == null)
                    {
                        return;
                    }

                    foreach (var galleryItem in result.Value.Data.Gallery)
                    {
                        if (galleryItem.Type != "ProductImage")
                        {
                            continue;
                        }

                        var imageUrl = galleryItem.Pic ?? galleryItem.LowPic;

                        if (string.IsNullOrEmpty(imageUrl))
                        {
                            continue;
                        }

                        // Check if image already exists using fresh context to avoid concurrency issues
                        using var checkContext = new EdunetDatafeedsEntities();
                        var existingImage = checkContext.KqmProductImages
                            .Any(i => i.KqmProductId == product.productID && i.ImageUrl == imageUrl);

                        if (existingImage)
                        {
                            continue;
                        }

                        // Download image
                        var response = httpClient.GetAsync(imageUrl).ConfigureAwait(false).GetAwaiter().GetResult();
                        if (!response.IsSuccessStatusCode)
                        {
                            continue;
                        }

                        var imageBytes = response.Content.ReadAsByteArrayAsync().ConfigureAwait(false).GetAwaiter()
                            .GetResult();

                        // Add to collection for batch processing
                        imagesToAdd.Add(new KqmProductImages
                        {
                            KqmProductId = product.productID,
                            Image = imageBytes,
                            ImageUrl = imageUrl
                        });

                        // Random delay between requests
                        Thread.Sleep(random.Next(1000, 3000));
                    }
                }
                catch
                {
                    // Continue processing other products on error
                }
            });

            // New Price Book processing
            Parallel.ForEach(priceBookProducts, new ParallelOptions { MaxDegreeOfParallelism = 2 }, item =>
            {
                try
                {
                    var product = item.Product;
                    var brandName = item.BrandName;

                    if (string.IsNullOrWhiteSpace(product.ProductSKU))
                    {
                        return;
                    }

                    var result = icecatService.GetProductInfoByManufacturerCode(
                        brandName,
                        product.ProductSKU,
                        [IcecatApiContentTypes.Image, IcecatApiContentTypes.Gallery]
                    ).ConfigureAwait(false).GetAwaiter().GetResult();

                    if (result.IsFailure)
                    {
                        return;
                    }

                    // Handle main image
                    if (result.Value.Data?.Image != null)
                    {
                        var mainImageUrl = result.Value.Data.Image.HighPic ?? result.Value.Data.Image.LowPic;
                        if (!string.IsNullOrEmpty(mainImageUrl))
                        {
                            using var checkMainContext = new EdunetDatafeedsEntities();
                            if (!checkMainContext.PriceBookItemImages.Any(i =>
                                    i.PriceBookItemID == product.ID && i.ImageURL == mainImageUrl))
                            {
                                var response = httpClient.GetAsync(mainImageUrl)
                                    .ConfigureAwait(false).GetAwaiter().GetResult();
                                if (response.IsSuccessStatusCode)
                                {
                                    var imageBytes = response.Content.ReadAsByteArrayAsync()
                                        .ConfigureAwait(false).GetAwaiter().GetResult();
                                    priceBookImagesToAdd.Add(new PriceBookItemImages
                                    {
                                        PriceBookItemID = product.ID,
                                        Image = imageBytes,
                                        ImageURL = mainImageUrl
                                    });
                                    Thread.Sleep(random.Next(1000, 3000));
                                }
                            }
                        }
                    }

                    // Handle gallery images
                    if (result.Value.Data?.Gallery != null)
                    {
                        foreach (var galleryItem in result.Value.Data.Gallery
                                     .Where(g => g.Type == "ProductImage"))
                        {
                            var imageUrl = galleryItem.Pic ?? galleryItem.LowPic;
                            if (string.IsNullOrEmpty(imageUrl))
                            {
                                continue;
                            }

                            using var checkContext = new EdunetDatafeedsEntities();
                            if (!checkContext.PriceBookItemImages.Any(i =>
                                    i.PriceBookItemID == product.ID && i.ImageURL == imageUrl))
                            {
                                var response = httpClient.GetAsync(imageUrl)
                                    .ConfigureAwait(false).GetAwaiter().GetResult();
                                if (response.IsSuccessStatusCode)
                                {
                                    var imageBytes = response.Content.ReadAsByteArrayAsync()
                                        .ConfigureAwait(false).GetAwaiter().GetResult();
                                    priceBookImagesToAdd.Add(new PriceBookItemImages
                                    {
                                        PriceBookItemID = product.ID,
                                        Image = imageBytes,
                                        ImageURL = imageUrl
                                    });
                                    Thread.Sleep(random.Next(1000, 3000));
                                }
                            }
                        }
                    }
                }
                catch
                {
                    // Continue processing other products on error
                }
            });

            // Batch add all images
            dbDataFeedsContext.KqmProductImages.AddRange(imagesToAdd);
            dbDataFeedsContext.PriceBookItemImages.AddRange(priceBookImagesToAdd);
            await dbDataFeedsContext.SaveChangesAsync();
        }
    }
}
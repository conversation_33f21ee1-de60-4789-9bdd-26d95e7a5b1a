using System;
using System.Data.Entity;
using System.Threading.Tasks;
using Datafeed_v2.Models.DbModels.Datafeed;
using Hangfire;
using Hangfire.Server;

namespace Datafeed_v2.Hangfire.Tasks
{
    public class TruncateProductSyncLogs
    {
        [DisableConcurrentExecution(60 * 60 * 2)]
        public static async Task Run(PerformContext context, EdunetDatafeedsEntities dbContext = null)
        {
            var dbDatafeed = dbContext ?? new EdunetDatafeedsEntities();

            try
            {
                // Clear all entries from ProductSyncLog
                // await db.Database.ExecuteSqlCommandAsync("TRUNCATE TABLE ProductSyncLog");
                var entries = await dbDatafeed.ProductSyncLog.ToArrayAsync();

                dbDatafeed.ProductSyncLog.RemoveRange(entries);
                await dbDatafeed.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log error before re-throwing
                dbDatafeed.ProductSyncLog.Add(new ProductSyncLog
                {
                    Message = $"Truncate failed: {ex.Message}",
                    SyncDateTime = DateTime.Now
                });
                await dbDatafeed.SaveChangesAsync();
                throw;
            }
        }
    }
}
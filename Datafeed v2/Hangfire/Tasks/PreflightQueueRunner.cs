using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using AIProductDescription;
using CSharpFunctionalExtensions;
using Datafeed_v2.Functions;
using Datafeed_v2.Models;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Models.NopModels;
using Datafeed_v2.Properties;
using Datafeed_v2.Services;
using Datafeed_v2.Singletons;
using FandoogleReflectorService;
using Hangfire;
using Hangfire.Server;
using KaseyaAPI;
using Newtonsoft.Json;

namespace System.Runtime.CompilerServices
{
    internal static class IsExternalInit
    {
    }
}

namespace Datafeed_v2.Hangfire.Tasks
{
    public abstract class PreflightQueueRunner
    {
        private const string AiGeneratedProductInfoProductSource = "KaseyaEstore";
        private static readonly decimal[] PriceDecrements = [0.25m, 0.50m, 0.75m, 1.00m];

        /// <summary>
        /// Logs a message to the database
        /// </summary>
        /// <param name="db">Database context</param>
        /// <param name="productSourceId">Optional product source ID for the log entry</param>
        /// <param name="message">Message to log</param>
        /// <param name="context">Optional Hangfire context for console logging</param>
        private static async Task LogMessageAsync(EdunetDatafeedsEntities db, int? productSourceId, string message,
            PerformContext context = null)
        {
            // Log to database
            db.PreflightLog.Add(new PreflightLog
            {
                ProductSourceId = productSourceId,
                LogDateTime = DateTime.Now, // Use local time
                Message = message
            });

            try
            {
                // Save immediately to capture logs even if subsequent operations fail
                await db.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log failure to log - best effort only
            }
        }

        /// <summary>
        /// Contains prerequisite data needed for product source processing
        /// </summary>
        /// <param name="UnprocessedSources">List of ProductSource entries that need to be processed</param>
        /// <param name="ExistingAiInfo">Maps source IDs to a set of existing AI-generated information</param>
        /// <param name="SourceMappings">Maps source IDs to their corresponding source type names</param>
        /// <param name="SkuToPriceMap">Maps product SKUs to their prices</param>
        /// <param name="SkuToAttributesMap">Maps product SKUs to their attribute arrays</param>
        /// <param name="CategoryAttributesMap">Maps category names to arrays of required attributes</param>
        /// <param name="SkusWithValidFandoogleCache">Set of SKUs that have valid (non-expired) Fandoogle cache entries</param>
        private record PrerequisiteData(
            List<ProductSource> UnprocessedSources,
            Dictionary<int, HashSet<string>> ExistingAiInfo,
            Dictionary<int, string> SourceMappings,
            Dictionary<string, decimal> SkuToPriceMap,
            Dictionary<string, string[]> SkuToAttributesMap,
            Dictionary<string, string[]> CategoryAttributesMap,
            HashSet<string> SkusWithValidFandoogleCache);

        /// <summary>
        /// Contains external product data fetched from various sources
        /// </summary>
        /// <param name="SkuToKqmIdAndPriceMap">Maps product SKUs to their corresponding KQM product IDs, recommended sell prices, and descriptions</param>
        /// <param name="KqmProductIds">Array of KQM product IDs</param>
        /// <param name="KqmIdToStockAndCostMap">Maps KQM product IDs to their stock levels and cost prices</param>
        /// <param name="KqmIdToImagesMap">Maps KQM product IDs to their images (as byte arrays) and image types</param>
        /// <param name="KqmIdToBrandNameMap">Maps KQM product IDs to their brand names</param>
        /// <param name="SkuToIcecatDataMap">Maps product SKUs to their Icecat data including title, descriptions and images</param>
        private record ExternalProductData(
            Dictionary<string, (uint kqmProductId, decimal recommendedSellPrice, string title, string shortDescription,
                string
                longDescription)> SkuToKqmIdAndPriceMap,
            uint[] KqmProductIds,
            Dictionary<uint, (uint stockCount, decimal costPrice)> KqmIdToStockAndCostMap,
            Dictionary<uint, List<KeyValuePair<byte[], string>>> KqmIdToImagesMap,
            Dictionary<uint, string> KqmIdToBrandNameMap,
            Dictionary<string, (string title, string shortDescription, string longDescription, List<byte[]> images)>
                SkuToIcecatDataMap);

        // Helper function to replicate GetProductAttributes logic
        private static async Task<Result<Dictionary<string, string[]>, string>> GetRequiredAttributesForCategories(
            IEnumerable<string> categoryNames, HttpClient diHttpClient = null)
        {
            var httpClient = diHttpClient ?? new HttpClient
            {
                BaseAddress = new Uri(Debugger.IsAttached ? "https://localhost:5001/" : Settings.Default.NopLiveSiteUrl)
            };

            var uniqueCategories = categoryNames.Where(c => !string.IsNullOrWhiteSpace(c)).Distinct().ToArray();
            if (!uniqueCategories.Any())
            {
                return Result.Success<Dictionary<string, string[]>, string>(new Dictionary<string, string[]>());
            }

            var categoryToAttributesMap = new Dictionary<string, string[]>();

            foreach (var category in uniqueCategories)
            {
                var encodedCategory = HttpUtility.UrlEncode(category);
                var attributesUrl =
                    $"ProductAttributes/GetSpecificationAttributesForCategory?categories={encodedCategory}";

                try
                {
                    var req = await httpClient.GetAsync(attributesUrl);
                    if (!req.IsSuccessStatusCode)
                    {
                        // Skip adding attributes for this category on failure
                        continue;
                    }

                    var res = await req.Content.ReadAsStringAsync();
                    var (_, isFailure, attributesArray, _) =
                        Result.Try(() => JsonConvert.DeserializeObject<ProductAttributes[]>(res));

                    if (isFailure || attributesArray == null)
                    {
                        // Skip on deserialization failure
                        continue;
                    }

                    // Find the specific attributes for the requested category within the response
                    var productAttributesForCategory = attributesArray.FirstOrDefault(pa =>
                        string.Equals(pa.Category.Trim(), category.Trim(), StringComparison.CurrentCultureIgnoreCase));

                    if (productAttributesForCategory?.Attributes != null &&
                        productAttributesForCategory.Attributes.Any())
                    {
                        categoryToAttributesMap[category] =
                            productAttributesForCategory.Attributes.Select(a => a.Name).ToArray();
                    }
                    else
                    {
                        // Category found, but no attributes defined for it. Store empty array.
                        categoryToAttributesMap[category] = [];
                    }
                }
                catch (Exception)
                {
                    // Continue with the next category on exception
                }
            }

            return Result.Success<Dictionary<string, string[]>, string>(categoryToAttributesMap);
        }

        /// <summary>
        /// Contains external data fetched specifically for Price Book items.
        /// </summary>
        /// <param name="SkuToKqmDataMap">Maps product SKUs to their KQM product IDs, recommended sell prices, and descriptions.</param>
        /// <param name="SkuToIcecatDataMap">Maps product SKUs to their Icecat data (title, descriptions).</param>
        /// <param name="SkuToFandooglePriceMap">Maps product SKUs to their Fandoogle Reflector sell prices.</param>
        private record ExternalPriceBookData(
            Dictionary<string, (uint kqmProductId, decimal recommendedSellPrice, string title, string shortDescription,
                string longDescription, List<string> errors)> SkuToKqmDataMap,
            Dictionary<string, (string title, string shortDescription, string longDescription, List<byte[]> images)>
                SkuToIcecatDataMap,
            Dictionary<string, decimal> SkuToFandooglePriceMap);

        /// <summary>
        /// Fetches external data (descriptions, pricing) for a batch of Price Book items.
        /// </summary>
        private static async Task<ExternalPriceBookData> FetchExternalDataForPriceBookItemsAsync(
            List<ImportedItemsFromPriceBook> priceBookItemsBatch,
            KaseyaApi kaseyaApi,
            IcecatApiService icecatService,
            Service fandoogleService,
            HttpClient httpClient,
            EdunetDatafeedsEntities dbDatafeed,
            PerformContext context)
        {
            var batchLogPrefix =
                $"[FetchExternalPB][Batch:{priceBookItemsBatch.FirstOrDefault()?.ID ?? 0}...{priceBookItemsBatch.LastOrDefault()?.ID ?? 0}]";
            await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Starting.", context);

            var sourceSkus = priceBookItemsBatch
                .Select(pb => pb.ProductSKU)
                .Where(sku => !string.IsNullOrWhiteSpace(sku))
                .Distinct()
                .ToArray();
            await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Identified {sourceSkus.Length} unique SKUs.",
                context);

            if (!sourceSkus.Any())
            {
                await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} No valid SKUs found. Returning empty data.",
                    context);
                return new ExternalPriceBookData(
                    new Dictionary<string, (uint kqmProductId, decimal recommendedSellPrice, string title, string
                        shortDescription, string longDescription, List<string> errors)>(),
                    new Dictionary<string, (string title, string shortDescription, string longDescription, List<byte[]>
                        images)>(), new Dictionary<string, decimal>());
            }

            // 1. Get KQM Product IDs, Recommended Sell Prices, and Descriptions
            await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Fetching KQM data.", context);
            Dictionary<string, (uint kqmProductId, decimal recommendedSellPrice, string title, string shortDescription,
                string longDescription, List<string> errors)> skuToKqmDataMap;
            var kqmResult =
                await PreflightFunctions.GetKqmProductIdAndRecommendedSellPriceForSkus(sourceSkus, kaseyaApi);
            if (kqmResult.IsSuccess)
            {
                skuToKqmDataMap = kqmResult.Value.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                await LogMessageAsync(dbDatafeed, null,
                    $"{batchLogPrefix} Successfully fetched KQM data for {skuToKqmDataMap.Count} SKUs.", context);
            }
            else
            {
                skuToKqmDataMap =
                    new Dictionary<string, (uint kqmProductId, decimal recommendedSellPrice, string title, string
                        shortDescription, string longDescription, List<string> errors)>();
                await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Failed to fetch KQM data: {kqmResult.Error}",
                    context);
            }

            // 2. Get Icecat Descriptions (and Images - ignored)
            await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Fetching Icecat data.", context);
            // Group by SKU (case-insensitive) and select the first valid Brand for each unique SKU
            var skusForIcecat = priceBookItemsBatch
                .Where(pb => !string.IsNullOrWhiteSpace(pb.ProductSKU)) // Ensure SKU is valid
                .GroupBy(pb => pb.ProductSKU, StringComparer.OrdinalIgnoreCase) // Group by SKU
                .Select(g => new KeyValuePair<string, string>(
                    g.Key, // The unique SKU
                    g.FirstOrDefault(pb => !string.IsNullOrWhiteSpace(pb.Brand))
                        ?.Brand // Find the first non-empty Brand
                ))
                .Where(kvp => !string.IsNullOrWhiteSpace(kvp.Value)) // Ensure a valid Brand was found
                .ToArray(); // Convert to array
            Dictionary<string, (string title, string shortDescription, string longDescription, List<byte[]> images)>
                skuToIcecatDataMap;
            if (skusForIcecat.Any())
            {
                var icecatResult =
                    await PreflightFunctions.GetProductDescriptionAndImagesFromIcecat(skusForIcecat, icecatService,
                        httpClient);
                if (icecatResult.IsSuccess)
                {
                    skuToIcecatDataMap = icecatResult.Value.ToDictionary(t => t.sku,
                        t => (t.title, t.shortDescription, t.longDescription, t.images));
                    await LogMessageAsync(dbDatafeed, null,
                        $"{batchLogPrefix} Successfully fetched Icecat data for {skuToIcecatDataMap.Count} SKUs.",
                        context);
                }
                else
                {
                    skuToIcecatDataMap =
                        new Dictionary<string, (string title, string shortDescription, string longDescription,
                            List<byte[]> images)>();
                    await LogMessageAsync(dbDatafeed, null,
                        $"{batchLogPrefix} Failed to fetch Icecat data: {icecatResult.Error}", context);
                }
            }
            else
            {
                skuToIcecatDataMap =
                    new Dictionary<string, (string title, string shortDescription, string longDescription, List<byte[]>
                        images)>();
                await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} No SKU/Brand pairs for Icecat.", context);
            }

            // 3. Get Fandoogle Reflector Pricing
            await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Fetching Fandoogle pricing.", context);
            Dictionary<string, decimal> skuToFandooglePriceMap;
            var pricingResult = await PreflightFunctions.GetFandoogleReflectorPricing(sourceSkus, fandoogleService);
            if (pricingResult.IsSuccess)
            {
                skuToFandooglePriceMap = pricingResult.Value.ToDictionary(t => t.sku, t => t.sellPrice);
                await LogMessageAsync(dbDatafeed, null,
                    $"{batchLogPrefix} Successfully fetched Fandoogle pricing for {skuToFandooglePriceMap.Count} SKUs.",
                    context);
            }
            else
            {
                skuToFandooglePriceMap = new Dictionary<string, decimal>();
                await LogMessageAsync(dbDatafeed, null,
                    $"{batchLogPrefix} Failed to fetch Fandoogle pricing: {pricingResult.Error}", context);
            }

            await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Finished fetching external data.", context);
            return new ExternalPriceBookData(skuToKqmDataMap, skuToIcecatDataMap, skuToFandooglePriceMap);
        }

        /// <summary>
        /// Processes a single Price Book item, updating descriptions and price if necessary.
        /// </summary>
        private static async Task ProcessSinglePriceBookItemAsync(
            ImportedItemsFromPriceBook item,
            ExternalPriceBookData externalData,
            Random random,
            EdunetDatafeedsEntities dbDatafeed,
            HttpClient httpClient,
            PerformContext context)
        {
            var itemId = item.ID;
            using var sha256 = SHA256.Create(); // For image checksums
            var sku = item.ProductSKU ?? "N/A";
            var logPrefix = $"[ProcessSinglePB][ID:{itemId}, SKU:{sku}]";
            await LogMessageAsync(dbDatafeed, null, $"{logPrefix} Starting.", context);

            var itemNeedsUpdate = false;
            var needsDescriptionUpdate = string.IsNullOrWhiteSpace(item.ProductDescription);
            var needsShortDescriptionUpdate = string.IsNullOrWhiteSpace(item.ProductShortDescription);
            var needsPriceUpdate = item.ProductPrice == 0m;

            await LogMessageAsync(dbDatafeed, null,
                $"{logPrefix} Needs Update: Desc={needsDescriptionUpdate}, ShortDesc={needsShortDescriptionUpdate}, Price={needsPriceUpdate}.",
                context);

            // Fetch data for the specific SKU
            externalData.SkuToIcecatDataMap.TryGetValue(sku, out var icecatData);
            externalData.SkuToKqmDataMap.TryGetValue(sku, out var kqmData);
            externalData.SkuToFandooglePriceMap.TryGetValue(sku, out var fandooglePrice);

            // 1. Update Descriptions (only if currently missing)
            if (needsDescriptionUpdate)
            {
                if (!string.IsNullOrWhiteSpace(icecatData.longDescription))
                {
                    item.ProductDescription = icecatData.longDescription;
                    itemNeedsUpdate = true;
                    await LogMessageAsync(dbDatafeed, null, $"{logPrefix} Updated ProductDescription from Icecat.",
                        context);
                }
                else if (!string.IsNullOrWhiteSpace(kqmData.longDescription))
                {
                    item.ProductDescription = kqmData.longDescription;
                    itemNeedsUpdate = true;
                    await LogMessageAsync(dbDatafeed, null, $"{logPrefix} Updated ProductDescription from KQM.",
                        context);
                }
                else
                {
                    await LogMessageAsync(dbDatafeed, null,
                        $"{logPrefix} No long description found from Icecat or KQM.", context);
                }
            }

            if (needsShortDescriptionUpdate)
            {
                if (!string.IsNullOrWhiteSpace(icecatData.shortDescription))
                {
                    item.ProductShortDescription = icecatData.shortDescription;
                    itemNeedsUpdate = true;
                    await LogMessageAsync(dbDatafeed, null, $"{logPrefix} Updated ProductShortDescription from Icecat.",
                        context);
                }
                else if (!string.IsNullOrWhiteSpace(kqmData.shortDescription))
                {
                    item.ProductShortDescription = kqmData.shortDescription;
                    itemNeedsUpdate = true;
                    await LogMessageAsync(dbDatafeed, null, $"{logPrefix} Updated ProductShortDescription from KQM.",
                        context);
                }
                else
                {
                    await LogMessageAsync(dbDatafeed, null,
                        $"{logPrefix} No short description found from Icecat or KQM.", context);
                }
            }

            // 2. Update Price (only if currently zero)
            if (needsPriceUpdate)
            {
                var fandooglePriceSet = false; // Track if Fandoogle price was set
                if (fandooglePrice > 0)
                {
                    // Use Fandoogle price (randomised) for caching
                    var randomisedPrice = fandooglePrice - PriceDecrements[random.Next(PriceDecrements.Length)];
                    var effectiveRandomisedPrice =
                        randomisedPrice > 0 ? randomisedPrice : 0.01m; // Ensure price is positive
                    item.ProductPrice = effectiveRandomisedPrice;
                    itemNeedsUpdate = true;
                    fandooglePriceSet = true; // Mark that Fandoogle processing happened
                    await LogMessageAsync(dbDatafeed, null,
                        $"{logPrefix} Processed Fandoogle price (Original: {fandooglePrice}, Randomised for cache: {effectiveRandomisedPrice}).",
                        context);

                    // Create/Update cache entry
                    var expiryDate = DateTime.UtcNow.AddDays(30);
                    var existingCacheEntry = await dbDatafeed.FandoogleReflectorCache
                        .FirstOrDefaultAsync(c => c.ProductSku == item.ProductSKU);

                    if (existingCacheEntry != null)
                    {
                        await LogMessageAsync(dbDatafeed, null, $"{logPrefix} Updating existing Fandoogle cache entry.",
                            context);
                        existingCacheEntry.RandomisedPrice = effectiveRandomisedPrice;
                        existingCacheEntry.OriginalPrice = fandooglePrice;
                        existingCacheEntry.ExpiresAt = expiryDate;
                    }
                    else
                    {
                        await LogMessageAsync(dbDatafeed, null, $"{logPrefix} Creating new Fandoogle cache entry.",
                            context);
                        var newCacheEntry = new FandoogleReflectorCache
                        {
                            ProductSku = item.ProductSKU,
                            RandomisedPrice = effectiveRandomisedPrice,
                            OriginalPrice = fandooglePrice,
                            ExpiresAt = expiryDate
                        };
                        dbDatafeed.FandoogleReflectorCache.Add(newCacheEntry);
                    }
                }

                // Always check and potentially use KQM price
                if (kqmData.recommendedSellPrice > 0)
                {
                    // Use KQM recommended price for the item itself
                    item.ProductPrice = kqmData.recommendedSellPrice;
                    itemNeedsUpdate = true;
                    await LogMessageAsync(dbDatafeed, null,
                        $"{logPrefix} Updated ProductPrice from KQM ({item.ProductPrice}). This overrides Fandoogle price if previously set.",
                        context);
                }
                else if (!fandooglePriceSet) // Only log 'no price found' if neither Fandoogle nor KQM provided one
                {
                    await LogMessageAsync(dbDatafeed, null,
                        $"{logPrefix} No positive price found from Fandoogle or KQM.", context);
                }
            }

            // 3. Process Images (from ImageURLs and Icecat)

            // Fetch existing images and calculate checksums to avoid duplicates
            await LogMessageAsync(dbDatafeed, null, $"{logPrefix} Fetching existing images and calculating checksums.",
                context);
            var existingImages = await dbDatafeed.PriceBookItemImages
                .Where(img => img.PriceBookItemID == item.ID)
                .Select(img => new { img.ImageURL, img.Image }) // Select URL and Bytes
                .ToListAsync();

            var existingDbImageUrlsSet = new HashSet<string>(
                existingImages.Where(img => !string.IsNullOrWhiteSpace(img.ImageURL)).Select(img => img.ImageURL),
                StringComparer.OrdinalIgnoreCase);

            var existingImageChecksums = new HashSet<string>();
            foreach (var existingImage in existingImages.Where(img => img.Image is { Length: > 0 }))
            {
                var checksumBytes = sha256.ComputeHash(existingImage.Image);
                existingImageChecksums.Add(Convert.ToBase64String(checksumBytes));
            }

            await LogMessageAsync(dbDatafeed, null,
                $"{logPrefix} Found {existingDbImageUrlsSet.Count} existing image URLs and {existingImageChecksums.Count} unique image checksums.",
                context);

            // 3a. Process ImageURLs
            if (!string.IsNullOrWhiteSpace(item.ImageURLs))
            {
                await LogMessageAsync(dbDatafeed, null, $"{logPrefix} Processing ImageURLs: {item.ImageURLs}", context);
                var imageUrls = item.ImageURLs.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(url => url.Trim())
                    .Where(url =>
                        !string.IsNullOrWhiteSpace(url) && (url.StartsWith("http://") || url.StartsWith("https://")))
                    .Distinct(); // Process unique URLs only

                foreach (var imageUrl in imageUrls)
                {
                    if (existingDbImageUrlsSet.Contains(imageUrl))
                    {
                        await LogMessageAsync(dbDatafeed, null,
                            $"{logPrefix} Skipping image download, URL already exists in DB: {imageUrl}", context);
                        continue; // Skip if this URL is already saved for this item
                    }

                    await LogMessageAsync(dbDatafeed, null,
                        $"{logPrefix} Attempting to download image from URL: {imageUrl}", context);
                    try
                    {
                        var response = await httpClient.GetAsync(imageUrl);
                        if (response.IsSuccessStatusCode)
                        {
                            var imageBytes = await response.Content.ReadAsByteArrayAsync();
                            if (imageBytes is { Length: > 0 })
                            {
                                // Check checksum for duplicates before adding
                                var checksumBytes = sha256.ComputeHash(imageBytes);
                                var newImageChecksum = Convert.ToBase64String(checksumBytes);

                                if (existingImageChecksums.Contains(newImageChecksum))
                                {
                                    await LogMessageAsync(dbDatafeed, null,
                                        $"{logPrefix} Skipping image download, duplicate content checksum found for URL: {imageUrl}",
                                        context);
                                }
                                else
                                {
                                    var newImage = new PriceBookItemImages
                                    {
                                        PriceBookItemID = item.ID,
                                        ImageURL = imageUrl,
                                        Image = imageBytes
                                    };
                                    dbDatafeed.PriceBookItemImages.Add(newImage);
                                    existingImageChecksums.Add(newImageChecksum); // Add checksum to set
                                    itemNeedsUpdate = true; // Mark that an update occurred (image added)
                                    await LogMessageAsync(dbDatafeed, null,
                                        $"{logPrefix} Successfully downloaded and added image from URL: {imageUrl}",
                                        context);
                                }

                                existingDbImageUrlsSet.Add(imageUrl);
                            }
                            else
                            {
                                await LogMessageAsync(dbDatafeed, null,
                                    $"{logPrefix} Downloaded image was empty from URL: {imageUrl}", context);
                            }
                        }
                        else
                        {
                            await LogMessageAsync(dbDatafeed, null,
                                $"{logPrefix} Failed to download image. Status code: {response.StatusCode} from URL: {imageUrl}",
                                context);
                        }
                    }
                    catch (Exception ex)
                    {
                        await LogMessageAsync(dbDatafeed, null,
                            $"{logPrefix} Exception during image download from URL {imageUrl}: {ex.Message}", context);
                    }
                }
            }
            else
            {
                await LogMessageAsync(dbDatafeed, null, $"{logPrefix} No ImageURLs found for this item.", context);
            }

            // 3b. Process Icecat Images
            if (icecatData.images != null && icecatData.images.Any())
            {
                await LogMessageAsync(dbDatafeed, null,
                    $"{logPrefix} Processing {icecatData.images.Count} images from Icecat.", context);
                foreach (var icecatImageBytes in icecatData.images)
                {
                    if (icecatImageBytes == null || icecatImageBytes.Length == 0)
                    {
                        await LogMessageAsync(dbDatafeed, null, $"{logPrefix} Skipping empty/null image from Icecat.",
                            context);
                        continue;
                    }

                    var checksumBytes = sha256.ComputeHash(icecatImageBytes);
                    var newImageChecksum = Convert.ToBase64String(checksumBytes);

                    if (existingImageChecksums.Contains(newImageChecksum))
                    {
                        await LogMessageAsync(dbDatafeed, null,
                            $"{logPrefix} Skipping duplicate Icecat image (Checksum: {newImageChecksum}).", context);
                        continue;
                    }

                    await LogMessageAsync(dbDatafeed, null,
                        $"{logPrefix} Adding new image from Icecat (Checksum: {newImageChecksum}).", context);
                    var newImage = new PriceBookItemImages
                    {
                        PriceBookItemID = item.ID,
                        ImageURL = null, // No URL for Icecat images
                        Image = icecatImageBytes
                    };
                    dbDatafeed.PriceBookItemImages.Add(newImage);
                    existingImageChecksums.Add(newImageChecksum); // Add checksum to set
                    itemNeedsUpdate = true; // Mark that an update occurred
                }
            }

            if (!itemNeedsUpdate)
            {
                await LogMessageAsync(dbDatafeed, null, $"{logPrefix} No updates were applied.", context);
            }

            await LogMessageAsync(dbDatafeed, null, $"{logPrefix} Finished.", context);
            // Note: Saving changes will happen outside this function, likely after processing a batch.
        }

        // Use a constant resource name to ensure only one batch runs at a time across all workers.
        [DisableConcurrentExecution("preflight-batch-processing", 60 * 60 * 8)]
        [SkipWhenPreviousJobIsRunning]
        public static async Task ProcessProductSourceBatch(
            List<int> productSourceIds,
            PerformContext context,
            EdunetDatafeedsEntities diDbDatafeed = null,
            KaseyaApi diKaseyaApi = null,
            IcecatApiService diIcecatApiService = null,
            Service diFandoogleReflectorService = null,
            Library diAiLibrary = null,
            HttpClient diHttpClient = null,
            bool isUnderTest = false)
        {
            using var dbDatafeed = diDbDatafeed ?? new EdunetDatafeedsEntities();
            KaseyaApi kaseyaApi;
            if (diKaseyaApi != null)
            {
                kaseyaApi = diKaseyaApi;
            }
            else
            {
                var currentHour = DateTime.Now.Hour;
                if (currentHour is >= 18 or < 6)
                {
                    const uint requestsPerMinute = 50;
                    kaseyaApi = new KaseyaApi(Settings.Default.KQMBaseUrl, Settings.Default.KQMApiKey,
                        requestsPerMinute);
                }
                else
                {
                    kaseyaApi = KaseyaApiSingleton.Instance;
                }
            }

            var icecatService = diIcecatApiService ?? IcecatApiServiceSingleton.Instance;
            var fandoogleService = diFandoogleReflectorService ?? FandoogleReflectorSingleton.Instance;
            var aiLibrary = diAiLibrary ?? AILibrarySingleton.Instance;
            var httpClient = diHttpClient ?? new HttpClient
            {
                BaseAddress = new Uri(Debugger.IsAttached ? "https://localhost:5001/" : Settings.Default.NopLiveSiteUrl)
            };
            using var sha256 = SHA256.Create();
            var random = new Random();

            var batchLogPrefix = $"[ProcessBatch][IDs:{string.Join(",", productSourceIds)}]";
            await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Starting processing.", context);

            try
            {
                // Fetch the actual ProductSource entities for this batch
                var currentBatch = await dbDatafeed.ProductSource
                    .Where(ps => productSourceIds.Contains(ps.ID))
                    .Include(productSource => productSource.ProductSourceImages)
                    .ToListAsync();

                if (!currentBatch.Any())
                {
                    await LogMessageAsync(dbDatafeed, null,
                        $"{batchLogPrefix} No sources found for the provided IDs. Exiting.", context);
                    return;
                }

                await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Fetched {currentBatch.Count} sources.",
                    context);

                // --- Start: Logic moved from Run method's loop ---

                // Fetch prerequisite data for the current batch
                await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Fetching prerequisite data.", context);
                var prerequisiteData = await FetchPrerequisiteDataForBatchAsync(
                    currentBatch,
                    dbDatafeed,
                    fandoogleService,
                    httpClient,
                    null, // Explicitly pass null for kaseyaScraper
                    aiLibrary,
                    isUnderTest,
                    context);
                await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Fetched prerequisite data.", context);

                // Get source SKUs for external data
                var sourceSkus = currentBatch
                    .Select(s => s.ProductSku)
                    .Where(sku => !string.IsNullOrWhiteSpace(sku))
                    .Distinct()
                    .ToArray();
                await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Identified {sourceSkus.Length} unique SKUs.",
                    context);

                // Fetch external product data for the current batch
                await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Fetching external product data.", context);
                var externalData = await FetchExternalProductDataAsync(
                    sourceSkus,
                    currentBatch,
                    kaseyaApi,
                    icecatService,
                    httpClient,
                    dbDatafeed,
                    context);
                await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Fetched external product data.", context);

                // Calculate image checksums for the current batch (used for deduplication)
                await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Calculating existing image checksums.",
                    context);
                var existingImageChecksums = CalculateExistingImageChecksums(
                    currentBatch,
                    sha256);
                await LogMessageAsync(dbDatafeed, null,
                    $"{batchLogPrefix} Calculated {existingImageChecksums.Count} unique existing image checksums.",
                    context);

                // Process each ProductSource in the current batch
                await LogMessageAsync(dbDatafeed, null,
                    $"{batchLogPrefix} Starting processing loop for {currentBatch.Count} sources.", context);
                foreach (var source in currentBatch)
                {
                    // Ensure the source is still in a processable state (e.g., New) before processing
                    if (source.Status != (int)ProductSourceStatuses.New)
                    {
                        await LogMessageAsync(dbDatafeed, source.ID,
                            $"{batchLogPrefix} Skipping source ID: {source.ID}, SKU: {source.ProductSku} as its status is already {source.Status}.",
                            context);
                        continue;
                    }

                    await LogMessageAsync(dbDatafeed, source.ID,
                        $"{batchLogPrefix} Processing source ID: {source.ID}, SKU: {source.ProductSku}", context);
                    await UpdateSourceStatusAndSaveChanges(
                        source,
                        dbDatafeed,
                        prerequisiteData,
                        externalData,
                        existingImageChecksums,
                        random, // Use the batch-specific random instance
                        sha256,
                        isUnderTest,
                        context);
                    await LogMessageAsync(dbDatafeed, source.ID,
                        $"{batchLogPrefix} Finished processing source ID: {source.ID}. Final Status: {source.Status}",
                        context);
                }

                await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Finished processing loop.", context);

                // --- End: Logic moved from Run method's loop ---
            }
            catch (Exception ex)
            {
                await LogMessageAsync(dbDatafeed, null,
                    $"{batchLogPrefix} Unhandled exception in batch processing: {ex.Message} --- StackTrace: {ex.StackTrace}",
                    context);
                // Optionally, mark remaining unprocessed sources in the batch as errored or retry?
                // For now, just log the error. Hangfire will handle retry based on job configuration.
                throw; // Re-throw so Hangfire knows the job failed
            }
            finally
            {
                await LogMessageAsync(dbDatafeed, null, $"{batchLogPrefix} Finished batch processing.", context);
                // Dispose context if we created it
                if (diDbDatafeed == null)
                {
                    dbDatafeed.Dispose();
                }

                // Dispose HttpClient if we created it
                if (diHttpClient == null)
                {
                    httpClient.Dispose();
                }

                // Dispose KaseyaApi if we created it
                if (diKaseyaApi == null && kaseyaApi is IDisposable disposableKaseyaApi)
                {
                    disposableKaseyaApi.Dispose();
                }
            }
        }

        [DisableConcurrentExecution(60 * 60 * 6)]
        [SkipWhenPreviousJobIsRunning]
        public static async Task Run(PerformContext context,
            EdunetDatafeedsEntities diDbDatafeed = null,
            KaseyaApi diKaseyaApi = null,
            IcecatApiService diIcecatApiService = null,
            Service diFandoogleReflectorService = null,
            Library diAiLibrary = null,
            HttpClient diHttpClient = null,
            bool isUnderTest = false)
        {
            using var timer = new Timer(_ => context?.SendHeartbeat(), null, TimeSpan.Zero, TimeSpan.FromMinutes(15));

            var dbDatafeed = diDbDatafeed ?? new EdunetDatafeedsEntities();

            KaseyaApi kaseyaApi;
            if (diKaseyaApi != null)
            {
                kaseyaApi = diKaseyaApi;
            }
            else
            {
                kaseyaApi = KaseyaApiSingleton.Instance;
                await LogMessageAsync(dbDatafeed, null,
                    "[Run] Using KaseyaApi singleton instance for Price Book processing.", context);
            }

            var icecatService = diIcecatApiService ?? IcecatApiServiceSingleton.Instance;
            var fandoogleService = diFandoogleReflectorService ?? FandoogleReflectorSingleton.Instance;
            var httpClient = diHttpClient ?? new HttpClient
            {
                BaseAddress = new Uri(Debugger.IsAttached ? "https://localhost:5001/" : Settings.Default.NopLiveSiteUrl)
            };

            try
            {
                await LogMessageAsync(dbDatafeed, null, "[Run] Starting PreflightQueueRunner.", context);

                // --- Modified ProductSource Batch Enqueuing ---
                await LogMessageAsync(dbDatafeed, null, "[Run] Starting ProductSource batch enqueuing.", context);
                const int batchSize = 20;
                var batchNumber = 0;
                var totalSourcesEnqueued = 0;

                List<int> currentBatchIds;
                do
                {
                    // Fetch only the IDs for the next batch
                    currentBatchIds = await dbDatafeed.ProductSource
                        .Where(ps => ps.Status == (int)ProductSourceStatuses.New)
                        .OrderBy(ps => ps.ID) // Ensure consistent ordering
                        .Skip(batchNumber * batchSize)
                        .Take(batchSize)
                        .Select(ps => ps.ID) // Select only IDs
                        .ToListAsync();

                    if (!currentBatchIds.Any())
                    {
                        await LogMessageAsync(dbDatafeed, null,
                            $"[Run] No more unprocessed sources found after enqueuing {totalSourcesEnqueued} sources across {batchNumber} batches.",
                            context);
                        break;
                    }

                    await LogMessageAsync(dbDatafeed, null,
                        $"[Run] Enqueuing batch {batchNumber + 1} with {currentBatchIds.Count} source IDs.", context);

                    // Enqueue the background job to process this batch of IDs
                    // Pass null for context; Hangfire provides it to the executing job if the parameter exists
                    BackgroundJob.Enqueue(() =>
                        ProcessProductSourceBatch(currentBatchIds, null, null, null, null, null, null, null,
                            isUnderTest));

                    totalSourcesEnqueued += currentBatchIds.Count;
                    batchNumber++;
                } while (currentBatchIds.Count == batchSize);

                await LogMessageAsync(dbDatafeed, null,
                    $"[Run] Finished enqueuing {totalSourcesEnqueued} sources in {batchNumber} batches.", context);

                // --- START: Price Book Item Processing ---
                await LogMessageAsync(dbDatafeed, null, "[Run][PB] Starting Price Book item processing.", context);
                const int priceBookBatchSize = 20; // Use a potentially larger batch size for PB items
                var priceBookBatchNumber = 0;
                List<ImportedItemsFromPriceBook> currentPriceBookBatch;

                do
                {
                    // Fetch the next batch of Price Book items needing updates
                    currentPriceBookBatch = await dbDatafeed.ImportedItemsFromPriceBook
                        .Where(pb => !pb.IsPublished)
                        .OrderBy(pb => pb.ID) // Ensure consistent ordering
                        .Skip(priceBookBatchNumber * priceBookBatchSize)
                        .Take(priceBookBatchSize)
                        .ToListAsync();

                    if (!currentPriceBookBatch.Any())
                    {
                        await LogMessageAsync(dbDatafeed, null,
                            "[Run][PB] No more Price Book items found needing updates. Exiting batch processing.",
                            context);
                        break;
                    }

                    var pbBatchLogPrefix = $"[Run][PB][Batch {priceBookBatchNumber + 1}]";
                    await LogMessageAsync(dbDatafeed, null,
                        $"{pbBatchLogPrefix} Processing batch with {currentPriceBookBatch.Count} Price Book items.",
                        context);

                    // Fetch external data for the current Price Book batch
                    await LogMessageAsync(dbDatafeed, null, $"{pbBatchLogPrefix} Fetching external data.", context);
                    var externalPriceBookData = await FetchExternalDataForPriceBookItemsAsync(
                        currentPriceBookBatch,
                        kaseyaApi,
                        icecatService,
                        fandoogleService,
                        httpClient,
                        dbDatafeed,
                        context);
                    await LogMessageAsync(dbDatafeed, null, $"{pbBatchLogPrefix} Fetched external data.", context);

                    var pbRandom = new Random(); // Use a separate random instance if needed

                    // Process each Price Book item in the current batch
                    await LogMessageAsync(dbDatafeed, null, $"{pbBatchLogPrefix} Starting processing loop.", context);
                    foreach (var item in currentPriceBookBatch)
                    {
                        await ProcessSinglePriceBookItemAsync(
                            item,
                            externalPriceBookData,
                            pbRandom,
                            dbDatafeed,
                            httpClient,
                            context);
                    }

                    // Save changes for the processed Price Book batch
                    await LogMessageAsync(dbDatafeed, null, $"{pbBatchLogPrefix} Saving changes for batch.", context);
                    await dbDatafeed.SaveChangesAsync();
                    await LogMessageAsync(dbDatafeed, null,
                        $"{pbBatchLogPrefix} Changes saved. Finished processing batch.", context);

                    priceBookBatchNumber++;
                } while (currentPriceBookBatch.Count == priceBookBatchSize);

                await LogMessageAsync(dbDatafeed, null,
                    $"[Run][PB] Processed {priceBookBatchNumber} Price Book batches. Price Book processing complete.",
                    context);
                // --- END: Price Book Item Processing ---
            }
            catch (Exception ex)
            {
                await LogMessageAsync(dbDatafeed, null,
                    $"[Run] Unhandled exception in PreflightQueueRunner Run method (outside batch jobs): {ex.Message} --- StackTrace: {ex.StackTrace}",
                    context);
                // Re-throw the exception so Hangfire knows the main enqueuing/PB job failed
                throw;
            }
            finally
            {
                await LogMessageAsync(dbDatafeed, null, "[Run] Exiting PreflightQueueRunner Run method.", context);

                // Dispose context if we created it
                if (diDbDatafeed == null) dbDatafeed.Dispose();
                // Dispose HttpClient if we created it
                if (diHttpClient == null) httpClient.Dispose();
                // Dispose KaseyaApi if we created it (and it's disposable)
                if (diKaseyaApi == null && kaseyaApi is IDisposable disposableKaseyaApiRun)
                    disposableKaseyaApiRun.Dispose();
            }
        }

        /// <summary>
        /// Fetches and aggregates all prerequisite data needed for processing a batch of product sources.
        /// </summary>
        /// <param name="currentBatch">The current batch of product sources to process</param>
        /// <param name="dbDatafeed">Database context for accessing product data</param>
        /// <param name="fandoogleService">Service for retrieving product pricing information</param>
        /// <param name="httpClient">HTTP client for making web requests</param>
        /// <param name="kaseyaScraper">Service for scraping Kaseya product information</param>
        /// <param name="aiLibrary">Library for AI-powered attribute extraction</param>
        /// <param name="isUnderTest">Flag indicating if running in test mode</param>
        /// <param name="context">Hangfire context for logging</param>
        /// <returns>
        /// PrerequisiteData containing:
        /// - List of product sources in the current batch
        /// - Existing AI-generated product information
        /// - Category mappings for product sources
        /// - Product pricing information
        /// - Product attributes from Kaseya
        /// - Required attributes for each category
        /// </returns>
        /// <summary>
        /// Fetches and aggregates all prerequisite data needed for processing product sources.
        /// </summary>
        /// <param name="dbDatafeed">Database context for accessing product data</param>
        /// <param name="fandoogleService">Service for retrieving product pricing information</param>
        /// <param name="httpClient">HTTP client for making web requests</param>
        /// <param name="kaseyaScraper">Service for scraping Kaseya product information</param>
        /// <param name="aiLibrary">Library for AI-powered attribute extraction</param>
        /// <param name="isUnderTest">Flag indicating if running in test mode</param>
        /// <param name="context">Hangfire context for logging</param>
        /// <returns>
        /// PrerequisiteData containing:
        /// - List of unprocessed product sources
        /// - Existing AI-generated product information
        /// - Category mappings for product sources
        /// - Product pricing information
        /// - Product attributes from Kaseya
        /// - Required attributes for each category
        /// </returns>
        [Obsolete("Use FetchPrerequisiteDataForBatchAsync instead which processes sources in batches")]
        private static async Task<PrerequisiteData> FetchPrerequisiteDataAsync(
            EdunetDatafeedsEntities dbDatafeed,
            Service fandoogleService,
            HttpClient httpClient,
            IKaseyaEstoreScraperService _,
            Library aiLibrary,
            bool isUnderTest,
            PerformContext context)
        {
            await LogMessageAsync(dbDatafeed, null, "[FetchPrereq] Starting.", context);

            // 1. Get unprocessed product sources
            await LogMessageAsync(dbDatafeed, null,
                "[FetchPrereq][Step 1.1] Fetching unprocessed ProductSource entries.", context);
            var unprocessedSources = await dbDatafeed.ProductSource
                .Where(ps => ps.Status == (int)ProductSourceStatuses.New)
                .Include(productSource => productSource.ProductSourceImages)
                .ToListAsync();
            await LogMessageAsync(dbDatafeed, null,
                $"[FetchPrereq][Step 1.1] Found {unprocessedSources.Count} unprocessed sources.", context);

            if (!unprocessedSources.Any())
            {
                await LogMessageAsync(dbDatafeed, null,
                    "[FetchPrereq] No unprocessed sources found. Returning empty data.", context);
                return new PrerequisiteData(
                    unprocessedSources,
                    new Dictionary<int, HashSet<string>>(),
                    new Dictionary<int, string>(),
                    new Dictionary<string, decimal>(),
                    new Dictionary<string, string[]>(),
                    new Dictionary<string, string[]>(),
                    []);
            }

            // Call the batch version with all unprocessed sources
            return await FetchPrerequisiteDataForBatchAsync(
                unprocessedSources,
                dbDatafeed,
                fandoogleService,
                httpClient,
                null, // Explicitly pass null for kaseyaScraper
                aiLibrary,
                isUnderTest,
                context);
        }

        private static async Task<PrerequisiteData> FetchPrerequisiteDataForBatchAsync(
            List<ProductSource> currentBatch,
            EdunetDatafeedsEntities dbDatafeed,
            Service fandoogleService,
            HttpClient httpClient,
            IKaseyaEstoreScraperService _, // Keep parameter for signature compatibility, but ignore it
            Library aiLibrary,
            bool isUnderTest,
            PerformContext context)
        {
            await LogMessageAsync(dbDatafeed, null, "[FetchPrereq] Starting for batch.", context);

            // Use the provided batch instead of querying for unprocessed sources
            await LogMessageAsync(dbDatafeed, null,
                $"[FetchPrereq][Step 1.1] Processing batch with {currentBatch.Count} sources.", context);

            if (!currentBatch.Any())
            {
                await LogMessageAsync(dbDatafeed, null,
                    "[FetchPrereq] Empty batch. Returning empty data.", context);
                return new PrerequisiteData(
                    currentBatch,
                    new Dictionary<int, HashSet<string>>(),
                    new Dictionary<int, string>(),
                    new Dictionary<string, decimal>(),
                    new Dictionary<string, string[]>(),
                    new Dictionary<string, string[]>(),
                    []);
            }

            // 2. Fetch existing AI Info for unprocessed sources
            await LogMessageAsync(dbDatafeed, null, "[FetchPrereq][Step 1.2] Fetching existing AI info.", context);
            var unprocessedSourceIds = currentBatch.Select(s => s.ID).ToList();
            var groupedAiInfo = await dbDatafeed.AIGeneratedProductInfo
                .Where(ai =>
                    unprocessedSourceIds.Contains(ai.ProductId) && ai.ProductSource == ProductType.ProductSource)
                .GroupBy(ai => ai.ProductId)
                .Select(g => new
                {
                    ProductId = g.Key,
                    Attributes = g.Select(ai => ai.Attribute).Distinct()
                })
                .ToListAsync();

            var existingAiInfo = groupedAiInfo
                .ToDictionary(
                    x => x.ProductId,
                    x => new HashSet<string>(x.Attributes)
                );
            await LogMessageAsync(dbDatafeed, null,
                $"[FetchPrereq][Step 1.2] Found existing AI info for {existingAiInfo.Count} sources.", context);

            // 3. Fetch category mappings based on SourceTypeIds (CSV)
            await LogMessageAsync(dbDatafeed, null,
                "[FetchPrereq][Step 1.3] Fetching category mappings based on SourceTypeIds.", context);
            var allSourceTypeIds = currentBatch
                .SelectMany(s => (s.SourceTypeIds ?? string.Empty).Split(',')) // Split CSV string, handle null
                .Select(idStr => idStr.Trim()) // Trim whitespace
                .Where(idStr => int.TryParse(idStr, out var _)) // Ensure it's a valid integer string
                .Select(int.Parse) // Convert to integer
                .Distinct() // Get unique IDs
                .ToArray();
            await LogMessageAsync(dbDatafeed, null,
                $"[FetchPrereq][Step 1.3] Found {allSourceTypeIds.Length} unique SourceTypeIds across the batch.",
                context);

            var sourceMappings = new Dictionary<int, string>(); // Initialise empty dictionary
            if (allSourceTypeIds.Any())
            {
                sourceMappings = await dbDatafeed.ProductSourcesForNopCategory
                    .Where(m => allSourceTypeIds.Contains(m.SourceTypeId))
                    .ToDictionaryAsync(m => m.ID, m => m.CategoryName); // Key by Product Source ID
            }

            await LogMessageAsync(dbDatafeed, null,
                $"[FetchPrereq][Step 1.3] Found {sourceMappings.Count} category mappings for the unique SourceTypeIds.",
                context);

            // 4. Get Fandoogle Reflector Pricing
            await LogMessageAsync(dbDatafeed, null, "[FetchPrereq][Step 1.4] Fetching Fandoogle Reflector pricing.",
                context);
            var allSourceSkus = currentBatch
                .Select(s => s.ProductSku)
                .Where(sku => !string.IsNullOrWhiteSpace(sku))
                .Distinct()
                .ToArray();
            await LogMessageAsync(dbDatafeed, null,
                $"[FetchPrereq][Step 1.4] Identified {allSourceSkus.Length} total unique SKUs from unprocessed sources.",
                context);

            // Find SKUs with valid (non-expired) cache entries
            var utcNow = DateTime.UtcNow;
            // Ensure skusWithValidCache is initialised as a HashSet
            var skusWithValidCache = new HashSet<string>(await dbDatafeed.FandoogleReflectorCache
                .Where(c => allSourceSkus.Contains(c.ProductSku) && c.ExpiresAt > utcNow)
                .Select(c => c.ProductSku)
                .Distinct()
                .ToListAsync());
            await LogMessageAsync(dbDatafeed, null,
                $"[FetchPrereq][Step 1.4] Found {skusWithValidCache.Count} SKUs with valid Fandoogle cache entries.",
                context);

            // Filter SKUs to only fetch those without a valid cache entry
            var sourceSkusForPricing = allSourceSkus
                .Except(skusWithValidCache)
                .ToArray();
            await LogMessageAsync(dbDatafeed, null,
                $"[FetchPrereq][Step 1.4] Identified {sourceSkusForPricing.Length} unique SKUs requiring Fandoogle pricing fetch.",
                context);

            Dictionary<string, decimal> skuToPriceMap;
            if (sourceSkusForPricing.Any())
            {
                var pricingResult =
                    await PreflightFunctions.GetFandoogleReflectorPricing(sourceSkusForPricing, fandoogleService);
                if (pricingResult.IsSuccess)
                {
                    skuToPriceMap = pricingResult.Value.ToDictionary(t => t.sku, t => t.sellPrice);
                    await LogMessageAsync(dbDatafeed, null,
                        $"[FetchPrereq][Step 1.4] Successfully fetched Fandoogle pricing for {skuToPriceMap.Count} SKUs.",
                        context);
                }
                else
                {
                    skuToPriceMap = new Dictionary<string, decimal>();
                    await LogMessageAsync(dbDatafeed, null,
                        $"[FetchPrereq][Step 1.4] Failed to fetch Fandoogle pricing: {pricingResult.Error}", context);
                }
            }
            else
            {
                skuToPriceMap = new Dictionary<string, decimal>();
                await LogMessageAsync(dbDatafeed, null,
                    "[FetchPrereq][Step 1.4] No SKUs require Fandoogle pricing fetch.",
                    context);
            }

            // 5. Get required attributes for categories - SKIPPED
            var categoryAttributesMap = new Dictionary<string, string[]>(); // Initialise as empty
            await LogMessageAsync(dbDatafeed, null,
                "[FetchPrereq][Step 1.5] SKIPPED fetching required attributes for categories.", context);

            // 6. Get Kaseya attributes - SKIPPED
            var skuToAttributesMap = new Dictionary<string, string[]>(); // Initialise as empty
            await LogMessageAsync(dbDatafeed, null, "[FetchPrereq][Step 1.6] SKIPPED fetching Kaseya attributes.",
                context);

            await LogMessageAsync(dbDatafeed, null, "[FetchPrereq] Finished fetching prerequisite data.", context);
            return new PrerequisiteData(
                currentBatch,
                existingAiInfo,
                sourceMappings,
                skuToPriceMap,
                skuToAttributesMap,
                categoryAttributesMap ?? new Dictionary<string, string[]>(),
                skusWithValidCache);
        }

        /// <summary>
        /// Fetches product data from external services (Icecat and KQM) for a set of product SKUs
        /// </summary>
        /// <param name="sourceSkus">Array of product SKUs to fetch data for</param>
        /// <param name="unprocessedSources">List of unprocessed product sources containing SKU and brand information</param>
        /// <param name="kaseyaApi">API client for accessing Kaseya services</param>
        /// <param name="icecatService">Service for retrieving product information from Icecat</param>
        /// <param name="httpClient">HTTP client for making web requests</param>
        /// <param name="dbDatafeed">Database context for logging</param>
        /// <param name="context">Hangfire context for logging</param>
        /// <returns>
        /// ExternalProductData containing:
        /// - Mapping of SKUs to KQM product IDs
        /// - Array of unique KQM product IDs
        /// - Mapping of KQM IDs to stock counts
        /// - Mapping of KQM IDs to product images
        /// - Mapping of KQM IDs to brand names
        /// - Mapping of SKUs to Icecat product data (title, descriptions and images)
        /// </returns>
        private static async Task<ExternalProductData> FetchExternalProductDataAsync(
            string[] sourceSkus,
            List<ProductSource> unprocessedSources,
            KaseyaApi kaseyaApi,
            IcecatApiService icecatService,
            HttpClient httpClient,
            EdunetDatafeedsEntities dbDatafeed,
            PerformContext context)
        {
            await LogMessageAsync(dbDatafeed, null, "[FetchExternal] Starting for batch.", context);

            // 1. Prepare for Icecat call
            await LogMessageAsync(dbDatafeed, null, "[FetchExternal][Step 3.1] Preparing SKUs for Icecat.", context);
            var skusForIcecat = unprocessedSources
                .Select(s => new KeyValuePair<string, string>(s.ProductSku, s.Brand))
                .Where(kvp => !string.IsNullOrWhiteSpace(kvp.Key) && !string.IsNullOrWhiteSpace(kvp.Value))
                .ToArray();
            await LogMessageAsync(dbDatafeed, null,
                $"[FetchExternal][Step 3.1] Prepared {skusForIcecat.Length} SKU/Brand pairs for Icecat.", context);

            // 2. Get Icecat Descriptions and Images
            await LogMessageAsync(dbDatafeed, null,
                "[FetchExternal][Step 3.2] Fetching Icecat descriptions and images.", context);
            var icecatResult = skusForIcecat.Any()
                ? await PreflightFunctions.GetProductDescriptionAndImagesFromIcecat(skusForIcecat, icecatService,
                    httpClient)
                : Result.Success(
                    new List<(string sku, string title, string shortDescription, string longDescription, List<byte[]>
                        images)>());

            Dictionary<string, (string title, string shortDescription, string longDescription, List<byte[]> images)>
                skuToIcecatDataMap;
            if (icecatResult.IsSuccess)
            {
                skuToIcecatDataMap = icecatResult.Value.ToDictionary(t => t.sku,
                    t => (t.title, t.shortDescription, t.longDescription, t.images));
                await LogMessageAsync(dbDatafeed, null,
                    $"[FetchExternal][Step 3.2] Successfully fetched Icecat data for {skuToIcecatDataMap.Count} SKUs.",
                    context);
            }
            else
            {
                skuToIcecatDataMap =
                    new Dictionary<string, (string title, string shortDescription, string longDescription, List<byte[]>
                        images)>();
                await LogMessageAsync(dbDatafeed, null,
                    $"[FetchExternal][Step 3.2] Failed to fetch Icecat data: {icecatResult.Error}", context);
            }

            // 3. Get KQM Product IDs, Recommended Sell Prices, and Descriptions for SKUs
            await LogMessageAsync(dbDatafeed, null,
                "[FetchExternal][Step 3.3] Fetching KQM Product IDs, recommended prices, and descriptions.", context);
            Dictionary<string, (uint kqmProductId, decimal recommendedSellPrice, string title, string shortDescription,
                string longDescription)> skuToKqmIdAndPriceMap; // Added title
            uint[] kqmProductIds;
            var kqmIdPriceDescResult =
                await PreflightFunctions.GetKqmProductIdAndRecommendedSellPriceForSkus(sourceSkus, kaseyaApi);
            if (kqmIdPriceDescResult.IsFailure)
            {
                await LogMessageAsync(dbDatafeed, null,
                    $"[FetchExternal][Step 3.3] Failed to fetch KQM IDs/Prices/Descriptions: {kqmIdPriceDescResult.Error}",
                    context);
                skuToKqmIdAndPriceMap =
                    new Dictionary<string, (uint kqmProductId, decimal recommendedSellPrice, string title, string
                        shortDescription, string longDescription)>();
                kqmProductIds = [];
            }
            else
            {
                // Filter out entries with errors before creating the dictionary, and log errors.
                var validKqmEntries =
                    new List<KeyValuePair<string, (uint kqmProductId, decimal recommendedSellPrice, string title, string
                        shortDescription, string longDescription, List<string> errors)>>();
                foreach (var kvp in kqmIdPriceDescResult.Value)
                {
                    if (kvp.Value.errors != null && kvp.Value.errors.Any())
                    {
                        foreach (var error in kvp.Value.errors)
                        {
                            await LogMessageAsync(dbDatafeed,
                                null, // Assuming no specific source ID for this general fetch log
                                $"[FetchExternal][Step 3.3][KQM Error SKU: {kvp.Key}] {error}",
                                context);
                        }
                        // Optionally, decide if you want to exclude this SKU from further processing
                        // For now, we'll still include it in the map if it has an ID, but it will carry no valid data if errors occurred.
                        // If an entry has errors but kqmProductId is 0, it effectively won't be used successfully later anyway.
                    }

                    // We still add it to a temporary list to build the map,
                    // the `kvp.Value` which is a tuple still contains the kqmProductId, recommendedSellPrice etc.
                    // these would be 0 or empty if errors occurred during their fetch.
                    validKqmEntries.Add(kvp);
                }

                skuToKqmIdAndPriceMap = validKqmEntries.ToDictionary(
                    kvp => kvp.Key,
                    kvp => (kvp.Value.kqmProductId, kvp.Value.recommendedSellPrice, kvp.Value.title,
                        kvp.Value.shortDescription, kvp.Value.longDescription)
                );

                kqmProductIds = skuToKqmIdAndPriceMap.Values
                    .Where(v => v.kqmProductId > 0) // Only consider valid KQM Product IDs
                    .Select(v => v.kqmProductId).Distinct().ToArray();

                await LogMessageAsync(dbDatafeed, null,
                    $"[FetchExternal][Step 3.3] Successfully processed KQM IDs/Prices/Descriptions for {skuToKqmIdAndPriceMap.Count} SKUs. Found {kqmProductIds.Length} unique KQM IDs with non-zero ID. Errors (if any) were logged individually.",
                    context);
            }

            // 4. Get KQM Stock Counts and Cost Prices
            await LogMessageAsync(dbDatafeed, null,
                $"[FetchExternal][Step 3.4] Fetching KQM stock/cost for {kqmProductIds.Length} KQM IDs.", context);
            Dictionary<uint, (uint stockCount, decimal costPrice)> kqmIdToStockAndCostMap;
            if (kqmProductIds.Any())
            {
                var stockAndCostResult =
                    await PreflightFunctions.GetKqmSupplierStockCountAndCostPriceForProductIds(kqmProductIds,
                        kaseyaApi);
                if (stockAndCostResult.IsSuccess)
                {
                    kqmIdToStockAndCostMap = stockAndCostResult.Value.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                    await LogMessageAsync(dbDatafeed, null,
                        $"[FetchExternal][Step 3.4] Successfully fetched KQM stock/cost for {kqmIdToStockAndCostMap.Count} KQM IDs.",
                        context);
                }
                else
                {
                    kqmIdToStockAndCostMap = new Dictionary<uint, (uint stockCount, decimal costPrice)>();
                    await LogMessageAsync(dbDatafeed, null,
                        $"[FetchExternal][Step 3.4] Failed to fetch KQM stock/cost: {stockAndCostResult.Error}",
                        context);
                }
            }
            else
            {
                kqmIdToStockAndCostMap = new Dictionary<uint, (uint stockCount, decimal costPrice)>();
                await LogMessageAsync(dbDatafeed, null, "[FetchExternal][Step 3.4] No KQM IDs to fetch stock/cost for.",
                    context);
            }

            // 5. Get KQM Images
            await LogMessageAsync(dbDatafeed, null,
                $"[FetchExternal][Step 3.5] Fetching KQM images for {kqmProductIds.Length} KQM IDs.", context);
            Dictionary<uint, List<KeyValuePair<byte[], string>>> kqmIdToImagesMap;
            if (kqmProductIds.Any())
            {
                var kqmImagesResult =
                    await PreflightFunctions.GetKqmImagesForProductIds(kqmProductIds, kaseyaApi, httpClient);
                if (kqmImagesResult.IsSuccess)
                {
                    kqmIdToImagesMap = kqmImagesResult.Value.ToDictionary(t => t.productId, t => t.images);
                    await LogMessageAsync(dbDatafeed, null,
                        $"[FetchExternal][Step 3.5] Successfully fetched KQM images for {kqmIdToImagesMap.Count} KQM IDs.",
                        context);
                }
                else
                {
                    kqmIdToImagesMap = new Dictionary<uint, List<KeyValuePair<byte[], string>>>();
                    await LogMessageAsync(dbDatafeed, null,
                        $"[FetchExternal][Step 3.5] Failed to fetch KQM images: {kqmImagesResult.Error}", context);
                }
            }
            else
            {
                kqmIdToImagesMap = new Dictionary<uint, List<KeyValuePair<byte[], string>>>();
                await LogMessageAsync(dbDatafeed, null, "[FetchExternal][Step 3.5] No KQM IDs to fetch images for.",
                    context);
            }

            // 6. Get KQM Brand Names
            await LogMessageAsync(dbDatafeed, null,
                $"[FetchExternal][Step 3.6] Fetching KQM brand names for {kqmProductIds.Length} KQM IDs.", context);
            Dictionary<uint, string> kqmIdToBrandNameMap;
            if (kqmProductIds.Any())
            {
                var brandNamesResult = await PreflightFunctions.GetKqmBrandNamesForProductIds(kqmProductIds, kaseyaApi);
                if (brandNamesResult.IsSuccess)
                {
                    kqmIdToBrandNameMap = brandNamesResult.Value.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                    await LogMessageAsync(dbDatafeed, null,
                        $"[FetchExternal][Step 3.6] Successfully fetched KQM brand names for {kqmIdToBrandNameMap.Count} KQM IDs.",
                        context);
                }
                else
                {
                    kqmIdToBrandNameMap = new Dictionary<uint, string>();
                    await LogMessageAsync(dbDatafeed, null,
                        $"[FetchExternal][Step 3.6] Failed to fetch KQM brand names: {brandNamesResult.Error}",
                        context);
                }
            }
            else
            {
                kqmIdToBrandNameMap = new Dictionary<uint, string>();
                await LogMessageAsync(dbDatafeed, null,
                    "[FetchExternal][Step 3.6] No KQM IDs to fetch brand names for.",
                    context);
            }

            await LogMessageAsync(dbDatafeed, null, "[FetchExternal] Finished fetching external data.", context);
            return new ExternalProductData(
                skuToKqmIdAndPriceMap,
                kqmProductIds,
                kqmIdToStockAndCostMap,
                kqmIdToImagesMap,
                kqmIdToBrandNameMap,
                skuToIcecatDataMap);
        }

        /// <summary>
        /// Calculates SHA-256 checksums for all existing product images across the provided sources.
        /// </summary>
        /// <param name="unprocessedSources">List of product sources containing images to process</param>
        /// <param name="sha256">SHA-256 hash algorithm instance to use for checksum calculation</param>
        /// <returns>A HashSet containing Base64-encoded SHA-256 checksums of all existing images</returns>
        private static HashSet<string> CalculateExistingImageChecksums(
            List<ProductSource> unprocessedSources,
            SHA256 sha256)
        {
            var existingImageChecksums = new HashSet<string>();

            foreach (var source in unprocessedSources)
            {
                foreach (var existingImage in source.ProductSourceImages)
                {
                    if (existingImage.ImageBytes is not { Length: > 0 })
                    {
                        continue;
                    }

                    var hashBytes = sha256.ComputeHash(existingImage.ImageBytes);
                    existingImageChecksums.Add(Convert.ToBase64String(hashBytes));
                }
            }

            return existingImageChecksums;
        }

        /// <summary>
        /// Updates the status of a product source and saves changes to the database based on processing results.
        /// </summary>
        /// <param name="source">The product source to update</param>
        /// <param name="dbDatafeed">Database context for data operations</param>
        /// <param name="prerequisiteData">Pre-fetched data required for processing</param>
        /// <param name="externalData">Data fetched from external services (KQM, Icecat)</param>
        /// <param name="existingImageChecksums">Set of checksums for existing product images</param>
        /// <param name="random">Random number generator for price calculations</param>
        /// <param name="sha256">SHA256 hasher for image checksum calculation</param>
        /// <param name="isUnderTest">Flag indicating if running in test mode</param>
        /// <param name="context">Hangfire context for logging</param>
        /// <returns>Task representing the asynchronous operation</returns>
        /// <remarks>
        /// This method:
        /// 1. Processes the source with fetched data
        /// 2. Checks if the source meets publishing requirements
        /// 3. Updates source status to Published if requirements are met
        /// 4. Falls back to NotCompliant status if processing fails or requirements aren't met
        /// 5. Handles database operations differently when under test
        /// </remarks>
        private static async Task UpdateSourceStatusAndSaveChanges(
            ProductSource source,
            EdunetDatafeedsEntities dbDatafeed,
            PrerequisiteData prerequisiteData,
            ExternalProductData externalData,
            HashSet<string> existingImageChecksums,
            Random random,
            SHA256 sha256,
            bool isUnderTest,
            PerformContext context)
        {
            var sourceId = source.ID; // Capture ID for logging in case source becomes null
            await LogMessageAsync(dbDatafeed, sourceId, $"[UpdateStatus] Starting for Source ID: {sourceId}.", context);

            // Get existing AI attributes for this source
            await LogMessageAsync(dbDatafeed, sourceId, "[UpdateStatus][Step 5a.1] Getting existing AI attributes.",
                context);
            var existingAiAttributes = prerequisiteData.ExistingAiInfo.TryGetValue(source.ID, out var attrs)
                ? attrs
                : []; // Ensure it's not null
            await LogMessageAsync(dbDatafeed, sourceId,
                $"[UpdateStatus][Step 5a.1] Found {existingAiAttributes.Count} existing AI attributes.", context);

            var initialStatus = source.Status; // Store the initial status
            await LogMessageAsync(dbDatafeed, sourceId, $"[UpdateStatus] Initial Status: {initialStatus}.", context);

            var isPublishable = false;

            try
            {
                // Process the source with all fetched data
                await LogMessageAsync(dbDatafeed, sourceId, "[UpdateStatus][Step 5a.2] Processing single source data.",
                    context);
                var hasStockCount = false;
                var isPriceRequirementMet = false;
                (hasStockCount, isPriceRequirementMet) = await ProcessSingleSourceAsync(
                    source,
                    dbDatafeed,
                    prerequisiteData,
                    externalData,
                    existingAiAttributes,
                    existingImageChecksums,
                    prerequisiteData.SkusWithValidFandoogleCache,
                    random,
                    sha256,
                    context);
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[UpdateStatus][Step 5a.2] Processed single source. Results: hasStockCount={hasStockCount}, isPriceRequirementMet={isPriceRequirementMet}.",
                    context);

                // Check if all requirements are met to mark as Published
                await LogMessageAsync(dbDatafeed, sourceId,
                    "[UpdateStatus][Step 5a.3] Checking if source is publishable.", context);
                isPublishable = CheckIfSourceIsPublishable(
                    source,
                    existingAiAttributes,
                    hasStockCount,
                    isPriceRequirementMet,
                    dbDatafeed,
                    context);
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[UpdateStatus][Step 5a.3] Publishable check result: {isPublishable}.", context);

                if (isPublishable)
                {
                    source.Status = (int)ProductSourceStatuses.Published;
                    await LogMessageAsync(dbDatafeed, sourceId,
                        $"[UpdateStatus] Source meets requirements. Setting Status to Published ({source.Status}).",
                        context);
                }
                else
                {
                    await LogMessageAsync(dbDatafeed, sourceId,
                        $"[UpdateStatus] Source does not meet requirements. Status remains {source.Status}.", context);
                }

                // Save changes for this source
                await LogMessageAsync(dbDatafeed, sourceId, "[UpdateStatus][Step 5a.4] Saving changes.", context);
                await dbDatafeed.SaveChangesAsync();
                await LogMessageAsync(dbDatafeed, sourceId, "[UpdateStatus][Step 5a.4] Changes saved.", context);
            }
            catch (Exception ex)
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[UpdateStatus] Exception during processing/saving for Source ID {sourceId}: {ex.Message} --- StackTrace: {ex.StackTrace}",
                    context);
                // Let finally block handle status update if needed
            }
            finally
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[UpdateStatus][Finally] Entering finally block. Current Status: {source.Status}, Initial Status: {initialStatus}.",
                    context);
                // Apply NotCompliant status if needed
                if (source.Status == initialStatus &&
                    !isPublishable) // Still at initial status and wasn't deemed publishable
                {
                    await LogMessageAsync(dbDatafeed, sourceId,
                        $"[UpdateStatus][Finally] Status unchanged ({initialStatus}) and not publishable. Attempting to set to NotCompliant.",
                        context);
                    try
                    {
                        if (!isUnderTest)
                        {
                            await LogMessageAsync(dbDatafeed, sourceId,
                                "[UpdateStatus][Finally] Reloading entity before setting NotCompliant (Not Under Test).",
                                context);
                            var entry = dbDatafeed.Entry(source);
                            await entry.ReloadAsync();
                            await LogMessageAsync(dbDatafeed, sourceId,
                                $"[UpdateStatus][Finally] Entity reloaded. Status after reload: {entry.Entity.Status}.",
                                context);

                            if (entry.Entity.Status == initialStatus) // Check status *after* reload
                            {
                                await LogMessageAsync(dbDatafeed, sourceId,
                                    "[UpdateStatus][Finally] Status still initial after reload. Setting to NotCompliant.",
                                    context);
                                entry.Entity.Status = (int)ProductSourceStatuses.NotCompliant;
                                await LogMessageAsync(dbDatafeed, sourceId,
                                    "[UpdateStatus][Finally] Saving NotCompliant status.", context);
                                await dbDatafeed.SaveChangesAsync();
                                await LogMessageAsync(dbDatafeed, sourceId,
                                    "[UpdateStatus][Finally] NotCompliant status saved.", context);
                            }
                            else
                            {
                                await LogMessageAsync(dbDatafeed, sourceId,
                                    "[UpdateStatus][Finally] Status changed after reload. Not setting NotCompliant.",
                                    context);
                            }
                        }
                        else
                        {
                            await LogMessageAsync(dbDatafeed, sourceId,
                                "[UpdateStatus][Finally] Setting status to NotCompliant (Under Test).", context);
                            // Simpler path for tests
                            source.Status = (int)ProductSourceStatuses.NotCompliant;
                            await LogMessageAsync(dbDatafeed, sourceId,
                                "[UpdateStatus][Finally] Saving NotCompliant status (Under Test).", context);
                            await dbDatafeed.SaveChangesAsync();
                            await LogMessageAsync(dbDatafeed, sourceId,
                                "[UpdateStatus][Finally] NotCompliant status saved (Under Test).", context);
                        }
                    }
                    catch (Exception ex)
                    {
                        await LogMessageAsync(dbDatafeed, sourceId,
                            $"[UpdateStatus][Finally] Exception while trying to set status to NotCompliant for Source ID {sourceId}: {ex.Message} --- StackTrace: {ex.StackTrace}",
                            context);
                    }
                }
                else
                {
                    await LogMessageAsync(dbDatafeed, sourceId,
                        $"[UpdateStatus][Finally] Status was changed or source was publishable. No NotCompliant update needed.",
                        context);
                }

                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[UpdateStatus][Finally] Exiting finally block for Source ID: {sourceId}. Final Status: {source.Status}",
                    context);
            }
        }

        /// <summary>
        /// Processes a single product source by updating its data from various external sources and checking requirements.
        /// </summary>
        /// <param name="source">The product source to process</param>
        /// <param name="dbDatafeed">Database context for accessing and saving product data</param>
        /// <param name="prerequisiteData">Pre-fetched data required for processing</param>
        /// <param name="externalData">Data from external sources (KQM, Icecat)</param>
        /// <param name="existingAiAttributes">Set of AI-generated attributes already present for the product</param>
        /// <param name="existingImageChecksums">Set of existing image checksums to prevent duplicates</param>
        /// <param name="skusWithValidFandoogleCache">Set of SKUs with valid Fandoogle cache entries</param>
        /// <param name="random">Random number generator for price calculations</param>
        /// <param name="sha256">SHA256 hasher for image checksum calculation</param>
        /// <param name="context">Hangfire context for logging</param>
        /// <returns>
        /// A tuple containing:
        /// - hasStockCount: Whether the product has valid stock count information
        /// - isPriceRequirementMet: Whether the product has valid pricing information
        /// </returns>
        private static async Task<(bool hasStockCount, bool isPriceRequirementMet)> ProcessSingleSourceAsync(
            ProductSource source,
            EdunetDatafeedsEntities dbDatafeed,
            PrerequisiteData prerequisiteData,
            ExternalProductData externalData,
            HashSet<string> existingAiAttributes,
            HashSet<string> existingImageChecksums,
            HashSet<string> skusWithValidFandoogleCache,
            Random random,
            SHA256 sha256,
            PerformContext context)
        {
            var sourceId = source.ID;
            var sku = source.ProductSku ?? "N/A";
            await LogMessageAsync(dbDatafeed, sourceId,
                $"[ProcessSingle] Starting for Source ID: {sourceId}, SKU: {sku}.", context);

            var hasStockCount = false;
            // Check if AI already provided a price
            var isPriceRequirementMet = existingAiAttributes.Contains(ProductInfoAttributes.SellPrice);
            var fandooglePriceUsedThisRun = false; // Flag to track if Fandoogle price was fetched/processed *this run*

            // Initialise price flags to 'Run But Not Found' - they'll be updated if found
            source.CompetitorPricePreflightFlag = (int)PreflightFlagStatuses.RunButNotFound;
            source.KaseyaRecommendedSellPricePreflightFlag = (int)PreflightFlagStatuses.RunButNotFound;

            await LogMessageAsync(dbDatafeed, sourceId,
                $"[ProcessSingle][Price] Initial check: isPriceRequirementMet (from AI) = {isPriceRequirementMet}.",
                context);

            // --- Fandoogle Price Check ---
            // Checks Fandoogle fetch/cache to potentially set isPriceRequirementMet and Competitor flag.
            await LogMessageAsync(dbDatafeed, sourceId,
                "[ProcessSingle][Price] Checking Fandoogle Reflector price (fetch/cache).", context);

            // Try Fandoogle Reflector price fetched *this run* first
            if (prerequisiteData.SkuToPriceMap.TryGetValue(source.ProductSku, out var fandoogleSellPrice))
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[ProcessSingle][Price] Fandoogle price found for SKU {sku} (fetched this run): {fandoogleSellPrice}.",
                    context);
                var randomisedPrice = fandoogleSellPrice - PriceDecrements[random.Next(PriceDecrements.Length)];
                var expiryDate = DateTime.UtcNow.AddDays(30);
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[ProcessSingle][Price] Calculated randomised price: {randomisedPrice}, Expiry: {expiryDate}.",
                    context);

                // Check if a cache entry already exists for this SKU
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[ProcessSingle][Price] Checking/Updating FandoogleReflectorCache for SKU {sku}.", context);
                var existingCacheEntry = await dbDatafeed.FandoogleReflectorCache
                    .FirstOrDefaultAsync(c => c.ProductSku == source.ProductSku);

                if (existingCacheEntry != null)
                {
                    await LogMessageAsync(dbDatafeed, sourceId,
                        "[ProcessSingle][Price] Updating existing Fandoogle cache entry.", context);
                    // Update existing entry
                    existingCacheEntry.RandomisedPrice = randomisedPrice;
                    existingCacheEntry.OriginalPrice = fandoogleSellPrice;
                    existingCacheEntry.ExpiresAt = expiryDate;
                }
                else
                {
                    await LogMessageAsync(dbDatafeed, sourceId,
                        "[ProcessSingle][Price] Creating new Fandoogle cache entry.", context);
                    // Create a new cache entry
                    var newCacheEntry = new FandoogleReflectorCache
                    {
                        ProductSku = source.ProductSku,
                        RandomisedPrice = randomisedPrice,
                        OriginalPrice = fandoogleSellPrice,
                        ExpiresAt = expiryDate
                    };
                    dbDatafeed.FandoogleReflectorCache.Add(newCacheEntry);
                }

                source.CompetitorPricePreflightFlag =
                    (int)PreflightFlagStatuses.Found; // Found Fandoogle price this run
                isPriceRequirementMet = true; // Price requirement met via Fandoogle this run
                fandooglePriceUsedThisRun = true; // Mark Fandoogle price as used this run
                await LogMessageAsync(dbDatafeed, sourceId,
                    "[ProcessSingle][Price] Fandoogle price processed (this run). isPriceRequirementMet = true, fandooglePriceUsedThisRun = true.",
                    context);
            }
            // If Fandoogle price wasn't fetched this run, check if a valid cache entry *already exists*
            else if (skusWithValidFandoogleCache.Contains(source.ProductSku))
            {
                source.CompetitorPricePreflightFlag =
                    (int)PreflightFlagStatuses.Found; // Found Fandoogle price via cache
                isPriceRequirementMet = true; // Price requirement met via existing valid cache
                await LogMessageAsync(dbDatafeed, sourceId,
                    "[ProcessSingle][Price] Valid Fandoogle cache entry exists. isPriceRequirementMet = true.",
                    context);
            }
            else
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    "[ProcessSingle][Price] No Fandoogle price found via fetch or valid cache.", context);
            }

            // --- KQM Price Check ---
            // Always check KQM price, regardless of Fandoogle outcome.
            // This sets source.SellPrice and the KQM flag if KQM price is available and positive.
            // Note: We no longer automatically set isPriceRequirementMet here - that will be determined later
            // based on the dual-source validation logic (Fandoogle OR both KQM prices).
            var kqmRecommendedPriceAvailable = false; // Track if KQM recommended price is available
            await LogMessageAsync(dbDatafeed, sourceId,
                $"[ProcessSingle][Price] Checking KQM recommended price for SKU {sku}.", context);
            if (externalData.SkuToKqmIdAndPriceMap.TryGetValue(source.ProductSku, out var kqmDataForPrice) &&
                kqmDataForPrice.recommendedSellPrice > 0)
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[ProcessSingle][Price] KQM recommended price found: {kqmDataForPrice.recommendedSellPrice}. Setting source.SellPrice.",
                    context);
                source.SellPrice = kqmDataForPrice.recommendedSellPrice; // Set SellPrice directly from KQM
                source.KaseyaRecommendedSellPricePreflightFlag =
                    (int)PreflightFlagStatuses.Found; // Found KQM recommended price
                kqmRecommendedPriceAvailable = true; // Mark KQM recommended price as available
                await LogMessageAsync(dbDatafeed, sourceId,
                    "[ProcessSingle][Price] KQM recommended price processed. source.SellPrice set, KQM flag set, kqmRecommendedPriceAvailable = true.",
                    context);
            }
            else
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    "[ProcessSingle][Price] No usable KQM recommended price found.", context);
            }

            // Final check on price requirement status after considering AI, Fandoogle, and KQM
            if (!isPriceRequirementMet)
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    "[ProcessSingle][Price] Final check: Price requirement NOT met by AI, Fandoogle, or KQM.", context);
            }

            // Find KQM ID and full data for the source's SKU (needed for stock/cost/images regardless of price source)
            await LogMessageAsync(dbDatafeed, sourceId, $"[ProcessSingle][KQM Data] Looking up KQM data for SKU {sku}.",
                context);
            var kqmDataFound = externalData.SkuToKqmIdAndPriceMap.TryGetValue(source.ProductSku, out var kqmData);
            var kqmId = kqmData.kqmProductId; // Extract kqmId if found
            await LogMessageAsync(dbDatafeed, sourceId,
                $"[ProcessSingle][KQM Data] KQM data found: {kqmDataFound}. KQM ID: {(kqmDataFound ? kqmId.ToString() : "N/A")}.",
                context);

            // Initialise Icecat flags to 'Run But Not Found'
            source.IcecatShortDescriptionPreflightFlag = (int)PreflightFlagStatuses.RunButNotFound;
            source.IcecatLongDescriptionPreflightFlag = (int)PreflightFlagStatuses.RunButNotFound;
            source.IcecatImagesPreflightFlag = (int)PreflightFlagStatuses.RunButNotFound;
            var icecatImageAdded = false; // Track if any image is added

            // Update from Icecat (apply first)
            await LogMessageAsync(dbDatafeed, sourceId,
                $"[ProcessSingle][Icecat] Checking for Icecat data for SKU {sku}.", context);
            var icecatDataFound = // Keep track if Icecat data was found
                externalData.SkuToIcecatDataMap.TryGetValue(source.ProductSku, out var localIcecatData);
            if (icecatDataFound)
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    "[ProcessSingle][Icecat] Icecat data found. Processing title, descriptions and images.", context);

                if (!string.IsNullOrWhiteSpace(localIcecatData.title))
                {
                    await LogMessageAsync(dbDatafeed, sourceId,
                        "[ProcessSingle][Icecat] Updating Title from Icecat.", context);
                    source.ProductTitle = localIcecatData.title;
                }

                if (!existingAiAttributes.Contains(ProductInfoAttributes.ShortDescription) &&
                    !string.IsNullOrWhiteSpace(localIcecatData.shortDescription))
                {
                    await LogMessageAsync(dbDatafeed, sourceId,
                        "[ProcessSingle][Icecat] Updating ShortDescription from Icecat.", context);
                    source.ProductShortDescription = localIcecatData.shortDescription;
                    source.IcecatShortDescriptionPreflightFlag =
                        (int)PreflightFlagStatuses.Found; // Found Icecat Short Description
                }

                if (!existingAiAttributes.Contains(ProductInfoAttributes.LongDescription) &&
                    !string.IsNullOrWhiteSpace(localIcecatData.longDescription))
                {
                    await LogMessageAsync(dbDatafeed, sourceId,
                        "[ProcessSingle][Icecat] Updating LongDescription from Icecat.", context);
                    source.ProductLongDescription = localIcecatData.longDescription;
                    source.IcecatLongDescriptionPreflightFlag =
                        (int)PreflightFlagStatuses.Found; // Found Icecat Long Description
                }

                // Process and add new images from Icecat
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[ProcessSingle][Icecat] Processing {localIcecatData.images?.Count ?? 0} images from Icecat.",
                    context);
                foreach (var imgBytes in localIcecatData.images ?? Enumerable.Empty<byte[]>())
                {
                    if (imgBytes is not { Length: > 0 })
                    {
                        await LogMessageAsync(dbDatafeed, sourceId,
                            "[ProcessSingle][Icecat] Skipping invalid/empty image byte array.", context);
                        continue;
                    }

                    var hashBytes = sha256.ComputeHash(imgBytes);
                    var newImageChecksum = Convert.ToBase64String(hashBytes);

                    if (existingImageChecksums.Contains(newImageChecksum))
                    {
                        await LogMessageAsync(dbDatafeed, sourceId,
                            $"[ProcessSingle][Icecat] Skipping duplicate image (Checksum: {newImageChecksum}).",
                            context);
                        continue;
                    }

                    await LogMessageAsync(dbDatafeed, sourceId,
                        $"[ProcessSingle][Icecat] Adding new image (Checksum: {newImageChecksum}).", context);
                    dbDatafeed.ProductSourceImages.Add(new ProductSourceImages
                    {
                        ProductSourceId = source.ID,
                        ImageBytes = imgBytes,
                        ImageUrl = null // From Icecat
                    });
                    existingImageChecksums.Add(newImageChecksum); // Add to set to prevent duplicates within this run
                    icecatImageAdded = true; // Mark that at least one image was added
                }
            }
            else
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    "[ProcessSingle][Icecat] No Icecat data found for this SKU.", context);
            }

            // Set Icecat Images flag if any were added
            if (icecatImageAdded)
            {
                source.IcecatImagesPreflightFlag = (int)PreflightFlagStatuses.Found;
            }

            // Initialise KQM flags (except price, already done) to 'Run But Not Found'
            source.KaseyaShortDescriptionPreflightFlag = (int)PreflightFlagStatuses.RunButNotFound;
            source.KaseyaLongDescriptionPreflightFlag = (int)PreflightFlagStatuses.RunButNotFound;
            source.KaseyaImagesPreflightFlag = (int)PreflightFlagStatuses.RunButNotFound;
            source.KaseyaCostPricePreflightFlag = (int)PreflightFlagStatuses.RunButNotFound;
            var kqmImageAdded = false; // Track if any KQM image is added

            // Update Title from KQM IN ALL CASES if KQM data was found
            if (kqmDataFound && !string.IsNullOrWhiteSpace(kqmData.title))
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[ProcessSingle][KQM Title] Updating Title from KQM ('{kqmData.title}'). This overrides any previous title.",
                    context);
                source.ProductTitle = kqmData.title;
            }

            // Fallback to KQM Descriptions if not set by Icecat and not present in AI
            if (kqmDataFound) // Only proceed if we have KQM data
            {
                // Fallback for Short Description
                if (!existingAiAttributes.Contains(ProductInfoAttributes.ShortDescription) && // Not in AI
                    string.IsNullOrWhiteSpace(source.ProductShortDescription) && // Not set by Icecat (or was empty)
                    !string.IsNullOrWhiteSpace(kqmData.shortDescription)) // KQM has a value
                {
                    await LogMessageAsync(dbDatafeed, sourceId,
                        "[ProcessSingle][KQM Desc] Updating ShortDescription from KQM (Icecat was empty/missing).",
                        context);
                    source.ProductShortDescription = kqmData.shortDescription;
                    source.KaseyaShortDescriptionPreflightFlag =
                        (int)PreflightFlagStatuses.Found; // Found KQM Short Description
                }

                // Fallback for Long Description
                if (!existingAiAttributes.Contains(ProductInfoAttributes.LongDescription) && // Not in AI
                    string.IsNullOrWhiteSpace(source.ProductLongDescription) && // Not set by Icecat (or was empty)
                    !string.IsNullOrWhiteSpace(kqmData.longDescription)) // KQM has a value
                {
                    await LogMessageAsync(dbDatafeed, sourceId,
                        "[ProcessSingle][KQM Desc] Updating LongDescription from KQM (Icecat was empty/missing).",
                        context);
                    source.ProductLongDescription = kqmData.longDescription;
                    source.KaseyaLongDescriptionPreflightFlag =
                        (int)PreflightFlagStatuses.Found; // Found KQM Long Description
                }
            }

            // Add KQM Images
            await LogMessageAsync(dbDatafeed, sourceId,
                $"[ProcessSingle][KQM Images] Checking for KQM images for KQM ID {kqmId}.", context);
            if (kqmDataFound && externalData.KqmIdToImagesMap.TryGetValue(kqmId, out var localKqmImages))
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[ProcessSingle][KQM Images] Found {localKqmImages?.Count ?? 0} KQM images. Processing.", context);
                foreach (var imgKvp in localKqmImages.Where(imgKvp =>
                             source.ProductSourceImages.All(psi => psi.ImageUrl != imgKvp.Value)))
                {
                    // Simple check for duplicates based on URL before adding
                    await LogMessageAsync(dbDatafeed, sourceId,
                        $"[ProcessSingle][KQM Images] Adding new KQM image (URL: {imgKvp.Value}).", context);
                    dbDatafeed.ProductSourceImages.Add(new ProductSourceImages
                    {
                        ProductSourceId = source.ID,
                        ImageBytes = imgKvp.Key,
                        ImageUrl = imgKvp.Value
                    });
                    kqmImageAdded = true; // Mark that at least one KQM image was added
                    // Note: We aren't checksumming KQM images here, relying on URL uniqueness check. Consider adding checksum if needed.
                }
            }
            else
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    "[ProcessSingle][KQM Images] No KQM images found or KQM ID not available.", context);
            }

            // Set KQM Images flag if any were added
            if (kqmImageAdded)
            {
                source.KaseyaImagesPreflightFlag = (int)PreflightFlagStatuses.Found;
            }

            // Update Stock and Cost Price from KQM
            var kqmCostPriceAvailable = false; // Track if KQM cost price is available
            await LogMessageAsync(dbDatafeed, sourceId,
                $"[ProcessSingle][KQM Stock/Cost] Checking for KQM stock/cost for KQM ID {kqmId}.", context);
            if (kqmDataFound && externalData.KqmIdToStockAndCostMap.TryGetValue(kqmId, out var stockAndCost))
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[ProcessSingle][KQM Stock/Cost] Found stock/cost data: Stock={stockAndCost.stockCount}, Cost={stockAndCost.costPrice}. Updating source.",
                    context);
                source.QtyAvailable = (int)stockAndCost.stockCount;
                hasStockCount = true;
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[ProcessSingle][KQM Stock/Cost] Updated QtyAvailable to {source.QtyAvailable}. hasStockCount = true.",
                    context);

                // Update Cost Price if it's greater than zero
                if (stockAndCost.costPrice > 0m)
                {
                    source.CostPrice = stockAndCost.costPrice;
                    source.KaseyaCostPricePreflightFlag = (int)PreflightFlagStatuses.Found; // Found KQM Cost Price
                    kqmCostPriceAvailable = true; // Mark KQM cost price as available
                    await LogMessageAsync(dbDatafeed, sourceId,
                        $"[ProcessSingle][KQM Stock/Cost] Updated CostPrice to {source.CostPrice}. kqmCostPriceAvailable = true.",
                        context);
                }
                else
                {
                    await LogMessageAsync(dbDatafeed, sourceId,
                        $"[ProcessSingle][KQM Stock/Cost] KQM CostPrice is not positive ({stockAndCost.costPrice}). Skipping CostPrice update.",
                        context);
                }
            }
            else
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    "[ProcessSingle][KQM Stock/Cost] No KQM stock/cost data found or KQM ID not available.", context);
            }

            // Update Brand from KQM
            await LogMessageAsync(dbDatafeed, sourceId,
                $"[ProcessSingle][KQM Brand] Checking for KQM brand information for KQM ID {kqmId}.", context);
            if (kqmDataFound && externalData.KqmIdToBrandNameMap.TryGetValue(kqmId, out var brandName) &&
                !string.IsNullOrWhiteSpace(brandName))
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[ProcessSingle][KQM Brand] Found brand name from KQM: '{brandName}'. Updating source.Brand.",
                    context);
                source.Brand = brandName;
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[ProcessSingle][KQM Brand] Updated Brand to '{source.Brand}' from KQM.", context);
            }
            else
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    "[ProcessSingle][KQM Brand] No KQM brand information found or KQM ID not available.", context);
            }

            // --- Dual-Source Price Validation Logic ---
            // Apply the new pricing requirement logic:
            // 1. If Fandoogle price is available → isPriceRequirementMet remains true (set earlier)
            // 2. If Fandoogle price is NOT available → check if BOTH KQM cost price AND recommended sell price are available
            await LogMessageAsync(dbDatafeed, sourceId,
                $"[ProcessSingle][Price Validation] Applying dual-source validation. fandooglePriceUsedThisRun={fandooglePriceUsedThisRun}, kqmRecommendedPriceAvailable={kqmRecommendedPriceAvailable}, kqmCostPriceAvailable={kqmCostPriceAvailable}.",
                context);

            if (!isPriceRequirementMet) // Only check if price requirement hasn't been met yet (by AI or Fandoogle)
            {
                if (!fandooglePriceUsedThisRun && !skusWithValidFandoogleCache.Contains(source.ProductSku))
                {
                    // Fandoogle price is NOT available, check if both KQM prices are available
                    if (kqmRecommendedPriceAvailable && kqmCostPriceAvailable)
                    {
                        isPriceRequirementMet = true;
                        await LogMessageAsync(dbDatafeed, sourceId,
                            "[ProcessSingle][Price Validation] Fandoogle price not available, but both KQM recommended sell price and cost price are available. isPriceRequirementMet = true.",
                            context);
                    }
                    else
                    {
                        await LogMessageAsync(dbDatafeed, sourceId,
                            $"[ProcessSingle][Price Validation] Fandoogle price not available and KQM dual-price requirement not met (recommended: {kqmRecommendedPriceAvailable}, cost: {kqmCostPriceAvailable}). isPriceRequirementMet remains false.",
                            context);
                    }
                }
                else
                {
                    await LogMessageAsync(dbDatafeed, sourceId,
                        "[ProcessSingle][Price Validation] Fandoogle price is available (used this run or valid cache), no need to check KQM dual-price requirement.",
                        context);
                }
            }
            else
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    "[ProcessSingle][Price Validation] Price requirement already met (by AI or Fandoogle), skipping dual-source validation.",
                    context);
            }

            // Re-check competitor pricing *only if Fandoogle price was available this run*
            await LogMessageAsync(dbDatafeed, sourceId,
                $"[ProcessSingle][Competitor Check] Checking if Fandoogle price was used this run (fandooglePriceUsedThisRun={fandooglePriceUsedThisRun}).",
                context);
            if (fandooglePriceUsedThisRun && // Check the flag set earlier
                prerequisiteData.SkuToPriceMap.TryGetValue(source.ProductSku, out var sellPriceFromMap))
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    $"[ProcessSingle][Competitor Check] Fandoogle price used this run. Comparing Fandoogle price ({sellPriceFromMap}) to CostPrice ({source.CostPrice}).",
                    context);
                // Use the original Fandoogle price for comparison
                if (source.CostPrice != 0 &&
                    sellPriceFromMap < source.CostPrice) // Compare Fandoogle price to CostPrice
                {
                    await LogMessageAsync(dbDatafeed, sourceId,
                        $"[ProcessSingle][Competitor Check] Fandoogle price ({sellPriceFromMap}) is cheaper than CostPrice ({source.CostPrice}). Checking if already marked.",
                        context);
                    // Check if already marked as cheaper
                    var exists = await dbDatafeed.ProductsCheaperAtCompetitor
                        .AnyAsync(q => q.ProductSku.Trim().ToLower() == source.ProductSku.ToLower());
                    await LogMessageAsync(dbDatafeed, sourceId,
                        $"[ProcessSingle][Competitor Check] Already marked as cheaper: {exists}.", context);

                    if (!exists)
                    {
                        await LogMessageAsync(dbDatafeed, sourceId,
                            "[ProcessSingle][Competitor Check] Adding SKU to ProductsCheaperAtCompetitor.", context);
                        dbDatafeed.ProductsCheaperAtCompetitor.Add(new ProductsCheaperAtCompetitor
                        {
                            ProductSku = source.ProductSku,
                            CompetitorPrice = sellPriceFromMap // Store the Fandoogle price
                        });
                    }
                }
                else
                {
                    await LogMessageAsync(dbDatafeed, sourceId,
                        $"[ProcessSingle][Competitor Check] Fandoogle price ({sellPriceFromMap}) is not cheaper than CostPrice ({source.CostPrice}).",
                        context);
                }
            }
            else
            {
                await LogMessageAsync(dbDatafeed, sourceId,
                    "[ProcessSingle][Competitor Check] Skipping competitor check (Fandoogle price not used or not found).",
                    context);
            }

            // Check and Save Kaseya Attributes - SKIPPED
            await LogMessageAsync(dbDatafeed, sourceId, "[ProcessSingle][Kaseya Attrs] SKIPPED attribute processing.",
                context);

            await LogMessageAsync(dbDatafeed, sourceId,
                $"[ProcessSingle] Finished processing. Returning: hasStockCount={hasStockCount}, isPriceRequirementMet={isPriceRequirementMet}.",
                context);
            return (hasStockCount, isPriceRequirementMet);
        }

        /// <summary>
        /// Determines if a product source meets all requirements for publishing.
        /// </summary>
        /// <param name="source">The product source to evaluate</param>
        /// <param name="existingAiAttributes">Set of AI-generated attributes already present for the product</param>
        /// <param name="hasStockCount">Whether the product has valid stock count information</param>
        /// <param name="isPriceRequirementMet">Whether the product has valid pricing information</param>
        /// <param name="dbDatafeed">Database context for accessing product data</param>
        /// <param name="context">Hangfire context for logging</param>
        /// <returns>True if the product source meets all publishing requirements, false otherwise</returns>
        private static bool CheckIfSourceIsPublishable(
            ProductSource source,
            HashSet<string> existingAiAttributes,
            bool hasStockCount,
            bool isPriceRequirementMet,
            EdunetDatafeedsEntities dbDatafeed,
            PerformContext context)
        {
            var sourceId = source.ID;
            LogMessageAsync(dbDatafeed, sourceId, $"[CheckPublishable] Starting for Source ID: {sourceId}.", context)
                .GetAwaiter().GetResult();
            LogMessageAsync(dbDatafeed, sourceId,
                $"[CheckPublishable] Input: hasStockCount={hasStockCount}, isPriceRequirementMet={isPriceRequirementMet}.",
                context).GetAwaiter().GetResult();

            // Determine if attributes are present - SKIPPED
            var hasAttributes = true; // Assume attributes requirement is met since we are skipping the check
            LogMessageAsync(dbDatafeed, sourceId,
                    $"[CheckPublishable][Attrs] SKIPPED attribute check. Assuming hasAttributes = {hasAttributes}.",
                    context).GetAwaiter()
                .GetResult();

            // Check required data points
            var hasTitle = !string.IsNullOrWhiteSpace(source.ProductTitle);
            LogMessageAsync(dbDatafeed, sourceId,
                $"[CheckPublishable][Title] hasTitle: {hasTitle} (Source: {!string.IsNullOrWhiteSpace(source.ProductTitle)}).",
                context).GetAwaiter().GetResult();

            var hasShortDescription = !string.IsNullOrWhiteSpace(source.ProductShortDescription) ||
                                      existingAiAttributes.Contains(ProductInfoAttributes.ShortDescription);
            LogMessageAsync(dbDatafeed, sourceId,
                $"[CheckPublishable][Desc] hasShortDescription: {hasShortDescription} (Source: {!string.IsNullOrWhiteSpace(source.ProductShortDescription)}, AI: {existingAiAttributes.Contains(ProductInfoAttributes.ShortDescription)}).",
                context).GetAwaiter().GetResult();

            var hasLongDescription = !string.IsNullOrWhiteSpace(source.ProductLongDescription) ||
                                     existingAiAttributes.Contains(ProductInfoAttributes.LongDescription);
            LogMessageAsync(dbDatafeed, sourceId,
                $"[CheckPublishable][Desc] hasLongDescription: {hasLongDescription} (Source: {!string.IsNullOrWhiteSpace(source.ProductLongDescription)}, AI: {existingAiAttributes.Contains(ProductInfoAttributes.LongDescription)}).",
                context).GetAwaiter().GetResult();

            var hasImagesLocally = dbDatafeed.ProductSourceImages.Local.Any(psi => psi.ProductSourceId == source.ID);
            LogMessageAsync(dbDatafeed, sourceId,
                    $"[CheckPublishable][Images] Images added locally this run: {hasImagesLocally}.", context)
                .GetAwaiter()
                .GetResult();
            var hasImagesInDb = false;
            if (!hasImagesLocally)
            {
                hasImagesInDb = dbDatafeed.ProductSourceImages.Any(psi => psi.ProductSourceId == source.ID);
                LogMessageAsync(dbDatafeed, sourceId,
                        $"[CheckPublishable][Images] Images found in DB: {hasImagesInDb}.", context).GetAwaiter()
                    .GetResult();
            }

            var hasImages = hasImagesLocally || hasImagesInDb;
            LogMessageAsync(dbDatafeed, sourceId, $"[CheckPublishable][Images] Final hasImages result: {hasImages}.",
                context).GetAwaiter().GetResult();

            // All conditions must be met
            var isPublishable = hasTitle && hasShortDescription && hasLongDescription && hasStockCount && hasImages &&
                                isPriceRequirementMet; // Removed hasAttributes check
            LogMessageAsync(dbDatafeed, sourceId,
                $"[CheckPublishable] Final Result: isPublishable = {isPublishable} (Title:{hasTitle} && ShortDesc:{hasShortDescription} && LongDesc:{hasLongDescription} && Stock:{hasStockCount} && Images:{hasImages} && Price:{isPriceRequirementMet}). Attribute check skipped.",
                context).GetAwaiter().GetResult();

            return isPublishable;
        }
    }
}
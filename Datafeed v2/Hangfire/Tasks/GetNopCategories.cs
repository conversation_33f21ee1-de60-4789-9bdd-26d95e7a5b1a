using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Properties;
using Newtonsoft.Json;

namespace Datafeed_v2.Hangfire.Tasks;

public class GetNopCategories
{
    public static void Run(EdunetDatafeedsEntities dataFeedsContext = null)
    {
        const string categoriesUrl = "Categories/GetNopCategories";
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        using var client = new HttpClient();
        client.BaseAddress = new Uri(Debugger.IsAttached ? "https://localhost:5001/" : Settings.Default.NopLiveSiteUrl);
        // client.BaseAddress = new Uri(!Debugger.IsAttached ? "https://localhost:5001/" : Settings.Default.NopLiveSiteUrl);
        var req = client.GetAsync(categoriesUrl).ConfigureAwait(false).GetAwaiter().GetResult();
        if (req.IsSuccessStatusCode == false)
        {
            throw new Exception($"Failed to get categories from Nop: {req.StatusCode}");
        }

        var res = req.Content.ReadAsStringAsync().ConfigureAwait(false).GetAwaiter().GetResult();
        var anonType = new[]
        {
            new
            {
                Id = 0,
                Name = default(string),
                Deleted = false,
                Published = false,
                CreatedOnUtc = default(DateTime),
                UpdatedOnUtc = default(DateTime),
                ParentCategoryId = 0
            }
        };
        var nopCategories = JsonConvert.DeserializeAnonymousType(res, anonType);
        var newEntries = new List<NopCategories>();
        foreach (var nopCategory in nopCategories)
        {
            var existingCategory = dbDataFeedsContext.NopCategories.FirstOrDefault(q => q.CategoryId == nopCategory.Id);
            if (existingCategory != null)
            {
                existingCategory.Name = nopCategory.Name;
                existingCategory.Deleted = nopCategory.Deleted;
                existingCategory.Published = nopCategory.Published;
                existingCategory.CreatedOnUtc = nopCategory.CreatedOnUtc;
                existingCategory.UpdatedOnUtc = nopCategory.UpdatedOnUtc;
                existingCategory.ParentCategoryId = nopCategory.ParentCategoryId;
                continue;
            }

            newEntries.Add(new NopCategories
            {
                CategoryId = nopCategory.Id,
                Name = nopCategory.Name,
                Deleted = nopCategory.Deleted,
                Published = nopCategory.Published,
                CreatedOnUtc = nopCategory.CreatedOnUtc,
                UpdatedOnUtc = nopCategory.UpdatedOnUtc,
                ParentCategoryId = nopCategory.ParentCategoryId
            });
        }

        dbDataFeedsContext.NopCategories.AddRange(newEntries);
        dbDataFeedsContext.SaveChanges();
    }
}
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Datafeed_v2.Models;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Services;
using Hangfire;
using Hangfire.Server;

namespace Datafeed_v2.Hangfire.Tasks
{
    public abstract class GetDescriptionsFromKqm
    {
        private const int BatchSize = 20; // Process in batches

        private static async Task LogMessageAsync(EdunetDatafeedsEntities db, string message,
            PerformContext context = null)
        {
            db.PreflightLog.Add(new PreflightLog
                { LogDateTime = DateTime.Now, Message = $"[GetDescriptionsFromKqm] {message}" });
            await db.SaveChangesAsync();
        }

        [DisableConcurrentExecution(60 * 60 * 6)]
        [SkipWhenPreviousJobIsRunning]
        public static async Task Run(PerformContext context,
            EdunetDatafeedsEntities diDbDatafeed = null,
            IGetDescriptionFromKqmService diScraperService = null)
        {
            ThreadPool.SetMaxThreads(10, 2);

            using var dbDatafeed = diDbDatafeed ?? new EdunetDatafeedsEntities();
            // Use using for the service if it's created here, or manage disposal if injected long-term
            var scraperService = diScraperService ?? new GetDescriptionFromKqmService();

            await LogMessageAsync(dbDatafeed, "[Run] Starting GetDescriptionsFromKqm task.", context);

            try
            {
                var batchNumber = 0;
                List<ProductSource> currentBatch;

                do
                {
                    context?.SendHeartbeat(); // Send heartbeat before processing each batch

                    // Fetch the next batch of sources needing descriptions
                    currentBatch = await dbDatafeed.ProductSource
                        .Where(ps =>
                            ps.Status == (int)ProductSourceStatuses.New ||
                            ps.Status == (int)ProductSourceStatuses.NotCompliant)
                        .OrderBy(ps => ps.ID)
                        .Skip(batchNumber * BatchSize)
                        .Take(BatchSize)
                        .Include(ps => ps.ProductSourceImages) // Include images for publishable check
                        .ToListAsync();

                    if (!currentBatch.Any())
                    {
                        await LogMessageAsync(dbDatafeed,
                            "[Run] No more sources found needing descriptions. Exiting batch processing.", context);
                        break;
                    }

                    await LogMessageAsync(dbDatafeed,
                        $"[Run] Processing batch {batchNumber + 1} with {currentBatch.Count} sources.", context);

                    var skusToFetch = currentBatch
                        .Select(s => s.ProductSku)
                        .Where(sku => !string.IsNullOrWhiteSpace(sku))
                        .Distinct()
                        .ToList();

                    if (!skusToFetch.Any())
                    {
                        await LogMessageAsync(dbDatafeed,
                            $"[Run][Batch {batchNumber + 1}] No valid SKUs found in batch. Skipping.", context);
                        batchNumber++;
                        continue;
                    }

                    // Fetch descriptions using the service
                    await LogMessageAsync(dbDatafeed,
                        $"[Run][Batch {batchNumber + 1}] Fetching descriptions for {skusToFetch.Count} SKUs.", context);
                    var fetchResult = await scraperService.FetchDescriptionsAsync(skusToFetch);

                    if (fetchResult.IsFailure)
                    {
                        await LogMessageAsync(dbDatafeed,
                            $"[Run][Batch {batchNumber + 1}] Failed to fetch descriptions: {fetchResult.Error}. Skipping batch.",
                            context);
                        // Decide how to handle batch failure: skip, retry later, mark as failed?
                        // For now, we skip the batch and continue to the next.
                        batchNumber++;
                        continue;
                    }

                    var fetchedDescriptions = fetchResult.Value.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                    await LogMessageAsync(dbDatafeed,
                        $"[Run][Batch {batchNumber + 1}] Successfully fetched {fetchedDescriptions.Count} descriptions.",
                        context);

                    // Process results for the batch
                    foreach (var source in currentBatch.Where(source => !string.IsNullOrWhiteSpace(source.ProductSku)))
                    {
                        if (fetchedDescriptions.TryGetValue(source.ProductSku, out var description) &&
                            !string.IsNullOrWhiteSpace(description))
                        {
                            await LogMessageAsync(dbDatafeed,
                                $"[Run][Batch {batchNumber + 1}] Updating description for Source ID: {source.ID}, SKU: {source.ProductSku}.",
                                context);
                            source.ProductLongDescription = description;

                            // Check if the source is now publishable
                            if (IsSourcePublishable(source, dbDatafeed))
                            {
                                await LogMessageAsync(dbDatafeed,
                                    $"[Run][Batch {batchNumber + 1}] Source ID: {source.ID} is now publishable. Setting status to Published.",
                                    context);
                                source.Status = (int)ProductSourceStatuses.Published;
                            }
                            else
                            {
                                // Otherwise, leave it as NotCompliant if it already was
                                if (source.Status != (int)ProductSourceStatuses.New)
                                {
                                    continue;
                                }

                                // If it was 'New' and still not publishable after getting description, mark as 'NotCompliant'
                                await LogMessageAsync(dbDatafeed,
                                    $"[Run][Batch {batchNumber + 1}] Source ID: {source.ID} is still not publishable. Setting status to NotCompliant.",
                                    context);
                                source.Status = (int)ProductSourceStatuses.NotCompliant;
                            }
                        }
                        else
                        {
                            await LogMessageAsync(dbDatafeed,
                                $"[Run][Batch {batchNumber + 1}] No description fetched or description was empty for Source ID: {source.ID}, SKU: {source.ProductSku}.",
                                context);
                            if (source.Status != (int)ProductSourceStatuses.New)
                            {
                                continue;
                            }

                            // If it was 'New', mark as 'NotCompliant' as we couldn't get a description
                            await LogMessageAsync(dbDatafeed,
                                $"[Run][Batch {batchNumber + 1}] Setting status to NotCompliant for Source ID: {source.ID}.",
                                context);
                            source.Status = (int)ProductSourceStatuses.NotCompliant;
                        }
                    }

                    // Save changes for the batch
                    try
                    {
                        await LogMessageAsync(dbDatafeed, $"[Run][Batch {batchNumber + 1}] Saving changes for batch.",
                            context);
                        await dbDatafeed.SaveChangesAsync();
                        await LogMessageAsync(dbDatafeed, $"[Run][Batch {batchNumber + 1}] Changes saved.", context);
                    }
                    catch (Exception ex)
                    {
                        await LogMessageAsync(dbDatafeed,
                            $"[Run][Batch {batchNumber + 1}] Error saving changes: {ex.Message} --- StackTrace: {ex.StackTrace}",
                            context);
                        // Consider how to handle save failures - potentially break the loop?
                    }

                    batchNumber++;
                } while (currentBatch.Count == BatchSize);

                await LogMessageAsync(dbDatafeed,
                    $"[Run] Processed {batchNumber} batches. GetDescriptionsFromKqm task finished.", context);
            }
            catch (Exception ex)
            {
                await LogMessageAsync(dbDatafeed,
                    $"[Run] Unhandled exception in GetDescriptionsFromKqm: {ex.Message} --- StackTrace: {ex.StackTrace}",
                    context);
                throw; // Re-throw so Hangfire knows the job failed
            }
            finally
            {
                await LogMessageAsync(dbDatafeed, "[Run] Exiting GetDescriptionsFromKqm task.", context);
                // Dispose context if created here
                if (diDbDatafeed == null)
                {
                    dbDatafeed.Dispose();
                }
                // Service disposal handled by using statement or caller if injected
            }
        }

        /// <summary>
        /// Checks if a ProductSource meets the basic requirements to be published.
        /// Adapted from PreflightQueueRunner.CheckIfSourceIsPublishable.
        /// </summary>
        private static bool IsSourcePublishable(ProductSource source, EdunetDatafeedsEntities db)
        {
            if (source == null)
            {
                return false;
            }

            // Basic checks for essential fields
            var hasTitle = !string.IsNullOrWhiteSpace(source.ProductTitle);
            var hasShortDescription = !string.IsNullOrWhiteSpace(source.ProductShortDescription);
            var hasLongDescription =
                !string.IsNullOrWhiteSpace(source.ProductLongDescription); // Check the field we just updated
            var hasStockCount = source.QtyAvailable >= 0; // Allow zero stock
            var hasCostPrice = source.CostPrice > 0; // Cost must be positive

            // Check for Sell Price: Either directly set or via Fandoogle cache
            var hasSellPrice = source.SellPrice is > 0;
            if (!hasSellPrice)
            {
                // Check Fandoogle cache (synchronous check for simplicity here, consider async if needed)
                var cachedPrice = db.FandoogleReflectorCache
                    .FirstOrDefault(c => c.ProductSku == source.ProductSku && c.ExpiresAt > DateTime.UtcNow);
                hasSellPrice = cachedPrice != null;
            }

            // Check for images (either loaded in context or already in DB)
            var hasImages = source.ProductSourceImages.Any() ||
                            db.ProductSourceImages.Any(psi => psi.ProductSourceId == source.ID);

            return hasTitle
                   && hasShortDescription
                   && hasLongDescription
                   && hasStockCount
                   && hasCostPrice
                   && hasSellPrice
                   && hasImages;
        }
    }
}
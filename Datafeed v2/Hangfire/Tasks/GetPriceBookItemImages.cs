using System;
using System.Data.Entity.Validation;
using System.Linq;
using System.Net.Http;
using System.Text;
using Datafeed_v2.Models.DbModels.Datafeed;
using Hangfire;
using Hangfire.Server;

namespace Datafeed_v2.Hangfire.Tasks;

[DisableConcurrentExecution(60 * 60 * 2)]
public class GetPriceBookItemImages
{
    private static readonly HttpClient HttpClient = new HttpClient();

    public static void Run(PerformContext context, EdunetDatafeedsEntities dataFeedsContext = null)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();

        var importedItems = dbDataFeedsContext.ImportedItemsFromPriceBook.ToList();

        const int batchSize = 20;
        var count = 0;

        foreach (var item in importedItems)
        {
            if (string.IsNullOrWhiteSpace(item.ImageURLs))
            {
                continue;
            }

            var urls = item.ImageURLs.Split(new string[] { ";" }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var url in urls)
            {
                var alreadyDownloaded = dbDataFeedsContext.PriceBookItemImages
                    .Any(pi => pi.PriceBookItemID == item.ID && pi.ImageURL == url);
                if (alreadyDownloaded)
                {
                    continue;
                }

                var response = HttpClient.GetAsync(url).ConfigureAwait(false).GetAwaiter().GetResult();
                if (!response.IsSuccessStatusCode)
                {
                    continue;
                }

                var imageBytes = response.Content.ReadAsByteArrayAsync().ConfigureAwait(false).GetAwaiter().GetResult();

                // Create and add new PriceBookItemImages record
                dbDataFeedsContext.PriceBookItemImages.Add(new PriceBookItemImages
                {
                    PriceBookItemID = item.ID,
                    ImageURL = url,
                    Image = imageBytes
                });

                count++;
                if (count % batchSize != 0) continue;

                try
                {
                    dbDataFeedsContext.SaveChanges();
                }
                catch (DbEntityValidationException e)
                {
                    var sb = new StringBuilder();
                    foreach (var validationErrors in e.EntityValidationErrors)
                    {
                        sb.AppendLine(validationErrors.ValidationErrors.First().ErrorMessage);
                    }

                    throw new Exception(sb.ToString());
                }
            }
        }

        if (count % batchSize == 0) return;

        try
        {
            dbDataFeedsContext.SaveChanges();
        }
        catch (DbEntityValidationException e)
        {
            var sb = new StringBuilder();
            foreach (var validationErrors in e.EntityValidationErrors)
            {
                sb.AppendLine(validationErrors.ValidationErrors.First().ErrorMessage);
            }

            throw new Exception(sb.ToString());
        }
    }
}
using System;
using System.Data.Entity;
using System.Data.Entity.Validation;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Helpers;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Services;
using Datafeed_v2.Singletons;
using Hangfire;
using Hangfire.Server;

namespace Datafeed_v2.Hangfire.Tasks;

public class GetProductDescriptionsFromIcecat
{
    [DisableConcurrentExecution(60 * 60 * 2)]
    public static async Task Run(PerformContext context,
        EdunetDatafeedsEntities dataFeedsContext = null,
        IcecatApiService diIcecatApiService = null)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();

        try
        {
            // Get all products from database
            var kqmProducts = await dbDataFeedsContext.KQMProducts.ToListAsync();

            // Log start of process
            dbDataFeedsContext.ProductSyncLog.Add(new ProductSyncLog
            {
                Message = $"Starting Icecat description update for {kqmProducts.Count} KQM products",
                SyncDateTime = DateTime.Now
            });
            await dbDataFeedsContext.SaveChangesAsync();

            var icecatApiService = diIcecatApiService ?? IcecatApiServiceSingleton.Instance;
            var updatedCount = 0;

            foreach (var product in kqmProducts)
            {
                var brand = dbDataFeedsContext.KQMBrands.SingleOrDefault(b => b.brandID == product.brandID);
                if (brand == null || string.IsNullOrEmpty(product.manufacturerPartNumber))
                {
                    continue;
                }

                // Get product info from Icecat API with retry logic
                var result = await RetryHelper.ExecuteWithRetryAsync(
                    () => icecatApiService.GetProductInfoByManufacturerCode(
                        brand.name,
                        product.manufacturerPartNumber,
                        [IcecatApiContentTypes.GeneralInfo]
                    ),
                    r => r.IsFailure && r.Error?.Contains("Rate limit exceeded") == true
                ).ConfigureAwait(false);

                var (_, iceCatFailed, icecatResult, icecatError) = result;

                if (iceCatFailed)
                {
                    dbDataFeedsContext.ProductSyncLog.Add(new ProductSyncLog
                    {
                        ProductId = product.productID,
                        Message = $"Icecat API failed for product {product.productID}: {icecatError}",
                        SyncDateTime = DateTime.Now
                    });

                    continue;
                }

                var icecatDescription = icecatResult.Data?.GeneralInfo?.Description?.LongDescription
                                        ?? icecatResult.Data?.GeneralInfo?.SummaryDescription?.LongDescription;

                if (!string.IsNullOrEmpty(icecatDescription) && product.description != icecatDescription)
                {
                    product.description = icecatDescription;
                    updatedCount++;

                    // Log update
                    dbDataFeedsContext.ProductSyncLog.Add(new ProductSyncLog
                    {
                        ProductId = product.productID,
                        Message = $"Updated description from Icecat for product {product.productID}",
                        SyncDateTime = DateTime.Now
                    });

                    continue;
                }

                dbDataFeedsContext.ProductSyncLog.Add(new ProductSyncLog
                {
                    ProductId = product.productID,
                    Message =
                        $"Icecat description empty or unchanged for product {product.productID}: {icecatDescription}",
                    SyncDateTime = DateTime.Now
                });
            }

            try
            {
                await dbDataFeedsContext.SaveChangesAsync();

                // Log completion
                dbDataFeedsContext.ProductSyncLog.Add(new ProductSyncLog
                {
                    Message = $"Completed Icecat descriptions update. Updated {updatedCount} products",
                    SyncDateTime = DateTime.Now
                });
                await dbDataFeedsContext.SaveChangesAsync();
            }
            catch (DbEntityValidationException e)
            {
                // Error handling identical to FetchAllProducts
                var error = new StringBuilder();
                error.AppendLine($"Failed to save Icecat descriptions: {e.Message}");
                foreach (var err in e.EntityValidationErrors)
                {
                    error.AppendLine(
                        $"Entity of type {err.Entry.Entity.GetType().Name} in state {err.Entry.State} has validation errors:");
                    foreach (var ve in err.ValidationErrors)
                    {
                        error.AppendLine($"Property: {ve.PropertyName}, Error: {ve.ErrorMessage}");
                    }
                }

                throw new Exception(error.ToString());
            }
        }
        catch (Exception ex)
        {
            // Identical error handling structure to FetchAllProducts
            dbDataFeedsContext.ProductSyncLog.Add(new ProductSyncLog
            {
                Message = $"Icecat description task failed: {ex.Message}",
                SyncDateTime = DateTime.Now
            });
            await dbDataFeedsContext.SaveChangesAsync();
            throw;
        }
    }
}
using System;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using CSharpFunctionalExtensions;
using Datafeed_v2.Helpers;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Models.TransformationPackages;
using LogErrorsToAutotask;

namespace Datafeed_v2.Controllers;

[Authorize]
[LogErrorsToAutotask(Source = "Datafeedv2")]
public class TransformationPackagesController : Controller
{
    // GET
    public ActionResult Index()
    {
        return View();
    }

    public async Task<ActionResult> GetTransformationPackages(EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var transformationPackages = dbDataFeedsContext.CommerceTransformationPackages.AsEnumerable();
        var viewModel = new GetTransformationPackagesModel { TransformationPackages = transformationPackages };
        if (viewModel.TransformationPackages.Any() == false)
        {
            await responseBase.DisplayHtmxError();
            return Content("No transformation packages found");
        }

        return PartialView("_TransformationPackages", viewModel);
    }

    public async Task<ActionResult> GetTransformationPackage(uint transformationPackageId,
        EdunetDatafeedsEntities dataFeedsContext = null, Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var transformationPackages =
            await dbDataFeedsContext.CommerceTransformationPackages.SingleOrDefaultAsync(q =>
                q.ID == transformationPackageId);
        var viewModel = new GetTransformationPackageModel { TransformationPackage = transformationPackages };
        if (viewModel.TransformationPackage == null)
        {
            await responseBase.DisplayHtmxError();
            return Content("No transformation package found");
        }

        return PartialView("_TransformationPackage", viewModel);
    }

    public async Task<ActionResult> GetAddNewTransformationPackageModal()
    {
        return PartialView("_AddNewTransformationPackageModal");
    }

    public async Task<ActionResult> UpdateTransformationPackage(uint transformationPackageId,
        CommerceTransformationPackages transformationPackage,
        EdunetDatafeedsEntities dataFeedsContext = null, Maybe<HttpResponseBase> responseBaseMock = default)
    {
        // Use provided data context or create a new one if not provided
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        // Retrieve the transformation package to update from the database
        var transformationPackageToUpdate =
            await dbDataFeedsContext.CommerceTransformationPackages.SingleOrDefaultAsync(q =>
                q.ID == transformationPackageId);

        // If the transformation package is not found, return an error view
        if (transformationPackageToUpdate == null)
        {
            await responseBase.DisplayHtmxError();
            return Content("Could not update transformation package: could not find transformation package");
        }

        // If no data is supplied for the update, return an error view
        if (transformationPackage == null)
        {
            await responseBase.DisplayHtmxError();
            return Content("Could not update transformation package: no data supplied");
        }

        // Validate the input data; if invalid, return an error view
        if (string.IsNullOrEmpty(transformationPackage.PackageName) ||
            string.IsNullOrEmpty(transformationPackage.PackageDescription) ||
            transformationPackage.PercentMarkup <= 0 ||
            transformationPackage.PercentMarkup > 100)
        {
            await responseBase.DisplayHtmxError();
            return Content("Could not update transformation package: input data is invalid");
        }

        // Update the transformation package with the new data
        transformationPackageToUpdate.PackageName = transformationPackage.PackageName;
        transformationPackageToUpdate.PackageDescription = transformationPackage.PackageDescription;
        transformationPackageToUpdate.PercentMarkup = transformationPackage.PercentMarkup;
        transformationPackageToUpdate.EnablePercentMarkup = transformationPackage.EnablePercentMarkup;
        transformationPackageToUpdate.EnableAI = transformationPackage.EnableAI;

        // Save changes to the database asynchronously
        _ = dbDataFeedsContext.SaveChangesAsync(); // Don't need to wait for this to save, continue on full steam ahead

        // Prepare the success view model and return the updated view
        var viewModel = new GetTransformationPackageModel
        {
            TransformationPackage = transformationPackageToUpdate
        };

        await responseBase.DisplayHtmxSuccess("Transformation package updated successfully");
        return PartialView("_TransformationPackage", viewModel);
    }

    public async Task<ActionResult> AddTransformationPackage(CommerceTransformationPackages transformationPackage,
        EdunetDatafeedsEntities dataFeedsContext = null, Maybe<HttpResponseBase> responseBaseMock = default)
    {
        // Use provided data context or create a new one if not provided
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        // Retrieve all transformation packages from the database
        var transformationPackages = dbDataFeedsContext.CommerceTransformationPackages.AsEnumerable();

        // Check if the provided transformation package data is null
        if (transformationPackage == null)
        {
            // Prepare the error view model and return the view
            await responseBase.DisplayHtmxError();
            return Content("Could not create new transformation package: no data supplied");
        }

        // Validate the input data; if invalid, return an error view
        if (string.IsNullOrEmpty(transformationPackage.PackageName) ||
            string.IsNullOrEmpty(transformationPackage.PackageDescription) ||
            transformationPackage.PercentMarkup <= 0 ||
            transformationPackage.PercentMarkup > 100)
        {
            // Prepare the error view model and return the view
            await responseBase.DisplayHtmxError();
            return Content("Could not create new transformation package: input data is invalid");
        }

        // Enable percent markup by default
        transformationPackage.EnablePercentMarkup = true;

        // Add the new transformation package to the database
        dbDataFeedsContext.CommerceTransformationPackages?.Add(transformationPackage);

        // Save changes to the database asynchronously
        await dbDataFeedsContext.SaveChangesAsync();
        // Don't need to fetch the transformation packages again after the addition, because we are using AsEnumerable()

        // Prepare the success view model and return the updated view
        var viewModel = new GetTransformationPackagesModel
            { TransformationPackages = transformationPackages };

        // Check if there are any transformation packages; if not, set an error message
        if ((viewModel.TransformationPackages ?? Array.Empty<CommerceTransformationPackages>()).Any() == false)
        {
            await responseBase.DisplayHtmxError();
            return Content("No transformation packages found");
        }

        await responseBase.DisplayHtmxSuccess("Transformation package added successfully");
        return PartialView("_TransformationPackages", viewModel);
    }
}
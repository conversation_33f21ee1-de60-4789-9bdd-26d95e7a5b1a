#nullable enable
using System;
using System.Data.Entity;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using CSharpFunctionalExtensions;
using CsvHelper;
using Datafeed_v2.Helpers;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Models.Jim2Api;
using Datafeed_v2.Models.KQM;
using LogErrorsToAutotask;

namespace Datafeed_v2.Controllers;

[Authorize]
[LogErrorsToAutotask(Source = "Datafeedv2")]
public class KQMController : Controller
{
    [AllowAnonymous]
    public async Task<ActionResult> CsvFeed()
    {
        // Get stock data from Jim2 using the helper
        var stockDataResult = await Jim2StockHelper.GetStockData(Response);
        if (stockDataResult.IsFailure)
        {
            Response.TrySkipIisCustomErrors = true;
            Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Content(stockDataResult.Error);
        }

        var (stockOnHand, stockCodeColumnIndex, qtyAvailableColumnIndex) = stockDataResult.Value;
        var firstResultSet = stockOnHand.resultSets.First();

        var memStream = new MemoryStream();
        var writer = new StreamWriter(memStream);
        var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

        // Get StockNo column index which is specific to this method
        var stockNoColumnIndexResult = firstResultSet.GetColumnIndex("StockNo");
        if (stockNoColumnIndexResult.IsFailure)
        {
            Response.TrySkipIisCustomErrors = true;
            Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Content(stockNoColumnIndexResult.Error);
        }

        var priceRrpColumnIndexResult = firstResultSet.GetColumnIndex("PriceRRP");
        if (priceRrpColumnIndexResult.IsFailure)
        {
            Response.TrySkipIisCustomErrors = true;
            Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Content(priceRrpColumnIndexResult.Error);
        }

        var descriptionColumnIndexResult = firstResultSet.GetColumnIndex("Description");
        if (descriptionColumnIndexResult.IsFailure)
        {
            Response.TrySkipIisCustomErrors = true;
            Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Content(descriptionColumnIndexResult.Error);
        }

        var longDescriptionColumnIndexResult = firstResultSet.GetColumnIndex("LongDescription");
        if (longDescriptionColumnIndexResult.IsFailure)
        {
            Response.TrySkipIisCustomErrors = true;
            Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Content(longDescriptionColumnIndexResult.Error);
        }

        var buyUnitColumnIndexResult = firstResultSet.GetColumnIndex("BuyUnit");
        if (buyUnitColumnIndexResult.IsFailure)
        {
            Response.TrySkipIisCustomErrors = true;
            Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Content(buyUnitColumnIndexResult.Error);
        }

        var qtyAvailableColumnIndexResult = firstResultSet.GetColumnIndex("QtyAvailable");
        if (qtyAvailableColumnIndexResult.IsFailure)
        {
            Response.TrySkipIisCustomErrors = true;
            Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Content(qtyAvailableColumnIndexResult.Error);
        }

        var priceIncL1ColumnIndexResult = firstResultSet.GetColumnIndex("PriceInc L1");
        if (priceIncL1ColumnIndexResult.IsFailure)
        {
            Response.TrySkipIisCustomErrors = true;
            Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Content(priceIncL1ColumnIndexResult.Error);
        }

        var priceExL1ColumnIndexResult = firstResultSet.GetColumnIndex("PriceEx L1");
        if (priceExL1ColumnIndexResult.IsFailure)
        {
            Response.TrySkipIisCustomErrors = true;
            Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Content(priceExL1ColumnIndexResult.Error);
        }

        var categoryColumnIndexResult = firstResultSet.GetColumnIndex("Category1");
        if (categoryColumnIndexResult.IsFailure)
        {
            Response.TrySkipIisCustomErrors = true;
            Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Content(categoryColumnIndexResult.Error);
        }

        var brandColumnIndexResult = firstResultSet.GetColumnIndex("Brand");
        if (brandColumnIndexResult.IsFailure)
        {
            Response.TrySkipIisCustomErrors = true;
            Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Content(brandColumnIndexResult.Error);
        }

        var avgCostColumnIndexResult = firstResultSet.GetColumnIndex("AvgCost");
        if (avgCostColumnIndexResult.IsFailure)
        {
            Response.TrySkipIisCustomErrors = true;
            Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Content(avgCostColumnIndexResult.Error);
        }

        var csvModel = firstResultSet.rows.Select(q => new GetDataCsvModel
        {
            SKU = q[stockCodeColumnIndex].ToString(),
            RRP_Ex = 0,
            RRP_Inc = Result.Try(() => Convert.ToDecimal(q[priceRrpColumnIndexResult.Value].ToString()))
                .GetValueOrDefault(0),
            Product_Description = q[descriptionColumnIndexResult.Value].ToString(),
            Stock = Result.Try(() => Convert.ToDecimal(q[qtyAvailableColumnIndex].ToString()))
                .GetValueOrDefault(0),
            Image_URL = string.Empty,
            Manufacturer = Result.Try(() => q[brandColumnIndexResult.Value].ToString()).GetValueOrDefault(string.Empty),
            Price_Inc = Result.Try(() => Convert.ToDecimal(q[avgCostColumnIndexResult.Value].ToString()))
                .GetValueOrDefault(0),
            Price_Ex = Result.Try(() => Convert.ToDecimal(q[avgCostColumnIndexResult.Value].ToString()))
                .GetValueOrDefault(0),
            Product_Name = Result.Try(() => q[longDescriptionColumnIndexResult.Value].ToString())
                .GetValueOrDefault(string.Empty),
            Category = Result.Try(() => q[categoryColumnIndexResult.Value].ToString()).GetValueOrDefault(string.Empty)
        }).ToArray();

        csv.Context.RegisterClassMap<GetDataCsvModelMap>();
        await csv.WriteRecordsAsync(csvModel);
        await writer.FlushAsync();
        var test = memStream.ToArray();

        return File(test, "text/csv", "StockOnHand.csv");
    }

    public async Task<ActionResult> GetCategories(EdunetDatafeedsEntities dataFeedsContext = null)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();

        var parentCategories = await dbDataFeedsContext.NopCategories
            .Where(q => q.ParentCategoryId == 0).ToArrayAsync();
        var viewModel = new GetCategoriesModel { Categories = parentCategories };

        return PartialView("_Categories", viewModel);
    }

    public async Task<ActionResult> GetAllProducts(EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        // Initialise the database context if not provided
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        // Retrieve products for the given category and group them by productID (there will be multiple entries for each supplier)
        // var products = dbDataFeedsContext.KQMProducts.GroupBy(q => q.productID).AsEnumerable();
        var products = dbDataFeedsContext.PriceBookAndKQMMultiplexedProducts.GroupBy(q => q.ProductID).AsEnumerable();

        // Retrieve all transformation packages asynchronously
        var transformationPackages = await dbDataFeedsContext.CommerceTransformationPackages.ToArrayAsync();

        // Initialise the view model with products and transformation packages
        var viewModel = new GetProductsForCategoryModel
            { IsViewAllProducts = true, Products = products, TransformationPackages = transformationPackages };

        // If no products are found, set an error message in the view model
        if (viewModel.Products.Any() == false)
        {
            await responseBase.DisplayHtmxError();
            return Content("No products found");
        }

        // Return the partial view with the view model
        return PartialView("_ProductsForCategory", viewModel);
    }

    public async Task<ActionResult> GetAddNewProductModal()
    {
        return PartialView("_AddNewProductModal");
    }

    [HttpPost]
    public async Task<ActionResult> AddProductSKU(string ProductSKU,
        EdunetDatafeedsEntities dataFeedsContext = null, Maybe<HttpResponseBase> responseBaseMock = default)
    {
        // Initialise the database context if not provided
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        if (string.IsNullOrEmpty(ProductSKU))
        {
            await responseBase.DisplayHtmxError();
            return Content("Please enter a product SKU");
        }

        var productSkuRequest = new KQMPendingProductSKUs
        {
            ProductSKU = ProductSKU.Trim(),
            RequestingUser = (User.Identity as ClaimsIdentity)?.Claims
                .SingleOrDefault(q => q.Type == "preferred_username")?.Value,
            RequestedDateTime = DateTime.Now,
        };
        dbDataFeedsContext.KQMPendingProductSKUs.Add(productSkuRequest);
        await dbDataFeedsContext.SaveChangesAsync();

        // Retrieve products for the given category and group them by productID (there will be multiple entries for each supplier)
        var products =
            dbDataFeedsContext.PriceBookAndKQMMultiplexedProducts.GroupBy(q => q.ProductID)
                .AsEnumerable();

        // Retrieve all transformation packages asynchronously
        var transformationPackages = await dbDataFeedsContext.CommerceTransformationPackages.ToArrayAsync();

        // Initialise the view model with products and transformation packages
        var viewModel = new GetProductsForCategoryModel
            { Products = products, TransformationPackages = transformationPackages };

        await responseBase.DisplayHtmxSuccess("Product SKU added to the request queue successfully");
        return PartialView("_ProductsForCategory", viewModel);
    }

    public async Task<ActionResult> GetProductsOrChildrenCategoriesForCategory(uint nopCategoryId,
        EdunetDatafeedsEntities dataFeedsContext = null, Maybe<HttpResponseBase> responseBaseMock = default)
    {
        // Initialise the database context if not provided
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        // Get the products
        var products = dbDataFeedsContext.PriceBookAndKQMMultiplexedProducts
            .Where(q => q.CategoryID == nopCategoryId)
            .GroupBy(q => q.ProductID).AsEnumerable();

        var category = dbDataFeedsContext.NopCategories.SingleOrDefault(q => q.CategoryId == nopCategoryId);
        if (category == null)
        {
            await responseBase.DisplayHtmxError();
            return Content("Category not found");
        }

        var childrenCategories = await dbDataFeedsContext.NopCategories.Where(q => q.ParentCategoryId != 0)
            .ToArrayAsync();

        foreach (var productGroup in products)
        {
            foreach (var product in productGroup)
            {
                product.CategoryID =
                    (int)nopCategoryId; // overwrite the product (KQM) categoryID with the selected Datafeed categoryID
            }
        }

        // Retrieve all transformation packages asynchronously
        var transformationPackages = await dbDataFeedsContext.CommerceTransformationPackages.ToArrayAsync();

        // Initialise the view model with products and transformation packages
        var viewModel = new GetProductsForCategoryModel
        {
            Products = products,
            TransformationPackages = transformationPackages,
            ChildrenCategories = childrenCategories.AsMaybe()
        };

        // Get the transformation package ID applied to the category, if any
        var transformationPackageId =
            (await dbDataFeedsContext.NopCategories.SingleOrDefaultAsync(q => q.ID == nopCategoryId))
            ?.AppliedTransformationPackage ?? -1;

        // Find the transformation package by ID
        var transformationPackage =
            transformationPackages.SingleOrDefault(q => q.ID == transformationPackageId);

        // If a transformation package is found, set it in the view model
        if (transformationPackage == null)
        {
            // probably don't need to show an error
            // viewModel.Error = "No transformation package found for this category";
        }
        else
        {
            viewModel.AppliedTransformationPackageForCategory = transformationPackage.ID;
            viewModel.AppliedTransformationPackageNameForCategory = transformationPackage.PackageName;
        }

        // If no products are found, set an error message in the view model
        if (viewModel.Products.Any() == false)
        {
            // TODO: Maybe show all products category if no products are found
            await responseBase.DisplayHtmxError("No products found for this category");
            // Don't return here because we still want an empty table to show
        }

        // Return the partial view with the view model
        return PartialView("_ProductsForCategory", viewModel);
    }

    public async Task<ActionResult> GetProduct(uint productId, uint nopCategoryId, bool isViewAllProducts,
        EdunetDatafeedsEntities dataFeedsContext = null, Maybe<HttpResponseBase> responseBaseMock = default)
    {
        // Initialise the database context if not provided
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        // Retrieve all product entries for the given product ID
        var products = await dbDataFeedsContext.PriceBookAndKQMMultiplexedProducts
            .Where(q => q.ProductID == productId)
            .ToArrayAsync();

        foreach (var product in products)
        {
            product.CategoryID =
                // (int)datafeedCategoryId; // overwrite the product (KQM) categoryID with the selected Datafeed categoryID
                (int)nopCategoryId; // overwrite the product (KQM) categoryID with the selected Datafeed categoryID
        }

        // Retrieve all transformation packages
        var transformationPackages = dbDataFeedsContext.CommerceTransformationPackages.ToArray();

        // Map each product to its applied transformation package name
        // var categoryTransformId = (await dbDataFeedsContext.DatafeedCategories.SingleOrDefaultAsync(q =>
        //     q.ID == datafeedCategoryId))?.AppliedTransformationPackage ?? -1;
        var categoryTransformId = (await dbDataFeedsContext.NopCategories.SingleOrDefaultAsync(q =>
            q.ID == nopCategoryId))?.AppliedTransformationPackage ?? -1;
        var categoryTransform = transformationPackages.SingleOrDefault(q => q.ID == categoryTransformId);
        var productsAppliedTransformationPackage = products.Select(q =>
            transformationPackages.SingleOrDefault(t => t.ID == q.AppliedTransformationPackage));

        // Combine products with their corresponding transformation package names
        var productsWithAppliedTransformationPackageName = products.Zip(productsAppliedTransformationPackage,
            (p, t) =>
            {
                p.AppliedTransformationPackageClass =
                    t ?? categoryTransform; // if the product has no transformation package, use the category transformation package
                return p;
            });

        // Initialise the view model with products, suppliers, and transformation packages
        var viewModel = new GetProductModel
        {
            IsViewAllProducts = isViewAllProducts,
            Products = productsWithAppliedTransformationPackageName,
            KqmProductImages = dbDataFeedsContext.KqmProductImages.Where(q => q.KqmProductId == productId).ToArray(),
            TransformationPackages = transformationPackages
        };

        // If no products are found, set an error message in the view model
        if (viewModel.Products.Any() == false)
        {
            await responseBase.DisplayHtmxError();
            return Content("No products found for this ID");
        }

        // Return the partial view with the view model
        return PartialView("_Product", viewModel);
    }

    public async Task<ActionResult> ApplyTransformationPackageToProduct(uint productId,
        uint transformationPackageId,
        EdunetDatafeedsEntities dataFeedsContext = null, Maybe<HttpResponseBase> responseBaseMock = default)
    {
        // Initialise the database context if not provided
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        // Retrieve all product entries for the given product ID
        var products = dbDataFeedsContext.PriceBookAndKQMMultiplexedProducts
            .Where(q => q.ProductID == productId)
            .AsNoTracking() // Need this because this view currently doesn't have any primary key so LINQ doesn't track updates properly
            .AsEnumerable();

        // Retrieve all transformation packages
        var transformationPackages = await dbDataFeedsContext.CommerceTransformationPackages.ToArrayAsync();

        // Initialise the view model with suppliers and transformation packages
        var viewModel = new GetProductModel
        {
            TransformationPackages = transformationPackages
        };

        // Check that the transformation package exists
        var transformationPackage = transformationPackages.SingleOrDefault(q => q.ID == transformationPackageId);
        if (transformationPackage == null)
        {
            // Set an error message if the transformation package is not found
            await responseBase.DisplayHtmxError();
            return Content("No transformation package found for this ID");
        }

        // Apply the transformation package to each product
        foreach (var product in products)
        {
            switch (product.Source)
            {
                case 1: // KQM
                    var kqmProduct = await dbDataFeedsContext.KQMProducts.FirstOrDefaultAsync(q =>
                        q.productID == product.ProductID);
                    if (kqmProduct == null)
                    {
                        await responseBase.DisplayHtmxError();
                        return Content("No KQM product found for this ID");
                    }

                    kqmProduct.AppliedTransformationPackage = transformationPackage.ID;
                    break;
                case 2: // PriceBook
                    var priceBookProduct = await dbDataFeedsContext.ImportedItemsFromPriceBook.FirstOrDefaultAsync(q =>
                        q.ID == product.ProductID);
                    if (priceBookProduct == null)
                    {
                        await responseBase.DisplayHtmxError();
                        return Content("No PriceBook product found for this ID");
                    }

                    priceBookProduct.AppliedTransformationPackage = transformationPackage.ID;
                    break;
            }
        }

        // Save changes to the database
        await dbDataFeedsContext.SaveChangesAsync();

        // Map each product to its applied transformation package name
        var productsAppliedTransformationPackage = products.Select(q =>
            transformationPackages.SingleOrDefault(t => t.ID == q.AppliedTransformationPackage));

        // Combine products with their corresponding transformation package names
        var productsWithAppliedTransformationPackageName = products.Zip(productsAppliedTransformationPackage,
            (p, t) =>
            {
                p.AppliedTransformationPackageClass = t;
                return p;
            });

        // Update the view model with the products
        viewModel.Products = productsWithAppliedTransformationPackageName;

        // If no products are found, set an error message in the view model
        if (viewModel.Products.Any() == false)
        {
            await responseBase.DisplayHtmxError();
            return Content("No products found for this ID");
        }

        // Return the partial view with the view model
        return PartialView("_Product", viewModel);
    }

    public async Task<ActionResult> ApplyTransformationPackageToCategory(uint categoryId,
        uint transformationPackageId,
        EdunetDatafeedsEntities dataFeedsContext = null, Maybe<HttpResponseBase> responseBaseMock = default)
    {
        // Initialise the database context if not provided
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        // Retrieve products for the given category and group them by productID (there will be multiple entries for each supplier)
        var products = await dbDataFeedsContext.PriceBookAndKQMMultiplexedProducts
            .Where(q => q.CategoryID == categoryId)
            .GroupBy(q => q.ProductID).ToArrayAsync();
        var viewModel = new GetProductsForCategoryModel
        {
            Products = products,
            TransformationPackages = dbDataFeedsContext.CommerceTransformationPackages.ToArray()
        };

        // If no products are found, set an error message in the view model and return the partial view
        if (viewModel.Products.Any() == false)
        {
            await responseBase.DisplayHtmxError();
            return Content("No products found for this category");
        }

        // Retrieve the category by ID and ensure it exists
        // var category = await dbDataFeedsContext.KQMCategories.SingleOrDefaultAsync(q => q.categoryID == categoryId);
        // var category = await dbDataFeedsContext.DatafeedCategories.SingleOrDefaultAsync(q => q.ID == categoryId);
        var category = await dbDataFeedsContext.NopCategories.SingleOrDefaultAsync(q => q.ID == categoryId);
        Debug.Assert(category != null, nameof(category) + " != default");

        // Retrieve all transformation packages
        var transformationPackages = await dbDataFeedsContext.CommerceTransformationPackages.ToArrayAsync();

        // Check that the transformation package exists
        var transformationPackage = transformationPackages.SingleOrDefault(q => q.ID == transformationPackageId);
        if (transformationPackage == null)
        {
            // Set an error message if the transformation package is not found
            await responseBase.DisplayHtmxError();
            return Content("No transformation package found for this ID");
        }

        // Update the view model with the transformation package details
        viewModel.AppliedTransformationPackageForCategory = transformationPackage.ID;
        viewModel.AppliedTransformationPackageNameForCategory = transformationPackage.PackageName;

        // Apply the transformation package to the category
        category.AppliedTransformationPackage = transformationPackage.ID;

        // Save changes to the database
        await dbDataFeedsContext.SaveChangesAsync();

        // Return the partial view with the view model
        return PartialView("_ProductsForCategory", viewModel);
    }
}
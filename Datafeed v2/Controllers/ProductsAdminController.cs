using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using CSharpFunctionalExtensions;
using Datafeed_v2.Hangfire.Tasks;
using Datafeed_v2.Helpers;
using Datafeed_v2.Models;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Models.NopModels;
using Datafeed_v2.Models.ProductsAdmin;
using Datafeed_v2.Properties;
using Datafeed_v2.Services;
using Datafeed_v2.Singletons;
using Hangfire;
using LogErrorsToAutotask;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Datafeed_v2.Controllers;

public interface IUrlHelper
{
    string Action(string actionName, string controllerName);
}

public class DiUrlHelper(UrlHelper urlHelper) : IUrlHelper
{
    public DiUrlHelper() : this(null)
    {
    }

    public virtual bool IsValid => urlHelper != null;

    public virtual string Action(string actionName, string controllerName)
    {
        return urlHelper.Action(actionName, controllerName);
    }
}

public class AllowHtmlBinder : IModelBinder
{
    public object BindModel(ControllerContext controllerContext, ModelBindingContext bindingContext)
    {
        var request = controllerContext.HttpContext.Request;
        var name = bindingContext.ModelName;
        return request.Unvalidated[name];
    }
}

[Authorize]
[LogErrorsToAutotask(Source = "Datafeedv2")]
public class ProductsAdminController : Controller
{
    private const string MissingDescription = "Missing Description";
    private const string MissingImages = "Missing Images";
    private const string MissingPricing = "Missing Pricing";

    public ActionResult Index()
    {
        return View();
    }

    private static async Task<Result<(string, string, string[], string[]), string>> GeneratePerplexityDescriptionAsync(
        string prompt, string[] features, string[] specs)
    {
        var (perplexitySucceeded, _, perplexityResponse, perplexityError) =
            await AILibrarySingleton.Instance.GetPerplexityResponseAsync(prompt, features, specs);
        if (perplexitySucceeded == false)
        {
            return Result.Failure<(string, string, string[], string[]), string>(perplexityError.Message);
        }

        var perplexityJson = perplexityResponse.Choices.FirstOrDefault()?.Message.Content
            .Replace("```json", "").Replace("```", "");
        if (string.IsNullOrWhiteSpace(perplexityJson))
        {
            return Result.Failure<(string, string, string[], string[]), string>(
                "Failed to generate Perplexity description: JSON content is empty or null");
        }

        JObject perplexityParsedJson;
        try
        {
            perplexityParsedJson = JObject.Parse(perplexityJson);
        }
        catch (JsonReaderException ex)
        {
            return Result.Failure<(string, string, string[], string[]), string>(
                $"Failed to parse Perplexity JSON: {ex.Message}");
        }

        var perplexityDescription = perplexityParsedJson["product_description"];
        if (perplexityDescription == null)
        {
            return Result.Failure<(string, string, string[], string[]), string>(
                "Failed to generate Perplexity description");
        }

        var perplexityShortDescription = perplexityParsedJson["product_short_description"];
        if (perplexityShortDescription == null)
        {
            return Result.Failure<(string, string, string[], string[]), string>(
                "Failed to generate Perplexity short description");
        }

        var perplexityFeatures = perplexityParsedJson["product_features"];
        if (perplexityFeatures == null)
        {
            return Result.Failure<(string, string, string[], string[]), string>(
                "Failed to generate Perplexity features");
        }

        var perplexitySpecs = perplexityParsedJson["product_specifications"];
        if (perplexitySpecs == null)
        {
            return Result.Failure<(string, string, string[], string[]), string>(
                "Failed to generate Perplexity specifications");
        }

        var perplexityParsedFeatures = JArray.Parse(perplexityFeatures.ToString())
            .Select(feature => feature.Value<string>()).ToArray();
        var perplexityParsedSpecs =
            JArray.Parse(perplexitySpecs.ToString()).Select(spec => spec.Value<string>()).ToArray();

        return Result.Success<(string, string, string[], string[]), string>((perplexityDescription.Value<string>(),
            perplexityShortDescription.Value<string>(),
            perplexityParsedFeatures, perplexityParsedSpecs));
    }

    private static async Task<Result<(string, string, string[], string[]), string>> GenerateGeminiDescriptionAsync(
        string prompt, string[] features, string[] specs)
    {
        var (geminiSucceeded, _, geminiResponse, geminiError) =
            await AILibrarySingleton.Instance.GetGeminiResponseAsync(prompt, features, specs);
        if (geminiSucceeded == false)
        {
            return Result.Failure<(string, string, string[], string[]), string>(geminiError.Message);
        }

        var geminiJson = geminiResponse.Candidates.FirstOrDefault()?.Content.Parts.FirstOrDefault()?.Text
            .Replace("```json", "").Replace("```", "");
        if (string.IsNullOrWhiteSpace(geminiJson))
        {
            return Result.Failure<(string, string, string[], string[]), string>(
                "Failed to generate Gemini description");
        }

        var geminiParsedJson = JObject.Parse(geminiJson);
        var geminiDescription = geminiParsedJson["product_description"];
        if (geminiDescription == null)
        {
            return Result.Failure<(string, string, string[], string[]), string>(
                "Failed to generate Gemini description");
        }

        var geminiShortDescription = geminiParsedJson["product_short_description"];
        if (geminiShortDescription == null)
        {
            return Result.Failure<(string, string, string[], string[]), string>(
                "Failed to generate Gemini short description");
        }

        var geminiFeatures = geminiParsedJson["product_features"];
        if (geminiFeatures == null)
        {
            return Result.Failure<(string, string, string[], string[]), string>("Failed to generate Gemini features");
        }

        var geminiSpecs = geminiParsedJson["product_specifications"];
        if (geminiSpecs == null)
        {
            return Result.Failure<(string, string, string[], string[]), string>(
                "Failed to generate Gemini specifications");
        }

        var geminiParsedFeatures =
            JArray.Parse(geminiFeatures.ToString()).Select(feature => feature.Value<string>()).ToArray();
        var geminiParsedSpecs = JArray.Parse(geminiSpecs.ToString()).Select(spec => spec.Value<string>()).ToArray();

        return Result.Success<(string, string, string[], string[]), string>((geminiDescription.Value<string>(),
            geminiShortDescription.Value<string>(),
            geminiParsedFeatures, geminiParsedSpecs));
    }

    private static async Task<Result<(string, string, string[], string[]), string>>
        GenerateKaseyaGeminiDescriptionAsync(
            string description, string[] requiredSpecifications, string specifications)
    {
        var (geminiSucceeded, _, geminiResponse, geminiError) =
            await AILibrarySingleton.Instance.GetKaseyaGeminiResponseAsync(description, requiredSpecifications,
                specifications);
        if (geminiSucceeded == false)
        {
            return Result.Failure<(string, string, string[], string[]), string>(geminiError.Message);
        }

        var geminiJson = geminiResponse.Candidates.FirstOrDefault()?.Content.Parts.FirstOrDefault()?.Text
            .Replace("```json", "").Replace("```", "");
        if (string.IsNullOrWhiteSpace(geminiJson))
        {
            return Result.Failure<(string, string, string[], string[]), string>(
                "Failed to generate Gemini description");
        }

        var geminiParsedJson = JObject.Parse(geminiJson);
        var geminiDescription = geminiParsedJson["product_description"];
        if (geminiDescription == null)
        {
            return Result.Failure<(string, string, string[], string[]), string>(
                "Failed to generate Gemini description");
        }

        var geminiShortDescription = geminiParsedJson["product_short_description"];
        if (geminiShortDescription == null)
        {
            return Result.Failure<(string, string, string[], string[]), string>(
                "Failed to generate Gemini short description");
        }

        // var geminiFeatures = geminiParsedJson["product_features"];
        // if (geminiFeatures == null)
        // {
        //     return Result.Failure<(string, string, string[], string[]), string>("Failed to generate Gemini features");
        // }

        var geminiSpecs = geminiParsedJson["product_specifications"];
        if (geminiSpecs == null)
        {
            return Result.Failure<(string, string, string[], string[]), string>(
                "Failed to generate Gemini specifications");
        }

        // var geminiParsedFeatures =
        //     JArray.Parse(geminiFeatures.ToString()).Select(feature => feature.Value<string>()).ToArray();
        var geminiParsedSpecs = JArray.Parse(geminiSpecs.ToString()).Select(spec => spec.Value<string>()).ToArray();

        return Result.Success<(string, string, string[], string[]), string>((geminiDescription.Value<string>(),
            geminiShortDescription.Value<string>(), [], geminiParsedSpecs));
    }

    private static async Task<Result<ProductAttributes[]>> GetProductAttributes(string category)
    {
        var encodedCategory = HttpUtility.UrlEncode(category);
        var attributesUrl = $"ProductAttributes/GetSpecificationAttributesForCategory?categories={encodedCategory}";
        using var client = new HttpClient();
        client.BaseAddress = new Uri(Debugger.IsAttached ? "https://localhost:5001/" : Settings.Default.NopLiveSiteUrl);
        var req = await client.GetAsync(attributesUrl);
        if (req.IsSuccessStatusCode == false)
        {
            return Result.Failure<ProductAttributes[]>(
                $"Failed to get attributes for product category `{category}`: {req.StatusCode}");
        }

        var res = await req.Content.ReadAsStringAsync();
        var (_, isFailure, attributes, error) =
            Result.Try(() => JsonConvert.DeserializeObject<ProductAttributes[]>(res));
        return isFailure ? Result.Failure<ProductAttributes[]>(error) : Result.Success<ProductAttributes[]>(attributes);
    }

    [HttpPost]
    public async Task<ActionResult> GenerateDescriptionForPriceBookProduct(uint productId, string model,
        EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var product = dbDataFeedsContext.ImportedItemsFromPriceBook.SingleOrDefault(q => q.ID == productId);
        if (product == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Product not found").DisplayAsAlert("danger");
        }

        var productMapping =
            dbDataFeedsContext.MappedPriceBookItemsForNopCategory.SingleOrDefault(q => q.ID == productId);
        if (productMapping == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Could not get category - product is not mapped").DisplayAsAlert("danger");
        }

        var category =
            dbDataFeedsContext.NopCategories.SingleOrDefault(q => q.CategoryId == productMapping.NopCategoryId);
        if (category == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Category not found").DisplayAsAlert("danger");
        }

        var (_, getProductAttributesFailed, productAttributes, getProductAttributesError) =
            await GetProductAttributes(category.Name);
        if (getProductAttributesFailed)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content(getProductAttributesError).DisplayAsAlert("danger");
        }

        if (productAttributes.Any() == false)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("No attributes found for product").DisplayAsAlert("danger");
        }

        // Check if the product has already been processed
        var productInfo = dbDataFeedsContext.AIGeneratedProductInfo
            .Where(q => q.ProductId == product.ID && q.ProductSource == ProductType.Pricebook).ToArray();
        if (productInfo.Any())
        {
            var description =
                productInfo.SingleOrDefault(q => q.Attribute == ProductInfoAttributes.LongDescription)?.Value ??
                string.Empty;
            var shortDescription =
                productInfo.SingleOrDefault(q => q.Attribute == ProductInfoAttributes.ShortDescription)?.Value ??
                string.Empty;
            var features = productInfo.Where(q => q.Attribute == ProductInfoAttributes.Feature).Select(q => q.Value)
                .ToArray();
            var specs = productInfo.Where(q => q.Attribute == ProductInfoAttributes.Spec).Select(q => q.Value)
                .ToArray();

            responseBase.Headers.Add("hx-trigger", JsonConvert.SerializeObject(new
            {
                aiDescription = new
                {
                    description,
                    shortDescription,
                    features,
                    specs
                }
            }));
            return new HttpStatusCodeResult(HttpStatusCode.NoContent);
        }

        var prompt = $"{product.Brand} {product.ProductSKU}";

        Result<(string, string, string[], string[]), string> descriptionResult;

        // Determine which model to use based on the passed argument
        var productAttributesForCategory = productAttributes.FirstOrDefault(q => string.Equals(q.Category.Trim(),
            category.Name.Trim(),
            StringComparison.CurrentCultureIgnoreCase));
        if (productAttributesForCategory == null || productAttributesForCategory.Attributes.Any() == false)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("No attributes found for category").DisplayAsAlert("danger");
        }

        if (model.Equals("Gemini", StringComparison.OrdinalIgnoreCase))
        {
            descriptionResult = await GenerateGeminiDescriptionAsync(prompt, [""],
                productAttributesForCategory.Attributes.Select(q => q.Name).ToArray());
        }
        else if (model.Equals("Perplexity", StringComparison.OrdinalIgnoreCase))
        {
            descriptionResult = await GeneratePerplexityDescriptionAsync(prompt, [""],
                productAttributesForCategory.Attributes.Select(q => q.Name).ToArray());
        }
        else
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Invalid model specified").DisplayAsAlert("danger");
        }

        if (descriptionResult.IsFailure)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content(descriptionResult.Error).DisplayAsAlert("danger");
        }

        // Save the response to the database
        dbDataFeedsContext.AIGeneratedProductInfo.Add(new AIGeneratedProductInfo
        {
            ProductId = (int)productId,
            ProductSource = ProductType.Pricebook,
            Attribute = ProductInfoAttributes.LongDescription,
            Value = descriptionResult.Value.Item1,
        });
        dbDataFeedsContext.AIGeneratedProductInfo.Add(new AIGeneratedProductInfo
        {
            ProductId = (int)productId,
            ProductSource = ProductType.Pricebook,
            Attribute = ProductInfoAttributes.ShortDescription,
            Value = descriptionResult.Value.Item2,
        });

        foreach (var feature in descriptionResult.Value.Item3)
        {
            dbDataFeedsContext.AIGeneratedProductInfo.Add(new AIGeneratedProductInfo
            {
                ProductId = (int)productId,
                ProductSource = ProductType.ProductSource,
                Attribute = ProductInfoAttributes.Feature,
                Value = feature
            });
        }

        foreach (var spec in descriptionResult.Value.Item4)
        {
            dbDataFeedsContext.AIGeneratedProductInfo.Add(new AIGeneratedProductInfo
            {
                ProductId = (int)productId,
                ProductSource = ProductType.Pricebook,
                Attribute = ProductInfoAttributes.Spec,
                Value = spec
            });
        }

        await dbDataFeedsContext.SaveChangesAsync();

        responseBase.Headers.Add("hx-trigger", JsonConvert.SerializeObject(new
        {
            aiDescription = new
            {
                description = descriptionResult.Value.Item1,
                shortDescription = descriptionResult.Value.Item2,
                features = descriptionResult.Value.Item3,
                specs = descriptionResult.Value.Item4
            }
        }));
        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }

    [HttpPost]
    public async Task<ActionResult> GenerateDescriptionForProductSource(uint sourceId, string model,
        EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var productSource = dbDataFeedsContext.ProductSource.SingleOrDefault(q => q.ID == sourceId);
        if (productSource == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Product Source not found").DisplayAsAlert("danger");
        }

        var productSourceMapping =
            dbDataFeedsContext.ProductSourcesForNopCategory.SingleOrDefault(q => q.ID == productSource.ID);
        if (productSourceMapping == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Could not get category - product source is not mapped").DisplayAsAlert("danger");
        }

        var brand = productSource.Brand;

        var category =
            dbDataFeedsContext.NopCategories.SingleOrDefault(q => q.CategoryId == productSourceMapping.CategoryId);
        if (category == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Category not found").DisplayAsAlert("danger");
        }

        var (_, getProductAttributesFailed, productAttributes, getProductAttributesError) =
            await GetProductAttributes(category.Name);
        if (getProductAttributesFailed)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content(getProductAttributesError).DisplayAsAlert("danger");
        }

        if (productAttributes.Any() == false)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("No attributes found for product").DisplayAsAlert("danger");
        }

        // Check if the product has already been processed
        var productInfo = dbDataFeedsContext.AIGeneratedProductInfo
            .Where(q => q.ProductId == productSource.ID && q.ProductSource == ProductType.ProductSource).ToArray();
        if (productInfo.Any())
        {
            var description =
                productInfo.SingleOrDefault(q => q.Attribute == ProductInfoAttributes.LongDescription)?.Value ??
                string.Empty;
            var shortDescription =
                productInfo.SingleOrDefault(q => q.Attribute == ProductInfoAttributes.ShortDescription)?.Value ??
                string.Empty;
            var features = productInfo.Where(q => q.Attribute == ProductInfoAttributes.Feature).Select(q => q.Value)
                .ToArray();
            var specs = productInfo.Where(q => q.Attribute == ProductInfoAttributes.Spec).Select(q => q.Value)
                .ToArray();

            responseBase.Headers.Add("hx-trigger", JsonConvert.SerializeObject(new
            {
                aiDescription = new
                {
                    description,
                    shortDescription,
                    features,
                    specs
                }
            }));
            return new HttpStatusCodeResult(HttpStatusCode.NoContent);
        }

        var prompt = $"{brand} {productSource.ProductSku}";

        Result<(string, string, string[], string[]), string> descriptionResult;

        // Determine which model to use based on the passed argument
        var productAttributesForCategory = productAttributes.FirstOrDefault(q => string.Equals(q.Category.Trim(),
            category.Name.Trim(),
            StringComparison.CurrentCultureIgnoreCase));
        if (productAttributesForCategory == null || productAttributesForCategory.Attributes.Any() == false)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("No attributes found for category").DisplayAsAlert("danger");
        }

        if (model.Equals("Gemini", StringComparison.OrdinalIgnoreCase))
        {
            descriptionResult = await GenerateGeminiDescriptionAsync(prompt, [""],
                productAttributesForCategory.Attributes.Select(q => q.Name).ToArray());
        }
        else if (model.Equals("Perplexity", StringComparison.OrdinalIgnoreCase))
        {
            descriptionResult = await GeneratePerplexityDescriptionAsync(prompt, [""],
                productAttributesForCategory.Attributes.Select(q => q.Name).ToArray());
        }
        else
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Invalid model specified").DisplayAsAlert("danger");
        }

        if (descriptionResult.IsFailure)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content(descriptionResult.Error).DisplayAsAlert("danger");
        }

        // Save the response to the database
        dbDataFeedsContext.AIGeneratedProductInfo.Add(new AIGeneratedProductInfo
        {
            ProductId = (int)sourceId,
            ProductSource = ProductType.ProductSource,
            Attribute = ProductInfoAttributes.LongDescription,
            Value = descriptionResult.Value.Item1,
        });
        dbDataFeedsContext.AIGeneratedProductInfo.Add(new AIGeneratedProductInfo
        {
            ProductId = (int)sourceId,
            ProductSource = ProductType.ProductSource,
            Attribute = ProductInfoAttributes.ShortDescription,
            Value = descriptionResult.Value.Item2,
        });

        foreach (var feature in descriptionResult.Value.Item3)
        {
            dbDataFeedsContext.AIGeneratedProductInfo.Add(new AIGeneratedProductInfo
            {
                ProductId = (int)sourceId,
                ProductSource = ProductType.ProductSource,
                Attribute = ProductInfoAttributes.Feature,
                Value = feature
            });
        }

        foreach (var spec in descriptionResult.Value.Item4)
        {
            dbDataFeedsContext.AIGeneratedProductInfo.Add(new AIGeneratedProductInfo
            {
                ProductId = (int)sourceId,
                ProductSource = ProductType.ProductSource,
                Attribute = ProductInfoAttributes.Spec,
                Value = spec
            });
        }

        await dbDataFeedsContext.SaveChangesAsync();

        responseBase.Headers.Add("hx-trigger", JsonConvert.SerializeObject(new
        {
            aiDescription = new
            {
                description = descriptionResult.Value.Item1,
                shortDescription = descriptionResult.Value.Item2,
                features = descriptionResult.Value.Item3,
                specs = descriptionResult.Value.Item4
            }
        }));
        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }

    /// <summary>
    /// Retrieves product information from Icecat API for a given product source, with fallback to Kaseya if needed.
    /// </summary>
    /// <param name="sourceId">The ID of the product source to fetch information for</param>
    /// <param name="dataFeedsContext">Optional database context. If null, creates new context</param>
    /// <param name="responseBaseMock">Optional HTTP response base for testing</param>
    /// <returns>
    /// - On success: Returns NoContent (204) with product info in hx-trigger header
    /// - On failure: Returns error message with "danger" alert
    /// </returns>
    [HttpPost]
    public async Task<ActionResult> GetIcecatProductInfoForProductSource(uint sourceId,
        EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        // Initialise database context and response base, using defaults if not provided
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        // Retrieve the KQM product from database
        var productSource = dbDataFeedsContext.ProductSource.SingleOrDefault(q => q.ID == sourceId);
        if (productSource == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Product Source not found").DisplayAsAlert("danger");
        }

        // Get the brand information for the product
        var brand = productSource.Brand;

        // Query Icecat API for product information
        var icecatService = IcecatApiServiceSingleton.Instance;
        var (_, reqFailed, result, reqError) = await icecatService.GetProductInfoByManufacturerCode(brand,
            productSource.ProductSku,
            [IcecatApiContentTypes.GeneralInfo, IcecatApiContentTypes.ReasonsToBuy]);
        if (reqFailed)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content(reqError).DisplayAsAlert("danger");
        }

        // Get specifications from Icecat or fallback to Kaseya if none found
        var specs = result.Data?.ReasonsToBuy?.ToList() ?? [];
        if (!specs.Any())
        {
            // Attempt to get specifications from Kaseya
            var kaseyaScraper = new KaseyaEstoreScraperService();
            var (_, scrapeFailed, scrapeResult, _) =
                await kaseyaScraper.FetchProductInformationAsync(productSource.ProductSku);

            if (!scrapeFailed && scrapeResult?.ProductSpecifications != null)
            {
                // Extract and flatten specifications from Kaseya
                specs = scrapeResult.ProductSpecifications
                    .SelectMany(s => s.Values)
                    .ToList();

                // Process Kaseya specs through AI for better formatting
                var productSourceMapping = dbDataFeedsContext.ProductSourcesForNopCategory
                    .SingleOrDefault(q => q.ID == sourceId);
                if (productSourceMapping == null)
                {
                    responseBase.RetargetAlertOnProductsAdminPage();
                    return Content("Product source mapping not found").DisplayAsAlert("danger");
                }

                // Get category information for the product
                var category = dbDataFeedsContext.NopCategories
                    .SingleOrDefault(q => q.CategoryId == productSourceMapping.CategoryId);
                if (category == null)
                {
                    responseBase.RetargetAlertOnProductsAdminPage();
                    return Content("Category not found").DisplayAsAlert("danger");
                }

                // Retrieve product attributes for the category
                var (_, getProductAttributesFailed, productAttributes, getProductAttributesError) =
                    await GetProductAttributes(category.Name);
                if (getProductAttributesFailed)
                {
                    responseBase.RetargetAlertOnProductsAdminPage();
                    return Content(getProductAttributesError).DisplayAsAlert("danger");
                }

                // Find matching attributes for the category
                var productAttributesForCategory = productAttributes.FirstOrDefault(q =>
                    string.Equals(q.Category.Trim(), category.Name.Trim(),
                        StringComparison.CurrentCultureIgnoreCase));
                if (productAttributesForCategory?.Attributes == null ||
                    !productAttributesForCategory.Attributes.Any())
                {
                    responseBase.RetargetAlertOnProductsAdminPage();
                    return Content("No attributes found for category").DisplayAsAlert("danger");
                }

                // Generate AI-enhanced description using Gemini
                var descriptionResult = await GenerateKaseyaGeminiDescriptionAsync(
                    result.Data?.GeneralInfo?.Description?.LongDescription ??
                    result.Data?.GeneralInfo?.SummaryDescription?.LongDescription ?? string.Empty,
                    productAttributesForCategory.Attributes.Select(q => q.Name).ToArray(),
                    string.Join(", ", specs)
                );

                if (descriptionResult.IsFailure)
                {
                    responseBase.RetargetAlertOnProductsAdminPage();
                    return Content(descriptionResult.Error).DisplayAsAlert("danger");
                }

                // Use AI-generated specifications
                specs = descriptionResult.Value.Item4.ToList();
            }
        }

        // Prepare the response with product information
        var longDescription = result.Data?.GeneralInfo?.Description?.LongDescription;
        responseBase.Headers.Add("hx-trigger", JsonConvert.SerializeObject(new
        {
            icecatDescription = new
            {
                description = string.IsNullOrWhiteSpace(longDescription)
                    ? result.Data?.GeneralInfo?.SummaryDescription?.LongDescription
                    : longDescription,
                shortDescription = result.Data?.GeneralInfo?.SummaryDescription?.ShortDescription ?? string.Empty,
                features = new List<string>(),
                specs
            }
        }));
        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }

    [HttpPost]
    public async Task<ActionResult> ScrapeDescriptionForProductSource(uint sourceId,
        EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var productSource = dbDataFeedsContext.ProductSource.SingleOrDefault(q => q.ID == sourceId);
        if (productSource == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Product Source not found").DisplayAsAlert("danger");
        }

        var productSourceMapping =
            dbDataFeedsContext.ProductSourcesForNopCategory.SingleOrDefault(q => q.ID == sourceId);
        if (productSourceMapping == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Could not get category - product source is not mapped").DisplayAsAlert("danger");
        }

        var category =
            dbDataFeedsContext.NopCategories.SingleOrDefault(q => q.CategoryId == productSourceMapping.CategoryId);
        if (category == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Category not found").DisplayAsAlert("danger");
        }

        var (_, getProductAttributesFailed, productAttributes, getProductAttributesError) =
            await GetProductAttributes(category.Name);
        if (getProductAttributesFailed)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content(getProductAttributesError).DisplayAsAlert("danger");
        }

        if (productAttributes.Any() == false)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("No attributes found for product").DisplayAsAlert("danger");
        }

        var kaseyaScraperService = new KaseyaEstoreScraperService();
        var (_, scrapeFailed, scrapeResult, scrapeError) =
            await kaseyaScraperService.FetchProductInformationAsync(productSource.ProductSku);
        if (scrapeFailed)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content(scrapeError).DisplayAsAlert("danger");
        }

        var productAttributesForCategory = productAttributes.FirstOrDefault(q => string.Equals(q.Category.Trim(),
            category.Name.Trim(),
            StringComparison.CurrentCultureIgnoreCase));
        if (productAttributesForCategory == null || productAttributesForCategory.Attributes.Any() == false)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("No attributes found for category").DisplayAsAlert("danger");
        }

        if (string.IsNullOrWhiteSpace(scrapeResult.ProductDescription))
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Unable to scrape description from Kaseya Estore").DisplayAsAlert("danger");
        }

        var specs = scrapeResult.ProductSpecifications.SelectMany(q => q.Values).ToList();
        var descriptionResult = await GenerateKaseyaGeminiDescriptionAsync(scrapeResult.ProductDescription,
            productAttributesForCategory.Attributes.Select(q => q.Name).ToArray(),
            string.Join(", ", specs));

        if (descriptionResult.IsFailure)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content(descriptionResult.Error).DisplayAsAlert("danger");
        }

        responseBase.Headers.Add("hx-trigger", JsonConvert.SerializeObject(new
        {
            kaseyaDescription = new
            {
                description = scrapeResult.ProductDescription,
                shortDescription = descriptionResult.Value.Item2,
                features = new List<string>(),
                specs = descriptionResult.Value.Item4
            }
        }));
        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }

    [HttpPost]
    public async Task<ActionResult> UploadImageFromUrlForPriceBookProduct(uint productId, List<string> urls,
        EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        if (urls == null || !urls.Any())
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("No image URLs provided").DisplayAsAlert("danger");
        }

        var product = dbDataFeedsContext.ImportedItemsFromPriceBook.SingleOrDefault(q => q.ID == productId);
        if (product == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Product not found").DisplayAsAlert("danger");
        }

        var errorMessages = new List<string>();
        var successfulCount = 0;

        foreach (var url in urls.Distinct())
        {
            if (!Uri.TryCreate(url, UriKind.Absolute, out var uri) ||
                (uri.Scheme != Uri.UriSchemeHttp && uri.Scheme != Uri.UriSchemeHttps))
            {
                errorMessages.Add($"Invalid image URL: {url}");
                continue;
            }

            try
            {
                using var client = new HttpClient();
                var imageBytes = await client.GetByteArrayAsync(uri);

                var priceBookItemImage = new PriceBookItemImages
                {
                    PriceBookItemID = (int)productId,
                    ImageURL = url,
                    Image = imageBytes
                };
                dbDataFeedsContext.PriceBookItemImages.Add(priceBookItemImage);
                successfulCount++;
            }
            catch (Exception ex)
            {
                errorMessages.Add($"Error downloading {url}: {ex.Message}");
            }
        }

        await dbDataFeedsContext.SaveChangesAsync();

        if (errorMessages.Any())
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content($"Successfully added {successfulCount} images, but encountered errors:<br>" +
                           string.Join("<br>", errorMessages)).DisplayAsAlert("warning");
        }

        SetResponseHeaders(responseBase);
        return await GetDisabledProducts(dbDataFeedsContext);
    }

    [HttpPost]
    public async Task<ActionResult> UploadImagesForPriceBookProduct(uint productId, HttpPostedFileBase[] images,
        EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        try
        {
            var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
            var responseBase = responseBaseMock.GetValueOrDefault(Response);

            var product = dbDataFeedsContext.ImportedItemsFromPriceBook.SingleOrDefault(q => q.ID == productId);
            if (product == null)
            {
                return Json(new { success = false, error = "Product not found" });
            }

            if (images == null || images.Length == 0)
            {
                return Json(new { success = false, error = "No images uploaded" });
            }

            var uploadedCount = 0;
            foreach (var image in images)
            {
                if (image is not { ContentLength: > 0 }) continue;

                // Read the image to a byte array
                using var ms = new MemoryStream();
                await image.InputStream.CopyToAsync(ms);
                var imageBytes = ms.ToArray();

                // Save image information to the database
                var priceBookItemImage = new PriceBookItemImages
                {
                    PriceBookItemID = (int)productId,
                    ImageURL = string.Empty,
                    Image = imageBytes
                };
                dbDataFeedsContext.PriceBookItemImages.Add(priceBookItemImage);
                uploadedCount++;
            }

            await dbDataFeedsContext.SaveChangesAsync();

            return Json(new { success = true, message = $"{uploadedCount} images uploaded successfully" });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, error = ex.Message });
        }
    }

    [HttpPost]
    public async Task<ActionResult> UploadImageFromUrlForProductSource(uint sourceId, List<string> urls,
        EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        if (urls == null || !urls.Any())
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("No image URLs provided").DisplayAsAlert("danger");
        }

        var productSource = dbDataFeedsContext.ProductSource.SingleOrDefault(q => q.ID == sourceId);
        if (productSource == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Product Source not found").DisplayAsAlert("danger");
        }

        var errorMessages = new List<string>();
        var successfulCount = 0;

        foreach (var url in urls.Distinct())
        {
            if (!Uri.TryCreate(url, UriKind.Absolute, out var uri) ||
                (uri.Scheme != Uri.UriSchemeHttp && uri.Scheme != Uri.UriSchemeHttps))
            {
                errorMessages.Add($"Invalid image URL: {url}");
                continue;
            }

            try
            {
                using var client = new HttpClient();
                var imageBytes = await client.GetByteArrayAsync(uri);

                var productSourceImage = new ProductSourceImages
                {
                    ProductSourceId = (int)sourceId,
                    ImageUrl = url,
                    ImageBytes = imageBytes
                };
                dbDataFeedsContext.ProductSourceImages.Add(productSourceImage);
                successfulCount++;
            }
            catch (Exception ex)
            {
                errorMessages.Add($"Error downloading {url}: {ex.Message}");
            }
        }

        await dbDataFeedsContext.SaveChangesAsync();

        if (errorMessages.Any())
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content($"Successfully added {successfulCount} images, but encountered errors:<br>" +
                           string.Join("<br>", errorMessages)).DisplayAsAlert("warning");
        }

        SetResponseHeaders(responseBase);
        return await GetDisabledProducts(dbDataFeedsContext);
    }

    [HttpPost]
    public async Task<ActionResult> UploadImagesForProductSource(uint sourceId, HttpPostedFileBase[] images,
        EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        try
        {
            var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
            var responseBase = responseBaseMock.GetValueOrDefault(Response);

            var productSource = dbDataFeedsContext.ProductSource.SingleOrDefault(q => q.ID == sourceId);
            if (productSource == null)
            {
                return Json(new { success = false, error = "Product Source not found" });
            }

            if (images == null || images.Length == 0)
            {
                return Json(new { success = false, error = "No images uploaded" });
            }

            var uploadedCount = 0;
            foreach (var image in images)
            {
                if (image is not { ContentLength: > 0 }) continue;

                // Read the image to a byte array
                using var ms = new MemoryStream();
                await image.InputStream.CopyToAsync(ms);
                var imageBytes = ms.ToArray();

                // Save image information to the database
                var productSourceImage = new ProductSourceImages
                {
                    ProductSourceId = (int)sourceId,
                    ImageBytes = imageBytes,
                    ImageUrl = string.Empty
                };
                dbDataFeedsContext.ProductSourceImages.Add(productSourceImage);
                uploadedCount++;
            }

            await dbDataFeedsContext.SaveChangesAsync();

            return Json(new { success = true, message = $"{uploadedCount} images uploaded successfully" });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, error = ex.Message });
        }
    }

    // Comprehensive Image Management Endpoints for Modal
    [HttpPost]
    public async Task<ActionResult> GetProductImages(int productId, string productType)
    {
        try
        {
            using var dbContext = new EdunetDatafeedsEntities();
            var images = new List<object>();

            if (productType.Equals(ProductType.ProductSource, StringComparison.OrdinalIgnoreCase))
            {
                var productSourceImages = await dbContext.ProductSourceImages
                    .Where(img => img.ProductSourceId == productId)
                    .OrderBy(img => img.ID)
                    .ToListAsync();

                images = productSourceImages.Select(img => new
                {
                    id = img.ID,
                    url = !string.IsNullOrEmpty(img.ImageUrl)
                        ? img.ImageUrl
                        : $"/ProductsAdmin/GetProductSourceImage/{img.ID}",
                    type = "ProductSource"
                }).Cast<object>().ToList();
            }
            else if (productType.Equals(ProductType.Pricebook, StringComparison.OrdinalIgnoreCase))
            {
                var priceBookImages = await dbContext.PriceBookItemImages
                    .Where(img => img.PriceBookItemID == productId)
                    .OrderBy(img => img.ID)
                    .ToListAsync();

                images = priceBookImages.Select(img => new
                {
                    id = img.ID,
                    url = !string.IsNullOrEmpty(img.ImageURL)
                        ? img.ImageURL
                        : $"/ProductsAdmin/GetPriceBookItemImage/{img.ID}",
                    type = "PriceBookItem"
                }).Cast<object>().ToList();
            }

            return Json(new { success = true, images = images });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, error = ex.Message });
        }
    }

    [HttpPost]
    public async Task<ActionResult> UploadProductImages(int productId, string productType, HttpPostedFileBase[] images)
    {
        try
        {
            using var dbContext = new EdunetDatafeedsEntities();

            // Validate input parameters
            if (productId <= 0)
            {
                return Json(new { success = false, error = "Invalid product ID provided" });
            }

            if (string.IsNullOrWhiteSpace(productType))
            {
                return Json(new { success = false, error = "Product type is required" });
            }

            if (images == null || images.Length == 0)
            {
                return Json(new { success = false, error = "No images provided" });
            }

            // Validate product type
            if (!productType.Equals(ProductType.ProductSource, StringComparison.OrdinalIgnoreCase) &&
                !productType.Equals(ProductType.Pricebook, StringComparison.OrdinalIgnoreCase))
            {
                return Json(new
                {
                    success = false,
                    error = $"Invalid product type: {productType}. Expected 'productsource' or 'pricebook'"
                });
            }

            // Verify the product exists
            if (productType.Equals(ProductType.ProductSource, StringComparison.OrdinalIgnoreCase))
            {
                var productExists = await dbContext.ProductSource.AnyAsync(p => p.ID == productId);
                if (!productExists)
                {
                    return Json(new { success = false, error = $"ProductSource with ID {productId} not found" });
                }
            }
            else if (productType.Equals(ProductType.Pricebook, StringComparison.OrdinalIgnoreCase))
            {
                var productExists = await dbContext.ImportedItemsFromPriceBook.AnyAsync(p => p.ID == productId);
                if (!productExists)
                {
                    return Json(new { success = false, error = $"PriceBook item with ID {productId} not found" });
                }
            }

            var uploadedCount = 0;
            var skippedCount = 0;
            var errors = new List<string>();

            foreach (var image in images)
            {
                if (image?.ContentLength > 0)
                {
                    try
                    {
                        // Validate file size (5MB limit)
                        if (image.ContentLength > 5 * 1024 * 1024)
                        {
                            errors.Add($"{image.FileName}: File size exceeds 5MB limit");
                            continue;
                        }

                        // Validate file type
                        if (!image.ContentType.StartsWith("image/"))
                        {
                            errors.Add($"{image.FileName}: Only image files are allowed");
                            continue;
                        }

                        // Read image to byte array
                        using var ms = new MemoryStream();
                        await image.InputStream.CopyToAsync(ms);
                        var imageBytes = ms.ToArray();

                        if (productType.Equals(ProductType.ProductSource, StringComparison.OrdinalIgnoreCase))
                        {
                            var productSourceImage = new ProductSourceImages
                            {
                                ProductSourceId = productId,
                                ImageBytes = imageBytes,
                                ImageUrl = string.Empty
                            };
                            dbContext.ProductSourceImages.Add(productSourceImage);
                        }
                        else if (productType.Equals(ProductType.Pricebook, StringComparison.OrdinalIgnoreCase))
                        {
                            var priceBookItemImage = new PriceBookItemImages
                            {
                                PriceBookItemID = productId,
                                Image = imageBytes,
                                ImageURL = string.Empty
                            };
                            dbContext.PriceBookItemImages.Add(priceBookItemImage);
                        }

                        uploadedCount++;
                    }
                    catch (Exception imageEx)
                    {
                        errors.Add($"{image.FileName}: {imageEx.Message}");
                    }
                }
                else
                {
                    skippedCount++;
                }
            }

            if (uploadedCount > 0)
            {
                await dbContext.SaveChangesAsync();
            }

            // Prepare response message
            var message = $"{uploadedCount} images uploaded successfully";
            if (skippedCount > 0)
            {
                message += $", {skippedCount} files skipped (empty)";
            }

            if (errors.Any())
            {
                message += $", {errors.Count} files had errors";
            }

            var response = new
            {
                success = true,
                message = message,
                uploadedCount = uploadedCount,
                skippedCount = skippedCount,
                errors = errors.Any() ? errors : null
            };

            return Json(response);
        }
        catch (Exception ex)
        {
            return Json(new { success = false, error = $"Server error: {ex.Message}" });
        }
    }

    [HttpPost]
    public async Task<ActionResult> RemoveProductImage(int imageId)
    {
        try
        {
            using var dbContext = new EdunetDatafeedsEntities();

            // Try to find the image in ProductSourceImages first
            var productSourceImage = await dbContext.ProductSourceImages.FindAsync(imageId);
            if (productSourceImage != null)
            {
                dbContext.ProductSourceImages.Remove(productSourceImage);
                await dbContext.SaveChangesAsync();
                return Json(new { success = true, message = "Image removed successfully" });
            }

            // Try to find the image in PriceBookItemImages
            var priceBookItemImage = await dbContext.PriceBookItemImages.FindAsync(imageId);
            if (priceBookItemImage != null)
            {
                dbContext.PriceBookItemImages.Remove(priceBookItemImage);
                await dbContext.SaveChangesAsync();
                return Json(new { success = true, message = "Image removed successfully" });
            }

            return Json(new { success = false, error = "Image not found" });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, error = ex.Message });
        }
    }

    // Image serving endpoints
    public ActionResult GetProductSourceImage(int id)
    {
        try
        {
            using var dbContext = new EdunetDatafeedsEntities();
            var image = dbContext.ProductSourceImages.Find(id);

            if (image?.ImageBytes != null)
            {
                return File(image.ImageBytes, "image/jpeg");
            }

            return HttpNotFound();
        }
        catch
        {
            return HttpNotFound();
        }
    }

    public ActionResult GetPriceBookItemImage(int id)
    {
        try
        {
            using var dbContext = new EdunetDatafeedsEntities();
            var image = dbContext.PriceBookItemImages.Find(id);

            if (image?.Image != null)
            {
                return File(image.Image, "image/jpeg");
            }

            return HttpNotFound();
        }
        catch
        {
            return HttpNotFound();
        }
    }

    [HttpPost]
    public async Task<ActionResult> ImportCsv(HttpPostedFileBase csvFile,
        EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var dbContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var response = responseBaseMock.GetValueOrDefault(Response);

        if (IsInvalidCsvFile(csvFile))
        {
            return Content("No CSV file uploaded").DisplayAsAlert("danger");
        }

        using var reader = new StreamReader(csvFile.InputStream);
        await reader.ReadLineAsync(); // Skip the header line
        await ProcessCsvLines(reader, dbContext);

        await dbContext.SaveChangesAsync();

        response.Headers.Add("hx-refresh", "true");
        return await GetDisabledProducts(dbContext);
    }

    private static bool IsInvalidCsvFile(HttpPostedFileBase csvFile)
    {
        return csvFile == null || csvFile.ContentLength == 0;
    }

    private static async Task ProcessCsvLines(StreamReader reader, EdunetDatafeedsEntities dbContext)
    {
        const int productIdIndex = 0;
        // Indices: 0=ProductId, 1=SKU, 2=Title, 3=ShortDescription, 4=Description
        const int shortDescriptionIndex = 3;
        const int descriptionIndex = 4;

        while (!reader.EndOfStream)
        {
            var line = await reader.ReadLineAsync();
            var values = ParseCsvLine(line);

            if (values.Length < 5) // Ensure enough columns for ID, SKU, Title, ShortDesc, Desc
            {
                continue;
            }

            if (!uint.TryParse(values[productIdIndex], out var productId))
            {
                continue;
            }

            var description = values[descriptionIndex].Trim();
            var shortDescription = values[shortDescriptionIndex].Trim();
            UpdateProductDescription(dbContext, productId, shortDescription, description);
        }
    }

    private static string[] ParseCsvLine(string line)
    {
        var values = new List<string>();
        var inQuotes = false;
        var currentValue = new StringBuilder();

        for (var i = 0; i < line.Length; i++)
        {
            var c = line[i];

            switch (c)
            {
                case '"' when (i == 0 || line[i - 1] != '\\'):
                    inQuotes = !inQuotes;
                    break;
                case ',' when !inQuotes:
                    values.Add(currentValue.ToString());
                    currentValue.Clear();
                    break;
                default:
                    currentValue.Append(c);
                    break;
            }
        }

        values.Add(currentValue.ToString());
        return values.ToArray();
    }

    private static void UpdateProductDescription(EdunetDatafeedsEntities dbContext, uint productId,
        string shortDescription, string description)
    {
        var product = dbContext.ProductSource.SingleOrDefault(q => q.ID == productId);
        if (product == null)
        {
            return;
        }

        if (!string.IsNullOrWhiteSpace(shortDescription))
        {
            product.ProductShortDescription = shortDescription;
        }

        if (!string.IsNullOrWhiteSpace(description))
        {
            product.ProductLongDescription = description;
        }
    }

    private static void SetResponseHeaders(HttpResponseBase response)
    {
        response.Headers.Add("hx-retarget", "#mainPageContent");
        response.Headers.Add("hx-reswap", "innerHTML");
        response.Headers.Add("hx-trigger", "successfulUpdate");
    }

    [HttpPost]
    public async Task<ActionResult> SetDescriptionForPriceBookProduct(uint productId, string shortDescription,
        [ModelBinder(typeof(AllowHtmlBinder))] string description, string[] features,
        string[] specs,
        EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var product = dbDataFeedsContext.ImportedItemsFromPriceBook.SingleOrDefault(q => q.ID == productId);
        if (product == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Product not found").DisplayAsAlert("danger");
        }

        var productMapping =
            dbDataFeedsContext.MappedPriceBookItemsForNopCategory.SingleOrDefault(q => q.ID == productId);
        if (productMapping == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Cannot get category - product is not mapped").DisplayAsAlert("danger");
        }

        if (string.IsNullOrWhiteSpace(description))
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Description cannot be empty").DisplayAsAlert("danger");
        }

        product.ProductShortDescription = shortDescription.Trim();
        product.ProductDescription = description.Trim();
        await dbDataFeedsContext.SaveChangesAsync();

        SetResponseHeaders(responseBase);
        return await GetDisabledProducts(dbDataFeedsContext);
    }

    [HttpPost]
    public async Task<ActionResult> SetDescriptionForProductSource(uint sourceId, string shortDescription,
        [ModelBinder(typeof(AllowHtmlBinder))] string description, string[] features,
        string[] specs,
        EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var productSource = dbDataFeedsContext.ProductSource.SingleOrDefault(q => q.ID == sourceId);
        if (productSource == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Product Source not found").DisplayAsAlert("danger");
        }

        var productSourceMapping =
            dbDataFeedsContext.ProductSourcesForNopCategory.SingleOrDefault(q => q.ID == sourceId);
        if (productSourceMapping == null)
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Cannot get category - product source is not mapped").DisplayAsAlert("danger");
        }

        if (string.IsNullOrWhiteSpace(description))
        {
            responseBase.RetargetAlertOnProductsAdminPage();
            return Content("Description cannot be empty").DisplayAsAlert("danger");
        }

        productSource.ProductShortDescription = shortDescription.Trim();
        productSource.ProductLongDescription = description.Trim();
        await dbDataFeedsContext.SaveChangesAsync();

        SetResponseHeaders(responseBase);
        return await GetDisabledProducts(dbDataFeedsContext);
    }

    public async Task<ActionResult> CountDisabledProducts(EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default, DiUrlHelper urlHelper = null)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var diUrlHelper = urlHelper is { IsValid: true } ? urlHelper : new DiUrlHelper(Url);
        var disabledProductsCount =
            dbDataFeedsContext.GetDisabledProductSourceCount().SingleOrDefault().GetValueOrDefault(0);

        return Content($"""
                        <sl-badge class="relative right-4" variant="{(disabledProductsCount > 0 ? "warning" : "neutral")}" pill {(disabledProductsCount > 0 ? "pulse" : string.Empty)}
                                              hx-get="{diUrlHelper.Action("CountDisabledProducts", "ProductsAdmin")}"
                                              hx-trigger="every 60s"
                                              hx-swap="outerHTML"
                                              hx-target="this">{disabledProductsCount}
                                    </sl-badge>
                        """);
    }

    public async Task<ActionResult> CountProductsCheaperAtCompetitor(EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default, DiUrlHelper urlHelper = null)
    {
        var diUrlHelper = urlHelper is { IsValid: true } ? urlHelper : new DiUrlHelper(Url);
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();

        // todo: move this to a stored procedure
        var count = await dbDataFeedsContext.ProductsCheaperAtCompetitorSanitised
            .Where(p => p.OurCostPrice.HasValue && p.CompetitorPrice < p.OurCostPrice.Value)
            .CountAsync();

        return Content($"""
                        <sl-badge class="relative right-4" variant="{(count > 0 ? "danger" : "neutral")}" pill {(count > 0 ? "pulse" : string.Empty)}
                                              hx-get="{diUrlHelper.Action("CountProductsCheaperAtCompetitor", "ProductsAdmin")}"
                                              hx-trigger="every 30s"
                                              hx-swap="outerHTML"
                                              hx-target="this">{count}
                                    </sl-badge>
                        """);
    }

    public async Task<ActionResult> GetDisabledProducts(EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        // Data fetching is now handled by GetDisabledProductsData
        // Return the view structure only. DataTables will populate it via AJAX.
        return PartialView("_DisabledProducts");
    }

    [HttpPost]
    public async Task<ActionResult> GetDisabledProductsData(EdunetDatafeedsEntities dataFeedsContext = null)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();

        try
        {
            // Extract DataTables parameters from the request
            var draw = int.TryParse(Request.Form["draw"], out var d) ? d : 0;
            var start = int.TryParse(Request.Form["start"], out var s) ? s : 0;
            var length = int.TryParse(Request.Form["length"], out var l) ? l : 10;
            var searchValue = Request.Form["search[value]"]?.ToLower() ?? "";
            var sortColumnIndex = int.TryParse(Request.Form["order[0][column]"], out var sci) ? sci : 0;
            var sortDirection = Request.Form["order[0][dir]"]?.ToLower() ?? "asc";
            // Get the specific search value for the 'Reason' column (index 5)
            var reasonSearchValue = Request.Form["columns[5][search][value]"]?.ToLower() ?? "";

            // Define sortable columns mapping (index -> property name)
            var columns = new Dictionary<int, string>
            {
                { 0, "Sku" },
                { 1, "Title" },
                { 2, "ShortDescription" },
                { 3, "LongDescription" },
                { 4, "Source" },
                { 5, "RawReason" }
                // Column 6 (Actions) is not sortable
            };

            // --- Fetch and combine data ---
            // First, get the raw data from DisabledProductsSanitised
            var productSourcesQuery = dbDataFeedsContext.DisabledProductsSanitised.Where(q => q.Reason.Trim() != "")
                .Select(source => new
                {
                    source.ID,
                    Type = "ProductSource",
                    Sku = source.ProductSku,
                    Title = source.ProductTitle,
                    ShortDescription = source.ProductShortDescription,
                    LongDescription = source.ProductLongDescription,
                    SourceTypeIds = source.SourceTypeIds, // Keep raw string for processing in memory
                    Reason = source.Reason,
                    source.Brand
                });

            var priceBookItemsQuery =
                from item in dbDataFeedsContext.DisabledPriceBookProductsSanitised.Where(q => q.Reason.Trim() != "")
                join priceBook in dbDataFeedsContext.ImportedPriceBooks on item.PriceBookID equals priceBook.ID into
                    priceBookJoin
                from priceBook in priceBookJoin.DefaultIfEmpty()
                select new
                {
                    item.ID,
                    Type = "PriceBook",
                    Sku = item.ProductSKU,
                    Title = item.ProductName,
                    ShortDescription = item.ProductShortDescription,
                    LongDescription = item.ProductDescription,
                    Source = priceBook != null ? priceBook.Name : "Unknown",
                    Reason = item.Reason,
                    Brand = item.Brand
                };

            // Combine the queries
            // First, get all ProductSourceList data for lookup
            var allProductSourceTypes = await dbDataFeedsContext.ProductSourceList.ToListAsync().ConfigureAwait(false);
            var productSourceTypeLookup = allProductSourceTypes.ToDictionary(psl => psl.ID, psl => psl.Name);

            // Process Product Sources
            var productSourcesData = await productSourcesQuery.ToListAsync().ConfigureAwait(false);
            var combinedList = (from source in productSourcesData
                let hasMissingDescription = source.Reason.Contains(MissingDescription)
                let hasMissingImages = source.Reason.Contains(MissingImages)
                let hasMissingCompetitorPrice = source.Reason.Contains(MissingPricing)
                let errorCount = (hasMissingDescription ? 1 : 0) + (hasMissingImages ? 1 : 0) +
                                 (hasMissingCompetitorPrice ? 1 : 0)
                let sourceNames = GetSourceNamesFromIds(source.SourceTypeIds, productSourceTypeLookup)
                select new DisabledProductDataRow
                {
                    Id = (uint)source.ID,
                    Type = source.Type,
                    Sku = source.Sku,
                    Title = source.Title,
                    ShortDescription = source.ShortDescription,
                    LongDescription = source.LongDescription,
                    Source = sourceNames,
                    RawReason = source.Reason,
                    HasMissingDescription = hasMissingDescription,
                    HasMissingImages = hasMissingImages,
                    HasMissingCompetitorPrice = hasMissingCompetitorPrice,
                    ErrorCount = errorCount
                }).ToList();

            // Process Price Book Items
            combinedList.AddRange(from item in await priceBookItemsQuery.ToListAsync().ConfigureAwait(false)
                let hasMissingDescription = item.Reason.Contains(MissingDescription)
                let hasMissingImages = item.Reason.Contains(MissingImages)
                let hasMissingCompetitorPrice = item.Reason.Contains(MissingPricing)
                let errorCount = (hasMissingDescription ? 1 : 0) + (hasMissingImages ? 1 : 0) +
                                 (hasMissingCompetitorPrice ? 1 : 0)
                select new DisabledProductDataRow
                {
                    Id = (uint)item.ID,
                    Type = item.Type,
                    Sku = item.Sku,
                    Title = item.Title,
                    ShortDescription = item.ShortDescription,
                    LongDescription = item.LongDescription,
                    Source = $"Price Book - {item.Source}",
                    RawReason = item.Reason,
                    HasMissingDescription = hasMissingDescription,
                    HasMissingImages = hasMissingImages,
                    HasMissingCompetitorPrice = hasMissingCompetitorPrice,
                    ErrorCount = errorCount
                });

            var queryableData = combinedList.AsQueryable();

            // --- Total Records ---
            var recordsTotal = queryableData.Count();

            // --- Filtering ---
            if (!string.IsNullOrEmpty(searchValue))
            {
                queryableData = queryableData.Where(p =>
                    (p.Sku != null && p.Sku.ToLower().Contains(searchValue)) ||
                    (p.Title != null && p.Title.ToLower().Contains(searchValue)) ||
                    (p.ShortDescription != null &&
                     p.ShortDescription.ToLower().Contains(searchValue)) ||
                    (p.LongDescription != null &&
                     p.LongDescription.ToLower().Contains(searchValue)) ||
                    (p.Source != null && p.Source.ToLower().Contains(searchValue)) ||
                    (p.RawReason != null && p.RawReason.ToLower().Contains(searchValue))
                );
            }

            // Apply column-specific 'Reason' filter
            if (!string.IsNullOrEmpty(reasonSearchValue))
            {
                queryableData = queryableData.Where(p =>
                    p.RawReason != null && p.RawReason.ToLower().Contains(reasonSearchValue));
            }

            // --- Filtered Records Count ---
            var recordsFiltered = queryableData.Count();

            // --- Sorting ---
            if (columns.TryGetValue(sortColumnIndex, out var sortColumn) && !string.IsNullOrEmpty(sortColumn))
            {
                if (sortDirection == "asc")
                {
                    queryableData = queryableData.OrderBy(p => sortColumn == "Sku" ? p.Sku :
                        sortColumn == "Title" ? p.Title :
                        sortColumn == "ShortDescription" ? p.ShortDescription :
                        sortColumn == "LongDescription" ? p.LongDescription :
                        sortColumn == "Source" ? p.Source :
                        p.RawReason);
                }
                else
                {
                    queryableData = queryableData.OrderByDescending(p => sortColumn == "Sku" ? p.Sku :
                        sortColumn == "Title" ? p.Title :
                        sortColumn == "ShortDescription" ? p.ShortDescription :
                        sortColumn == "LongDescription" ? p.LongDescription :
                        sortColumn == "Source" ? p.Source :
                        p.RawReason);
                }
            }

            // --- Pagination ---
            var pagedData = queryableData.Skip(start).Take(length).ToList();

            // --- Generate HTML for complex columns (Reason, Actions) ---
            foreach (var row in pagedData)
            {
                row.ReasonHtml = GenerateReasonHtml(row);
                row.ActionsHtml = GenerateActionsHtml(row, Url);
                row.LongDescription = TruncateString(row.LongDescription, 100);
            }

            // --- Prepare Response ---
            var response = new DataTablesResponse<DisabledProductDataRow>
            {
                Draw = draw,
                RecordsTotal = recordsTotal,
                RecordsFiltered = recordsFiltered,
                Data = pagedData
            };

            var disabledProductsData = JsonConvert.SerializeObject(response);
            return Content(disabledProductsData, "application/json");
        }
        catch (Exception ex)
        {
            // Return an error response to DataTables
            var draw = int.TryParse(Request.Form["draw"], out var d) ? d : 0;
            return Json(new DataTablesResponse<DisabledProductDataRow>
            {
                Draw = draw,
                RecordsTotal = 0,
                RecordsFiltered = 0,
                Data = [],
                Error = "An error occurred while processing your request."
            }, JsonRequestBehavior.AllowGet);
        }
    }

    // Helper method to generate Reason HTML badges
    private static string GenerateReasonHtml(DisabledProductDataRow row)
    {
        var sb = new StringBuilder();
        if (row.HasMissingDescription)
        {
            sb.Append(
                """<span class="inline-flex items-center rounded-full bg-red-50 px-2.5 py-1 text-md font-medium text-red-700 ring-1 ring-inset ring-red-600/20 mr-1 mb-1">Missing description</span>""");
        }

        if (row.HasMissingImages)
        {
            sb.Append(
                """<span class="inline-flex items-center rounded-full bg-yellow-50 px-2.5 py-1 text-md font-medium text-yellow-700 ring-1 ring-inset ring-yellow-600/20 mr-1 mb-1">Missing images</span>""");
        }

        if (row.HasMissingCompetitorPrice)
        {
            sb.Append(
                """<span class="inline-flex items-center rounded-full bg-emerald-50 px-2.5 py-1 text-md font-medium text-emerald-700 ring-1 ring-inset ring-emerald-600/20 mr-1 mb-1">Missing competitor price</span>""");
        }

        return sb.ToString();
    }

    // Helper method to generate Actions HTML dropdown
    private string GenerateActionsHtml(DisabledProductDataRow row, UrlHelper url)
    {
        var sb = new StringBuilder();
        var errorCount = row.ErrorCount;
        var menuClass = "";
        var hxVals = "";
        var idParamName = "";

        if (row.Type == "ProductSource")
        {
            errorCount++; // Account for Rerun Preflight
            menuClass = "kqmFixesMenu";
            idParamName = "sourceId";
            hxVals = $"'{{'{idParamName}': {row.Id}}}'";

            sb.Append($"""
                       <sl-dropdown hoist>
                                                   <sl-button slot="trigger" size="medium" caret class="bg-white border border-gray-300 text-gray-700 hover:bg-gray-50">
                                                       Fix
                                                       <sl-badge variant="warning" pill pulse class="ml-2">{errorCount}</sl-badge>
                                                   </sl-button>
                                                   <sl-menu class="{menuClass} min-w-[160px]" data-product-id="{row.Id}">
                                                       <sl-menu-label>Fixes</sl-menu-label>
                                                       <sl-menu-item value="rerun-preflight"
                                                                     hx-post="{url.Action("RunPreflightForProductSource", "ProductsAdmin")}"
                                                                     hx-vals={hxVals}
                                                                     hx-target="#tableAlertContainer"
                                                                     hx-swap="innerHTML"
                                                                     hx-indicator="#global-loader">
                                                           Rerun Preflight
                                                       </sl-menu-item>
                       """);
        }
        else // PriceBook
        {
            menuClass = "priceBookFixesMenu";
            idParamName = "productId";
            hxVals = $"'{{{idParamName}': {row.Id}}}'";

            sb.Append($"""
                       <sl-dropdown hoist>
                                                   <sl-button slot="trigger" size="medium" caret class="bg-white border border-gray-300 text-gray-700 hover:bg-gray-50">
                                                       Fix
                                                       <sl-badge variant="warning" pill pulse class="ml-2">{errorCount}</sl-badge>
                                                   </sl-button>
                                                   <sl-menu class="{menuClass} min-w-[160px]" data-product-id="{row.Id}">
                                                       <sl-menu-label>Fixes</sl-menu-label>
                       """);
        }

        if (row.HasMissingDescription)
        {
            sb.Append("""<sl-menu-item value="description">Add Description</sl-menu-item>""");
        }

        if (row.HasMissingImages)
        {
            sb.Append("""<sl-menu-item value="images">Add Images</sl-menu-item>""");
        }

        sb.Append(@"</sl-menu></sl-dropdown>");
        return sb.ToString();
    }

    // Helper method for truncating strings
    private static string TruncateString(string value, int maxLength)
    {
        if (string.IsNullOrEmpty(value))
        {
            return value;
        }

        return value.Length <= maxLength ? value : value[..maxLength] + "...";
    }

    // Helper method to get source names from comma-separated IDs
    private static string GetSourceNamesFromIds(string sourceTypeIds, Dictionary<int, string> productSourceTypeLookup)
    {
        if (string.IsNullOrWhiteSpace(sourceTypeIds))
        {
            return "Unknown";
        }

        var sourceNames = new List<string>();
        var idStrings = sourceTypeIds.Split(',');

        foreach (var idString in idStrings)
        {
            var trimmedId = idString.Trim();
            if (string.IsNullOrWhiteSpace(trimmedId) || !int.TryParse(trimmedId, out var id))
            {
                continue;
            }

            sourceNames.Add(productSourceTypeLookup.TryGetValue(id, out var sourceName) ? sourceName : "Unknown");
        }

        return sourceNames.Any() ? string.Join(", ", sourceNames) : "Unknown";
    }

    public async Task<ActionResult> GetProductsCheaperAtCompetitor(EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var viewModel = await GetProductsCheaperAtCompetitorViewModel(dbDataFeedsContext).ConfigureAwait(false);

        return View(viewModel);
    }

    private async Task<GetProductsCheaperAtCompetitorModel> GetProductsCheaperAtCompetitorViewModel(
        EdunetDatafeedsEntities dbDataFeedsContext)
    {
        // Filter products cheaper at competitor
        var products = await dbDataFeedsContext.ProductsCheaperAtCompetitorSanitised
            .Where(p => p.OurCostPrice.HasValue && p.CompetitorPrice < p.OurCostPrice.Value)
            .ToListAsync();

        var viewModel = new GetProductsCheaperAtCompetitorModel
        {
            Products = products
        };

        return viewModel;
    }

    public async Task<ActionResult> GetProductSourcesWithNoMappings(EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var productSources = dbDataFeedsContext.ProductSource.ToArray();
        var productSourceMappings = dbDataFeedsContext.ProductSourcesForNopCategory.ToArray();

        var productSourcesWithoutMappings = productSources.Where(q => productSourceMappings.All(y => y.ID != q.ID))
            .ToArray();

        var viewModel = new GetProductSourcesWithNoMappingModel { Products = productSourcesWithoutMappings };

        if (viewModel.Products.Any() == false)
        {
            await responseBase.DisplayHtmxError();
            return Content("No product sources without mappings found");
        }

        return PartialView("_ProductSourcesWithNoMappings", viewModel);
    }

    public async Task<ActionResult> GetProductSourceWithNoMappings(uint sourceId,
        EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var productSource = dbDataFeedsContext.ProductSource.Include(productSource => productSource.ProductSourceImages)
            .SingleOrDefault(q => q.ID == sourceId);
        if (productSource == null)
        {
            await responseBase.DisplayHtmxError();
            return Content("Product Source not found");
        }

        var productSourceMappings = dbDataFeedsContext.ProductSourcesForNopCategory
            .Where(q => q.ID == productSource.ID).ToArray();
        var mappedNopCategories = dbDataFeedsContext.NopCategories.Where(q =>
                productSourceMappings.Any(y => y.CategoryId == q.CategoryId))
            .ToArray();
        var unmappedNopCategories = dbDataFeedsContext.NopCategories
            .Where(q => mappedNopCategories.All(y => y.ID != q.CategoryId))
            .ToArray();

        var viewModel = new GetProductSourceWithNoMappingsModel
        {
            ProductSource = productSource,
            ProductSourceImages = productSource.ProductSourceImages.ToArray(),
            MappedNopCategories = mappedNopCategories,
            UnmappedNopCategories = unmappedNopCategories
        };

        return PartialView("_ProductSourceWithNoMappings", viewModel);
    }

    [HttpPost]
    public async Task<ActionResult> RunPreflightForProductSource(uint sourceId,
        EdunetDatafeedsEntities dataFeedsContext = null,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var productSource = await dbDataFeedsContext.ProductSource.SingleOrDefaultAsync(q => q.ID == sourceId);
        if (productSource == null)
        {
            return Content("Product Source not found").DisplayAsAlert("danger");
        }

        // Update status to New
        productSource.Status = (int)ProductSourceStatuses.New;
        await dbDataFeedsContext.SaveChangesAsync();

        // Queue the preflight runner
        BackgroundJob.Enqueue(() =>
            PreflightQueueRunner.Run(null, null, null, null, null, null, null, false));

        return Content($"Successfully queued preflight for Product Source SKU: {productSource.ProductSku}")
            .DisplayAsAlert("success");
    }

    public async Task<ActionResult> GenerateDisabledProductSourcesCsv(string reason = null,
        EdunetDatafeedsEntities dataFeedsContext = null)
    {
        var dbDataFeedsContext = dataFeedsContext ?? new EdunetDatafeedsEntities();
        var queryProductSources = dbDataFeedsContext.DisabledProductsSanitised
            .Where(q => q.Reason.Trim() != "");

        var queryPriceBookItems = dbDataFeedsContext.DisabledPriceBookProductsSanitised
            .Where(q => q.Reason.Trim() != "");

        // Apply reason filter if provided
        if (!string.IsNullOrEmpty(reason))
        {
            var searchReason = reason.Replace("-", " "); // Convert filter value to match DB format
            queryProductSources = queryProductSources.Where(q => q.Reason.Contains(searchReason));
            queryPriceBookItems = queryPriceBookItems.Where(q => q.Reason.Contains(searchReason));
        }

        var disabledProductSources = await queryProductSources
            .Select(product => new
            {
                product.ID, product.ProductSku, product.ProductTitle, product.ProductShortDescription,
                product.ProductLongDescription
            })
            .ToListAsync();

        var disabledPriceBookItems = await queryPriceBookItems
            .Select(item => new
            {
                item.ID, ProductSku = item.ProductSKU, ProductTitle = item.ProductName,
                ProductShortDescription = item.ProductShortDescription,
                ProductLongDescription = item.ProductDescription
            })
            .ToListAsync();

        // Combine the results
        var combinedDisabledProducts = disabledProductSources
            .Concat(disabledPriceBookItems)
            .GroupBy(p => new
            {
                p.ID, p.ProductSku
            }) // Group by ID and SKU to avoid duplicates if somehow possible across types with same ID/SKU
            .Select(g => g.First())
            .ToList();

        var csvBuilder = new StringBuilder();
        csvBuilder.AppendLine("ProductId,SKU,Title,ShortDescription,LongDescription");

        foreach (var product in combinedDisabledProducts)
        {
            var title = EscapeCsvField(product.ProductTitle);
            var shortDescription = EscapeCsvField(product.ProductShortDescription);
            var longDescription = EscapeCsvField(product.ProductLongDescription);
            csvBuilder.AppendLine($"{product.ID},{product.ProductSku},{title},{shortDescription},{longDescription}");
        }

        var key = Guid.NewGuid();
        var fileNameSuffix = string.IsNullOrEmpty(reason) ? "" : $"_{reason}";
        var tempFileName = $"DisabledProducts{fileNameSuffix}_{key}.csv";
        var tempFilePath = Path.Combine(Path.GetTempPath(), tempFileName);
        System.IO.File.WriteAllText(tempFilePath, csvBuilder.ToString());

        // Pass the reason to the download action
        Response.Headers.Add("HX-Redirect", Url.Action("DownloadDisabledProductSourcesCsv", new { key, reason }));
        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }

    private static string EscapeCsvField(string field)
    {
        if (string.IsNullOrEmpty(field))
        {
            return string.Empty;
        }

        // Check if the field contains a comma, double quote, or newline
        if (!field.Contains(",") && !field.Contains("\"") && !field.Contains("\n") && !field.Contains("\r"))
        {
            return field;
        }

        // Escape double quotes by doubling them
        var escapedField = field.Replace("\"", "\"\"");
        // Enclose the field in double quotes
        return $"\"{escapedField}\"";
    }

    [HttpGet]
    public Task<ActionResult> DownloadDisabledProductSourcesCsv(Guid key, string reason = null)
    {
        var fileNameSuffix = string.IsNullOrEmpty(reason) ? "" : $"_{reason}";
        var tempFileName = $"DisabledProducts{fileNameSuffix}_{key}.csv";
        var tempFilePath = Path.Combine(Path.GetTempPath(), tempFileName);

        if (!System.IO.File.Exists(tempFilePath)) return Task.FromResult<ActionResult>(HttpNotFound());

        var fileBytes = System.IO.File.ReadAllBytes(tempFilePath);
        System.IO.File.Delete(tempFilePath);
        return Task.FromResult<ActionResult>(File(fileBytes, "text/csv", tempFileName));
    }
}
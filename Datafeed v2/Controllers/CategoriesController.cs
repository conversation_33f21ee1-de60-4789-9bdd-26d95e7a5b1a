using System;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using CSharpFunctionalExtensions;
using Datafeed_v2.Functions;
using Datafeed_v2.Helpers;
using Datafeed_v2.Models.Categories;
using Datafeed_v2.Models.DbModels.Datafeed;
using LogErrorsToAutotask;
using Newtonsoft.Json;

namespace Datafeed_v2.Controllers;

[Authorize]
[LogErrorsToAutotask(Source = "Datafeedv2")]
public class CategoriesController : Controller
{
    private readonly EdunetDatafeedsEntities _dbDatafeed;

    // Default constructor for environments without DI setup for this controller
    public CategoriesController() : this(new EdunetDatafeedsEntities())
    {
    }

    // Constructor injection for the new service
    public CategoriesController(
        EdunetDatafeedsEntities dbDatafeed)
    {
        _dbDatafeed = dbDatafeed ?? throw new ArgumentNullException(nameof(dbDatafeed));
    }

    // GET
    public ActionResult Index()
    {
        return View();
    }

    public async Task<ActionResult> UpdatePriceBookItemDescription(uint selectedNopCategoryId, uint productId,
        string description,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var product = _dbDatafeed.ImportedItemsFromPriceBook.SingleOrDefault(q => q.ID == productId);
        if (product == null)
        {
            responseBase.RetargetAlertOnMappedProductsPage();
            return Content("Product not found").DisplayAsAlert("danger");
        }

        product.ProductDescription = description;
        await _dbDatafeed.SaveChangesAsync();

        return RedirectToAction("GetMappedProductSourcesForNopCategory", "Categories",
            new
            {
                categoryId = selectedNopCategoryId, productIdUpdatedTransformationPackageOrPriceOverride = productId
            });
    }


    public async Task<ActionResult> UnmapPriceBookFromNopCategory(uint selectedNopCategoryId, uint priceBookId,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var priceBookMapping =
            _dbDatafeed.PriceBookToNopCategoryMapping.SingleOrDefault(q =>
                q.PriceBookId == priceBookId && q.NopCategoryId == selectedNopCategoryId);
        if (priceBookMapping == null)
        {
            responseBase.RetargetAlertOnMappedProductsPage();
            return Content("PriceBook not found");
        }

        _dbDatafeed.PriceBookToNopCategoryMapping.Remove(priceBookMapping);
        await _dbDatafeed.SaveChangesAsync();

        return RedirectToAction("GetMappedProductSourcesForNopCategory", "Categories",
            new { categoryId = selectedNopCategoryId });
    }


    public async Task<ActionResult> SetTransformationPackageForNopCategory(uint selectedNopCategoryId,
        uint selectedTransformationPackageId,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var nopCategory = _dbDatafeed.NopCategories.SingleOrDefault(q => q.CategoryId == selectedNopCategoryId);
        if (nopCategory == null)
        {
            await responseBase.DisplayHtmxError("Category not found");
            return Content("Category not found");
        }

        var transformationPackage =
            _dbDatafeed.CommerceTransformationPackages.SingleOrDefault(q =>
                q.ID == selectedTransformationPackageId);
        if (transformationPackage == null)
        {
            await responseBase.DisplayHtmxError("Transformation package not found");
            return Content("Transformation package not found");
        }

        nopCategory.AppliedTransformationPackage = transformationPackage.ID;
        await _dbDatafeed.SaveChangesAsync();

        return RedirectToAction("GetMappedProductSourcesForNopCategory", "Categories",
            new { categoryId = selectedNopCategoryId, updateTransformationPackageSelection = true });
    }

    public async Task<ActionResult> MapPriceBookToNopCategory(uint selectedPriceBookId, uint selectedNopCategoryId,
        bool autoMapNewItems,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        if (_dbDatafeed.PriceBookToNopCategoryMapping.Any(q =>
                q.NopCategoryId == selectedNopCategoryId && q.PriceBookId == selectedPriceBookId))
        {
            await responseBase.DisplayHtmxError("Price book is already mapped to this Nop category");
            return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
        }

        var mapping = new PriceBookToNopCategoryMapping
        {
            PriceBookId = (int)selectedPriceBookId,
            NopCategoryId = (int)selectedNopCategoryId
        };
        _dbDatafeed.PriceBookToNopCategoryMapping.Add(mapping);
        await _dbDatafeed.SaveChangesAsync();

        var priceBook = _dbDatafeed.ImportedPriceBooks.SingleOrDefault(q => q.ID == selectedPriceBookId);
        if (priceBook == null)
        {
            await responseBase.DisplayHtmxError("Price book not found");
            return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
        }

        priceBook.AutoMapNewItems = autoMapNewItems;
        await _dbDatafeed.SaveChangesAsync();

        return RedirectToAction("GetMappedProductSourcesForNopCategory", "Categories",
            new { categoryId = selectedNopCategoryId });
    }

    public async Task<ActionResult> MapPriceBookItemsToNopCategory(uint[] itemIdsToMapToNopCategory,
        uint selectedNopCategoryId,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        foreach (var itemIdToMapToNopCategory in itemIdsToMapToNopCategory)
        {
            if (_dbDatafeed.PriceBookImportedItemToNopCategoryMapping.Any(q =>
                    q.PriceBookItemId == itemIdToMapToNopCategory && q.NopCategoryId == selectedNopCategoryId))
            {
                continue;
            }

            var mapping = new PriceBookImportedItemToNopCategoryMapping
            {
                PriceBookItemId = (int)itemIdToMapToNopCategory,
                NopCategoryId = (int)selectedNopCategoryId
            };
            _dbDatafeed.PriceBookImportedItemToNopCategoryMapping.Add(mapping);
        }

        await _dbDatafeed.SaveChangesAsync();

        return RedirectToAction("GetMappedProductSourcesForNopCategory", "Categories",
            new { categoryId = selectedNopCategoryId });
    }

    public async Task<ActionResult> GetItemsForPriceBook(uint selectedPriceBookId, uint selectedNopCategoryId,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        if (_dbDatafeed.PriceBookToNopCategoryMapping.Any(q =>
                q.PriceBookId == selectedPriceBookId && q.NopCategoryId == selectedNopCategoryId))
        {
            return Content(
                "There are no unmapped products for this price book (price book is mapped to this Nop category)");
        }

        var mappedItemIds = _dbDatafeed.PriceBookImportedItemToNopCategoryMapping
            .Where(q => q.NopCategoryId == selectedNopCategoryId).Select(q => q.PriceBookItemId).ToArray();
        var unmappedItems = _dbDatafeed.ImportedItemsFromPriceBook
            .Where(q => mappedItemIds.Contains(q.ID) == false).ToArray();

        return PartialView("_UnmappedItemsForPriceBook", unmappedItems);
    }

    public async Task<ActionResult> GetChildNopCategories(uint parentCategoryId,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var parentCategory = _dbDatafeed.NopCategories.SingleOrDefault(q => q.CategoryId == parentCategoryId);
        if (parentCategory == null)
        {
            await responseBase.DisplayHtmxError("Parent category does not exist");
            return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
        }

        var childCategories = _dbDatafeed.NopCategories
            .Where(q => q.ParentCategoryId == parentCategory.CategoryId)
            .ToArray();

        var viewModel = new GetChildNopCategoriesModel
            { ParentCategoryName = parentCategory.Name, ChildCategories = childCategories };
        if (viewModel.ChildCategories.Any() == false)
        {
            await responseBase.DisplayHtmxError("No child categories found");
            Response.StatusCode = (int)HttpStatusCode.NoContent;
            return new HttpStatusCodeResult(HttpStatusCode.NoContent);
        }

        return PartialView("_NopChildCategories", viewModel);
    }

    public async Task<ActionResult> GetParentNopCategories(
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var parentCategories = _dbDatafeed.NopCategories
            .Where(q => q.ParentCategoryId == 0)
            .ToArray();
        var transformationPackages = _dbDatafeed.CommerceTransformationPackages.AsEnumerable();
        foreach (var category in parentCategories)
        {
            category.AppliedTransformationPackageClass =
                transformationPackages.SingleOrDefault(q => q.ID == category.AppliedTransformationPackage);
        }

        var viewModel = new GetParentNopCategoriesModel { ParentCategories = parentCategories };
        if (viewModel.ParentCategories.Any() == false)
        {
            await responseBase.DisplayHtmxError("No parent categories found");
        }

        return PartialView("_NopParentCategories", viewModel);
    }

    public async Task<ActionResult> GetProductsForSourceType(uint sourceTypeId,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var sourceType = await _dbDatafeed.ProductSourceList.FindAsync((int)sourceTypeId);
        if (sourceType == null)
        {
            // Returning a simple content result indicating the error.
            return Content(
                $"<sl-alert variant='danger' open closable><sl-icon slot='icon' name='exclamation-octagon'></sl-icon>Source Type with ID {sourceTypeId} not found.</sl-alert>");
        }

        // Pre-calculate string representations outside the LINQ query
        var sourceTypeIdString = sourceTypeId.ToString();
        var startsWithPattern = sourceTypeIdString + ",";
        var endsWithPattern = "," + sourceTypeIdString;
        var containsPattern = "," + sourceTypeIdString + ",";

        var products = await _dbDatafeed.ProductSource
            .Where(p => p.SourceTypeIds != null &&
                        (p.SourceTypeIds == sourceTypeIdString || // Exact match
                         p.SourceTypeIds.StartsWith(startsWithPattern) || // Starts with ID,
                         p.SourceTypeIds.EndsWith(endsWithPattern) || // Ends with ,ID
                         p.SourceTypeIds.Contains(containsPattern))) // Contains ,ID,
            .AsNoTracking()
            .ToListAsync();

        return PartialView("_ProductsForSourceType", products);
    }

    [HttpPost]
    public async Task<ActionResult> MapProductSourceTypeToNopCategory(uint selectedNopCategoryId, uint sourceTypeId,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        // Use the existing function which handles validation and creation
        var mapResult =
            await ProductSourceListFunctions.MapToNopCategory(sourceTypeId, selectedNopCategoryId, _dbDatafeed);

        if (mapResult.IsFailure)
        {
            // Return an alert partial view or content to be displayed in the modal's alert container
            responseBase.StatusCode = (int)HttpStatusCode.BadRequest; // Set status code for HTMX error handling
            return Content(
                $"<sl-alert variant='danger' open closable><sl-icon slot='icon' name='exclamation-octagon'></sl-icon>Error mapping source type: {mapResult.Error}</sl-alert>");
        }

        // On success, redirect to refresh the main view content
        return RedirectToAction("GetMappedProductSourcesForNopCategory", "Categories",
            new { categoryId = selectedNopCategoryId });
    }

    public async Task<ActionResult> GetMappedSourceTypesForNopCategory(uint categoryId,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var nopCategory = await _dbDatafeed.NopCategories.SingleOrDefaultAsync(q => q.CategoryId == categoryId);
        if (nopCategory == null)
        {
            await responseBase.DisplayHtmxError("Category not found");
            return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
        }

        var mappedSourceTypes = await _dbDatafeed.ProductSourceTypesForNopCategory
            .Where(ps => ps.CategoryId == (int)categoryId)
            .ToListAsync();

        // Fetch mapped price book IDs
        var mappedPriceBookIds = await _dbDatafeed.PriceBookToNopCategoryMapping
            .Where(m => m.NopCategoryId == categoryId)
            .Select(m => m.PriceBookId)
            .ToListAsync();

        // Fetch the actual price book details
        var mappedPriceBooks = await _dbDatafeed.ImportedPriceBooks
            .Where(pb => mappedPriceBookIds.Contains(pb.ID))
            .ToListAsync();

        // Fetch all Nop Categories for the dropdown selector (used for moving source types)
        var allCategories = await _dbDatafeed.NopCategories.AsNoTracking().ToListAsync();

        var model = new GetMappedSourceTypesForNopCategoryModel
        {
            SelectedNopCategoryId = categoryId, // Pass the category ID
            MappedSourceTypes = mappedSourceTypes,
            Categories = allCategories,
            MappedPriceBooks = mappedPriceBooks // Set the mapped price books
        };

        return PartialView("_MappedSourceTypes", model);
    }

    public async Task<ActionResult> GetMappedProductSourcesForNopCategory(uint categoryId,
        bool updateTransformationPackageSelection = false,
        uint? productIdUpdatedTransformationPackageOrPriceOverride = null, bool unmappedProducts = false,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var nopCategory = await _dbDatafeed.NopCategories.SingleOrDefaultAsync(q => q.CategoryId == categoryId);
        if (nopCategory == null)
        {
            await responseBase.DisplayHtmxError("Category not found");
            return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
        }

        // Get mapped product sources (excluding archived products)
        var mappedProductSources = await _dbDatafeed.ProductSourcesForNopCategory
            .Where(ps => ps.CategoryId == categoryId && !ps.Archived)
            .ToArrayAsync();

        // Get all product source types
        var allSourceTypes = await _dbDatafeed.ProductSourceList.ToArrayAsync();

        // Get transformation packages
        var transformationPackages = await _dbDatafeed.CommerceTransformationPackages.ToArrayAsync();
        var selectedTransformationPackage = transformationPackages
            .SingleOrDefault(q => q.ID == nopCategory.AppliedTransformationPackage);

        // Get price book data (similar to GetMappedProductSourcesForNopCategory)
        var mappedPriceBookIds = await _dbDatafeed.PriceBookToNopCategoryMapping
            .Where(q => q.NopCategoryId == categoryId)
            .Select(q => q.PriceBookId)
            .ToArrayAsync();
        var allPriceBooks = await _dbDatafeed.ImportedPriceBooks.ToArrayAsync();
        var mappedPriceBooks = allPriceBooks.Where(q => mappedPriceBookIds.Contains(q.ID)).ToArray();
        var unmappedPriceBooks = allPriceBooks.Where(q => !mappedPriceBookIds.Contains(q.ID) && !q.Archived).ToArray();

        var mappedPriceBookItems = await _dbDatafeed.MappedPriceBookItemsForNopCategory
            .Where(q => q.NopCategoryId == categoryId)
            .AsNoTracking()
            .ToArrayAsync();

        // Get stock data from Jim2 using the helper
        var stockDataResult = await Jim2StockHelper.GetStockData(responseBase);
        if (stockDataResult.IsFailure)
        {
            // Error already displayed by GetStockData
            return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
        }

        var (stockOnHand, stockCodeColumnIndex, qtyAvailableColumnIndex) = stockDataResult.Value;
        var firstResultSet = stockOnHand.resultSets.First();

        // Update quantities for different product types
        Jim2StockHelper.UpdateProductSourceQuantities(mappedProductSources, firstResultSet, stockCodeColumnIndex,
            qtyAvailableColumnIndex);
        Jim2StockHelper.UpdatePriceBookItemQuantities(mappedPriceBookItems, firstResultSet, stockCodeColumnIndex,
            qtyAvailableColumnIndex);


        var model = new GetMappedProductSourcesForNopCategoryModel
        {
            SelectedNopCategoryId = categoryId,
            MappedProductSources = mappedProductSources,
            SourceTypes = allSourceTypes,
            TransformationPackages = transformationPackages,
            SelectedTransformationPackage = selectedTransformationPackage,
            MappedPriceBooks = mappedPriceBooks,
            UnmappedPriceBooks = unmappedPriceBooks,
            MappedPriceBookItems = mappedPriceBookItems, // Included for potential view compatibility/reuse

            // Pass through optional parameters
            UpdateTransformationPackageSelection = updateTransformationPackageSelection,
            ProductIdUpdatedTransformationPackageOrPriceOverride = productIdUpdatedTransformationPackageOrPriceOverride,
            UnmappedProducts = unmappedProducts
        };

        return PartialView("_MappedProductsForNopCategory", model);
    }

    [HttpPost]
    public async Task<ActionResult> CreateNewSourceTypeFromExcelSheet(uint selectedNopCategoryId, string sourceTypeName,
        HttpPostedFileBase excelFile, Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        // 1. Validate Nop Category
        var nopCategoryExists = await _dbDatafeed.NopCategories.AnyAsync(c => c.CategoryId == selectedNopCategoryId);
        if (!nopCategoryExists)
        {
            responseBase.StatusCode = (int)HttpStatusCode.BadRequest;
            return Content(
                $"<sl-alert variant='danger' open closable><sl-icon slot='icon' name='exclamation-octagon'></sl-icon>NopCommerce Category with ID {selectedNopCategoryId} not found.</sl-alert>");
        }

        // 2. Create New Source Type
        var createSourceTypeResult =
            await ProductSourceListFunctions.CreateNewSourceType(sourceTypeName, excelFile?.FileName, _dbDatafeed);
        if (createSourceTypeResult.IsFailure)
        {
            responseBase.StatusCode = (int)HttpStatusCode.BadRequest;
            return Content(
                $"<sl-alert variant='danger' open closable><sl-icon slot='icon' name='exclamation-octagon'></sl-icon>Error creating source type: {createSourceTypeResult.Error}</sl-alert>");
        }

        var newSourceType = createSourceTypeResult.Value;

        // 3. Add Products from CSV (if file provided)
        if (excelFile is { ContentLength: > 0 })
        {
            var addProductsResult =
                await ProductSourceFunctions.CreateNewProductSourcesFromExcelSheet((uint)newSourceType.ID, excelFile,
                    diDbDatafeed: _dbDatafeed);
            if (addProductsResult.IsFailure)
            {
                // Report the error
                responseBase.StatusCode = (int)HttpStatusCode.BadRequest;
                return Content(
                    $"<sl-alert variant='danger' open closable><sl-icon slot='icon' name='exclamation-octagon'></sl-icon>Error adding products from CSV: {addProductsResult.Error}</sl-alert>");
            }
        }

        // 4. Map Source Type to Nop Category
        var mapResult =
            await ProductSourceListFunctions.MapToNopCategory((uint)newSourceType.ID, selectedNopCategoryId,
                _dbDatafeed);
        if (mapResult.IsFailure)
        {
            // Report the error
            responseBase.StatusCode = (int)HttpStatusCode.BadRequest;
            return Content(
                $"<sl-alert variant='danger' open closable><sl-icon slot='icon' name='exclamation-octagon'></sl-icon>Error mapping source type to category: {mapResult.Error}</sl-alert>");
        }

        // 5. Success: Redirect to refresh the mapped products view
        return RedirectToAction("GetMappedProductSourcesForNopCategory", "Categories",
            new { categoryId = selectedNopCategoryId });
    }

    [HttpPost]
    public async Task<ActionResult> UpdateCostPriceForProductSource(uint selectedNopCategoryId, uint sourceId,
        decimal costPrice, Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        var updateResult = await ProductSourceFunctions.UpdateCostPrice(sourceId, costPrice, _dbDatafeed);

        if (updateResult.IsFailure)
        {
            responseBase.RetargetAlertOnMappedProductsPage();
            return Content(updateResult.Error).DisplayAsAlert("danger");
        }

        // Redirect back to the main view to show the updated data
        return RedirectToAction("GetMappedProductSourcesForNopCategory", "Categories",
            new
            {
                categoryId = selectedNopCategoryId,
                productIdUpdatedTransformationPackageOrPriceOverride =
                    sourceId // Re-using this param to potentially highlight row
            });
    }

    [HttpPost]
    public async Task<ActionResult> MoveSourceTypeMappingToNewNopCategory(uint currentNopCategoryId, uint sourceTypeId,
        uint targetNopCategoryId, Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        // Use the existing function which handles validation and moving
        var moveResult = await ProductSourceListFunctions.MoveSourceTypeMapping(sourceTypeId, currentNopCategoryId,
            targetNopCategoryId, _dbDatafeed);

        if (moveResult.IsFailure)
        {
            // Return an alert partial view or content to be displayed in the modal's alert container
            responseBase.StatusCode = (int)HttpStatusCode.BadRequest; // Set status code for HTMX error handling
            return Content(
                $"<sl-alert variant='danger' open closable><sl-icon slot='icon' name='exclamation-octagon'></sl-icon>Error moving source type mapping: {moveResult.Error}</sl-alert>");
        }

        // On success, redirect to refresh the main view content for the *target* category
        return await GetMappedProductSourcesForNopCategory(targetNopCategoryId);
    }

    [HttpPost]
    public async Task<ActionResult> RemoveSourceTypeToNopCategoryMapping(uint selectedNopCategoryId, uint sourceTypeId,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        // Use the existing function which handles validation and removal
        var removeResult =
            await ProductSourceListFunctions.RemoveSourceTypeMapping(sourceTypeId, selectedNopCategoryId, _dbDatafeed);

        if (removeResult.IsFailure)
        {
            // Return an alert partial view or content to be displayed in the modal's alert container
            responseBase.StatusCode = (int)HttpStatusCode.BadRequest; // Set status code for HTMX error handling
            // Note: This error might appear briefly before the target is swapped on success,
            // or persistently if the removal fails. Consider targeting a specific error div if needed.
            return Content(
                $"<sl-alert variant='danger' open closable><sl-icon slot='icon' name='exclamation-octagon'></sl-icon>Error removing source type mapping: {removeResult.Error}</sl-alert>");
        }

        // On success, redirect to refresh the main view content for the *current* category
        // This will effectively re-render the section, showing the mapping is gone.
        return await GetMappedProductSourcesForNopCategory(selectedNopCategoryId);
    }

    [HttpPost]
    public async Task<ActionResult> AddProductSourceBySkuToExistingSourceType(uint selectedNopCategoryId,
        uint sourceTypeId, string sku, Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        // 1. Validate SKU input
        if (string.IsNullOrWhiteSpace(sku))
        {
            responseBase.StatusCode = (int)HttpStatusCode.BadRequest;
            return Content(
                $"<sl-alert variant='danger' open closable><sl-icon slot='icon' name='exclamation-octagon'></sl-icon>SKU cannot be empty.</sl-alert>");
        }

        // 2. Attempt to create/update the ProductSource using the SKU and SourceTypeID
        var createResult = await ProductSourceFunctions.CreateNewProductSourceFromSku(sourceTypeId, sku, _dbDatafeed);

        if (createResult.IsFailure)
        {
            responseBase.StatusCode = (int)HttpStatusCode.BadRequest;
            // The function already provides a specific error message
            return Content(
                $"<sl-alert variant='danger' open closable><sl-icon slot='icon' name='exclamation-octagon'></sl-icon>Error adding product source by SKU: {createResult.Error}</sl-alert>");
        }

        // 3. Success: Redirect to refresh the mapped products view for the current category.
        // This ensures the context is refreshed, although the newly added SKU might not appear
        // in this specific category view unless the source type itself is mapped here.
        // Consider adding a success message via TempData or another mechanism if needed.
        // For HTMX, simply returning the refreshed partial is standard.
        return RedirectToAction("GetMappedProductSourcesForNopCategory", "Categories",
            new { categoryId = selectedNopCategoryId });
    }

    [HttpPost]
    public async Task<ActionResult> ArchiveSelectedProductSources(string productIdsJson,
        Maybe<HttpResponseBase> responseBaseMock = default)
    {
        var responseBase = responseBaseMock.GetValueOrDefault(Response);

        try
        {
            // Validate input parameters
            if (string.IsNullOrWhiteSpace(productIdsJson))
            {
                responseBase.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(new { success = false, message = "No product IDs provided." });
            }

            // Parse product IDs
            int[] productIds;
            try
            {
                productIds = JsonConvert.DeserializeObject<int[]>(productIdsJson);
            }
            catch (JsonException)
            {
                responseBase.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(new { success = false, message = "Invalid product IDs format." });
            }

            if (productIds == null || productIds.Length == 0)
            {
                responseBase.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(new { success = false, message = "No product IDs provided." });
            }

            // Find product sources to archive
            var productSources = await _dbDatafeed.ProductSource
                .Where(ps => productIds.Contains(ps.ID))
                .ToListAsync();

            if (productSources.Count != productIds.Length)
            {
                responseBase.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(new { success = false, message = "One or more product IDs are invalid." });
            }

            // Archive the product sources
            foreach (var productSource in productSources)
            {
                productSource.Archived = true;
            }

            // Save changes to database
            await _dbDatafeed.SaveChangesAsync();

            return Json(new { success = true, message = $"Successfully archived {productSources.Count} product(s)." });
        }
        catch (Exception ex)
        {
            responseBase.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Json(new { success = false, message = $"An error occurred while archiving products: {ex.Message}" });
        }
    }
}
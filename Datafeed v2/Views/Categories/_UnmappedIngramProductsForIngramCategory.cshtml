@model Datafeed_v2.Models.DbModels.Datafeed.IngramProducts[]

<div id="unmappedProductsForIngramCategoryContainer">
    <table id="unmappedProductsForIngramCategory" class="table table-striped max-w-1/2"
           _="on load call initDatatable('unmappedProductsForIngramCategory', { order: [[0, 'asc']], paging: false, scrollY: '200px' })">
        <thead>
        <tr>
            <th></th>
            <th>Ingram Part Number</th>
            <th>Vendor Part Number</th>
            <th>Ingram Description</th>
            <th>Sub Category</th>
        </tr>
        </thead>

        <tbody>
        @foreach (var product in Model)
        {
            <tr>
                <td><input type="checkbox" id="<EMAIL>" value="@product.ID"/></td>
                <td><label for="<EMAIL>">@product.IngramPartNumber</label></td>
                <td><label for="<EMAIL>">@product.VendorPartNumber</label></td>
                <td><label for="<EMAIL>">@product.IngramPartDescription</label></td>
                <td><label for="<EMAIL>">@product.SubCategoryName</label></td>
            </tr>
        }
        </tbody>
    </table>
</div>
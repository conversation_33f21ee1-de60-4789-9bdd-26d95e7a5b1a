@model IEnumerable<Datafeed_v2.Models.DbModels.Datafeed.KQMProducts>

<div id="unmappedProductsForQuoteTableContainer">
    <table id="unmappedProductsForQuoteTable" class="table table-striped max-w-1/2"
           _="on load call initDatatable('unmappedProductsForQuoteTable', { order: [[1, 'asc']], paging: false, scrollY: '200px' })">
        <thead>
        <tr>
            <th></th>
            <th>Product SKU</th>
            <th>Product Title</th>
            <th>Product Description</th>
        </tr>
        </thead>

        <tbody>
        @foreach (var product in Model)
        {
            <tr>
                <td><input type="checkbox" id="<EMAIL>" value="@product.ID"/></td>
                <td><label for="<EMAIL>">@product.manufacturerPartNumber</label></td>
                <td><label for="<EMAIL>">@product.title</label></td>
                <td><label for="<EMAIL>">@product.description</label></td>
            </tr>
        }
        </tbody>
    </table>
</div>
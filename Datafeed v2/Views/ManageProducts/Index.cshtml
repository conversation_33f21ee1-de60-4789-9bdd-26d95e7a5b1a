@using Datafeed_v2.Models
@model Datafeed_v2.Models.ManageProducts.IndexModel

@{
    ViewBag.Title = "Manage Products";
}

@Html.Partial("_ErrorAlert")
@Html.Partial("_SuccessAlert")

<h1 class="text-2xl font-semibold mb-4">@ViewBag.Title</h1>
<div class="container mx-auto px-4 py-6">
    <sl-tab-group>
        <sl-tab slot="nav" panel="products">Products</sl-tab>
        <sl-tab slot="nav" panel="expired-sell-price-overrides">
            Expired Sell Price Overrides
            <sl-badge id="expired-sell-price-overrides-badge" variant="danger" pill class="px-5"
                      hx-get="@Url.Action("CountExpiredSellPriceOverrides", "ManageProducts")"
                      hx-trigger="load"
                      hx-swap="innerHTML">
                <sl-spinner style="font-size: 0.7em; --track-width: 2px;"></sl-spinner> @* Show spinner while loading *@
            </sl-badge>
        </sl-tab>
        <sl-tab slot="nav" panel="expired-cost-price-overrides">
            Expired Cost Price Overrides
            <sl-badge id="expired-cost-price-overrides-badge" variant="danger" pill class="px-5"
                      hx-get="@Url.Action("CountExpiredCostPriceOverrides", "ManageProducts")"
                      hx-trigger="load"
                      hx-swap="innerHTML">
                <sl-spinner style="font-size: 0.7em; --track-width: 2px;"></sl-spinner>
            </sl-badge>
        </sl-tab>
        <sl-tab slot="nav" panel="new-items">New Price Book Items</sl-tab>

        <sl-tab-panel name="products">
            <div class="mb-6 flex items-center justify-between bg-white p-4 rounded-lg shadow-sm">
                <div class="flex items-center gap-4">
                    <sl-select id="statusFilter" class="w-[250px]" size="medium" placeholder="Filter by status"
                               clearable>
                        <sl-option value="">All statuses</sl-option>
                        @foreach (var status in Model.ProductStatuses)
                        {
                            <sl-option value="@(status.Value.Replace(" ", "-").ToLower())">@status.Value</sl-option>
                        }
                    </sl-select>
                </div>
            </div>
            <div class="bg-white shadow-md rounded-lg overflow-hidden">
                <table id="productsTable" class="min-w-full divide-y divide-gray-200 table-fixed">
                    <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider w-10">
                            <sl-checkbox id="selectAllProductsCheckbox"></sl-checkbox>
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">SKU
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider w-1/4">
                            Title
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Brand
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Short Description
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Long Description
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Competitor
                            Price
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Sell
                            Price
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Cost
                            Price
                            (ex. GST)
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Margin
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                    </thead>

                    <tbody class="bg-white divide-y divide-gray-200">
                    @* DataTables will populate this dynamically *@
                    </tbody>

                    <tfoot>
                    <tr>
                        <td colspan="12" class="px-6 py-4">
                            <div class="flex justify-between items-center">
                                <div class="flex items-center gap-4">
                                    <sl-select id="bulkStatusSelect" placeholder="Select status for update" class="w-64"
                                               hoist>
                                        @foreach (var status in Model.ProductStatuses)
                                        {
                                            <sl-option value="@status.Key">@status.Value</sl-option>
                                        }
                                    </sl-select>
                                    <sl-button id="bulkUpdateStatusButton" variant="primary" size="medium">
                                        <sl-icon slot="prefix" name="check2-square"></sl-icon>
                                        Update Selected Statuses
                                        <sl-spinner slot="suffix"
                                                    style="display: none; font-size: 1em; --track-width: 2px;"></sl-spinner>
                                    </sl-button>
                                    <div id="bulkUpdateAlertContainer"
                                         class="flex-grow"></div> @* Container for alerts *@
                                </div>
                                <sl-button variant="success" size="medium" pill class="float-right mx-4"
                                           onclick="htmx.find('#mapProductSourceTypeModal').show();">
                                    <sl-icon slot="prefix" name="link-45deg"></sl-icon>
                                    Map Product Source Type
                                </sl-button>
                            </div>
                        </td>
                    </tr>
                    </tfoot>
                </table>
            </div>
        </sl-tab-panel>

        <sl-tab-panel name="expired-cost-price-overrides">
            <div class="mb-6 flex items-center justify-between bg-white p-4 rounded-lg shadow-sm">
                <div class="flex items-center gap-4">
                    <div class="flex items-center gap-2">
                        <span class="text-gray-700 font-medium">Expired between:</span>
                        <sl-input type="date" id="expiredCostPriceStartDate" size="medium" class="w-[160px]"></sl-input>
                        <span class="text-gray-500">-</span>
                        <sl-input type="date" id="expiredCostPriceEndDate" size="medium" class="w-[160px]"></sl-input>
                    </div>
                    <sl-button size="medium" variant="primary" id="applyCostPriceDateFilter">Apply Filter</sl-button>
                    <sl-button size="medium" variant="neutral" id="resetCostPriceDateFilter">Reset</sl-button>
                </div>
            </div>
            <div class="bg-white shadow-md rounded-lg overflow-hidden">
                <table id="expiredCostPriceOverridesTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">SKU
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Title
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Original Cost Price
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Override Cost Price
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Expiry Date
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Days
                            Expired
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Note
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    @if (Model.AllExpiredCostPriceOverrides != null)
                    {
                        foreach (var expiredItem in Model.AllExpiredCostPriceOverrides)
                        {
                            var product = expiredItem.Product;
                            var overrideDetails = expiredItem.OverrideDetails;
                            var daysExpired = (DateTime.Now.Date - overrideDetails.ExpiryDate.Date).Days;

                            <tr class="hover:bg-gray-50 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap text-md">@product.ProductSku</td>
                                <td class="px-6 py-4 text-md max-w-xs truncate"
                                    title="@product.ProductTitle">@product.ProductTitle</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="price-value text-md">@product.CostPrice.ToString("C")</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span
                                        class="price-value text-md font-medium text-red-600">@overrideDetails.CostPrice.ToString("C")</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-md text-red-600">@overrideDetails.ExpiryDate.ToString("dd-MM-yyyy")</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-md font-medium bg-red-100 text-red-800">
                                        @daysExpired day@(daysExpired != 1 ? "s" : "")
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-md max-w-xs truncate"
                                    title="@overrideDetails.Note">@overrideDetails.Note</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center space-x-2">
                                        <button
                                            class="review-cost-price-override p-1.5 rounded-full hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                                            data-product-id="@product.ID"
                                            data-product-source="@ProductType.ProductSource"
                                            data-override-id="@overrideDetails.ID"
                                            aria-label="Review cost price override">
                                            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path
                                                    d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"></path>
                                                <path fill-rule="evenodd"
                                                      d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"
                                                      clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                        <button
                                            class="remove-cost-price-override p-1.5 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors"
                                            data-product-id="@product.ID"
                                            data-product-source="@ProductType.ProductSource"
                                            data-override-id="@overrideDetails.ID"
                                            aria-label="Remove cost price override">
                                            <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                      d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                                      clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                    else
                    {
                        <tr>
                            <td colspan="8" class="text-center py-4 text-gray-500">No expired cost price overrides
                                found.
                            </td>
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        </sl-tab-panel>
        <sl-tab-panel name="expired-sell-price-overrides">
            <div class="mb-6 flex items-center justify-between bg-white p-4 rounded-lg shadow-sm">
                <div class="flex items-center gap-4">
                    <div class="flex items-center gap-2">
                        <span class="text-gray-700 font-medium">Expired between:</span>
                        <sl-input type="date" id="expiredSellPriceStartDate" size="medium" class="w-[160px]"></sl-input>
                        <span class="text-gray-500">-</span>
                        <sl-input type="date" id="expiredSellPriceEndDate" size="medium" class="w-[160px]"></sl-input>
                    </div>
                    <sl-button size="medium" variant="primary" id="applySellPriceDateFilter">Apply Filter</sl-button>
                    <sl-button size="medium" variant="neutral" id="resetSellPriceDateFilter">Reset</sl-button>
                </div>
                @* Note: Search functionality will be handled by DataTable's built-in search for this table *@
            </div>
            <div class="bg-white shadow-md rounded-lg overflow-hidden">
                <table id="expiredSellPriceOverridesTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">SKU
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Title
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Original Price
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Override Price
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Expiry Date
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Days
                            Expired
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Note
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    @foreach (var vm in Model.ProductSources)
                    {
                        var source = vm.ProductSource.Source;
                        var activeOverride = vm.ProductSource.ActiveOverride;

                        if (activeOverride != null && activeOverride.ExpiryDate < DateTime.Now)
                        {
                            var daysExpired = (DateTime.Now - activeOverride.ExpiryDate).Days;
                            <tr class="hover:bg-gray-50 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap text-md">@source.ProductSku</td>
                                <td class="px-6 py-4 text-md max-w-xs truncate"
                                    title="@source.ProductTitle">@source.ProductTitle</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span
                                        class="price-value text-md">@source.SellPrice.GetValueOrDefault(0).ToString("C")</span>
                                </td> @* Original Sell Price *@
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span
                                        class="price-value text-md font-medium text-red-600">@activeOverride.SellPrice.ToString("C")</span>
                                </td> @* Override Price *@
                                <td class="px-6 py-4 whitespace-nowrap text-md text-red-600">@activeOverride.ExpiryDate.ToString("dd-MM-yyyy")</td> @* Expiry Date *@
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-md font-medium bg-red-100 text-red-800">
                                        @daysExpired day@(daysExpired != 1 ? "s" : "")
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-md max-w-xs truncate"
                                    title="@activeOverride.Note">@activeOverride.Note</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center space-x-2">
                                        <button
                                            class="review-sell-price-override p-1.5 rounded-full hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                                            data-product-id="@source.ID"
                                            data-product-source="@ProductType.ProductSource"
                                            data-override-id="@activeOverride.ID"
                                            aria-label="Review sell price override">
                                            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path
                                                    d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"></path>
                                                <path fill-rule="evenodd"
                                                      d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"
                                                      clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                        <button
                                            class="remove-sell-price-override p-1.5 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors"
                                            data-product-id="@source.ID"
                                            data-product-source="@ProductType.ProductSource"
                                            data-override-id="@activeOverride.ID"
                                            aria-label="Remove sell price override">
                                            <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                      d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                                      clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                    </tbody>
                </table>
            </div>
        </sl-tab-panel>
        <sl-tab-panel name="new-items">
            <div class="mb-6 flex items-center justify-end bg-white p-4 rounded-lg shadow-sm">
                <sl-tooltip content="Export newly imported items to a CSV file">
                    <sl-button hx-get="@Url.Action("GenerateNewlyImportedPriceBookItemsCsv", "ManageProducts")"
                               size="medium"
                               class="bg-white border border-gray-300 text-gray-700 hover:bg-gray-50">
                        <sl-icon slot="prefix" name="download"></sl-icon>
                        Export
                    </sl-button>
                </sl-tooltip>
            </div>
            <div class="bg-white shadow-md rounded-lg overflow-hidden">
                <table id="newItemsTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                            Category
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">SKU
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Sell
                            Price
                        </th>
                        <th class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Qty
                        </th>
                    </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    @if (Model.NewlyImportedItems != null && Model.NewlyImportedItems.Any())
                    {
                        foreach (var item in Model.NewlyImportedItems)
                        {
                            <tr class="hover:bg-gray-50 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap text-md">@item.Category</td>
                                <td class="px-6 py-4 whitespace-nowrap text-md">@item.Sku</td>
                                <td class="px-6 py-4 whitespace-nowrap text-md">@item.SellPrice.ToString("C")</td>
                                <td class="px-6 py-4 whitespace-nowrap text-md">@item.Qty</td>
                            </tr>
                        }
                    }
                    else
                    {
                        <tr>
                            <td colspan="4" class="text-center py-4 text-gray-500">No newly imported items found.</td>
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        </sl-tab-panel>
    </sl-tab-group>
</div>

@* Modal for Reviewing Expired Sell Price Overrides *@
<sl-dialog id="reviewSellPriceOverrideModal" label="Review Expired Sell Price Override" class="dialog-overview"
           style="--width: 550px;">
    <div id="reviewSellPriceOverrideModalAlertContainer"></div> @* Container for error/success messages *@

    <div class="mb-4">
        <h3 class="text-lg font-semibold mb-2">Product Information</h3>
        <div class="grid grid-cols-2 gap-4">
            <div>
                <p class="text-sm text-gray-500">SKU</p>
                <p class="font-medium" id="review-sell-product-sku">N/A</p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Title</p>
                <p class="font-medium" id="review-sell-product-title">N/A</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-3 gap-4 mb-4">
        <div>
            <p class="text-sm text-gray-500">Current Competitor Price</p>
            <p class="font-medium" id="review-sell-competitor-price">$0.00</p>
        </div>
        <div>
            <p class="text-sm text-gray-500">Standard Price</p>
            <p class="font-medium" id="review-sell-standard-price">$0.00</p>
        </div>
        <div>
            <p class="text-sm text-gray-500">Cost Price</p>
            <p class="font-medium" id="review-sell-cost-price">$0.00</p>
        </div>
    </div>

    <sl-divider style="--spacing: 1rem;"></sl-divider>

    <div class="mb-4">
        <h3 class="text-lg font-semibold mb-2">Expired Sell Price Override Details</h3>
        <div class="grid grid-cols-2 gap-4 mb-2">
            <div>
                <p class="text-sm text-gray-500">Override Price</p>
                <p class="font-medium text-red-600" id="review-sell-override-price">$0.00</p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Expired On</p>
                <p class="font-medium text-red-600" id="review-sell-expired-date">N/A</p>
            </div>
        </div>
        <div class="mb-2">
            <p class="text-sm text-gray-500">Original Note</p>
            <p class="font-medium italic" id="review-sell-original-note">N/A</p>
        </div>
    </div>

    @* Using standard form submission for simplicity, can be enhanced with HTMX later if needed *@
    <form id="updateSellPriceOverrideForm">
        <input type="hidden" name="productId" id="review-sell-product-id" value="">
        <input type="hidden" name="productType" id="review-sell-product-type" value="">
        <input type="hidden" name="overrideId" id="review-sell-override-id" value="">

        <sl-input type="number" step="0.50" name="sellPrice" label="New Override Price" value="" required
                  class="mb-3"></sl-input>

        <sl-input type="date" name="expiresAt" label="New Expiry Date" required class="mb-3"></sl-input>

        <sl-textarea name="note" label="New Note" required resize="auto" class="mb-3"></sl-textarea>

        <div class="flex justify-between items-center mb-2">
            <div>
                <p class="text-sm text-gray-500">New Margin</p>
                <p class="font-medium" id="review-sell-new-margin">N/A</p>
            </div>
            <div class="flex gap-2">
                @* Buttons will be handled by JavaScript, not form submission or HTMX for now *@
                <sl-button variant="primary" id="updateSellPriceOverrideButton">
                    <sl-spinner id="updateSellPriceOverrideButtonIndicator" slot="prefix"
                                style="display: none; --track-width: 2px; font-size: 1em;"></sl-spinner>
                    Update Sell Price Override
                </sl-button>
                <sl-button variant="danger" id="removeSellPriceOverrideButton">
                    <sl-spinner id="removeSellPriceOverrideButtonIndicator" slot="prefix"
                                style="display: none; --track-width: 2px; font-size: 1em;"></sl-spinner>
                    Remove Sell Price Override
                </sl-button>
            </div>
        </div>
    </form>

    <sl-button slot="footer" variant="neutral" onclick="htmx.find('#reviewSellPriceOverrideModal').hide();">Cancel
    </sl-button>
</sl-dialog>

@* Modal for Reviewing Expired Cost Price Overrides *@
<sl-dialog id="reviewCostPriceOverrideModal" label="Review Expired Cost Price Override" class="dialog-overview"
           style="--width: 550px;">
    <div id="reviewCostPriceOverrideModalAlertContainer"></div>

    <div class="mb-4">
        <h3 class="text-lg font-semibold mb-2">Product Information</h3>
        <div class="grid grid-cols-2 gap-4">
            <div>
                <p class="text-sm text-gray-500">SKU</p>
                <p class="font-medium" id="review-cost-product-sku">N/A</p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Title</p>
                <p class="font-medium" id="review-cost-product-title">N/A</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-3 gap-4 mb-4">
        <div>
            <p class="text-sm text-gray-500">Current Competitor Price</p>
            <p class="font-medium" id="review-cost-competitor-price">$0.00</p>
        </div>
        <div>
            <p class="text-sm text-gray-500">Standard Sell Price</p>
            <p class="font-medium" id="review-cost-standard-sell-price">$0.00</p>
        </div>
        <div>
            <p class="text-sm text-gray-500">Original Cost Price</p>
            <p class="font-medium" id="review-cost-original-cost-price">$0.00</p>
        </div>
    </div>

    <sl-divider style="--spacing: 1rem;"></sl-divider>

    <div class="mb-4">
        <h3 class="text-lg font-semibold mb-2">Expired Cost Price Override Details</h3>
        <div class="grid grid-cols-2 gap-4 mb-2">
            <div>
                <p class="text-sm text-gray-500">Override Cost Price</p>
                <p class="font-medium text-red-600" id="review-cost-override-cost-price">$0.00</p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Expired On</p>
                <p class="font-medium text-red-600" id="review-cost-expired-date">N/A</p>
            </div>
        </div>
        <div class="mb-2">
            <p class="text-sm text-gray-500">Original Note</p>
            <p class="font-medium italic" id="review-cost-original-note">N/A</p>
        </div>
    </div>

    <form id="updateCostPriceOverrideForm">
        <input type="hidden" name="productId" id="review-cost-product-id" value="">
        <input type="hidden" name="productType" id="review-cost-product-type" value="">
        <input type="hidden" name="overrideId" id="review-cost-override-id" value="">

        <sl-input type="number" step="0.01" name="costPrice" label="New Override Cost Price" value="" required
                  class="mb-3"></sl-input>
        <sl-input type="date" name="expiresAt" label="New Expiry Date" required class="mb-3"></sl-input>
        <sl-textarea name="note" label="New Note" required resize="auto" class="mb-3"></sl-textarea>

        <div class="flex justify-between items-center mb-2">
            <div>
                <p class="text-sm text-gray-500">Approx. New Margin (vs Std Sell Price)</p>
                <p class="font-medium" id="review-cost-new-margin">N/A</p>
            </div>
            <div class="flex gap-2">
                <sl-button variant="primary" id="updateCostPriceOverrideButton">
                    <sl-spinner id="updateCostPriceOverrideButtonIndicator" slot="prefix"
                                style="display: none; --track-width: 2px; font-size: 1em;"></sl-spinner>
                    Update Cost Override
                </sl-button>
                <sl-button variant="danger" id="removeCostPriceOverrideButton">
                    <sl-spinner id="removeCostPriceOverrideButtonIndicator" slot="prefix"
                                style="display: none; --track-width: 2px; font-size: 1em;"></sl-spinner>
                    Remove Cost Override
                </sl-button>
            </div>
        </div>
    </form>
    <sl-button slot="footer" variant="neutral" onclick="htmx.find('#reviewCostPriceOverrideModal').hide();">Cancel
    </sl-button>
</sl-dialog>

@* Modal for Mapping Product Source Types *@
<sl-dialog id="mapProductSourceTypeModal" label="Map Product Source Type to Category" class="dialog-overview"
           style="--width: 500px;">
    <div id="mapProductSourceTypeModalAlertContainer"></div> @* Container for error messages *@

    <sl-select id="selectNopCategoryForMapping"
               name="selectedNopCategoryId"
               label="Select Nop Category"
               placeholder="Choose a category..."
               class="mb-4"
               required
               hoist>
        @foreach (var category in Model.NopCategories.OrderBy(c => c.Name))
        {
            <sl-option value="@category.CategoryId">@category.Name</sl-option>
        }
    </sl-select>
    <sl-divider style="--spacing: 2rem;"></sl-divider>

    @* Section for Creating New Source Type *@
    <form id="createNewSourceTypeForm"
          hx-post="@Url.Action("CreateNewSourceTypeFromExcelSheet", "Categories")"
          hx-encoding="multipart/form-data"
          hx-target="#mapProductSourceTypeModalAlertContainer"
          hx-swap="innerHTML"
          hx-include="[name='selectedNopCategoryId']"> @* Added hx-include *@

        <sl-input name="sourceTypeName" label="New Source Type Name" required class="mb-4"></sl-input>

        <label for="excelFile" class="block text-md font-medium text-gray-700 mb-1">Upload Product Excel Sheet</label>
        <input type="file" name="excelFile" id="excelFile" accept=".xlsx" required
               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-lg file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 mb-4"/>

        <sl-button type="submit" variant="success" hx-indicator="#createSourceTypeButtonIndicator">
            <sl-spinner id="createSourceTypeButtonIndicator" slot="prefix"
                        style="display: none; --track-width: 2px; --track-color: powderblue; font-size: 1em;"></sl-spinner>
            Create New & Map
        </sl-button>
    </form>

    <sl-divider style="--spacing: 2rem;"></sl-divider>
    <p class="text-center font-semibold mb-4">Or Select Existing</p>

    @* Check if there are any Nop Categories before allowing selection *@
    @if (Model.NopCategories.Any())
    {
        <sl-select id="selectSourceTypeForMapping"
                   name="sourceTypeId" @* Added name attribute *@
                   label="Select Source Type"
                   placeholder="Choose a source type..."
                   class="mb-4"
                   hoist
                   hx-get="@Url.Action("GetProductsForSourceType", "Categories")"
                   hx-target="#sourceTypeProductsTableContainer"
                   hx-swap="innerHTML"
                   hx-indicator="#sourceTypeLoadingIndicator"
                   hx-trigger="sl-change"
                   hx-include="[name='selectedNopCategoryId']"> @* Added hx-include *@
            @* Options will be populated dynamically or based on available unmapped types - Requires controller logic adjustment if needed *@
            @* For now, assume it might be populated or left empty initially *@
        </sl-select>

        <div id="sourceTypeLoadingIndicator" class="htmx-indicator">
            <sl-spinner></sl-spinner>
            Loading products...
        </div>
        <div id="sourceTypeProductsTableContainer">
            @* Product table will be loaded here by HTMX *@
        </div>

        <sl-button slot="footer" variant="primary"
                   hx-post="@Url.Action("MapProductSourceTypeToNopCategory", "Categories")"
                   hx-vals="js:{ sourceTypeId: document.getElementById('selectSourceTypeForMapping').value, selectedNopCategoryId: document.getElementById('selectNopCategoryForMapping').value }"
                   hx-target="#mapProductSourceTypeModalAlertContainer" @* Target alert container on error *@
                   hx-swap="innerHTML"
                   hx-on::before-request="if (!document.getElementById('selectNopCategoryForMapping').value) { htmx.trigger(htmx.find('#mapProductSourceTypeModal'), 'htmx:validation:fail', { message: 'Please select a Nop Category.' }); return false; } if (!document.getElementById('selectSourceTypeForMapping').value) { htmx.trigger(htmx.find('#mapProductSourceTypeModal'), 'htmx:validation:fail', { message: 'Please select a source type.' }); return false; } "
                   hx-on:htmx:validation:fail="htmx.find('#mapProductSourceTypeModalAlertContainer').innerHTML = `<sl-alert variant='danger' open closable><sl-icon slot='icon' name='exclamation-octagon'></sl-icon>${event.detail.message}</sl-alert>`"
                   hx-indicator="#mapSourceTypeButtonIndicator">
            <sl-spinner id="mapSourceTypeButtonIndicator" slot="prefix"
                        style="display: none; --track-width: 2px; font-size: 1em; --track-color: orange;"></sl-spinner>
            Map Source Type
        </sl-button>
    }
    else
    {
        <sl-alert variant="warning" open>
            <sl-icon slot="icon" name="exclamation-triangle"></sl-icon>
            No NopCommerce categories found. Please add categories before mapping source types.
        </sl-alert>
    }
    
    <sl-button slot="footer" variant="neutral" onclick="htmx.find('#mapProductSourceTypeModal').hide();">Cancel
    </sl-button>
</sl-dialog>

<style>
    @@keyframes shake {
        0%, 100% {
            transform: translateX(0);
        }
        25% {
            transform: translateX(-4px);
        }
        75% {
            transform: translateX(4px);
        }
    }

    [data-error] {
        animation: shake 0.5s ease-in-out;
    }

    [data-error] svg {
        color: orangered;
    }

    /* Add styles for expired override indicator */
    .expired-override-indicator {
        animation: pulse 1.5s infinite ease-in-out;
    }

    @@keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.6;
        }
    }

    /* Style for expired date in Days Expired column */
    .days-expired {
        font-weight: 500; /* Example style */
    }

    /* Ensure Shoelace selects within DataTables are visible */
    #productsTable sl-select::part(combobox),
    #expiredOverridesTable sl-select::part(combobox) {
        min-width: 150px; /* Adjust as needed */
    }

    #productsTable sl-select::part(listbox),
    #expiredOverridesTable sl-select::part(listbox) {
        z-index: 1050; /* Ensure dropdown appears above other elements */
    }

    sl-spinner.htmx-request {
        display: inline-flex !important;
    }
</style>

<script src="https://cdn.datatables.net/2.2.2/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/responsive/3.0.4/js/dataTables.responsive.min.js"></script>

<script type="module">
    // Configuration
    const CONFIG = {
        ENDPOINTS: {
            refreshPrice: '@(Url.Action("RefreshPrice", "ManageProducts"))',
            setSellPriceOverride: '@(Url.Action("SetSellPriceOverride", "ManageProducts"))',
            setStatusOverride: '@(Url.Action("SetStatusOverrideForProductSource", "ManageProducts"))',
            getProductSellPriceOverrideDetails: '@(Url.Action("GetProductSellPriceOverrideDetails", "ManageProducts"))',
            updateExpiredSellPriceOverride: '@(Url.Action("SetSellPriceOverride", "ManageProducts"))', // Use the same endpoint as setting a new override
            removeSellPriceOverride: '@(Url.Action("DeleteProductSourceSellPriceOverride", "ManageProducts"))',
            setStatusOverrideMultiple: '@(Url.Action("SetStatusOverrideForMultipleProductSources", "ManageProducts"))',
            editTitle: '@(Url.Action("EditTitleForProductSource", "ManageProducts"))',
            editShortDescription: '@(Url.Action("EditShortDescriptionForProductSource", "ManageProducts"))',
            editLongDescription: '@(Url.Action("EditLongDescriptionForProductSource", "ManageProducts"))',
            setBrand: '@(Url.Action("SetBrand", "ManageProducts"))',
            setCostPriceOverride: '@(Url.Action("SetCostPriceOverride", "ManageProducts"))',
            getProductCostPriceOverrideDetails: '@(Url.Action("GetProductCostPriceOverrideDetails", "ManageProducts"))',
            removeCostPriceOverride: '@(Url.Action("DeleteProductSourceCostPriceOverride", "ManageProducts"))'
        },
        CURRENCY_FORMAT: {
            locale: 'en-AU',
            currency: 'AUD'
        },
        UI: {
            successDuration: 3000,
            errorDuration: 2000
        }
    };

    // HTML escape/unescape helper functions
    function escapeHtml(unsafe) {
        if (unsafe === null || unsafe === undefined) return '';
        return unsafe
            .toString()
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    function unescapeHtml(safe) {
        if (safe === null || safe === undefined) return '';
        return safe
            .toString()
            .replace(/&amp;/g, "&")
            .replace(/&lt;/g, "<")
            .replace(/&gt;/g, ">")
            .replace(/&quot;/g, "\"")
            .replace(/&#039;/g, "'");
    }

    // Event Handlers for Expired Sell Price Overrides Tab
    class ExpiredSellPriceOverridesHandler {
        constructor(tableId) {
            this.dataTable = $('#' + tableId).DataTable(); // Get DataTable instance
            this.tableElement = document.getElementById(tableId);
            this.modal = document.getElementById('reviewSellPriceOverrideModal');
            this.form = document.getElementById('updateSellPriceOverrideForm');
            this.initialiseEventListeners();
        }

        initialiseEventListeners() {
            // Listener for Review and Remove buttons in the table
            this.tableElement.addEventListener('click', async (event) => {
                const reviewButton = event.target.closest('.review-sell-price-override');
                const removeButton = event.target.closest('.remove-sell-price-override');

                if (reviewButton && !reviewButton.disabled) {
                    await this.handleReviewSellPriceOverride(reviewButton);
                }
                if (removeButton && !removeButton.disabled) {
                    if (confirm('Are you sure you want to remove this sell price override directly? This cannot be undone.')) {
                        await this.handleDirectRemoveSellPriceOverride(removeButton);
                    }
                }
            });

            // Listeners for date filters
            const applyFilterBtn = document.getElementById('applySellPriceDateFilter');
            const resetFilterBtn = document.getElementById('resetSellPriceDateFilter');
            if (applyFilterBtn) applyFilterBtn.addEventListener('click', () => this.applyDateFilter());
            if (resetFilterBtn) resetFilterBtn.addEventListener('click', () => this.resetDateFilter());

            // Listeners for modal buttons
            const updateBtn = document.getElementById('updateSellPriceOverrideButton');
            const removeBtn = document.getElementById('removeSellPriceOverrideButton');
            if (updateBtn) updateBtn.addEventListener('click', () => this.handleSellPriceOverrideUpdateFromModal());
            if (removeBtn) removeBtn.addEventListener('click', () => this.handleRemoveSellPriceOverrideFromModal());

            // Listener for price input change in modal to update margin
            const priceInput = this.form.querySelector('[name="sellPrice"]');
            if (priceInput) priceInput.addEventListener('input', () => this.updateSellPriceMarginCalculation());
        }

        async handleReviewSellPriceOverride(button) {
            const productId = button.getAttribute('data-product-id');
            const overrideId = button.getAttribute('data-override-id'); // Get the override ID
            // Assuming ProductSource for expired overrides for now
            const productType = button.getAttribute('data-product-source') || '@ProductType.ProductSource';

            UI.setButtonLoading(button, true);
            try {
                // Fetch product and override details from the API
                const data = await API.getProductSellPriceOverrideDetails(productId, productType, overrideId);

                // Populate the modal with the fetched data
                this.populateReviewSellPriceModal(data);
                this.modal.show();
            } catch (error) {
                console.error('Failed to get sell price override details:', error);
                alert(`Error fetching sell price override details: ${error.message}`); // Simple alert
                UI.showError(button);
            } finally {
                UI.setButtonLoading(button, false);
            }
        }

        async handleDirectRemoveSellPriceOverride(button) {
            const overrideId = button.getAttribute('data-override-id');
            if (!overrideId) {
                console.error('Override ID not found on button.');
                alert('Could not remove override: Missing ID.');
                return;
            }

            const originalHtml = UI.setButtonLoading(button, true);
            try {
                // API call to remove override using the new endpoint
                await API.removeSellPriceOverride(parseInt(overrideId, 10));

                // Remove row from DataTable
                const row = button.closest('tr');
                this.dataTable.row(row).remove().draw();

                this.updateSellPriceOverridesCount(-1); // Update badge count
                // Optionally show success feedback
                // UI.showSuccessAlert('Override removed successfully.'); // Example
            } catch (error) {
                console.error('Failed to remove override directly:', error);
                alert(`Error removing override: ${error.message || 'Unknown error'}`);
                UI.showError(button); // Indicate error on the button itself
            } finally {
                // Button is removed with the row, so no need to restore HTML or loading state
                // UI.setButtonLoading(button, false); // Not strictly needed as row is gone
            }
        }

        async handleSellPriceOverrideUpdateFromModal() {
            const productId = this.form.querySelector('[name="productId"]').value;
            const productType = this.form.querySelector('[name="productType"]').value;
            const sellPriceInput = this.form.querySelector('[name="sellPrice"]');
            const expiresAtInput = this.form.querySelector('[name="expiresAt"]');
            const noteInput = this.form.querySelector('[name="note"]');

            const sellPrice = parseFloat(sellPriceInput.value);
            const expiresAt = expiresAtInput.value;
            const note = noteInput.value.trim();

            // Basic Validation
            let isValid = true;
            if (isNaN(sellPrice) || sellPrice < 0) {
                sellPriceInput.setCustomValidity('Please enter a valid non-negative price.');
                sellPriceInput.reportValidity();
                isValid = false;
            } else {
                sellPriceInput.setCustomValidity('');
            }

            if (!expiresAt) {
                expiresAtInput.setCustomValidity('Please select an expiry date.');
                expiresAtInput.reportValidity();
                isValid = false;
            } else {
                expiresAtInput.setCustomValidity('');
            }

            // Optional: Check if expiry date is in the past
            if (expiresAt && new Date(expiresAt) < new Date().setHours(0, 0, 0, 0)) {
                expiresAtInput.setCustomValidity('Expiry date cannot be in the past.');
                expiresAtInput.reportValidity();
                isValid = false;
            } else if (expiresAt) {
                expiresAtInput.setCustomValidity('');
            }


            if (!note) {
                noteInput.setCustomValidity('Please enter a note.');
                noteInput.reportValidity();
                isValid = false;
            } else {
                noteInput.setCustomValidity('');
            }

            if (!isValid) return;

            const updateButton = document.getElementById('updateSellPriceOverrideButton');
            UI.setButtonLoading(updateButton, true);
            const alertContainer = document.getElementById('reviewSellPriceOverrideModalAlertContainer');
            alertContainer.innerHTML = ''; // Clear previous alerts

            try {
                // API call to update override using the same endpoint as setting a new one
                await API.setSellPriceOverride(productId, productType, sellPrice, expiresAt, note);

                // If successful: Close modal, refresh table/row, update count
                this.modal.hide();
                alert('Override updated successfully! Table refresh needed.'); // Placeholder feedback
                // TODO: Implement table refresh or row update logic
                // this.dataTable.draw(); // Simple redraw
                this.updateSellPriceOverridesCount(-1); // Decrement count as it's no longer expired
            } catch (error) {
                console.error('Failed to update override:', error);
                alertContainer.innerHTML = `
    <sl-alert variant='danger' open closable>
        <sl-icon slot='icon' name='exclamation-octagon'></sl-icon>
        Error updating override: ${error.message}
    </sl-alert>
    `;
            } finally {
                UI.setButtonLoading(updateButton, false);
            }
        }

        async handleRemoveSellPriceOverrideFromModal() {
            if (!confirm('Are you sure you want to remove this price override? This will revert the product to its standard pricing.')) {
                return;
            }

            const overrideId = this.form.querySelector('[name="overrideId"]').value;
            if (!overrideId) {
                console.error('Override ID not found in modal form.');
                alert('Could not remove override: Missing ID.');
                return;
            }

            const removeButton = document.getElementById('removeSellPriceOverrideButton');
            UI.setButtonLoading(removeButton, true);
            const alertContainer = document.getElementById('reviewSellPriceOverrideModalAlertContainer');
            alertContainer.innerHTML = ''; // Clear previous alerts

            try {
                // API call to remove override using the new endpoint
                await API.removeSellPriceOverride(parseInt(overrideId, 10));

                // If successful: Close modal, refresh table/row, update count
                this.modal.hide();

                // Find and remove row from DataTable:
                // Find the row using the override ID stored in the button's data attribute
                const rowToRemove = this.dataTable.rows((idx, data, node) => {
                    const button = node.querySelector(`.remove-sell-price-override[data-override-id="${overrideId}"]`);
                    return !!button;
                }).nodes().to$(); // Get jQuery object for the row(s)

                if (rowToRemove.length) {
                    this.dataTable.row(rowToRemove).remove().draw();
                } else {
                    console.warn(`Could not find row with override ID ${overrideId} to remove from table after modal action.`);
                    this.dataTable.draw(); // Fallback redraw
                }
                this.updateSellPriceOverridesCount(-1); // Decrement count

            } catch (error) {
                console.error('Failed to remove override:', error);
                alertContainer.innerHTML = `
    <sl-alert variant='danger' open closable>
        <sl-icon slot='icon' name='exclamation-octagon'></sl-icon>
        Error removing override: ${error.message}
    </sl-alert>
    `;
            } finally {
                UI.setButtonLoading(removeButton, false);
            }
        }

        populateReviewSellPriceModal(data) {
            // Set hidden fields
            this.form.querySelector('[name="productId"]').value = data.productId;
            this.form.querySelector('[name="productType"]').value = data.productType;
            this.form.querySelector('[name="overrideId"]').value = data.overrideId; // Set override ID

            // Set text content
            document.getElementById('review-sell-product-sku').textContent = data.sku;
            document.getElementById('review-sell-product-title').textContent = data.title === '' ? 'N/A' : data.title;
            document.getElementById('review-sell-competitor-price').textContent = UI.formatPrice(data.competitorPrice);
            document.getElementById('review-sell-standard-price').textContent = UI.formatPrice(data.sellPrice);
            document.getElementById('review-sell-cost-price').textContent = UI.formatPrice(data.costPrice);
            document.getElementById('review-sell-override-price').textContent = UI.formatPrice(data.overridePrice);
            document.getElementById('review-sell-expired-date').textContent = this.formatExpiryDate(data.expiryDate,
                data.daysExpired);
            document.getElementById('review-sell-original-note').textContent = data.note || '(No note provided)';

            // Set form values
            this.form.querySelector('[name="sellPrice"]').value = data.overridePrice.toFixed(2);

            // Calculate a default new expiry date (e.g., 30 days from today)
            const defaultNewExpiry = new Date();
            defaultNewExpiry.setDate(defaultNewExpiry.getDate() + 30);
            this.form.querySelector('[name="expiresAt"]').value = this.formatDateForInput(defaultNewExpiry);

            this.form.querySelector('[name="note"]').value = data.note || '';

            // Calculate and display initial margin
            this.updateSellPriceMarginCalculation();

            // Clear any previous validation states
            this.form.querySelectorAll('sl-input, sl-textarea').forEach(el => {
                el.setCustomValidity('');
                el.removeAttribute('data-user-invalid');
            });
            document.getElementById('reviewSellPriceOverrideModalAlertContainer').innerHTML = '';
        }

        updateSellPriceMarginCalculation() {
            const sellPriceInput = this.form.querySelector('[name="sellPrice"]');
            const costPriceText = document.getElementById('review-sell-cost-price').textContent;
            const marginDisplay = document.getElementById('review-sell-new-margin');

            const sellPrice = parseFloat(sellPriceInput.value);
            const costPrice = parseFloat(costPriceText.replace(/[^0-9.-]+/g, ""));

            if (!isNaN(sellPrice) && !isNaN(costPrice) && costPrice > 0) {
                const margin = (sellPrice / costPrice) - 1;
                marginDisplay.textContent = margin.toLocaleString(CONFIG.CURRENCY_FORMAT.locale, {
                    style: 'percent',
                    minimumFractionDigits: 2
                });
                marginDisplay.className = margin < 0 ? "font-medium text-red-600" : "font-medium text-green-600";
            } else if (!isNaN(sellPrice) && costPrice === 0 && sellPrice > 0) {
                marginDisplay.textContent = 'Infinite';
                marginDisplay.className = "font-medium text-green-600";
            } else {
                marginDisplay.textContent = 'N/A';
                marginDisplay.className = "font-medium text-gray-600";
            }
        }

        formatExpiryDate(dateString, daysExpired) {
            try {
                // Assuming dateString is in a format parseable by Date constructor (like ISO 8601 from server)
                const date = new Date(dateString);
                if (isNaN(date)) throw new Error('Invalid date');
                const formattedDate = date.toLocaleDateString(CONFIG.CURRENCY_FORMAT.locale, {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                return `${formattedDate} (${daysExpired} days ago)`;
            } catch (e) {
                return `${dateString} (${daysExpired} days ago)`; // Fallback
            }
        }

        formatDateForInput(date) {
            // Returns YYYY-MM-DD
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        applyDateFilter() {
            const startDate = document.getElementById('expiredSellPriceStartDate').value;
            const endDate = document.getElementById('expiredSellPriceEndDate').value;

            // Store dates for filtering logic
            this.currentStartDate = startDate ? new Date(startDate + 'T00:00:00') : null;
            // Adjust end date to include the whole day
            this.currentEndDate = endDate ? new Date(endDate + 'T23:59:59') : null;

            // Add or replace the custom filter function
            $.fn.dataTable.ext.search.pop(); // Remove previous filter if exists
            $.fn.dataTable.ext.search.push((settings, data, dataIndex) => {
                if (settings.nTable.id !== 'expiredSellPriceOverridesTable') {
                    return true; // Don't filter other tables
                }
                // Only apply if dates are set
                if (!this.currentStartDate && !this.currentEndDate) {
                    return true;
                }

                debugger;
                const expiryDateStr = data[4]; // Expiry date column index (0-based)
                if (!expiryDateStr) return false; // Don't show rows without expiry date

                try {
                    // Parse the date string assuming dd/MM/yyyy format
                    const parts = expiryDateStr.split('-');
                    if (parts.length !== 3) return false; // Invalid format

                    // Rearrange to MM/dd/yyyy for reliable parsing by new Date()
                    const day = parseInt(parts[0], 10);
                    const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
                    const year = parseInt(parts[2], 10);

                    const expiryDate = new Date(year, month, day);
                    if (isNaN(expiryDate)) return false; // Invalid date format in cell

                    const startMatch = !this.currentStartDate || expiryDate >= this.currentStartDate;
                    const endMatch = !this.currentEndDate || expiryDate <= this.currentEndDate;

                    return startMatch && endMatch;
                } catch (e) {
                    console.warn("Could not parse date for filtering:", expiryDateStr);
                    return false; // Don't include rows with unparseable dates
                }
            });

            // Redraw the table with the filter applied
            this.dataTable.draw();
        }

        resetDateFilter() {
            document.getElementById('expiredSellPriceStartDate').value = '';
            document.getElementById('expiredSellPriceEndDate').value = '';
            this.currentStartDate = null;
            this.currentEndDate = null;

            // Remove the custom filter
            $.fn.dataTable.ext.search.pop();

            // Redraw the table
            this.dataTable.draw();
        }

        updateSellPriceOverridesCount(change) {
            const badgeElement = document.querySelector('sl-tab[panel="expired-sell-price-overrides"] sl-badge');
            if (badgeElement) {
                let count = parseInt(badgeElement.textContent.trim(), 10);
                if (isNaN(count)) {
                    const spinner = badgeElement.querySelector('sl-spinner');
                    if (spinner && badgeElement.textContent.trim() === spinner.outerHTML.trim()) {
                        // If badge only contains spinner, try to get initial count from server via HX-GET again or assume 0 based on need
                        // For simplicity, we'll defer count update if it's initially just a spinner; HTMX will populate it.
                        // Or, if HTMX has finished, and it's still not a number, try a default.
                        const initialCountFromHtml = badgeElement.getAttribute('data-initial-count'); // A way to preserve if needed
                        count = initialCountFromHtml ? parseInt(initialCountFromHtml, 10) : 0;
                        if (isNaN(count)) count = 0;
                    } else {
                        count = 0; // Fallback if text content isn't a number
                    }
                }
                count = Math.max(0, count + change);
                badgeElement.textContent = count.toString();
                badgeElement.variant = (count === 0) ? 'neutral' : 'danger'; // Also update variant
            }
        }
    } // End of ExpiredSellPriceOverridesHandler class

    // Event Handlers for Expired Cost Price Overrides Tab
    class ExpiredCostPriceOverridesHandler {
        constructor(tableId) {
            this.dataTable = $('#' + tableId).DataTable(); // Get DataTable instance
            this.tableElement = document.getElementById(tableId);
            this.modal = document.getElementById('reviewCostPriceOverrideModal');
            this.form = document.getElementById('updateCostPriceOverrideForm');
            this.initialiseEventListeners();
        }

        initialiseEventListeners() {
            // Listener for Review and Remove buttons in the table
            this.tableElement.addEventListener('click', async (event) => {
                const reviewButton = event.target.closest('.review-cost-price-override');
                const removeButton = event.target.closest('.remove-cost-price-override');

                if (reviewButton && !reviewButton.disabled) {
                    await this.handleReviewCostPriceOverride(reviewButton);
                }
                if (removeButton && !removeButton.disabled) {
                    if (confirm('Are you sure you want to remove this cost price override directly? This cannot be undone.')) {
                        await this.handleDirectRemoveCostPriceOverride(removeButton);
                    }
                }
            });

            // Listeners for date filters
            const applyFilterBtn = document.getElementById('applyCostPriceDateFilter');
            const resetFilterBtn = document.getElementById('resetCostPriceDateFilter');
            if (applyFilterBtn) applyFilterBtn.addEventListener('click', () => this.applyDateFilter());
            if (resetFilterBtn) resetFilterBtn.addEventListener('click', () => this.resetDateFilter());

            // Listeners for modal buttons
            const updateBtn = document.getElementById('updateCostPriceOverrideButton');
            const removeBtn = document.getElementById('removeCostPriceOverrideButton');
            if (updateBtn) updateBtn.addEventListener('click', () => this.handleUpdateCostPriceOverrideFromModal());
            if (removeBtn) removeBtn.addEventListener('click', () => this.handleRemoveCostPriceOverrideFromModal());

            // Listener for price input change in modal to update margin
            const priceInput = this.form.querySelector('[name="costPrice"]');
            if (priceInput) priceInput.addEventListener('input', () => this.updateCostPriceMarginCalculation());
        }

        async handleReviewCostPriceOverride(button) {
            const productId = button.getAttribute('data-product-id');
            const overrideId = button.getAttribute('data-override-id');
            const productType = button.getAttribute('data-product-source') || '@ProductType.ProductSource';

            UI.setButtonLoading(button, true);
            try {
                // Fetch product and override details from the API
                const data = await API.getProductCostPriceOverrideDetails(productId, overrideId);

                // Populate the modal with the fetched data
                this.populateReviewCostPriceModal(data);
                this.modal.show();
            } catch (error) {
                console.error('Failed to get cost price override details:', error);
                alert(`Error fetching cost price override details: ${error.message}`);
                UI.showError(button);
            } finally {
                UI.setButtonLoading(button, false);
            }
        }

        async handleDirectRemoveCostPriceOverride(button) {
            const overrideId = button.getAttribute('data-override-id');
            if (!overrideId) {
                console.error('Cost price override ID not found on button.');
                alert('Could not remove cost override: Missing ID.');
                return;
            }

            const originalHtml = UI.setButtonLoading(button, true);
            try {
                // API call to remove override using the new endpoint
                await API.removeCostPriceOverride(parseInt(overrideId, 10));

                // Remove row from DataTable
                const row = button.closest('tr');
                this.dataTable.row(row).remove().draw();

                this.updateCostPriceOverridesCount(-1); // Update badge count
            } catch (error) {
                console.error('Failed to remove cost price override directly:', error);
                alert(`Error removing cost price override: ${error.message || 'Unknown error'}`);
                UI.showError(button);
            } finally {
                // Button is removed with the row, so no need to restore HTML or loading state
            }
        }

        async handleUpdateCostPriceOverrideFromModal() {
            const productId = this.form.querySelector('[name="productId"]').value;
            const productType = this.form.querySelector('[name="productType"]').value;
            const costPriceInput = this.form.querySelector('[name="costPrice"]');
            const expiresAtInput = this.form.querySelector('[name="expiresAt"]');
            const noteInput = this.form.querySelector('[name="note"]');

            const costPrice = parseFloat(costPriceInput.value);
            const expiresAt = expiresAtInput.value;
            const note = noteInput.value.trim();

            // Basic Validation
            let isValid = true;
            if (isNaN(costPrice) || costPrice < 0) {
                costPriceInput.setCustomValidity('Please enter a valid non-negative price.');
                costPriceInput.reportValidity();
                isValid = false;
            } else {
                costPriceInput.setCustomValidity('');
            }

            if (!expiresAt) {
                expiresAtInput.setCustomValidity('Please select an expiry date.');
                expiresAtInput.reportValidity();
                isValid = false;
            } else {
                expiresAtInput.setCustomValidity('');
            }

            // Optional: Check if expiry date is in the past
            if (expiresAt && new Date(expiresAt) < new Date().setHours(0, 0, 0, 0)) {
                expiresAtInput.setCustomValidity('Expiry date cannot be in the past.');
                expiresAtInput.reportValidity();
                isValid = false;
            } else if (expiresAt) {
                expiresAtInput.setCustomValidity('');
            }

            if (!note) {
                noteInput.setCustomValidity('Please enter a note.');
                noteInput.reportValidity();
                isValid = false;
            } else {
                noteInput.setCustomValidity('');
            }

            if (!isValid) return;

            const updateButton = document.getElementById('updateCostPriceOverrideButton');
            UI.setButtonLoading(updateButton, true);
            const alertContainer = document.getElementById('reviewCostPriceOverrideModalAlertContainer');
            alertContainer.innerHTML = ''; // Clear previous alerts

            try {
                // API call to update override using the same endpoint as setting a new one
                await API.setCostPriceOverride(productId, productType, costPrice, expiresAt, note);

                // If successful: Close modal, refresh table/row, update count
                this.modal.hide();

                const overrideIdFromForm = this.form.querySelector('[name="overrideId"]').value;
                const rowToRemove = this.dataTable.rows((idx, data, node) => {
                    const buttonInRow = node.querySelector(`.review-cost-price-override[data-override-id="${overrideIdFromForm}"]`);
                    return !!buttonInRow;
                }).nodes().to$();

                if (rowToRemove.length) {
                    this.dataTable.row(rowToRemove).remove().draw();
                } else {
                    console.warn(`Could not find row with override ID ${overrideIdFromForm} to remove from expired cost price table after modal update.`);
                    this.dataTable.draw(); // Fallback redraw
                }
                this.updateCostPriceOverridesCount(-1); // Decrement count as it's no longer expired
            } catch (error) {
                console.error('Failed to update cost price override:', error);
                alertContainer.innerHTML = `
    <sl-alert variant='danger' open closable>
        <sl-icon slot='icon' name='exclamation-octagon'></sl-icon>
        Error updating cost price override: ${error.message}
    </sl-alert>
    `;
            } finally {
                UI.setButtonLoading(updateButton, false);
            }
        }

        async handleRemoveCostPriceOverrideFromModal() {
            if (!confirm('Are you sure you want to remove this cost price override? This will revert the product to its standard cost pricing.')) {
                return;
            }

            const overrideId = this.form.querySelector('[name="overrideId"]').value;
            if (!overrideId) {
                console.error('Cost price override ID not found in modal form.');
                alert('Could not remove cost price override: Missing ID.');
                return;
            }

            const removeButton = document.getElementById('removeCostPriceOverrideButton');
            UI.setButtonLoading(removeButton, true);
            const alertContainer = document.getElementById('reviewCostPriceOverrideModalAlertContainer');
            alertContainer.innerHTML = ''; // Clear previous alerts

            try {
                // API call to remove override using the new endpoint
                await API.removeCostPriceOverride(parseInt(overrideId, 10));

                // If successful: Close modal, refresh table/row, update count
                this.modal.hide();

                // Find and remove row from DataTable:
                const rowToRemove = this.dataTable.rows((idx, data, node) => {
                    const button = node.querySelector(`.remove-cost-price-override[data-override-id="${overrideId}"]`);
                    return !!button;
                }).nodes().to$();

                if (rowToRemove.length) {
                    this.dataTable.row(rowToRemove).remove().draw();
                } else {
                    console.warn(`Could not find row with cost price override ID ${overrideId} to remove from table after modal action.`);
                    this.dataTable.draw(); // Fallback redraw
                }
                this.updateCostPriceOverridesCount(-1); // Decrement count

            } catch (error) {
                console.error('Failed to remove cost price override:', error);
                alertContainer.innerHTML = `
    <sl-alert variant='danger' open closable>
        <sl-icon slot='icon' name='exclamation-octagon'></sl-icon>
        Error removing cost price override: ${error.message}
    </sl-alert>
    `;
            } finally {
                UI.setButtonLoading(removeButton, false);
            }
        }

        populateReviewCostPriceModal(data) {
            // Set hidden fields
            this.form.querySelector('[name="productId"]').value = data.productId;
            this.form.querySelector('[name="productType"]').value = data.productType;
            this.form.querySelector('[name="overrideId"]').value = data.overrideId;

            // Set text content
            document.getElementById('review-cost-product-sku').textContent = data.sku;
            document.getElementById('review-cost-product-title').textContent = data.title === '' ? 'N/A' : data.title;
            document.getElementById('review-cost-competitor-price').textContent = UI.formatPrice(data.competitorPrice);
            document.getElementById('review-cost-standard-sell-price').textContent = UI.formatPrice(data.sellPrice);
            document.getElementById('review-cost-original-cost-price').textContent = UI.formatPrice(data.originalCostPrice);
            document.getElementById('review-cost-override-cost-price').textContent = UI.formatPrice(data.overrideCostPrice);
            document.getElementById('review-cost-expired-date').textContent = this.formatExpiryDate(data.expiryDate, data.daysExpired);
            document.getElementById('review-cost-original-note').textContent = data.note || '(No note provided)';

            // Set form values
            this.form.querySelector('[name="costPrice"]').value = data.overrideCostPrice.toFixed(2);

            // Calculate a default new expiry date (e.g., 30 days from today)
            const defaultNewExpiry = new Date();
            defaultNewExpiry.setDate(defaultNewExpiry.getDate() + 30);
            this.form.querySelector('[name="expiresAt"]').value = this.formatDateForInput(defaultNewExpiry);

            this.form.querySelector('[name="note"]').value = data.note || '';

            // Calculate and display initial margin
            this.updateCostPriceMarginCalculation();

            // Clear any previous validation states
            this.form.querySelectorAll('sl-input, sl-textarea').forEach(el => {
                el.setCustomValidity('');
                el.removeAttribute('data-user-invalid');
            });
            document.getElementById('reviewCostPriceOverrideModalAlertContainer').innerHTML = '';
        }

        updateCostPriceMarginCalculation() {
            const costPriceInput = this.form.querySelector('[name="costPrice"]');
            const standardSellPriceText = document.getElementById('review-cost-standard-sell-price').textContent;
            const marginDisplay = document.getElementById('review-cost-new-margin');

            const newCostPrice = parseFloat(costPriceInput.value);
            const standardSellPrice = parseFloat(standardSellPriceText.replace(/[^0-9.-]+/g, ""));

            if (!isNaN(newCostPrice) && !isNaN(standardSellPrice) && standardSellPrice !== 0) {
                const marginAbsolute = standardSellPrice - newCostPrice;
                const marginPercent = (standardSellPrice > 0) ? (marginAbsolute / standardSellPrice) : (newCostPrice === 0 ? Infinity : -Infinity);

                marginDisplay.textContent = `${UI.formatPrice(marginAbsolute)} (${marginPercent.toLocaleString(CONFIG.CURRENCY_FORMAT.locale, {
                    style: 'percent',
                    minimumFractionDigits: 2
                })})`;
                marginDisplay.className = marginAbsolute < 0 ? "font-medium text-red-600" : "font-medium text-green-600";
            } else {
                marginDisplay.textContent = 'N/A';
                marginDisplay.className = "font-medium text-gray-600";
            }
        }

        formatExpiryDate(dateString, daysExpired) {
            try {
                const date = new Date(dateString);
                if (isNaN(date)) throw new Error('Invalid date');
                const formattedDate = date.toLocaleDateString(CONFIG.CURRENCY_FORMAT.locale, {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                return `${formattedDate} (${daysExpired} days ago)`;
            } catch (e) {
                return `${dateString} (${daysExpired} days ago)`;
            }
        }

        formatDateForInput(date) {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        applyDateFilter() {
            const startDate = document.getElementById('expiredCostPriceStartDate').value;
            const endDate = document.getElementById('expiredCostPriceEndDate').value;

            // Store dates for filtering logic
            this.currentStartDate = startDate ? new Date(startDate + 'T00:00:00') : null;
            // Adjust end date to include the whole day
            this.currentEndDate = endDate ? new Date(endDate + 'T23:59:59') : null;

            // Add or replace the custom filter function
            $.fn.dataTable.ext.search.pop(); // Remove previous filter if exists
            $.fn.dataTable.ext.search.push((settings, data, dataIndex) => {
                if (settings.nTable.id !== 'expiredCostPriceOverridesTable') {
                    return true; // Don't filter other tables
                }
                // Only apply if dates are set
                if (!this.currentStartDate && !this.currentEndDate) {
                    return true;
                }

                const expiryDateStr = data[4]; // Expiry date column index (0-based)
                if (!expiryDateStr) return false; // Don't show rows without expiry date

                try {
                    // Parse the date string assuming dd/MM/yyyy format
                    const parts = expiryDateStr.split('-');
                    if (parts.length !== 3) return false; // Invalid format

                    // Rearrange to MM/dd/yyyy for reliable parsing by new Date()
                    const day = parseInt(parts[0], 10);
                    const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
                    const year = parseInt(parts[2], 10);

                    const expiryDate = new Date(year, month, day);
                    if (isNaN(expiryDate)) return false; // Invalid date format in cell

                    const startMatch = !this.currentStartDate || expiryDate >= this.currentStartDate;
                    const endMatch = !this.currentEndDate || expiryDate <= this.currentEndDate;

                    return startMatch && endMatch;
                } catch (e) {
                    console.warn("Could not parse date for filtering:", expiryDateStr);
                    return false; // Don't include rows with unparseable dates
                }
            });

            // Redraw the table with the filter applied
            this.dataTable.draw();
        }

        resetDateFilter() {
            document.getElementById('expiredCostPriceStartDate').value = '';
            document.getElementById('expiredCostPriceEndDate').value = '';
            this.currentStartDate = null;
            this.currentEndDate = null;

            // Remove the custom filter
            $.fn.dataTable.ext.search.pop();

            // Redraw the table
            this.dataTable.draw();
        }

        updateCostPriceOverridesCount(change) {
            const badgeElement = document.querySelector('sl-tab[panel="expired-cost-price-overrides"] sl-badge');
            if (badgeElement) {
                let count = parseInt(badgeElement.textContent, 10);
                if (isNaN(count)) count = 0;
                count = Math.max(0, count + change);
                badgeElement.textContent = count.toString();

                // Hide the badge if count reaches zero
                badgeElement.style.display = (count <= 0) ? 'none' : '';
            }
        }
    } // End of ExpiredCostPriceOverridesHandler class

    // DataTable initialisation
    const initialiseDataTable = () => {
        return new DataTable('#productsTable', {
            responsive: true,
            serverSide: true, // Enable server-side processing
            processing: true, // Show processing indicator
            ajax: {
                url: '@(Url.Action("GetProductsData", "ManageProducts"))', // URL for data endpoint
                type: 'POST', // Use POST method
                error: function (xhr, error, thrown) { // Basic error handling
                    console.error("DataTables Error:", error, thrown);
                    alert('Error loading product data. Please try again.');
                }
            },
            columns: [ // Define columns and their data sources from ManageProductDataRow
                {data: 'CheckboxHtml', orderable: false, searchable: false},
                {data: 'Sku'},
                {
                    data: 'Title', orderable: true, searchable: true,
                    render: function (data, type, row) {
                        // Only allow editing for ProductSource items
                        if (row.Type !== '@ProductType.ProductSource') {
                            return data || ''; // Just display the title for non-ProductSource items
                        }

                        // Display HTML
                        const displayHtml = `<div class="title-display flex items-center justify-between gap-2 overflow-hidden">
                                                <span class="truncate block" title="${data || ''}">${data || '(empty)'}</span>
                                                <button class="edit-title flex-shrink-0 p-1.5 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" aria-label="Edit title">
                                                    <svg class="w-5 h-5 text-gray-500 hover:text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828zM5 14a1 1 0 11-2 0 1 1 0 012 0zm10-10l-1.414 1.414 2.828 2.828L17.828 5a1 1 0 00-1.414-1.414zM4 13H2v4h4v-2H4v-2z"></path>
                                                    </svg>
                                                </button>
                                            </div>`;

                        // Edit HTML (Hidden by default)
                        const editHtml = `<div class="hidden flex flex-col gap-2 title-edit" data-product-id="${row.Id}">
                                            <sl-input name="title" label="Title" value="${data || ''}" required class="w-full" data-original-value="${data || ''}"></sl-input>
                                            <div class="flex gap-1">
                                                <button class="save-title p-1.5 rounded-full hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors" aria-label="Save title">
                                                    <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
                                                </button>
                                                <button class="cancel-title p-1.5 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors" aria-label="Cancel edit">
                                                     <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                                                </button>
                                            </div>
                                          </div>`;

                        return displayHtml + editHtml;
                    }
                },
                { // Add brand column definition
                    data: 'Brand', orderable: true, searchable: true,
                    render: function (data, type, row) {
                        const brandDisplayValue = data || '(empty)'; // Show (empty) if brand is null/empty
                        const escapedData = escapeHtml(data || '');

                        // Display HTML
                        const displayHtml = `<div class="brand-display flex items-center justify-between gap-2 overflow-hidden">
                                                <span class="truncate block" title="${escapedData}">${brandDisplayValue}</span>
                                                <button class="edit-brand flex-shrink-0 p-1.5 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" aria-label="Edit brand">
                                                    <svg class="w-5 h-5 text-gray-500 hover:text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828zM5 14a1 1 0 11-2 0 1 1 0 012 0zm10-10l-1.414 1.414 2.828 2.828L17.828 5a1 1 0 00-1.414-1.414zM4 13H2v4h4v-2H4v-2z"></path>
                                                    </svg>
                                                </button>
                                            </div>`;

                        // Edit HTML (Hidden by default)
                        const editHtml = `<div class="hidden flex flex-col gap-2 brand-edit" data-product-id="${row.Id}" data-product-type="${row.Type}">
                                            <sl-input name="brand" label="Brand" value="${escapedData}" required class="w-full" data-original-value="${escapedData}"></sl-input>
                                            <div class="flex gap-1">
                                                <button class="save-brand p-1.5 rounded-full hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors" aria-label="Save brand">
                                                    <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
                                                </button>
                                                <button class="cancel-brand p-1.5 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors" aria-label="Cancel edit">
                                                     <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                                                </button>
                                            </div>
                                          </div>`;
                        return displayHtml + editHtml;
                    }
                },
                { // Add this new column definition for Short Description
                    data: 'ShortDescription', orderable: true, searchable: true,
                    render: function (data, type, row) {
                        // Only allow editing for ProductSource items
                        if (row.Type !== '@ProductType.ProductSource') {
                            return '<span class="text-gray-400">N/A</span>'; // Or just return '';
                        }

                        // Display HTML: Show a text button instead of an icon
                        const displayHtml = `<div class="short-desc-display flex items-center justify-center">
                                                <button class="edit-short-desc text-blue-600 hover:text-blue-800 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 rounded px-2 py-1 text-md" aria-label="Edit short description">
                                                    Click to edit
                                                </button>
                                            </div>`;

                        const editHtml = `<div class="hidden flex flex-col gap-2 short-desc-edit" data-product-id="${row.Id}">
                                            <sl-textarea name="shortDescription" label="Short Description" resize="auto" class="w-full" value="${data || ''}" data-original-value="${data || ''}"></sl-textarea>
                                            <div class="flex gap-1">
                                                <button class="save-short-desc p-1.5 rounded-full hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors" aria-label="Save short description">
                                                    <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
                                                </button>
                                                <button class="cancel-short-desc p-1.5 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors" aria-label="Cancel edit">
                                                     <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                                                </button>
                                            </div>
                                          </div>`;

                        return displayHtml + editHtml;
                    }
                },
                {
                    data: 'LongDescription', orderable: false, searchable: false,
                    render: function (data, type, row) {
                        // Only allow editing for ProductSource items
                        if (row.Type !== '@ProductType.ProductSource') {
                            return '<span class="text-gray-400">N/A</span>';
                        }

                        const escapedData = escapeHtml(data || ''); // Escape data here

                        // Display HTML: Show a text button
                        const displayHtml = `<div class="long-desc-display flex items-center justify-center">
                                                <button class="edit-long-desc text-blue-600 hover:text-blue-800 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 rounded px-2 py-1 text-md" aria-label="Edit long description">
                                                    Click to edit
                                                </button>
                                            </div>`;

                        // Edit HTML (Hidden by default)
                        const editHtml = `<div class="hidden flex flex-col gap-2 long-desc-edit" data-product-id="${row.Id}">
                                            <sl-textarea name="longDescription" label="Long Description" resize="auto" class="w-full" value="${escapedData}" data-original-value="${escapedData}"></sl-textarea>
                                            <div class="flex gap-1">
                                                <button class="save-long-desc p-1.5 rounded-full hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors" aria-label="Save long description">
                                                    <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
                                                </button>
                                                <button class="cancel-long-desc p-1.5 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors" aria-label="Cancel edit">
                                                     <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                                                </button>
                                            </div>
                                          </div>`;

                        return displayHtml + editHtml;
                    }
                },
                {
                    data: 'CompetitorPrice', orderable: true, searchable: false,
                    render: function (data, type, row) {
                        // Render Competitor Price with Refresh Button
                        const priceFormatted = UI.formatPrice(data);
                        const refreshButton = `
                            <button
                                class="refresh-price p-1.5 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors flex-shrink-0"
                                data-product-id="${row.Id}"
                                data-product-source="${row.Type}"
                                aria-label="Refresh price">
                                <svg class="w-6 h-6 text-gray-500 hover:text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                                </svg>
                            </button>`;
                        return `<div class="flex items-center gap-2 w-full">
                                <span class="price-value text-md truncate flex-grow">${priceFormatted}</span>
                                ${refreshButton}
                            </div>`;
                    }
                },
                {
                    data: 'SellPrice', orderable: true, searchable: false,
                    render: function (data, type, row) {
                        // Render Sell Price with Edit Button and Form
                        const priceFormatted = UI.formatPrice(data);
                        const editButton = `
                            <button
                                class="edit-sell-price p-1.5 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                                aria-label="Edit sell price">
                                <svg class="w-5 h-5 text-gray-500 hover:text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                     <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828zM5 14a1 1 0 11-2 0 1 1 0 012 0zm10-10l-1.414 1.414 2.828 2.828L17.828 5a1 1 0 00-1.414-1.414zM4 13H2v4h4v-2H4v-2z"></path>
                                </svg>
                            </button>`;

                        let expiredIndicator = "";
                        if (row.Type === '@ProductType.ProductSource' && row.ExpiryDate) {
                            const expiryDateObj = new Date(row.ExpiryDate); // Assumes ISO format from server
                            if (!isNaN(expiryDateObj) && expiryDateObj < new Date()) {
                                expiredIndicator = `
                                    <span
                                        class="expired-override-indicator inline-flex items-center px-2 py-0.5 rounded-full text-sm font-medium bg-red-100 text-red-800"
                                        title="Price override expired on ${expiryDateObj.toLocaleDateString('en-AU', {
                                    day: 'numeric',
                                    month: 'short',
                                    year: 'numeric'
                                })}">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                        </svg>
                                        Expired
                                    </span>`;
                            }
                        }

                        const editForm = `
                            <div class="hidden flex flex-col gap-2 sell-price-edit" data-product-id="${row.Id}" data-product-source="${row.Type}">
                                <sl-input type="number" step="0.50" name="sellPrice" label="Sell Price" value="${row.SellPrice}" required class="w-48"></sl-input>
                                ${row.Type === '@ProductType.ProductSource' ? `
                                    <sl-input type="date" name="expiresAt" label="Expiry Date" required class="w-64" value="${row.ExpiryDate ? new Date(row.ExpiryDate).toISOString().split('T')[0] : ''}" data-original-value="${row.ExpiryDate ? new Date(row.ExpiryDate).toISOString().split('T')[0] : ''}"></sl-input>
                                    <sl-textarea name="note" label="Note" required resize="auto" class="w-full" value="${row.Note || ''}" data-original-value="${row.Note || ''}"></sl-textarea>
                                ` : ''}
                                <div class="flex gap-1">
                                    <button class="save-sell-price p-1.5 rounded-full hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors" aria-label="Save sell price">
                                        <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
                                    </button>
                                    <button class="cancel-sell-price p-1.5 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors" aria-label="Cancel edit">
                                         <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                                    </button>
                                </div>
                            </div>`;

                        const sellPriceDisplayDiv = `
                            <div class="flex items-center gap-2 w-full sell-price-display">
                                <span class="price-value text-md truncate flex-grow">${priceFormatted}</span>
                                <div class="flex items-center gap-1 flex-shrink-0">
                                    ${expiredIndicator}
                                    ${editButton}
                                </div>
                            </div>`;

                        return `${sellPriceDisplayDiv}${editForm}`;
                    }
                },
                {
                    data: 'CostPrice', orderable: true, searchable: false,
                    render: function (data, type, row) {
                        // Render Cost Price with Edit Button and Form
                        const priceFormatted = UI.formatPrice(data); // 'data' here IS row.CostPrice
                        const editButton = `
                            <button
                                class="edit-cost-price p-1.5 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                                aria-label="Edit cost price">
                                <svg class="w-5 h-5 text-gray-500 hover:text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                     <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828zM5 14a1 1 0 11-2 0 1 1 0 012 0zm10-10l-1.414 1.414 2.828 2.828L17.828 5a1 1 0 00-1.414-1.414zM4 13H2v4h4v-2H4v-2z"></path>
                                </svg>
                            </button>`;

                        let expiredIndicator = "";
                        if (row.CostPriceExpiryDate) { // Assumes row.CostPriceExpiryDate is populated by server
                            const expiryDateObj = new Date(row.CostPriceExpiryDate);
                            if (!isNaN(expiryDateObj) && expiryDateObj < new Date()) {
                                expiredIndicator = `
                                    <span
                                        class="expired-override-indicator inline-flex items-center px-2 py-0.5 rounded-full text-sm font-medium bg-red-100 text-red-800"
                                        title="Cost price override expired on ${expiryDateObj.toLocaleDateString('en-AU', {
                                    day: 'numeric',
                                    month: 'short',
                                    year: 'numeric'
                                })}">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                        </svg>
                                        Expired
                                    </span>`;
                            }
                        }

                        const costPriceForInput = row.CostPrice; // This is the current effective cost price
                        const costPriceExpiryDateForInput = row.CostPriceExpiryDate ? new Date(row.CostPriceExpiryDate).toISOString().split('T')[0] : '';
                        const costPriceNoteForInput = row.CostPriceNote || '';

                        const editForm = `
                            <div class="hidden flex flex-col gap-2 cost-price-edit" data-product-id="${row.Id}" data-product-source="${row.Type}">
                                <sl-input type="number" step="0.01" name="costPrice" label="Cost Price (ex. GST)" value="${costPriceForInput}" required class="w-48"></sl-input>
                                <sl-input type="date" name="expiresAt" label="Expiry Date" required class="w-64" value="${costPriceExpiryDateForInput}" data-original-value="${costPriceExpiryDateForInput}"></sl-input>
                                <sl-textarea name="note" label="Note" required resize="auto" class="w-full" value="${costPriceNoteForInput}" data-original-value="${costPriceNoteForInput}"></sl-textarea>
                                <div class="flex gap-1">
                                    <button class="save-cost-price p-1.5 rounded-full hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors" aria-label="Save cost price">
                                        <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
                                    </button>
                                    <button class="cancel-cost-price p-1.5 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors" aria-label="Cancel edit">
                                         <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                                    </button>
                                </div>
                            </div>`;

                        const costPriceDisplayDiv = `
                            <div class="flex items-center gap-2 w-full cost-price-display">
                                <span class="price-value text-md truncate flex-grow">${priceFormatted}</span>
                                <div class="flex items-center gap-1 flex-shrink-0">
                                    ${expiredIndicator}
                                    ${editButton}
                                </div>
                            </div>`;

                        return `${costPriceDisplayDiv}${editForm}`;
                    }
                },
                {data: 'MarginHtml', orderable: true, searchable: false}, // Use pre-generated HTML
                {data: 'StatusHtml', orderable: true, searchable: true}, // Use pre-generated HTML, allow searching by underlying value (server handles this)
                {
                    data: null, orderable: false, searchable: false,
                    render: function (data, type, row) {
                        // Create a comprehensive edit button for all product types
                        return `
                            <div class="flex justify-center">
                                <sl-button
                                    variant="primary"
                                    size="medium"
                                    pill
                                    class="comprehensive-edit-btn"
                                    onclick="openComprehensiveEditModal(${row.Id}, '${row.Type}')"
                                    data-product-id="${row.Id}"
                                    data-product-type="${row.Type}">
                                    <sl-icon slot="prefix" name="pencil-square"></sl-icon>
                                    Edit
                                </sl-button>
                            </div>
                        `;
                    }
                }
            ],
            language: {
                search: "_INPUT_",
                searchPlaceholder: "Search SKU, Title...",
                processing: '<sl-spinner style="font-size: 2rem; --track-width: 4px;"></sl-spinner> Loading...' // Custom processing indicator
            },
            pageLength: 10,
            dom: '<"flex flex-col sm:flex-row justify-between items-center mb-4"f<"flex-1">l>rtip',
            // Add createdRow callback if needed for attaching complex event listeners not handled by delegation
            createdRow: function (row, data, dataIndex) {
                // Add data attributes to the row itself for easier access in event handlers if needed
                $(row).attr('data-product-id', data.Id);
                $(row).attr('data-product-type', data.Type);
                // Re-process HTMX attributes if any were added in render functions (though none are in this example)
                // htmx.process(row);
            }
        });
    };

    // DataTable initialisation for Expired Sell Price Overrides
    const initialiseExpiredSellPriceOverridesTable = () => {
        return new DataTable('#expiredSellPriceOverridesTable', {
            responsive: true,
            columnDefs: [
                {orderable: false, targets: [7]}, // Make Actions column non-orderable
                {type: 'date', targets: [4]}, // Hint for date sorting
                {type: 'num', targets: [5]} // Hint for numeric sorting (days expired)
            ],
            language: {
                search: "_INPUT_",
                searchPlaceholder: "Search SKU, Title, Note..." // Updated placeholder
            },
            pageLength: 10,
            order: [[5, 'desc']], // Sort by "Days Expired" column descending by default
            // Use DataTable's search input instead of a separate one for this table
            dom: '<"flex flex-col sm:flex-row justify-between items-center mb-4"f<"flex-1">l>rtip'
        });
    };

    // UI Components
    const UI = {
        spinnerSvg: `
            <svg class="animate-spin h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>`,

        setButtonLoading: (button, isLoading) => {
            const originalHtml = button.innerHTML;
            button.disabled = isLoading;
            button.innerHTML = isLoading ? UI.spinnerSvg : originalHtml;
            return originalHtml;
        },

        showError: (element, duration = CONFIG.UI.errorDuration) => {
            element.dataset.error = true;
            setTimeout(() => delete element.dataset.error, duration);
        },

        formatPrice: (price) => {
            return new Intl.NumberFormat(CONFIG.CURRENCY_FORMAT.locale, {
                style: 'currency',
                currency: CONFIG.CURRENCY_FORMAT.currency
            }).format(price);
        },

        createAlert: (message, variant = 'warning', duration = 10000, data = null) => {
            console.log('Creating alert:', {message, variant, duration, data});

            try {
                // Create a wrapper div for positioning
                const alertWrapper = document.createElement('div');
                alertWrapper.style.cssText = `
                    position: fixed !important;
                    top: 20px !important;
                    left: 0 !important;
                    right: 0 !important;
                    z-index: 10000 !important;
                    display: flex !important;
                    justify-content: center !important;
                    pointer-events: none !important;
                    margin: 0 !important;
                    padding: 0 20px !important;
                `;

                // Create the main alert element
                const alertElement = document.createElement('div');

                // Set up variant-specific styling
                const variantClasses = {
                    warning: 'bg-amber-50 border-amber-200 text-amber-900',
                    danger: 'bg-red-50 border-red-200 text-red-900',
                    success: 'bg-green-50 border-green-200 text-green-900',
                    info: 'bg-blue-50 border-blue-200 text-blue-900'
                };

                const iconClasses = {
                    warning: 'text-amber-400',
                    danger: 'text-red-400',
                    success: 'text-green-400',
                    info: 'text-blue-400'
                };

                const iconPaths = {
                    warning: 'M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z',
                    danger: 'M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.007v.008H12v-.008Z',
                    success: 'M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z',
                    info: 'M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z'
                };

                // Check if this is a pricing alert with competitor/cost price data
                if (data && data.reason === 'cheaper_at_competitor') {
                    alertElement.innerHTML = `
                        <div class="max-w-lg w-full ${variantClasses[variant]} border rounded-lg shadow-lg pointer-events-auto backdrop-blur-sm">
                            <div class="p-4">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 ${iconClasses[variant]}" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="${iconPaths[variant]}" />
                                        </svg>
                                    </div>
                                    <div class="ml-3 w-full">
                                        <h3 class="text-md font-semibold text-amber-800 mb-3">
                                            ⚠️ Pricing Alert: Product Flagged for Review
                                        </h3>
                                        
                                        <!-- Price Comparison with Visual Hierarchy -->
                                        <div class="bg-white rounded-md p-3 mb-3 shadow-sm">
                                            <div class="grid grid-cols-2 gap-4">
                                                <div class="text-center">
                                                    <div class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Competitor Price</div>
                                                    <div class="text-xl font-bold text-red-600 bg-red-50 rounded px-2 py-1 border border-red-200">
                                                        ${UI.formatPrice(data.competitorPrice)}
                                                    </div>
                                                    <div class="text-sm text-red-600 mt-1 font-medium">↓ Lower</div>
                                                </div>
                                                <div class="text-center">
                                                    <div class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Our Cost Price</div>
                                                    <div class="text-lg font-semibold text-gray-700 bg-gray-50 rounded px-2 py-1 border border-gray-200">
                                                        ${UI.formatPrice(data.costPrice)}
                                                    </div>
                                                    <div class="text-sm text-gray-600 mt-1">↑ Higher</div>
                                                </div>
                                            </div>
                                        </div>

                                        <p class="text-md text-amber-700 mb-3">
                                            This product has been automatically flagged because the competitor price is lower than our cost price.
                                        </p>
                                        
                                        <div class="flex items-center justify-between">
                                            <a href="/ProductsAdmin/GetProductsCheaperAtCompetitor" target="_blank"
                                               class="inline-flex items-center text-md font-medium text-blue-600 hover:text-blue-700 hover:underline transition-colors">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                                </svg>
                                                Review All Flagged Products
                                            </a>
                                            <button type="button" class="alert-close-btn text-gray-400 hover:text-gray-600 transition-colors p-1">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    // Standard alert layout for other types
                    alertElement.innerHTML = `
                        <div class="max-w-lg w-full ${variantClasses[variant]} border rounded-lg shadow-lg pointer-events-auto backdrop-blur-sm">
                            <div class="p-4">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 ${iconClasses[variant]}" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="${iconPaths[variant]}" />
                                        </svg>
                                    </div>
                                    <div class="ml-3 w-full">
                                        <div class="text-md">${message}</div>
                                    </div>
                                    <button type="button" class="alert-close-btn ml-auto pl-3 text-gray-400 hover:text-gray-600 transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                }

                alertWrapper.appendChild(alertElement);
                document.body.appendChild(alertWrapper);
                console.log('Alert element added to DOM');

                // Add close button functionality
                const closeBtn = alertElement.querySelector('.alert-close-btn');
                if (closeBtn) {
                    closeBtn.addEventListener('click', () => {
                        if (alertWrapper.parentNode) {
                            alertWrapper.parentNode.removeChild(alertWrapper);
                        }
                    });
                }

                // Add entrance animation
                alertElement.style.opacity = '0';
                alertElement.style.transform = 'translateY(-20px)';
                alertElement.style.transition = 'all 0.3s ease-out';

                // Trigger animation
                setTimeout(() => {
                    alertElement.style.opacity = '1';
                    alertElement.style.transform = 'translateY(0)';
                }, 10);

                // Auto-remove after duration
                setTimeout(() => {
                    if (alertWrapper.parentNode) {
                        alertElement.style.opacity = '0';
                        alertElement.style.transform = 'translateY(-20px)';
                        setTimeout(() => {
                            if (alertWrapper.parentNode) {
                                alertWrapper.parentNode.removeChild(alertWrapper);
                                console.log('Alert auto-removed after duration');
                            }
                        }, 300);
                    }
                }, duration);

                return alertElement;
            } catch (error) {
                console.error('Error creating alert:', error);
                alert(message.replace(/<[^>]*>/g, '')); // Fallback to browser alert
                return null;
            }
        }
    };

    // DataTable initialisation for Expired Cost Price Overrides
    const initialiseExpiredCostPriceOverridesTable = () => {
        return new DataTable('#expiredCostPriceOverridesTable', {
            responsive: true,
            columnDefs: [
                {orderable: false, targets: [7]}, // Actions column
                {type: 'date', targets: [4]},   // Expiry date
                {type: 'num', targets: [5]}     // Days expired
            ],
            language: {
                search: "_INPUT_",
                searchPlaceholder: "Search SKU, Title, Note..."
            },
            pageLength: 10,
            order: [[5, 'desc']], // Sort by Days Expired desc
            dom: '<"flex flex-col sm:flex-row justify-between items-center mb-4"f<"flex-1">l>rtip'
        });
    };

    // API Handlers
    const API = {
        async makeRequest(url, data, isJson) {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status}`);
            }

            return isJson ? response.json() : response.text();
        },

        async refreshPrice(productId, productSource) {
            return API.makeRequest(CONFIG.ENDPOINTS.refreshPrice, {
                productId,
                productSource
            }, true);
        },

        async setSellPriceOverride(productId, productSource, sellPrice, expiresAt, note) {
            const payload = {
                productId,
                productType: productSource, // Controller action expects 'productType'
                sellPrice
            };
            // Only include expiresAt and note for ProductSource type
            if (productSource === '@ProductType.ProductSource') {
                payload.expiresAt = expiresAt;
                payload.note = note;
            }
            return API.makeRequest(CONFIG.ENDPOINTS.setSellPriceOverride, payload, true);
        },

        async getProductSellPriceOverrideDetails(productId, productType, overrideId) {
            // Note: Using GET might be more appropriate, but sticking to POST for consistency with others for now.
            return API.makeRequest(CONFIG.ENDPOINTS.getProductSellPriceOverrideDetails, {
                productId,
                productType,
                overrideId
            }, true);
        },

        async setStatusOverride(productId, statusId) {
            return API.makeRequest(CONFIG.ENDPOINTS.setStatusOverride, {
                sourceId: productId, // Controller action expects 'sourceId'
                statusId
            }, true);
        },

        async removeSellPriceOverride(sellPriceOverrideId) {
            return API.makeRequest(CONFIG.ENDPOINTS.removeSellPriceOverride, {
                sellPriceOverrideId // Controller action expects this parameter name
            }, false); // Expecting 204 No Content, not JSON
        },

        async setCostPriceOverride(productId, productType, costPrice, expiresAt, note) {
            const payload = {
                productId,
                productType,
                costPrice
            };
            // Include expiresAt and note for ProductSource and PriceBook
            if (productType === '@ProductType.ProductSource' || productType === '@ProductType.Pricebook') {
                payload.expiresAt = expiresAt;
                payload.note = note;
            }
            return API.makeRequest(CONFIG.ENDPOINTS.setCostPriceOverride, payload, true);
        },

        async getProductCostPriceOverrideDetails(productId, overrideId) {
            return API.makeRequest(CONFIG.ENDPOINTS.getProductCostPriceOverrideDetails, {
                sourceId: productId,
                overrideId
            }, true);
        },

        async removeCostPriceOverride(costPriceOverrideId) {
            return API.makeRequest(CONFIG.ENDPOINTS.removeCostPriceOverride, {
                costPriceOverrideId
            }, false);
        },

        async setStatusOverrideMultiple(sourceIds, statusId) {
            return API.makeRequest(CONFIG.ENDPOINTS.setStatusOverrideMultiple, {
                sourceIds,
                statusId
            }, true); // Expecting JSON response { success: bool, count?: int, message?: string }
        },

        async editTitle(productId, newTitle) {
            return API.makeRequest(CONFIG.ENDPOINTS.editTitle, {
                productId,
                newTitle
            }, false); // Expecting text response (the updated title)
        },

        async editShortDescription(productId, newShortDescription) {
            return API.makeRequest(CONFIG.ENDPOINTS.editShortDescription, {
                productId,
                newShortDescription
            }, false); // Expecting text response (the updated description)
        },

        async editLongDescription(productId, newLongDescription) {
            return API.makeRequest(CONFIG.ENDPOINTS.editLongDescription, {
                productId,
                newLongDescription
            }, false); // Expecting text response (the updated description)
        },

        async setBrand(productId, productType, brand) {
            return API.makeRequest(CONFIG.ENDPOINTS.setBrand, {
                productId,
                productType,
                brand
            }, false); // Expecting text response (the updated brand)
        },

        async setCostPriceOverride(productId, productType, costPrice, expiresAt, note) {
            const payload = {
                productId,
                productType,
                costPrice,
                expiresAt,
                note
            };
            return API.makeRequest(CONFIG.ENDPOINTS.setCostPriceOverride, payload, true);
        }
    };

    // Event Handlers
    class ProductsTableHandler {
        constructor(tableId) {
            this.table = document.getElementById(tableId);
            this.initialiseEventListeners();
        }

        initialiseEventListeners() {
            this.table.addEventListener('click', async (event) => {
                const comprehensiveEditButton = event.target.closest('.comprehensive-edit-btn');
                const refreshButton = event.target.closest('.refresh-price');
                const editSellPriceButton = event.target.closest('.edit-sell-price');
                const saveSellPriceButton = event.target.closest('.save-sell-price');
                const cancelSellPriceButton = event.target.closest('.cancel-sell-price');
                const editStatusButton = event.target.closest('.edit-status');
                const saveStatusButton = event.target.closest('.save-status');
                const cancelStatusButton = event.target.closest('.cancel-status');
                const editShortDescButton = event.target.closest('.edit-short-desc');
                const saveShortDescButton = event.target.closest('.save-short-desc');
                const cancelShortDescButton = event.target.closest('.cancel-short-desc');
                const editLongDescButton = event.target.closest('.edit-long-desc');
                const saveLongDescButton = event.target.closest('.save-long-desc');
                const cancelLongDescButton = event.target.closest('.cancel-long-desc');
                const editTitleButton = event.target.closest('.edit-title');
                const saveTitleButton = event.target.closest('.save-title');
                const cancelTitleButton = event.target.closest('.cancel-title');
                const editBrandButton = event.target.closest('.edit-brand');
                const saveBrandButton = event.target.closest('.save-brand');
                const cancelBrandButton = event.target.closest('.cancel-brand');
                const editCostPriceButton = event.target.closest('.edit-cost-price');
                const saveCostPriceButton = event.target.closest('.save-cost-price');
                const cancelCostPriceButton = event.target.closest('.cancel-cost-price');

                if (comprehensiveEditButton && !comprehensiveEditButton.disabled) {
                    // The onclick handler will be called automatically, but we can also handle it here if needed
                    // The openComprehensiveEditModal function is called via onclick attribute
                } else if (refreshButton && !refreshButton.disabled) {
                    await this.handlePriceRefresh(refreshButton);
                } else if (editSellPriceButton) {
                    this.toggleEditSellPrice(editSellPriceButton);
                } else if (saveSellPriceButton && !saveSellPriceButton.disabled) {
                    await this.handleSellPriceUpdate(saveSellPriceButton);
                } else if (cancelSellPriceButton) {
                    this.toggleEditSellPrice(cancelSellPriceButton);
                } else if (editStatusButton) {
                    this.toggleEditStatus(editStatusButton);
                } else if (saveStatusButton && !saveStatusButton.disabled) {
                    await this.handleStatusUpdate(saveStatusButton);
                } else if (cancelStatusButton) {
                    this.toggleEditStatus(cancelStatusButton);
                } else if (editShortDescButton) {
                    this.toggleEditShortDesc(editShortDescButton);
                } else if (saveShortDescButton && !saveShortDescButton.disabled) {
                    await this.handleShortDescUpdate(saveShortDescButton);
                } else if (cancelShortDescButton) {
                    this.toggleEditShortDesc(cancelShortDescButton);
                } else if (editLongDescButton) {
                    this.toggleEditLongDesc(editLongDescButton);
                } else if (saveLongDescButton && !saveLongDescButton.disabled) {
                    await this.handleLongDescUpdate(saveLongDescButton);
                } else if (cancelLongDescButton) {
                    this.toggleEditLongDesc(cancelLongDescButton);
                } else if (editTitleButton) {
                    this.toggleEditTitle(editTitleButton);
                } else if (saveTitleButton && !saveTitleButton.disabled) {
                    await this.handleTitleUpdate(saveTitleButton);
                } else if (cancelTitleButton) {
                    this.toggleEditTitle(cancelTitleButton);
                } else if (editBrandButton) {
                    this.toggleEditBrand(editBrandButton);
                } else if (saveBrandButton && !saveBrandButton.disabled) {
                    await this.handleBrandUpdate(saveBrandButton);
                } else if (cancelBrandButton) {
                    this.toggleEditBrand(cancelBrandButton);
                } else if (editCostPriceButton) {
                    this.toggleEditCostPrice(editCostPriceButton);
                } else if (saveCostPriceButton && !saveCostPriceButton.disabled) {
                    await this.handleCostPriceUpdate(saveCostPriceButton);
                } else if (cancelCostPriceButton) {
                    this.toggleEditCostPrice(cancelCostPriceButton);
                }
            });
        }

        async handlePriceRefresh(button) {
            const originalHtml = UI.setButtonLoading(button, true);
            try {
                const data = await API.refreshPrice(
                    button.getAttribute('data-product-id'),
                    button.getAttribute('data-product-source')
                );

                console.log('Price refresh response:', data);

                // Check if the response indicates success or failure
                if (data.success === false) {
                    console.log('Handling failure response, reason:', data.reason);
                    // Handle different failure reasons
                    if (data.reason === 'cheaper_at_competitor') {
                        // Show alert for cheaper at competitor scenario
                        UI.createAlert('', 'warning', 15000, data);
                    } else {
                        // Handle other error types (service failure, no results, etc.)
                        console.error('Price refresh failed:', data.message);
                        UI.showError(button);
                        UI.createAlert(`Price refresh failed: ${data.message}`, 'danger', 5000);
                    }
                } else {
                    // Success case - update the competitor price in the table
                    const competitorPriceCell = button.closest('tr').querySelector('td:nth-child(7)');
                    if (competitorPriceCell) {
                        competitorPriceCell.querySelector('.price-value').textContent = UI.formatPrice(data.price);
                    }
                    // Note: Margin is not updated dynamically here. A full row refresh or table redraw would be needed.
                }
            } catch (error) {
                console.error('Price refresh failed:', error);
                UI.showError(button);
                UI.createAlert('Price refresh failed due to a network or server error.', 'danger', 5000);
            } finally {
                UI.setButtonLoading(button, false);
                // Restore original SVG content for the refresh button
                button.innerHTML = `<svg class="w-6 h-6 text-gray-500 hover:text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/></svg>`;
            }
        }

        toggleEditSellPrice(button) {
            const td = button.closest('td');
            const displayDiv = td.querySelector('.sell-price-display');
            const editDiv = td.querySelector('.sell-price-edit');
            const productSource = editDiv.getAttribute('data-product-source');

            displayDiv.classList.toggle('hidden');
            editDiv.classList.toggle('hidden');

            // Reset input values when cancelling/opening edit mode
            if (displayDiv.classList.contains('hidden')) { // Edit mode is being shown
                const priceInput = editDiv.querySelector('sl-input[name="sellPrice"]');
                const originalPriceText = displayDiv.querySelector('.price-value').textContent;
                const originalPrice = parseFloat(originalPriceText.replace(/[^0-9.-]+/g, ""));
                if (!isNaN(originalPrice)) {
                    priceInput.value = originalPrice;
                }

                // Reset expiry and note only if they exist (i.e., for ProductSource)
                if (productSource === '@ProductType.ProductSource') {
                    const expiryInput = editDiv.querySelector('sl-input[name="expiresAt"]');
                    const noteInput = editDiv.querySelector('sl-textarea[name="note"]');
                    if (expiryInput) {
                        expiryInput.value = expiryInput.getAttribute('data-original-value') || '';
                    }
                    if (noteInput) {
                        noteInput.value = noteInput.getAttribute('data-original-value') || '';
                    }
                }
            }
        }

        async handleSellPriceUpdate(button) {
            const editDiv = button.closest('.sell-price-edit');
            const productId = editDiv.getAttribute('data-product-id');
            const productSource = editDiv.getAttribute('data-product-source');
            const priceInput = editDiv.querySelector('sl-input[name="sellPrice"]');
            const sellPrice = parseFloat(priceInput.value);

            let expiresAt = null;
            let note = null;
            let validationPassed = true;

            if (isNaN(sellPrice) || sellPrice < 0) {
                alert('Please enter a valid non-negative sell price.');
                priceInput.setCustomValidity('Invalid price'); // Use Shoelace validation
                priceInput.reportValidity();
                validationPassed = false;
            } else {
                priceInput.setCustomValidity(''); // Clear previous validation
            }

            // Get and validate expiry and note ONLY for ProductSource
            if (productSource === '@ProductType.ProductSource') {
                const expiryInput = editDiv.querySelector('sl-input[name="expiresAt"]');
                const noteInput = editDiv.querySelector('sl-textarea[name="note"]');
                expiresAt = expiryInput.value; // Should be in 'yyyy-MM-dd' format
                note = noteInput.value.trim();

                if (!expiresAt) {
                    alert('Please select an expiry date.');
                    expiryInput.setCustomValidity('Expiry date is required');
                    expiryInput.reportValidity();
                    validationPassed = false;
                } else {
                    expiryInput.setCustomValidity('');
                }

                if (!note) {
                    alert('Please enter a note.');
                    noteInput.setCustomValidity('Note is required');
                    noteInput.reportValidity();
                    validationPassed = false;
                } else {
                    noteInput.setCustomValidity('');
                }
            }

            if (!validationPassed) {
                return; // Stop if validation failed
            }

            const originalHtml = UI.setButtonLoading(button, true);
            const saveButtonContainer = button.parentElement; // Keep reference to restore buttons
            try {
                // Pass expiresAt and note to the API call
                const data = await API.setSellPriceOverride(productId, productSource, sellPrice, expiresAt, note);
                const priceElement = button.closest('td').querySelector('.sell-price-display .price-value');
                priceElement.textContent = UI.formatPrice(data.price);

                // Update original values for expiry/note if save was successful
                if (productSource === '@ProductType.ProductSource') {
                    const expiryInput = editDiv.querySelector('sl-input[name="expiresAt"]');
                    const noteInput = editDiv.querySelector('sl-textarea[name="note"]');
                    if (expiryInput) expiryInput.setAttribute('data-original-value', expiresAt);
                    if (noteInput) noteInput.setAttribute('data-original-value', note);
                }

                this.toggleEditSellPrice(button); // Hide edit UI
                // Note: Margin is not updated dynamically here.
            } catch (error) {
                console.error('Sell price update failed:', error);
                alert(`Error updating sell price: ${error.message}`); // Simple alert
                UI.showError(editDiv); // Indicate error on the edit container
            } finally {
                UI.setButtonLoading(button, false); // Restore button state
                // Restore original save/cancel buttons (important if loading spinner replaced them)
                const cancelBtnHtml = `<button class="cancel-sell-price p-1.5 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors" aria-label="Cancel edit"><svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button>`;
                saveButtonContainer.innerHTML = originalHtml + cancelBtnHtml;
            }
        }

        toggleEditStatus(button) {
            const td = button.closest('td');
            const displayDiv = td.querySelector('.status-display');
            const editDiv = td.querySelector('.status-edit');

            displayDiv.classList.toggle('hidden');
            editDiv.classList.toggle('hidden');
        }

        async handleStatusUpdate(button) {
            // ... existing code ...
            try {
                const data = await API.setStatusOverride(productId, statusId);
                // Status is column index 8, so nth-child(9)
                const displayDivContainer = button.closest('td').querySelector('.status-display'); // Get the container
                if (displayDivContainer) {
                    // Replace the entire content of the display div, not just the inner badge div
                    displayDivContainer.parentElement.innerHTML = getStatusBadgeHtmlWithEdit(data.statusId, productId); // Use a new helper
                }
                // We don't call toggleEditStatus here because we replaced the HTML
                // this.toggleEditStatus(button); // Hide edit UI - REMOVED
            } catch (error) {
                // ... existing code ...
            } finally {
                // No need to restore button state/HTML as the parent TD content was replaced
                // UI.setButtonLoading(button, false);
                // saveButtonContainer.innerHTML = originalHtml;
            }
        }

        toggleEditTitle(button) {
            const td = button.closest('td');
            const displayDiv = td.querySelector('.title-display');
            const editDiv = td.querySelector('.title-edit');

            displayDiv.classList.toggle('hidden');
            editDiv.classList.toggle('hidden');

            // Reset input value when cancelling/opening edit mode
            if (displayDiv.classList.contains('hidden')) { // Edit mode is being shown
                const input = editDiv.querySelector('sl-input[name="title"]');
                input.value = input.getAttribute('data-original-value') || '';
            }
        }

        async handleTitleUpdate(button) {
            const editDiv = button.closest('.title-edit');
            const productId = editDiv.getAttribute('data-product-id');
            const input = editDiv.querySelector('sl-input[name="title"]');
            const newTitle = input.value.trim(); // Trim whitespace

            if (!newTitle) {
                alert('Title cannot be empty.');
                input.setCustomValidity('Title is required');
                input.reportValidity();
                return;
            } else {
                input.setCustomValidity('');
            }

            const originalHtml = UI.setButtonLoading(button, true);
            const saveButtonContainer = button.parentElement; // Keep reference to restore buttons

            try {
                const updatedTitle = await API.editTitle(productId, newTitle);

                // Update the display span and the original value attribute
                const displaySpan = button.closest('td').querySelector('.title-display span');
                displaySpan.textContent = updatedTitle || '(empty)';
                displaySpan.title = updatedTitle || ''; // Update tooltip
                input.setAttribute('data-original-value', updatedTitle); // Update original value for next edit/cancel

                this.toggleEditTitle(button); // Hide edit UI

            } catch (error) {
                console.error('Title update failed:', error);
                alert(`Error updating title: ${error.message}`); // Simple alert
                UI.showError(editDiv); // Indicate error on the edit container
            } finally {
                UI.setButtonLoading(button, false); // Restore button state
                // Restore original save/cancel buttons
                const cancelBtnHtml = `<button class="cancel-title p-1.5 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors" aria-label="Cancel edit"><svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button>`;
                saveButtonContainer.innerHTML = originalHtml + cancelBtnHtml;
            }
        }

        toggleEditBrand(button) {
            const td = button.closest('td');
            const displayDiv = td.querySelector('.brand-display');
            const editDiv = td.querySelector('.brand-edit');

            displayDiv.classList.toggle('hidden');
            editDiv.classList.toggle('hidden');

            // Reset input value when cancelling/opening edit mode
            if (displayDiv.classList.contains('hidden')) { // Edit mode is being shown
                const input = editDiv.querySelector('sl-input[name="brand"]');
                input.value = input.getAttribute('data-original-value') || '';
                input.setCustomValidity(''); // Clear any previous validation shown by browser/shoelace
            }
        }

        async handleBrandUpdate(button) {
            const editDiv = button.closest('.brand-edit');
            const productId = parseInt(editDiv.getAttribute('data-product-id'), 10);
            const productType = editDiv.getAttribute('data-product-type');
            const input = editDiv.querySelector('sl-input[name="brand"]');
            const newBrand = input.value.trim(); // Trim whitespace

            // Basic validation
            if (!newBrand) {
                alert('Brand cannot be empty.');
                input.setCustomValidity('Brand is required');
                input.reportValidity(); // Show Shoelace validation message
                return;
            } else {
                input.setCustomValidity(''); // Clear validation
            }

            const originalSaveButtonHtml = button.innerHTML; // Store original SVG of save button
            UI.setButtonLoading(button, true); // Show spinner on save button
            const saveButtonContainer = button.parentElement;

            try {
                const updatedBrand = await API.setBrand(productId, productType, newBrand);

                // Update the display span and the original value attribute
                const displaySpan = button.closest('td').querySelector('.brand-display span');
                displaySpan.textContent = updatedBrand || '(empty)';
                displaySpan.title = updatedBrand || ''; // Update tooltip
                input.setAttribute('data-original-value', updatedBrand || ''); // Update original value for next edit/cancel

                this.toggleEditBrand(button); // Hide edit UI

            } catch (error) {
                console.error('Brand update failed:', error);
                const errorMessage = await error.text ? await error.text() : error.message; // Try to get error message from response
                alert(`Error updating brand: ${errorMessage || error.message}`);
                UI.showError(editDiv);
            } finally {
                UI.setButtonLoading(button, false); // Restore button state
                button.innerHTML = originalSaveButtonHtml; // Restore original save button SVG
                // Ensure cancel button is still there (it should be, if only save button's innerHTML was changed)
            }
        }

        toggleEditShortDesc(button) {
            const td = button.closest('td');
            const displayDiv = td.querySelector('.short-desc-display');
            const editDiv = td.querySelector('.short-desc-edit');

            displayDiv.classList.toggle('hidden');
            editDiv.classList.toggle('hidden');

            // Reset input value when cancelling/opening edit mode
            if (displayDiv.classList.contains('hidden')) { // Edit mode is being shown
                const textarea = editDiv.querySelector('sl-textarea[name="shortDescription"]');
                textarea.value = textarea.getAttribute('data-original-value') || '';
            }
        }

        async handleShortDescUpdate(button) {
            const editDiv = button.closest('.short-desc-edit');
            const productId = editDiv.getAttribute('data-product-id');
            const textarea = editDiv.querySelector('sl-textarea[name="shortDescription"]');
            const newShortDescription = textarea.value.trim(); // Trim whitespace

            const originalHtml = UI.setButtonLoading(button, true);
            const saveButtonContainer = button.parentElement; // Keep reference to restore buttons

            try {
                const updatedDescription = await API.editShortDescription(productId, newShortDescription);

                // Update the original value attribute only
                textarea.setAttribute('data-original-value', updatedDescription); // Update original value for next edit/cancel

                this.toggleEditShortDesc(button); // Hide edit UI

            } catch (error) {
                console.error('Short description update failed:', error);
                alert(`Error updating short description: ${error.message}`); // Simple alert
                UI.showError(editDiv); // Indicate error on the edit container
            } finally {
                UI.setButtonLoading(button, false); // Restore button state
                // Restore original save/cancel buttons
                const cancelBtnHtml = `<button class="cancel-short-desc p-1.5 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors" aria-label="Cancel edit"><svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button>`;
                saveButtonContainer.innerHTML = originalHtml + cancelBtnHtml;
            }
        }

        toggleEditLongDesc(button) {
            const td = button.closest('td');
            const displayDiv = td.querySelector('.long-desc-display');
            const editDiv = td.querySelector('.long-desc-edit');

            displayDiv.classList.toggle('hidden');
            editDiv.classList.toggle('hidden');

            // Reset input value when cancelling/opening edit mode
            if (displayDiv.classList.contains('hidden')) { // Edit mode is being shown
                const textarea = editDiv.querySelector('sl-textarea[name="longDescription"]');
                textarea.value = textarea.getAttribute('data-original-value') || '';
            }
        }

        async handleLongDescUpdate(button) {
            const editDiv = button.closest('.long-desc-edit');
            const productId = editDiv.getAttribute('data-product-id');
            const textarea = editDiv.querySelector('sl-textarea[name="longDescription"]');
            const editedEscapedHtml = textarea.value.trim(); // This is the escaped HTML from the textarea
            const newLongDescription = unescapeHtml(editedEscapedHtml); // Unescape before sending to API

            const originalHtml = UI.setButtonLoading(button, true);
            const saveButtonContainer = button.parentElement; // Keep reference to restore buttons

            try {
                // Send the unescaped (raw) HTML to the API
                const updatedDescription = await API.editLongDescription(productId, newLongDescription);

                // `updatedDescription` from API is raw HTML. Escape it before storing.
                textarea.setAttribute('data-original-value', escapeHtml(updatedDescription)); // Escape for data-original-value

                this.toggleEditLongDesc(button); // Hide edit UI

            } catch (error) {
                console.error('Long description update failed:', error);
                alert(`Error updating long description: ${error.message}`); // Simple alert
                UI.showError(editDiv); // Indicate error on the edit container
            } finally {
                UI.setButtonLoading(button, false); // Restore button state
                // Restore original save/cancel buttons
                const cancelBtnHtml = `<button class="cancel-long-desc p-1.5 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors" aria-label="Cancel edit"><svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button>`;
                saveButtonContainer.innerHTML = originalHtml + cancelBtnHtml;
            }
        }

        toggleEditCostPrice(button) {
            const td = button.closest('td');
            const displayDiv = td.querySelector('.cost-price-display');
            const editDiv = td.querySelector('.cost-price-edit');

            displayDiv.classList.toggle('hidden');
            editDiv.classList.toggle('hidden');

            // Reset input values when cancelling/opening edit mode
            if (displayDiv.classList.contains('hidden')) { // Edit mode is being shown
                const priceInput = editDiv.querySelector('sl-input[name="costPrice"]');
                const originalPriceText = displayDiv.querySelector('.price-value').textContent;
                const originalPrice = parseFloat(originalPriceText.replace(/[^0-9.-]+/g, ""));
                if (!isNaN(originalPrice)) {
                    priceInput.value = originalPrice.toFixed(2); // Ensure two decimal places
                }

                const expiryInput = editDiv.querySelector('sl-input[name="expiresAt"]');
                const noteInput = editDiv.querySelector('sl-textarea[name="note"]');
                if (expiryInput) {
                    expiryInput.value = expiryInput.getAttribute('data-original-value') || '';
                }
                if (noteInput) {
                    noteInput.value = noteInput.getAttribute('data-original-value') || '';
                }
            }
        }

        async handleCostPriceUpdate(button) {
            const editDiv = button.closest('.cost-price-edit');
            const productId = editDiv.getAttribute('data-product-id');
            const productSource = editDiv.getAttribute('data-product-source');
            const priceInput = editDiv.querySelector('sl-input[name="costPrice"]');
            const costPrice = parseFloat(priceInput.value);

            const expiryInput = editDiv.querySelector('sl-input[name="expiresAt"]');
            const noteInput = editDiv.querySelector('sl-textarea[name="note"]');
            const expiresAt = expiryInput.value; // Should be in 'yyyy-MM-dd' format
            const note = noteInput.value.trim();

            let validationPassed = true;

            if (isNaN(costPrice) || costPrice < 0) {
                alert('Please enter a valid non-negative cost price.');
                priceInput.setCustomValidity('Invalid price');
                priceInput.reportValidity();
                validationPassed = false;
            } else {
                priceInput.setCustomValidity('');
            }

            if (!expiresAt) {
                alert('Please select an expiry date.');
                expiryInput.setCustomValidity('Expiry date is required');
                expiryInput.reportValidity();
                validationPassed = false;
            } else {
                expiryInput.setCustomValidity('');
            }

            if (!note) {
                alert('Please enter a note.');
                noteInput.setCustomValidity('Note is required');
                noteInput.reportValidity();
                validationPassed = false;
            } else {
                noteInput.setCustomValidity('');
            }

            if (!validationPassed) {
                return; // Stop if validation failed
            }

            const originalSaveButtonHtml = button.innerHTML; // Store original SVG for save button
            UI.setButtonLoading(button, true); // Show spinner on save button
            const saveButtonContainer = button.parentElement;

            try {
                const data = await API.setCostPriceOverride(productId, productSource, costPrice, expiresAt, note);

                const priceElement = button.closest('td').querySelector('.cost-price-display .price-value');
                priceElement.textContent = UI.formatPrice(data.costPrice); // Assuming API returns { costPrice: newCostPrice }

                // Update original values for expiry/note if save was successful
                if (expiryInput) expiryInput.setAttribute('data-original-value', expiresAt);
                if (noteInput) noteInput.setAttribute('data-original-value', note);

                // Update the data-original-value for the price input as well
                priceInput.setAttribute('data-original-value', data.costPrice.toFixed(2));

                // Also update the value attribute on the input in the hidden form,
                // so if user re-opens edit without page refresh, it has the latest saved value.
                priceInput.value = data.costPrice.toFixed(2);


                this.toggleEditCostPrice(button); // Hide edit UI
                // Note: Margin is not updated dynamically here. A full table redraw or row refresh would be needed.
            } catch (error) {
                console.error('Cost price update failed:', error);
                alert(`Error updating cost price: ${error.message}`); // Simple alert
                UI.showError(editDiv); // Indicate error on the edit container
            } finally {
                UI.setButtonLoading(button, false); // Restore button state
                button.innerHTML = originalSaveButtonHtml; // Restore original save button SVG
                // Ensure cancel button is still there (it should be if only save button's innerHTML was changed)
            }
        }
    }

    // Helper to generate status badge HTML
    const getStatusBadgeHtml = (statusId) => {
        const statusInt = parseInt(statusId, 10);
        switch (statusInt) {
            case 1: // NotPublished
                return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-md font-medium bg-yellow-100 text-yellow-800">Not Published</span>`;
            case 2: // NotCompliant
                return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-md font-medium bg-red-100 text-red-800">Not Compliant</span>`;
            case 3: // Published
                return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-md font-medium bg-green-100 text-green-800">Published</span>`;
            case 4: // New
                return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-md font-medium bg-blue-100 text-blue-800">New</span>`;
            default:
                return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-md font-medium bg-gray-100 text-gray-800">Unknown</span>`;
        }
    };

    // Helper to generate status badge HTML including the edit button and hidden form
    const getStatusBadgeHtmlWithEdit = (statusId, productId) => {
        const statusInt = parseInt(statusId, 10);
        let statusText = "Unknown";
        let badgeClass = "bg-gray-100 text-gray-800"; // Default

        // Assuming Model.ProductStatuses is available globally or passed differently
        // For now, hardcode based on known values
        const statuses = {1: "Not Published", 2: "Not Compliant", 3: "Published", 4: "New"};
        const statusClasses = {
            1: "bg-yellow-100 text-yellow-800",
            2: "bg-red-100 text-red-800",
            3: "bg-green-100 text-green-800",
            4: "bg-blue-100 text-blue-800"
        };

        if (statuses[statusInt]) {
            statusText = statuses[statusInt];
            badgeClass = statusClasses[statusInt] || badgeClass;
        }

        const statusBadge = `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-md font-medium ${badgeClass}">${statusText}</span>`;

        const editButton = `
            <button
                class="edit-status p-1.5 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                aria-label="Edit status">
                <svg class="w-5 h-5 text-gray-500 hover:text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828zM5 14a1 1 0 11-2 0 1 1 0 012 0zm10-10l-1.414 1.414 2.828 2.828L17.828 5a1 1 0 00-1.414-1.414zM4 13H2v4h4v-2H4v-2z"></path>
                </svg>
            </button>`;

        // Generate options dynamically - requires access to Model.ProductStatuses
        // Hardcoding options for now as passing the model to JS is complex here
        const statusOptionsHtml = Object.entries(statuses)
            .map(([id, text]) => `<sl-option value="${id}">${text}</sl-option>`)
            .join('');

        const editForm = `
            <div class="hidden flex flex-col gap-2 status-edit" data-product-id="${productId}">
                <sl-select name="statusId" label="Status" value="${statusId}" required class="w-64" hoist>
                    ${statusOptionsHtml}
                </sl-select>
                <div class="flex gap-1">
                    <button class="save-status p-1.5 rounded-full hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors" aria-label="Save status">
                        <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
                    </button>
                    <button class="cancel-status p-1.5 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors" aria-label="Cancel edit">
                        <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                    </button>
                </div>
            </div>`;

        return `<div class="flex items-center justify-between gap-2 status-display"><div>${statusBadge}</div>${editButton}</div>${editForm}`;
    };

    // Initialise
    const productsDataTable = initialiseDataTable(); // Initialise the main table
    window.productsDataTable = productsDataTable; // Make it globally accessible for the comprehensive edit modal
    new ProductsTableHandler('productsTable'); // Attach event handlers

    // --- Start: Initialise Expired Sell Price Overrides Tab ---
    const expiredSellPriceOverridesDataTable = initialiseExpiredSellPriceOverridesTable();
    const expiredSellPriceOverridesHandler = new ExpiredSellPriceOverridesHandler('expiredSellPriceOverridesTable');
    // --- End: Initialise Expired Sell Price Overrides Tab ---

    // --- Start: Initialise Expired Cost Price Overrides Tab ---
    const expiredCostPriceOverridesDataTable = initialiseExpiredCostPriceOverridesTable();
    const expiredCostPriceOverridesHandler = new ExpiredCostPriceOverridesHandler('expiredCostPriceOverridesTable');
    // --- End: Initialise Expired Cost Price Overrides Tab ---

    // --- Start: Modal JavaScript ---

    // --- Start: Status Filter Logic ---
    function getStatusFilter() {
        return document.getElementById('statusFilter');
    }

    getStatusFilter().addEventListener('sl-change', (event) => {
        const selectedStatusValue = event.target.value || ""; // Use empty string for "All statuses"

        // Send the selected value as the search term for the Status column (index 10)
        productsDataTable.column(10).search(selectedStatusValue).draw();
    });

    // --- End: Status Filter Logic ---

    function displayErrorAlert(message, tab) { // Keep this function signature for potential future use, even if 'tab' isn't used now
        if (!message) {
            return;
        }

        let alert = document.createElement('sl-alert');
        alert.setAttribute('variant', 'danger');
        alert.setAttribute('open', '');
        alert.setAttribute('closable', '');
        alert.innerHTML = `<sl-icon slot='icon' name='exclamation-octagon'></sl-icon>${message}`;
        // Target the modal's alert container
        const container = document.querySelector('#mapProductSourceTypeModalAlertContainer');
        if (container) {
            // Clear previous alerts before adding a new one
            container.innerHTML = '';
            container.prepend(alert);
        } else {
            console.error("Alert container not found for modal.");
        }
    }

    function prependErrorAlert(message, selector) {
        if (!message) {
            return;
        }

        let alert = document.createElement('sl-alert');
        alert.setAttribute('variant', 'danger');
        alert.setAttribute('open', '');
        alert.setAttribute('closable', '');
        alert.innerHTML = `<sl-icon slot='icon' name='exclamation-octagon'></sl-icon>${message}`;
        const targetElement = document.querySelector(selector);
        if (targetElement) {
            // Clear previous alerts near this element if necessary
            const existingAlert = targetElement.previousElementSibling;
            if (existingAlert && existingAlert.tagName === 'SL-ALERT') {
                existingAlert.remove();
            }
            targetElement.insertAdjacentElement('beforebegin', alert);
        } else {
            console.error(`Target element not found for selector: ${selector}`);
            // Fallback to modal alert container
            displayErrorAlert(message);
        }
    }

    // Add helper script for modal button indicator
    htmx.on('#mapProductSourceTypeModal [hx-post]', 'htmx:beforeRequest', function (evt) {
        const htmxElement = evt.detail.elt; // Element with hx-post
        let submitButton;

        if (htmxElement.tagName === 'FORM') {
            // For a form, find its submit button.
            // Assumes the form has one sl-button of type submit.
            submitButton = htmxElement.querySelector('sl-button[type="submit"]');
        } else if (htmxElement.tagName === 'SL-BUTTON') {
            // If hx-post is directly on an sl-button
            submitButton = htmxElement;
        } else {
            // If hx-post is on an element inside an sl-button (e.g. an icon)
            const parentButton = htmxElement.closest('sl-button');
            if (parentButton) {
                submitButton = parentButton;
            }
        }

        if (submitButton) {
            submitButton.disabled = true;
        }
    });

    htmx.on('#mapProductSourceTypeModal [hx-post]', 'htmx:afterRequest', function (evt) {
        const htmxElement = evt.detail.elt;
        let submitButton;

        if (htmxElement.tagName === 'FORM') {
            submitButton = htmxElement.querySelector('sl-button[type="submit"]');
        } else if (htmxElement.tagName === 'SL-BUTTON') {
            submitButton = htmxElement;
        } else {
            const parentButton = htmxElement.closest('sl-button');
            if (parentButton) {
                submitButton = parentButton;
            }
        }

        if (submitButton) {
            submitButton.disabled = false;
        }

        // If the request was successful (implies redirect from controller or specific instructions followed),
        // close the modal and reload the page.
        // A 204 No Content response typically indicates success without a body, often used for successful PUT/DELETE or POSTs that don't return content.
        // A 200 OK with content (like a partial for an alert) might also occur without a full page redirect from the server.
        // The original logic for reload/modal hide was specific to how the server responds after these hx-post actions.

        if (evt.detail.successful) {
            // Check if the server response indicates a redirect (captured by HTMX via HX-Redirect header or a 200 with HX-Trigger for client-side redirect)
            // or if the content swap implies success that should close the modal.
            // For simplicity aligning with original intent: if successful and not a 204 where no further action is explicitly defined by swap:
            if (evt.detail.xhr.status !== 204 || (evt.detail.xhr.status === 204 && htmxElement.id !== 'createNewSourceTypeForm' /* Keep modal open for form errors displayed in target */)) {
                // For 204 from the "Map Source Type" button, we expect the modal to close and page reload.
                // For the "Create New & Map" form, if it results in an error displayed in `mapProductSourceTypeModalAlertContainer`,
                // we wouldn't want to unconditionally close the modal here. The new logic below takes care of that.
            }

            // More nuanced handling based on what happened:
            // evt.detail.target is the element targeted by hx-target.
            // evt.detail.xhr contains the response.
            // If server sends HX-Redirect, HTMX handles it.
            // If server sends success HTML to swap (e.g., a success message), and that means "done":

            // Original logic was:
            // if (evt.detail.successful && evt.detail.xhr.status !== 204) { modal.hide(); window.location.reload(); }
            // else if (!evt.detail.successful) { console.log("Error"); }
            // else { modal.hide(); /* for 204 */ } => This implies 204 also closes modal.

            // Revised general logic for modal closure and reload on success:
            const modal = htmx.closest(htmxElement, 'sl-dialog');
            if (modal) {
                // If the swap target is the alert container and it received an error, don't close.
                // This is handled by `!evt.detail.failed` (new HTMX syntax) or `evt.detail.successful`
                // and checking if the response indicates an error state intended for the alert container.
                // The form `createNewSourceTypeForm` targets `#mapProductSourceTypeModalAlertContainer`.
                // If an error is swapped in, `evt.detail.successful` might still be true (200 OK with error partial).
                // If HTMX handles `HX-Redirect`, this `afterRequest` logic might not even need to close/reload.

                // Let's stick to a simpler version of original intent: close & reload on success unless target is alert and indicates error.
                let isErrorInAlert = false;
                if (htmxElement.tagName === 'FORM' && evt.detail.target && evt.detail.target.id === 'mapProductSourceTypeModalAlertContainer') {
                    if (evt.detail.target.querySelector('sl-alert[variant="danger"]')) {
                        isErrorInAlert = true;
                    }
                }

                // The "Map Source Type" button also targets mapProductSourceTypeModalAlertContainer for errors.
                if (htmxElement.matches('sl-button[hx-post*="MapProductSourceTypeToNopCategory"]') && evt.detail.target && evt.detail.target.id === 'mapProductSourceTypeModalAlertContainer') {
                    if (evt.detail.target.querySelector('sl-alert[variant="danger"]')) {
                        isErrorInAlert = true;
                    }
                }

                if (!isErrorInAlert) { // If no error was swapped into the alert container by this request
                    modal.hide();
                    // Only reload if it's not a simple content swap indicating an error.
                    // Controller Actions like CreateNewSourceTypeFromExcelSheet and MapProductSourceTypeToNopCategory
                    // redirect on full success. If they return a partial (e.g. error alert), they don't redirect.
                    // So, if we are here and `isErrorInAlert` is false, it implies a redirect happened or a 204 success.
                    // For 204, a reload might be needed to see changes if not handled by other HTMX swaps.
                    if (evt.detail.xhr.status === 204) { // Specifically for 204 (e.g., Map existing button)
                        window.location.reload(); // Reload to reflect mapping changes
                    }
                    // If it was a redirect, the page is already reloading.
                }
            }
        } else if (evt.detail.failed) { // Explicitly for network errors or non-2xx/3xx responses not handled as success by htmx
            // Error occurred - let's make sure it gets displayed in the alert container
            console.error("HTMX request failed:", evt.detail);

            // Get the alert container for the modal
            const alertContainer = document.getElementById('mapProductSourceTypeModalAlertContainer');

            if (alertContainer) {
                let errorMessage = "An unknown error occurred";

                // Try to extract meaningful error information from the response
                if (evt.detail.xhr && evt.detail.xhr.responseText) {
                    try {
                        // Check if response already contains an alert element
                        if (evt.detail.xhr.responseText.includes('<sl-alert') &&
                            evt.detail.xhr.responseText.includes('variant="danger"')) {
                            // If it's already formatted as an alert, use it directly
                            alertContainer.innerHTML = evt.detail.xhr.responseText;
                            return;
                        }

                        // Try parsing as JSON first
                        const jsonResponse = JSON.parse(evt.detail.xhr.responseText);
                        errorMessage = jsonResponse.message || jsonResponse.error || JSON.stringify(jsonResponse);
                    } catch (e) {
                        // If not parseable as JSON, use as plain text (could be HTML)
                        errorMessage = evt.detail.xhr.responseText;
                    }
                } else if (evt.detail.error) {
                    // Network error or other client-side error
                    errorMessage = evt.detail.error;
                } else if (evt.detail.xhr && evt.detail.xhr.status) {
                    // Use status text if available
                    errorMessage = `Server error: ${evt.detail.xhr.status} ${evt.detail.xhr.statusText}`;
                }

                // Display the error in the alert container
                alertContainer.innerHTML = errorMessage;
            }
        }
    });

    // Handle file input validation for Excel sheet
    const excelFileInput = document.getElementById('excelFile');
    if (excelFileInput) {
        excelFileInput.addEventListener('change', function (e) {
            const file = e.target.files[0];
            const form = e.target.closest('form'); // Find the parent form
            if (file && !file.name.toLowerCase().endsWith('.xlsx')) {
                if (form) {
                    prependErrorAlert('Please select an Excel file (.xlsx)', `#${form.id}`);
                } else {
                    displayErrorAlert('Please select an Excel file (.xlsx)'); // Fallback
                }
                e.target.value = ''; // Clear the file input
            } else {
                // Clear previous error alerts if the file is valid
                const existingAlert = form?.previousElementSibling;
                if (existingAlert && existingAlert.tagName === 'SL-ALERT' && existingAlert.textContent.includes('Excel file')) {
                    existingAlert.remove();
                }
                const modalAlertContainer = document.getElementById('mapProductSourceTypeModalAlertContainer');
                if (modalAlertContainer) modalAlertContainer.innerHTML = ''; // Clear general modal errors too
            }
        });
    }

    // Clear product list when Nop Category changes
    const nopCategorySelect = document.getElementById('selectNopCategoryForMapping');
    if (nopCategorySelect) {
        nopCategorySelect.addEventListener('sl-change', () => {
            const productContainer = document.getElementById('sourceTypeProductsTableContainer');
            const sourceTypeSelect = document.getElementById('selectSourceTypeForMapping');
            if (productContainer) productContainer.innerHTML = ''; // Clear loaded products
            if (sourceTypeSelect) sourceTypeSelect.value = ''; // Reset source type selection
            // Potentially trigger hx-get on sourceTypeSelect if you want to auto-load sources for the new category
        });
    }

    // Add event listener for the expired sell price overrides badge update
    const expiredSellPriceBadge = document.getElementById('expired-sell-price-overrides-badge');
    if (expiredSellPriceBadge) {
        expiredSellPriceBadge.addEventListener('htmx:afterSwap', function (event) {
            const count = parseInt(event.target.textContent.trim(), 10);
            if (!isNaN(count)) {
                event.target.variant = (count === 0) ? 'neutral' : 'danger';
            } else {
                event.target.variant = 'danger'; // Default to danger if parsing fails
            }
        });
    }

    // Add event listener for the expired cost price overrides badge update
    const expiredCostPriceBadge = document.getElementById('expired-cost-price-overrides-badge');
    if (expiredCostPriceBadge) {
        expiredCostPriceBadge.addEventListener('htmx:afterSwap', function (event) {
            const count = parseInt(event.target.textContent.trim(), 10);
            if (!isNaN(count)) {
                event.target.variant = (count === 0) ? 'neutral' : 'danger';
            } else {
                event.target.variant = 'danger'; // Default to danger if parsing fails
            }
        });
    }

    // --- End: Modal JavaScript ---

    // --- Start: Bulk Status Update Logic ---
    const selectAllCheckbox = document.getElementById('selectAllProductsCheckbox');
    const productsTableBody = document.querySelector('#productsTable tbody');
    const bulkStatusSelect = document.getElementById('bulkStatusSelect');
    const bulkUpdateButton = document.getElementById('bulkUpdateStatusButton');
    const bulkUpdateAlertContainer = document.getElementById('bulkUpdateAlertContainer');

    // Function to get all visible and relevant checkboxes
    function getVisibleProductCheckboxes() {
        // Use DataTable API to get nodes of visible rows, then query checkboxes within them
        const visibleRows = productsDataTable.rows({search: 'applied'}).nodes();
        return Array.from(visibleRows).reduce((acc, row) => {
            const checkbox = row.querySelector('td:first-child .product-source-checkbox');
            if (checkbox) {
                acc.push(checkbox);
            }
            return acc;
        }, []);
    }

    // Select/Deselect All Checkbox Handler
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('sl-change', (event) => {
            const isChecked = event.target.checked;
            const checkboxes = getVisibleProductCheckboxes();
            checkboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
        });
    }

    // Individual Checkbox Handler (to uncheck "Select All" if one is unchecked)
    if (productsTableBody) {
        productsTableBody.addEventListener('sl-change', (event) => {
            if (event.target.classList.contains('product-source-checkbox')) {
                if (!event.target.checked && selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                }
                // Optional: Check if all are checked to update selectAllCheckbox state
                // const allCheckboxes = getVisibleProductCheckboxes();
                // if (allCheckboxes.length > 0 && allCheckboxes.every(cb => cb.checked)) {
                //     selectAllCheckbox.checked = true;
                // }
            }
        });
    }

    // Bulk Update Button Handler
    if (bulkUpdateButton) {
        bulkUpdateButton.addEventListener('click', async () => {
            const selectedStatusId = bulkStatusSelect.value;
            const selectedCheckboxes = Array.from(productsTableBody.querySelectorAll('.product-source-checkbox[checked]'));
            const selectedSourceIds = selectedCheckboxes.map(cb => parseInt(cb.value, 10));
            const spinner = bulkUpdateButton.querySelector('sl-spinner');

            bulkUpdateAlertContainer.innerHTML = ''; // Clear previous alerts

            // Validation
            if (selectedSourceIds.length === 0) {
                bulkUpdateAlertContainer.innerHTML = `<sl-alert variant="warning" open closable><sl-icon slot="icon" name="exclamation-triangle"></sl-icon>Please select at least one product.</sl-alert>`;
                return;
            }
            if (!selectedStatusId) {
                bulkUpdateAlertContainer.innerHTML = `<sl-alert variant="warning" open closable><sl-icon slot="icon" name="exclamation-triangle"></sl-icon>Please select a status to apply.</sl-alert>`;
                return;
            }

            // Disable button and show spinner
            bulkUpdateButton.disabled = true;
            if (spinner) spinner.style.display = 'inline-flex';

            try {
                const result = await API.setStatusOverrideMultiple(selectedSourceIds, parseInt(selectedStatusId, 10));

                if (result.success) {
                    bulkUpdateAlertContainer.innerHTML = `<sl-alert variant="success" duration="3000" open closable><sl-icon slot="icon" name="check-circle"></sl-icon>Successfully updated status for ${result.count || 0} products. Reloading...</sl-alert>`;
                    // Reload the page to reflect changes easily
                    setTimeout(() => window.location.reload(), 1500);
                } else {
                    throw new Error(result.message || 'An unknown error occurred during bulk update.');
                }
            } catch (error) {
                console.error('Bulk status update failed:', error);
                bulkUpdateAlertContainer.innerHTML = `<sl-alert variant="danger" open closable><sl-icon slot="icon" name="exclamation-octagon"></sl-icon>Error updating statuses: ${error.message}</sl-alert>`;
                // Re-enable button on error
                bulkUpdateButton.disabled = false;
                if (spinner) spinner.style.display = 'none';
            }
            // Note: Button remains disabled on success because the page will reload.
        });
    }
    // --- End: Bulk Status Update Logic ---
</script>

@* Include the comprehensive edit modal *@
@Html.Partial("_ComprehensiveEditModal")

@* Include the comprehensive edit JavaScript *@
<script src="@Url.Content("~/Scripts/ManageProducts/ComprehensiveEdit.js")"></script>

@* Comprehensive Product Edit Modal *@
<sl-dialog id="comprehensiveEditModal" label="Edit Product" class="comprehensive-edit-modal"
           style="--width: 1200px; --height: 85vh;"
           role="dialog"
           aria-modal="true"
           aria-labelledby="modal-title"
           aria-describedby="modal-description">
    <div id="comprehensiveEditAlertContainer" class="mb-6" role="alert" aria-live="polite"></div>

    <!-- Loading indicator -->
    <div id="comprehensiveEditLoader" class="loader-container hidden">
        <div class="animate-spin rounded-full h-12 w-12 border-b-4 border-blue-600"></div>
        <p class="mt-4 text-center text-xl font-semibold text-gray-700">Loading product details...</p>
    </div>

    <!-- Modal content -->
    <div id="comprehensiveEditContent" class="hidden">
        <form id="comprehensiveEditForm">
            <input type="hidden" id="editProductId" name="productId"/>
            <input type="hidden" id="editProductType" name="productType"/>

            <!-- Current Product Status - Enhanced Design -->
            <div
                class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border-2 border-blue-200 p-8 rounded-xl shadow-lg mb-8">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-3xl font-bold text-blue-900 flex items-center">
                        <svg class="w-8 h-8 mr-3 text-blue-700" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                  clip-rule="evenodd"></path>
                        </svg>
                        Current Product Status
                    </h2>
                    <div id="previewModeIndicator" class="hidden">
                        <span
                            class="inline-flex items-center px-4 py-2 rounded-full text-lg font-semibold bg-yellow-100 text-yellow-800 border-2 border-yellow-300">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                <path fill-rule="evenodd"
                                      d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                                      clip-rule="evenodd"></path>
                            </svg>
                            Preview Mode
                        </span>
                    </div>
                </div>

                <!-- Enhanced Main pricing information with larger fonts -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div
                        class="bg-white p-6 rounded-xl border-2 border-blue-100 shadow-md hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-2xl font-bold text-gray-800">Cost Price</span>
                            <div id="costPriceOverrideIndicator" class="hidden">
                                <span
                                    class="inline-flex items-center px-3 py-1 rounded-full text-lg font-bold bg-green-100 text-green-800 border border-green-300">
                                    Override Active
                                </span>
                            </div>
                        </div>
                        <div class="text-4xl font-black text-blue-700 mb-2" id="currentCostPrice">-</div>
                        <div class="text-xl text-gray-600 font-semibold" id="costPriceSource">Base price</div>
                    </div>

                    <div
                        class="bg-white p-6 rounded-xl border-2 border-blue-100 shadow-md hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-2xl font-bold text-gray-800">Sell Price</span>
                            <div id="sellPriceOverrideIndicator" class="hidden">
                                <span
                                    class="inline-flex items-center px-3 py-1 rounded-full text-lg font-bold bg-green-100 text-green-800 border border-green-300">
                                    Override Active
                                </span>
                            </div>
                        </div>
                        <div class="text-4xl font-black text-blue-700 mb-2" id="currentSellPrice">-</div>
                        <div class="text-xl text-gray-600 font-semibold" id="sellPriceSource">Base price</div>
                    </div>

                    <div
                        class="bg-white p-6 rounded-xl border-2 border-blue-100 shadow-md hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-2xl font-bold text-gray-800">Margin</span>
                            <svg class="w-10 h-10 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                      d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 0a1 1 0 100 2h.01a1 1 0 100-2H9zm2 0a1 1 0 100 2h.01a1 1 0 100-2H11zm0-2a1 1 0 100 2h.01a1 1 0 100-2H11zm-2 0a1 1 0 100 2h.01a1 1 0 100-2H9zm-2 0a1 1 0 100 2h.01a1 1 0 100-2H7zm2-2a1 1 0 100 2h.01a1 1 0 100-2H9zm2 0a1 1 0 100 2h.01a1 1 0 100-2H11z"
                                      clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="text-5xl font-black text-green-600 mb-2" id="currentMargin">-</div>
                        <div class="text-2xl text-gray-600 font-bold" id="marginPercentage">-</div>
                    </div>

                    <div
                        class="bg-white p-6 rounded-xl border-2 border-blue-100 shadow-md hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-2xl font-bold text-gray-800">Status</span>
                            <svg class="w-10 h-10 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                      d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z"
                                      clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="text-4xl font-black text-blue-700 mb-2" id="currentStatus">-</div>
                        <div class="text-xl text-gray-600 font-semibold" id="lastModified">-</div>
                    </div>
                </div>

                <!-- Enhanced Preview section for price changes -->
                <div id="pricePreviewSection"
                     class="hidden bg-gradient-to-r from-yellow-50 to-amber-50 border-2 border-yellow-300 p-6 rounded-xl shadow-lg">
                    <h4 class="text-2xl font-bold text-yellow-800 mb-4 flex items-center">
                        <svg class="w-8 h-8 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                            <path fill-rule="evenodd"
                                  d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                                  clip-rule="evenodd"></path>
                        </svg>
                        Preview of Changes
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-xl">
                        <div class="bg-white p-4 rounded-lg border border-yellow-200">
                            <span class="font-bold text-yellow-800 block mb-2">New Cost Price:</span>
                            <span id="previewCostPrice" class="text-2xl text-yellow-700 font-black">-</span>
                        </div>
                        <div class="bg-white p-4 rounded-lg border border-yellow-200">
                            <span class="font-bold text-yellow-800 block mb-2">New Sell Price:</span>
                            <span id="previewSellPrice" class="text-2xl text-yellow-700 font-black">-</span>
                        </div>
                        <div class="bg-white p-4 rounded-lg border border-yellow-200">
                            <span class="font-bold text-yellow-800 block mb-2">New Margin:</span>
                            <span id="previewMargin" class="text-2xl font-black">-</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Tabbed Interface with Enhanced Accessibility -->
            <sl-tab-group class="modern-tab-group" role="tablist" aria-label="Product editing sections">
                <sl-tab slot="nav" panel="product-info" class="tab-large" role="tab" aria-controls="product-info-panel"
                        aria-label="Edit basic product information">
                    <sl-icon name="info-circle" class="mr-2" aria-hidden="true"></sl-icon>
                    Product Information
                </sl-tab>
                <sl-tab slot="nav" panel="descriptions" class="tab-large" role="tab" aria-controls="descriptions-panel"
                        aria-label="Edit product descriptions">
                    <sl-icon name="file-text" class="mr-2" aria-hidden="true"></sl-icon>
                    Descriptions
                </sl-tab>
                <sl-tab slot="nav" panel="pricing-overrides" class="tab-large" role="tab"
                        aria-controls="pricing-overrides-panel" aria-label="Manage pricing and overrides">
                    <sl-icon name="currency-dollar" class="mr-2" aria-hidden="true"></sl-icon>
                    Pricing & Overrides
                </sl-tab>
                <sl-tab slot="nav" panel="images" class="tab-large" role="tab" aria-controls="images-panel"
                        aria-label="Manage product images">
                    <sl-icon name="image" class="mr-2" aria-hidden="true"></sl-icon>
                    Images
                </sl-tab>

                <!-- Product Information Tab Panel -->
                <sl-tab-panel name="product-info" class="tab-panel-modern" role="tabpanel" id="product-info-panel"
                              aria-labelledby="product-info-tab">
                    <div
                        class="bg-gradient-to-br from-gray-50 to-blue-50 p-8 rounded-xl border border-gray-200 shadow-sm">
                        <h3 class="text-3xl font-bold mb-6 text-gray-800 flex items-center" id="product-info-heading">
                            <sl-icon name="info-circle" class="mr-3 text-blue-600" style="font-size: 2rem;"
                                     aria-hidden="true"></sl-icon>
                            Product Information
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div class="form-group">
                                <label for="editTitle" class="block text-xl font-bold text-gray-800 mb-3">
                                    Product Title <span class="text-red-500" aria-label="required">*</span>
                                </label>
                                <input
                                    type="text"
                                    id="editTitle"
                                    name="title"
                                    required
                                    aria-describedby="editTitle-help"
                                    autocomplete="off"
                                    autocapitalize="words"
                                    spellcheck="true"
                                    inputmode="text"
                                    class="w-full px-5 py-4 text-xl border-2 border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium"
                                    placeholder="Enter the product title">
                                <p id="editTitle-help" class="text-lg text-gray-600 mt-2 font-medium">Enter the product
                                    title</p>
                            </div>

                            <div class="form-group">
                                <label for="editSku" class="block text-xl font-bold text-gray-800 mb-3">
                                    SKU <span class="text-red-500">*</span>
                                </label>
                                <input
                                    type="text"
                                    id="editSku"
                                    name="sku"
                                    required
                                    autocomplete="off"
                                    autocapitalize="characters"
                                    spellcheck="false"
                                    inputmode="text"
                                    class="w-full px-5 py-4 text-xl border-2 border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium"
                                    placeholder="Enter the product SKU">
                                <p class="text-lg text-gray-600 mt-2 font-medium">Enter the product SKU</p>
                            </div>

                            <div class="form-group">
                                <label for="editBrand" class="block text-xl font-bold text-gray-800 mb-3">
                                    Brand <span class="text-red-500">*</span>
                                </label>
                                <input
                                    type="text"
                                    id="editBrand"
                                    name="brand"
                                    required
                                    autocomplete="off"
                                    autocapitalize="words"
                                    spellcheck="true"
                                    inputmode="text"
                                    class="w-full px-5 py-4 text-xl border-2 border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium"
                                    placeholder="Enter the product brand">
                                <p class="text-lg text-gray-600 mt-2 font-medium">Enter the product brand</p>
                            </div>

                            <div id="statusOverrideSection" class="form-group hidden">
                                <label for="editStatusOverride" class="block text-xl font-bold text-gray-800 mb-3">
                                    Status Override
                                </label>
                                <select
                                    id="editStatusOverride"
                                    name="statusOverride"
                                    class="w-full px-5 py-4 text-xl border-2 border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-4 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium">
                                    <option value="">No Override</option>
                                    <!-- Options will be populated dynamically from ProductStatuses enum -->
                                </select>
                                <p class="text-lg text-gray-600 mt-2 font-medium">Override the product status</p>
                            </div>
                        </div>
                    </div>
                </sl-tab-panel>

                <!-- Descriptions Tab Panel -->
                <sl-tab-panel name="descriptions" class="tab-panel-modern">
                    <div
                        class="bg-gradient-to-br from-gray-50 to-green-50 p-8 rounded-xl border border-gray-200 shadow-sm">
                        <h3 class="text-3xl font-bold mb-6 text-gray-800 flex items-center">
                            <sl-icon name="file-text" class="mr-3 text-green-600" style="font-size: 2rem;"></sl-icon>
                            Descriptions
                        </h3>
                        <div class="space-y-8">
                            <!-- Short Description -->
                            <div class="form-group">
                                <label for="editShortDescription" class="block text-xl font-bold text-gray-800 mb-3">
                                    Short Description
                                </label>
                                <textarea
                                    id="editShortDescription"
                                    name="shortDescription"
                                    rows="4"
                                    class="w-full px-5 py-4 text-xl border-2 border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-green-500 focus:border-green-500 transition-all duration-200 resize-y font-medium"
                                    placeholder="Enter a brief product description"></textarea>
                                <p class="text-lg text-gray-600 mt-2 font-medium">Enter a brief product description</p>
                            </div>

                            <!-- Long Description - Rich Text Editor -->
                            <div id="longDescriptionSection">
                                <div class="mb-2">
                                    <label class="block text-xl font-bold text-gray-800 mb-3">Long Description</label>
                                    <div id="editLongDescription" name="longDescription"
                                         class="tinymce-editor-enhanced"></div>
                                    <!-- Fallback textarea (hidden by default) -->
                                    <textarea
                                        id="editLongDescriptionFallback"
                                        name="longDescriptionFallback"
                                        rows="10"
                                        class="hidden w-full px-5 py-4 text-xl border-2 border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-green-500 focus:border-green-500 transition-all duration-200 resize-y font-medium"
                                        placeholder="Rich text editor failed to load - using plain text mode"></textarea>
                                    <p class="text-lg text-gray-600 mt-2 font-medium">Enter a detailed product
                                        description with rich formatting</p>
                                </div>
                            </div>

                            <!-- Description - Rich Text Editor -->
                            <div id="descriptionSection" class="hidden">
                                <div class="mb-2">
                                    <label class="block text-xl font-bold text-gray-800 mb-3">Description</label>
                                    <div id="editDescription" name="description" class="tinymce-editor-enhanced"></div>
                                    <!-- Fallback textarea (hidden by default) -->
                                    <textarea
                                        id="editDescriptionFallback"
                                        name="descriptionFallback"
                                        rows="10"
                                        class="hidden w-full px-5 py-4 text-xl border-2 border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-green-500 focus:border-green-500 transition-all duration-200 resize-y font-medium"
                                        placeholder="Rich text editor failed to load - using plain text mode"></textarea>
                                    <p class="text-lg text-gray-600 mt-2 font-medium">Enter the product description with
                                        rich formatting</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </sl-tab-panel>

                <!-- Pricing & Overrides Tab Panel -->
                <sl-tab-panel name="pricing-overrides" class="tab-panel-modern">
                    <div
                        class="bg-gradient-to-br from-orange-50 to-amber-50 border-2 border-orange-200 p-8 rounded-xl shadow-lg">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-3xl font-bold text-orange-800 flex items-center">
                                <sl-icon name="currency-dollar" class="mr-3 text-orange-600"
                                         style="font-size: 2rem;"></sl-icon>
                                Price Overrides
                            </h3>
                            <span
                                class="inline-flex items-center px-4 py-2 rounded-full text-xl font-bold bg-yellow-100 text-yellow-800 border-2 border-yellow-300">
                                <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                          clip-rule="evenodd"></path>
                                </svg>
                                Override Base Prices
                            </span>
                        </div>

                        <!-- Enhanced Sell Price Override -->
                        <div class="bg-white border-2 border-orange-100 p-6 rounded-xl mb-8 shadow-md">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-2xl font-bold text-orange-700 flex items-center">
                                    <svg class="w-8 h-8 mr-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z"
                                              clip-rule="evenodd"></path>
                                    </svg>
                                    Sell Price Override
                                </h4>
                                <div id="sellPriceOverrideStatus" class="hidden">
                                    <span
                                        class="inline-flex items-center px-3 py-1 rounded-full text-lg font-bold bg-green-100 text-green-800 border border-green-300">
                                        Active Override
                                    </span>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="form-group relative">
                                    <label for="editSellPriceOverride"
                                           class="block text-xl font-bold text-gray-800 mb-3">
                                        Override Price
                                    </label>
                                    <div class="relative">
                                        <div
                                            class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <span class="text-gray-500 text-xl font-bold">$</span>
                                        </div>
                                        <input
                                            type="number"
                                            id="editSellPriceOverride"
                                            name="sellPriceOverride"
                                            step="0.01"
                                            min="0"
                                            autocomplete="off"
                                            inputmode="decimal"
                                            pattern="[0-9]*\.?[0-9]*"
                                            class="w-full pl-10 pr-14 py-4 text-xl border-2 border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium"
                                            placeholder="0.00">
                                        <button type="button" id="clearSellPriceOverride"
                                                class="absolute inset-y-0 right-0 pr-4 flex items-center hidden">
                                            <svg class="h-6 w-6 text-gray-400 hover:text-gray-600" fill="currentColor"
                                                 viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                                      clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="text-lg text-gray-600 mt-2 font-medium">Enter override price (leave empty
                                        to remove)</p>
                                </div>

                                <div class="form-group">
                                    <label for="editSellPriceOverrideExpiryDate"
                                           class="block text-xl font-bold text-gray-800 mb-3">
                                        Expiry Date
                                    </label>
                                    <div class="relative">
                                        <input
                                            type="date"
                                            id="editSellPriceOverrideExpiryDate"
                                            name="sellPriceOverrideExpiryDate"
                                            class="w-full px-5 py-4 text-xl border-2 border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-4 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium">
                                    </div>
                                    <p class="text-lg text-gray-600 mt-2 font-medium">When the override expires</p>
                                </div>

                                <div class="form-group">
                                    <label for="editSellPriceOverrideNote"
                                           class="block text-xl font-bold text-gray-800 mb-3">
                                        Note
                                    </label>
                                    <textarea
                                        id="editSellPriceOverrideNote"
                                        name="sellPriceOverrideNote"
                                        rows="3"
                                        class="w-full px-5 py-4 text-xl border-2 border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-y font-medium"
                                        placeholder="Reason for the override"></textarea>
                                    <p class="text-lg text-gray-600 mt-2 font-medium">Reason for the override</p>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Cost Price Override -->
                        <div class="bg-white border-2 border-orange-100 p-6 rounded-xl shadow-md">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-2xl font-bold text-orange-700 flex items-center">
                                    <svg class="w-8 h-8 mr-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"
                                              clip-rule="evenodd"></path>
                                    </svg>
                                    Cost Price Override
                                </h4>
                                <div id="costPriceOverrideStatus" class="hidden">
                                    <span
                                        class="inline-flex items-center px-3 py-1 rounded-full text-lg font-bold bg-green-100 text-green-800 border border-green-300">
                                        Active Override
                                    </span>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="form-group relative">
                                    <label for="editCostPriceOverride"
                                           class="block text-xl font-bold text-gray-800 mb-3">
                                        Override Price
                                    </label>
                                    <div class="relative">
                                        <div
                                            class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <span class="text-gray-500 text-xl font-bold">$</span>
                                        </div>
                                        <input
                                            type="number"
                                            id="editCostPriceOverride"
                                            name="costPriceOverride"
                                            step="0.01"
                                            min="0"
                                            autocomplete="off"
                                            inputmode="decimal"
                                            pattern="[0-9]*\.?[0-9]*"
                                            class="w-full pl-10 pr-14 py-4 text-xl border-2 border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium"
                                            placeholder="0.00">
                                        <button type="button" id="clearCostPriceOverride"
                                                class="absolute inset-y-0 right-0 pr-4 flex items-center hidden">
                                            <svg class="h-6 w-6 text-gray-400 hover:text-gray-600" fill="currentColor"
                                                 viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                                      clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="text-lg text-gray-600 mt-2 font-medium">Enter override price (leave empty
                                        to remove)</p>
                                </div>

                                <div class="form-group">
                                    <label for="editCostPriceOverrideExpiryDate"
                                           class="block text-xl font-bold text-gray-800 mb-3">
                                        Expiry Date
                                    </label>
                                    <div class="relative">
                                        <input
                                            type="date"
                                            id="editCostPriceOverrideExpiryDate"
                                            name="costPriceOverrideExpiryDate"
                                            class="w-full px-5 py-4 text-xl border-2 border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-4 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium">
                                    </div>
                                    <p class="text-lg text-gray-600 mt-2 font-medium">When the override expires</p>
                                </div>

                                <div class="form-group">
                                    <label for="editCostPriceOverrideNote"
                                           class="block text-xl font-bold text-gray-800 mb-3">
                                        Note
                                    </label>
                                    <textarea
                                        id="editCostPriceOverrideNote"
                                        name="costPriceOverrideNote"
                                        rows="3"
                                        class="w-full px-5 py-4 text-xl border-2 border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-y font-medium"
                                        placeholder="Reason for the override"></textarea>
                                    <p class="text-lg text-gray-600 mt-2 font-medium">Reason for the override</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </sl-tab-panel>

                <!-- Images Tab Panel -->
                <sl-tab-panel name="images" class="tab-panel-modern">
                    <div
                        class="bg-gradient-to-br from-gray-50 to-purple-50 p-8 rounded-xl border border-gray-200 shadow-sm">
                        <h3 class="text-3xl font-bold mb-6 text-gray-800 flex items-center">
                            <sl-icon name="image" class="mr-3 text-purple-600" style="font-size: 2rem;"></sl-icon>
                            Product Images
                        </h3>

                        <!-- Existing Images -->
                        <div id="existingImagesSection" class="mb-8">
                            <h4 class="text-2xl font-bold mb-4 text-gray-700">Current Images</h4>
                            <div id="existingImagesList" class="grid grid-cols-2 md:grid-cols-4 gap-6">
                                <!-- Existing images will be populated here -->
                            </div>
                            <div id="noImagesMessage" class="text-xl text-gray-500 text-center py-8 hidden">
                                No images uploaded
                            </div>
                        </div>

                        <!-- Enhanced Image Upload -->
                        <div id="imageUploadSection">
                            <h4 class="text-2xl font-bold mb-4 text-gray-700">Upload New Images</h4>
                            <div
                                class="border-4 border-dashed border-gray-300 rounded-xl p-10 text-center hover:border-purple-400 hover:bg-purple-50 transition-all duration-200"
                                id="imageDropZone">
                                <svg class="w-16 h-16 text-gray-400 mb-4 mx-auto" fill="none" stroke="currentColor"
                                     viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <p class="text-2xl text-gray-600 mb-4 font-semibold">Drag and drop images here, or click
                                    to select</p>
                                <button type="button" id="selectImagesButton"
                                        class="inline-flex items-center px-8 py-4 border-2 border-gray-300 rounded-xl shadow-sm text-xl font-bold text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-4 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200">
                                    <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Select Images
                                </button>
                                <input type="file"
                                       id="imageFileInput"
                                       multiple
                                       accept="image/*"
                                       capture="environment"
                                       class="hidden"/>
                                <p class="text-lg text-gray-500 mt-4 font-medium">Maximum 20 images, 5MB each. Supported
                                    formats: JPG, PNG, GIF</p>
                            </div>

                            <!-- Enhanced Upload Progress -->
                            <div id="uploadProgressSection" class="mt-6 hidden">
                                <div class="bg-gray-200 rounded-full h-4">
                                    <div id="uploadProgress"
                                         class="bg-purple-600 h-4 rounded-full transition-all duration-300"
                                         style="width: 0%"></div>
                                </div>
                                <p class="text-xl text-gray-600 mt-3 font-bold">Uploading images...</p>
                            </div>
                        </div>
                    </div>
                </sl-tab-panel>
            </sl-tab-group>

        </form>
    </div>

    <!-- Enhanced Modal Footer -->
    <div slot="footer" class="flex justify-between items-center space-x-4 p-2" role="group" aria-label="Modal actions">
        <div class="flex space-x-4">
            <button type="button" id="comprehensiveEditSaveButton"
                    aria-label="Save all changes to the product"
                    class="inline-flex items-center px-10 py-5 border-2 border-transparent text-xl font-bold rounded-2xl shadow-lg text-white bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-4 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 transform hover:scale-105 touch-manipulation">
                <svg class="w-7 h-7 mr-3" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clip-rule="evenodd"></path>
                </svg>
                Save Changes
            </button>
            <button type="button" id="comprehensiveEditCancelButton"
                    aria-label="Cancel editing and close modal"
                    class="inline-flex items-center px-10 py-5 border-2 border-gray-300 text-xl font-bold rounded-2xl shadow-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-4 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105 touch-manipulation">
                <svg class="w-7 h-7 mr-3" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clip-rule="evenodd"></path>
                </svg>
                Cancel
            </button>
        </div>
        <button type="button" id="comprehensiveEditResetButton"
                aria-label="Reset all fields to original values"
                class="inline-flex items-center px-6 py-4 border-2 border-orange-300 text-lg font-bold rounded-xl shadow-md text-orange-700 bg-white hover:bg-orange-50 focus:outline-none focus:ring-4 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 transform hover:scale-105 touch-manipulation">
            <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                <path fill-rule="evenodd"
                      d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                      clip-rule="evenodd"></path>
            </svg>
            Reset Fields
        </button>
    </div>
</sl-dialog>

<style>
    /* Enhanced Modal Panel Styling */
    .comprehensive-edit-modal::part(panel) {
        max-height: 95vh;
        overflow-y: auto;
        border-radius: 20px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    .comprehensive-edit-modal::part(header) {
        padding: 2rem;
        border-bottom: 2px solid #e5e7eb;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 20px 20px 0 0;
    }

    .comprehensive-edit-modal::part(title) {
        font-size: 2rem;
        font-weight: 800;
        color: #1f2937;
        letter-spacing: -0.025em;
    }

    .comprehensive-edit-modal::part(body) {
        padding: 2rem;
        background: #fafafa;
    }

    .comprehensive-edit-modal::part(footer) {
        padding: 2rem;
        border-top: 2px solid #e5e7eb;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 0 0 20px 20px;
    }

    /* Enhanced Loader Container */
    .comprehensive-edit-modal .loader-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 300px;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-radius: 16px;
        margin: 2rem 0;
    }

    /* Modern Tab Group Styling */
    .comprehensive-edit-modal .modern-tab-group {
        --sl-color-primary-600: #2563eb;
        --sl-color-primary-50: #eff6ff;
        margin-top: 2rem;
    }

    .comprehensive-edit-modal .modern-tab-group::part(nav) {
        border-bottom: 3px solid #e5e7eb;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 12px 12px 0 0;
        padding: 0.5rem;
    }

    .comprehensive-edit-modal .tab-large {
        font-size: 1.25rem;
        font-weight: 700;
        padding: 1rem 2rem;
        border-radius: 8px;
        transition: all 0.2s ease-in-out;
    }

    .comprehensive-edit-modal .tab-large:hover {
        background: rgba(59, 130, 246, 0.1);
        transform: translateY(-2px);
    }

    .comprehensive-edit-modal .tab-large[active] {
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        color: #1e40af;
        border: 2px solid #3b82f6;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        font-weight: 800;
    }

    .comprehensive-edit-modal .tab-large[active] sl-icon {
        color: #1e40af;
    }

    .comprehensive-edit-modal .tab-panel-modern {
        padding: 2rem 0;
        animation: fadeInUp 0.3s ease-out;
    }

    /* Enhanced Image Thumbnails */
    .comprehensive-edit-modal .image-thumbnail {
        position: relative;
        border-radius: 12px;
        overflow: hidden;
        aspect-ratio: 1;
        background: #f3f4f6;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease-in-out;
    }

    .comprehensive-edit-modal .image-thumbnail:hover {
        transform: scale(1.05);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .comprehensive-edit-modal .image-thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .comprehensive-edit-modal .image-thumbnail .remove-button {
        position: absolute;
        top: 8px;
        right: 8px;
        background: rgba(239, 68, 68, 0.9);
        border-radius: 50%;
        padding: 6px;
        transition: all 0.2s ease-in-out;
    }

    .comprehensive-edit-modal .image-thumbnail:hover .remove-button {
        background: rgba(239, 68, 68, 1);
        transform: scale(1.1);
    }

    /* Enhanced Drop Zone */
    #imageDropZone.drag-over {
        border-color: #8b5cf6;
        background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
        transform: scale(1.02);
    }

    /* Enhanced TinyMCE Editor Styling */
    .comprehensive-edit-modal .tinymce-editor-enhanced {
        border: 2px solid #d1d5db;
        border-radius: 12px;
        min-height: 250px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .comprehensive-edit-modal .tox-tinymce {
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .comprehensive-edit-modal .tox .tox-editor-header {
        border-radius: 12px 12px 0 0;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }

    .comprehensive-edit-modal .tox .tox-edit-area {
        border-radius: 0 0 12px 12px;
    }

    /* Enhanced Field Success Feedback Styling */
    .comprehensive-edit-modal .field-success-feedback {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border: 2px solid #bbf7d0;
        border-radius: 12px;
        padding: 12px 16px;
        margin-top: 12px;
        margin-bottom: 8px;
        font-size: 1.125rem;
        line-height: 1.75rem;
        font-weight: 600;
        animation: slideInFade 0.4s ease-out;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    /* Enhanced Field Validation Feedback Styling */
    .comprehensive-edit-modal .field-validation-error {
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        border: 2px solid #fecaca;
        border-radius: 12px;
        padding: 12px 16px;
        margin-top: 12px;
        margin-bottom: 8px;
        font-size: 1.125rem;
        line-height: 1.75rem;
        font-weight: 600;
        animation: slideInFade 0.4s ease-out;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .comprehensive-edit-modal .field-validation-success {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border: 2px solid #bbf7d0;
        border-radius: 12px;
        padding: 10px 14px;
        margin-top: 8px;
        margin-bottom: 6px;
        font-size: 1rem;
        line-height: 1.5rem;
        font-weight: 600;
        animation: slideInFade 0.4s ease-out;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .comprehensive-edit-modal .field-validation-error sl-icon,
    .comprehensive-edit-modal .field-validation-success sl-icon {
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    .comprehensive-edit-modal .field-validation-error sl-icon {
        color: #dc2626;
    }

    .comprehensive-edit-modal .field-validation-success sl-icon {
        color: #16a34a;
    }

    /* Enhanced Field Change Indicators */
    .comprehensive-edit-modal .field-change-indicator {
        animation: pulse 2s infinite;
    }

    .comprehensive-edit-modal .field-change-indicator sl-badge {
        --sl-color-primary-600: #2563eb;
        --sl-color-primary-50: #eff6ff;
    }

    @@keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }

    .comprehensive-edit-modal .field-success-feedback sl-icon {
        color: #16a34a;
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    /* Enhanced Grid Layout Spacing */
    .comprehensive-edit-modal .grid > div .field-success-feedback {
        margin-left: 0;
        margin-right: 0;
    }

    /* Enhanced TinyMCE Editor Feedback */
    .comprehensive-edit-modal #longDescriptionSection .field-success-feedback,
    .comprehensive-edit-modal #descriptionSection .field-success-feedback {
        margin-top: 12px;
        margin-bottom: 0;
    }

    /* Enhanced Animations */
    @@keyframes slideInFade {
        from {
            opacity: 0;
            transform: translateY(-15px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Enhanced Visual Polish */
    .comprehensive-edit-modal .bg-gradient-to-r,
    .comprehensive-edit-modal .bg-gradient-to-br {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .comprehensive-edit-modal .bg-white {
        transition: all 0.3s ease-in-out;
    }

    .comprehensive-edit-modal .bg-white:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    /* Enhanced Clear Button Styling */
    .comprehensive-edit-modal .relative button {
        transition: all 0.3s ease-in-out;
        opacity: 0.7;
    }

    .comprehensive-edit-modal .relative button:hover {
        opacity: 1;
        transform: scale(1.1);
        background: rgba(239, 68, 68, 0.1);
        border-radius: 50%;
    }

    /* Enhanced Typography */
    .comprehensive-edit-modal h2,
    .comprehensive-edit-modal h3,
    .comprehensive-edit-modal h4 {
        letter-spacing: -0.025em;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    /* Enhanced Form Field Stability */
    .comprehensive-edit-modal input,
    .comprehensive-edit-modal textarea,
    .comprehensive-edit-modal select {
        position: relative;
        will-change: box-shadow, border-color;
        transition: all 0.2s ease-in-out;
    }

    /* Enhanced Form Field Focus States */
    .comprehensive-edit-modal input:focus,
    .comprehensive-edit-modal textarea:focus,
    .comprehensive-edit-modal select:focus {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15);
        transform: translateY(-1px);
    }

    /* Enhanced Form Group Styling */
    .comprehensive-edit-modal .form-group {
        position: relative;
        contain: layout style;
        transition: all 0.2s ease-in-out;
    }

    /* Enhanced Preview Section Styling */
    .comprehensive-edit-modal #pricePreviewSection {
        border-left: 6px solid #f59e0b;
        animation: slideInFade 0.4s ease-out;
        position: relative;
        overflow: hidden;
    }

    .comprehensive-edit-modal #pricePreviewSection::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.03) 50%, transparent 60%);
        animation: shimmer 8s infinite;
        pointer-events: none;
    }

    /* Enhanced Current Values Cards */
    .comprehensive-edit-modal .bg-white.p-6 {
        transition: all 0.3s ease-in-out;
        border-left: 4px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .comprehensive-edit-modal .bg-white.p-6:hover {
        border-left-color: #3b82f6;
        transform: translateX(4px) translateY(-2px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    }

    .comprehensive-edit-modal .bg-white.p-6::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.02), transparent);
        transition: left 1.2s ease-in-out;
        pointer-events: none;
    }

    .comprehensive-edit-modal .bg-white.p-6:hover::before {
        left: 100%;
    }

    /* Enhanced Button States */
    .comprehensive-edit-modal button:disabled {
        cursor: not-allowed;
        opacity: 0.5;
        transform: none !important;
    }

    /* Enhanced Grid Layout Stability */
    .comprehensive-edit-modal .grid > div {
        position: relative;
        min-height: fit-content;
        transition: all 0.2s ease-in-out;
    }

    /* Enhanced Layout Stability */
    .comprehensive-edit-modal .grid > div,
    .comprehensive-edit-modal .space-y-4 > div {
        contain: layout style;
    }

    /* Override Global Constraints */
    .comprehensive-edit-modal input,
    .comprehensive-edit-modal textarea,
    .comprehensive-edit-modal select {
        max-width: none !important;
    }

    /* Enhanced Typography and Readability */
    .comprehensive-edit-modal label {
        font-weight: 700;
        letter-spacing: -0.015em;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .comprehensive-edit-modal input,
    .comprehensive-edit-modal textarea,
    .comprehensive-edit-modal select {
        font-weight: 600;
        letter-spacing: -0.01em;
    }

    .comprehensive-edit-modal .text-gray-500,
    .comprehensive-edit-modal .text-gray-600 {
        font-weight: 600;
    }

    /* Enhanced Button Typography */
    .comprehensive-edit-modal button {
        font-weight: 700;
        letter-spacing: -0.015em;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    /* Enhanced Loading Indicator */
    .comprehensive-edit-modal .loader-container p {
        font-weight: 700;
        letter-spacing: -0.015em;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    /* CRITICAL: Force large margin values with high specificity */
    .comprehensive-edit-modal #currentMargin {
        font-size: 3rem !important; /* 48px - large */
        font-weight: 900 !important;
        line-height: 1.1 !important;
    }

    .comprehensive-edit-modal #marginPercentage {
        font-size: 2rem !important; /* 32px - large */
        font-weight: 700 !important;
        line-height: 1.2 !important;
    }

    /* Subtle Shimmer Animation */
    @@keyframes shimmer {
        0% {
            transform: translateX(-100%);
            opacity: 0;
        }
        50% {
            opacity: 1;
        }
        100% {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    @@media (min-width: 1200px) {
        .comprehensive-edit-modal {
            --width: 1200px;
            --height: 85vh;
        }
    }

    @@media (max-width: 1199px) {
        .comprehensive-edit-modal {
            --width: 95vw;
            --height: 90vh;
        }

        .comprehensive-edit-modal .grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .comprehensive-edit-modal .tab-large {
            font-size: 1.125rem;
            padding: 0.75rem 1.5rem;
        }
    }

    /* Tablet and Mobile */
    @@media (max-width: 768px) {
        .comprehensive-edit-modal {
            --width: 100vw;
            --height: 100vh;
            border-radius: 0;
        }

        .comprehensive-edit-modal::part(panel) {
            border-radius: 0;
            max-height: 100vh;
            height: 100vh;
        }

        .comprehensive-edit-modal::part(header) {
            padding: 1.5rem;
            border-radius: 0;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .comprehensive-edit-modal::part(body) {
            padding: 1.5rem;
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        .comprehensive-edit-modal::part(footer) {
            padding: 1.5rem;
            border-radius: 0;
            position: sticky;
            bottom: 0;
            z-index: 10;
        }

        .comprehensive-edit-modal .grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .comprehensive-edit-modal .flex.justify-between {
            flex-direction: column;
            gap: 1.5rem;
        }

        /* Enhanced Touch Targets - Minimum 44px */
        .comprehensive-edit-modal button {
            min-height: 44px;
            font-size: 1.125rem;
            padding: 1rem 1.5rem;
            touch-action: manipulation;
        }

        .comprehensive-edit-modal input,
        .comprehensive-edit-modal textarea,
        .comprehensive-edit-modal select {
            min-height: 44px;
            font-size: 1.125rem;
            padding: 1rem 1.25rem;
            touch-action: manipulation;
        }

        /* Prevent zoom on input focus */
        .comprehensive-edit-modal input,
        .comprehensive-edit-modal textarea,
        .comprehensive-edit-modal select {
            font-size: 16px; /* Prevents zoom on iOS */
        }

        /* Enhanced Tab Navigation for Touch */
        .comprehensive-edit-modal .tab-large {
            min-height: 44px;
            font-size: 1rem;
            padding: 1rem;
            touch-action: manipulation;
        }

        /* Maintain large font sizes on mobile for accessibility */
        .comprehensive-edit-modal h2 {
            font-size: 2rem;
        }

        .comprehensive-edit-modal h3 {
            font-size: 1.75rem;
        }

        .comprehensive-edit-modal h4 {
            font-size: 1.5rem;
        }

        .comprehensive-edit-modal label {
            font-size: 1.25rem;
            margin-bottom: 0.75rem;
        }

        /* Maintain large margin values on mobile - even larger for visibility */
        .comprehensive-edit-modal #currentMargin {
            font-size: 3.5rem !important; /* 56px - still very large on mobile */
            font-weight: 900 !important;
        }

        .comprehensive-edit-modal #marginPercentage {
            font-size: 1.75rem !important; /* 28px - still large on mobile */
            font-weight: 700 !important;
        }

        /* Mobile-specific enhancements */
        .comprehensive-edit-modal .bg-white.p-6 {
            padding: 1.5rem;
        }

        .comprehensive-edit-modal .tab-panel-modern {
            padding: 1rem 0;
        }

        /* Enhanced Image Upload for Mobile */
        .comprehensive-edit-modal #imageDropZone {
            padding: 2rem;
            min-height: 120px;
        }

        .comprehensive-edit-modal #selectImagesButton {
            min-height: 44px;
            padding: 1rem 2rem;
        }

        /* Better Mobile Grid Layout */
        .comprehensive-edit-modal .grid.grid-cols-2,
        .comprehensive-edit-modal .grid.grid-cols-3,
        .comprehensive-edit-modal .grid.grid-cols-4 {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        /* Enhanced Mobile Form Groups */
        .comprehensive-edit-modal .form-group {
            margin-bottom: 2rem;
        }

        /* Mobile-Friendly Clear Buttons */
        .comprehensive-edit-modal .relative button {
            min-width: 44px;
            min-height: 44px;
            padding: 0.75rem;
        }
    }

    /* Small Mobile Devices */
    @@media (max-width: 480px) {
        .comprehensive-edit-modal::part(header),
        .comprehensive-edit-modal::part(body),
        .comprehensive-edit-modal::part(footer) {
            padding: 1rem;
        }

        .comprehensive-edit-modal .bg-gradient-to-br {
            padding: 1rem;
        }

        /* Ensure margin values remain very large on small screens */
        .comprehensive-edit-modal #currentMargin {
            font-size: 3rem !important; /* 48px - still prominent on small screens */
            font-weight: 900 !important;
        }

        .comprehensive-edit-modal #marginPercentage {
            font-size: 1.5rem !important; /* 24px - still readable on small screens */
            font-weight: 700 !important;
        }

        /* Stack buttons vertically on very small screens */
        .comprehensive-edit-modal .flex.space-x-4 {
            flex-direction: column;
            gap: 1rem;
        }

        .comprehensive-edit-modal button {
            width: 100%;
            justify-content: center;
        }

        /* Smaller padding for very small screens */
        .comprehensive-edit-modal .grid {
            gap: 1rem;
        }

        .comprehensive-edit-modal .form-group {
            margin-bottom: 1.5rem;
        }
    }

    /* High contrast mode support */
    @@media (prefers-contrast: high) {
        .comprehensive-edit-modal {
            --sl-color-primary-600: #000000;
            --sl-color-primary-50: #ffffff;
        }

        .comprehensive-edit-modal .bg-gradient-to-r,
        .comprehensive-edit-modal .bg-gradient-to-br {
            background: #ffffff;
            border: 2px solid #000000;
        }

        .comprehensive-edit-modal input,
        .comprehensive-edit-modal textarea,
        .comprehensive-edit-modal select {
            border: 2px solid #000000;
        }
    }

    /* Mobile Landscape Orientation Support */
    @@media (max-width: 768px) and (orientation: landscape) {
        .comprehensive-edit-modal {
            --height: 100vh;
        }

        .comprehensive-edit-modal::part(header) {
            padding: 1rem 1.5rem;
        }

        .comprehensive-edit-modal::part(body) {
            padding: 1rem 1.5rem;
        }

        .comprehensive-edit-modal::part(footer) {
            padding: 1rem 1.5rem;
        }

        /* Adjust grid for landscape */
        .comprehensive-edit-modal .grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        /* Smaller font sizes in landscape to fit more content */
        .comprehensive-edit-modal h2 {
            font-size: 1.75rem;
        }

        .comprehensive-edit-modal h3 {
            font-size: 1.5rem;
        }

        .comprehensive-edit-modal #currentMargin {
            font-size: 2.5rem !important;
        }

        .comprehensive-edit-modal #marginPercentage {
            font-size: 1.5rem !important;
        }
    }

    /* Safe Area Insets for Modern Mobile Devices */
    @@media (max-width: 768px) {
        .comprehensive-edit-modal::part(panel) {
            padding-top: env(safe-area-inset-top);
            padding-bottom: env(safe-area-inset-bottom);
            padding-left: env(safe-area-inset-left);
            padding-right: env(safe-area-inset-right);
        }

        .comprehensive-edit-modal::part(header) {
            padding-top: calc(1.5rem + env(safe-area-inset-top));
        }

        .comprehensive-edit-modal::part(footer) {
            padding-bottom: calc(1.5rem + env(safe-area-inset-bottom));
        }
    }

    /* Touch-Friendly Enhancements */
    @@media (pointer: coarse) {
        /* For devices with coarse pointers (touch) */
        .comprehensive-edit-modal button,
        .comprehensive-edit-modal input,
        .comprehensive-edit-modal textarea,
        .comprehensive-edit-modal select {
            min-height: 44px;
        }

        .comprehensive-edit-modal .tab-large {
            min-height: 48px;
            padding: 1rem 1.5rem;
        }

        /* Enhanced touch feedback */
        .comprehensive-edit-modal button:active {
            transform: scale(0.98);
            transition: transform 0.1s ease;
        }

        .comprehensive-edit-modal .tab-large:active {
            transform: scale(0.98);
            transition: transform 0.1s ease;
        }

        /* Better touch scrolling */
        .comprehensive-edit-modal::part(body) {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
        }
    }

    /* High DPI Display Support */
    @@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        .comprehensive-edit-modal {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
    }

    /* Keyboard Appearance Handling for Mobile */
    @@media (max-width: 768px) {
        .comprehensive-edit-modal input:focus,
        .comprehensive-edit-modal textarea:focus,
        .comprehensive-edit-modal select:focus {
            /* Prevent viewport zoom on focus */
            font-size: 16px;
            /* Ensure the focused element is visible */
            scroll-margin-top: 100px;
            scroll-margin-bottom: 100px;
        }

        /* Adjust modal when virtual keyboard appears */
        .comprehensive-edit-modal.keyboard-visible {
            --height: 50vh;
        }

        .comprehensive-edit-modal.keyboard-visible::part(body) {
            max-height: 40vh;
            overflow-y: auto;
        }
    }

    /* Enhanced Accessibility for Mobile */
    @@media (max-width: 768px) {
        .comprehensive-edit-modal [role="tab"] {
            min-height: 48px;
            padding: 1rem;
        }

        .comprehensive-edit-modal [role="tabpanel"] {
            padding: 1rem 0;
        }

        /* Better focus indicators for mobile */
        .comprehensive-edit-modal *:focus {
            outline: 3px solid #3b82f6;
            outline-offset: 2px;
        }

        /* Enhanced button focus for accessibility */
        .comprehensive-edit-modal button:focus {
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
        }
    }

    /* Mobile-specific JavaScript enhancements */
    @@media (max-width: 768px) {
        /* Touch-active state for better feedback */
        .comprehensive-edit-modal .touch-active {
            background-color: rgba(59, 130, 246, 0.1);
            transform: scale(0.98);
            transition: all 0.1s ease;
        }

        /* Mobile-focused input styling */
        .comprehensive-edit-modal .mobile-focused {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
            transform: scale(1.02);
        }

        /* Tab swipe indicator */
        .comprehensive-edit-modal sl-tab-group::after {
            content: "← Swipe to navigate tabs →";
            display: block;
            text-align: center;
            font-size: 0.875rem;
            color: #6b7280;
            padding: 0.5rem;
            font-style: italic;
        }

        /* Hide swipe indicator on larger screens */
        @@media (min-width: 769px) {
            .comprehensive-edit-modal sl-tab-group::after {
                display: none;
            }
        }

        /* Enhanced image drop zone for mobile */
        .comprehensive-edit-modal #imageDropZone.touch-active {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-color: #3b82f6;
        }

        /* Mobile keyboard visible adjustments */
        .comprehensive-edit-modal.keyboard-visible::part(panel) {
            height: var(--mobile-vh, 50vh);
            max-height: var(--mobile-vh, 50vh);
        }

        .comprehensive-edit-modal.keyboard-visible::part(body) {
            max-height: calc(var(--mobile-vh, 50vh) - 200px);
            overflow-y: auto;
        }
    }

    /* Improved touch targets for all interactive elements */
    @@media (pointer: coarse) {
        .comprehensive-edit-modal sl-icon {
            min-width: 24px;
            min-height: 24px;
        }

        .comprehensive-edit-modal .remove-button {
            min-width: 44px;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .comprehensive-edit-modal .clear-button {
            min-width: 44px;
            min-height: 44px;
        }
    }

    /* Enhanced visual feedback for touch interactions */
    @@media (max-width: 768px) {
        .comprehensive-edit-modal button:active,
        .comprehensive-edit-modal sl-button:active {
            transform: scale(0.95);
            transition: transform 0.1s ease;
        }

        .comprehensive-edit-modal input:active,
        .comprehensive-edit-modal textarea:active,
        .comprehensive-edit-modal select:active {
            transform: scale(1.01);
            transition: transform 0.1s ease;
        }

        /* Better visual hierarchy on mobile */
        .comprehensive-edit-modal .form-group label {
            display: block;
            margin-bottom: 0.75rem;
            font-weight: 700;
        }

        .comprehensive-edit-modal .form-group p {
            margin-top: 0.5rem;
            font-size: 0.875rem;
            line-height: 1.4;
        }
    }

    /* Reduced motion support */
    @@media (prefers-reduced-motion: reduce) {
        .comprehensive-edit-modal *,
        .comprehensive-edit-modal *::before,
        .comprehensive-edit-modal *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
</style>

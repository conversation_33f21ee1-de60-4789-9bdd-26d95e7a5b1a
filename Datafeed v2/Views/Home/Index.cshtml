@{
    ViewBag.Title = "Home Page";
}

<h1 class="text-2xl font-semibold mb-4">@ViewBag.Title</h1>

<div id="addNewProductModal"
     class="modal modal-blur fade"
     style="display: none"
     aria-hidden="false"
     tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content"></div>
    </div>
</div>

@Html.Partial("_ErrorAlert")
@Html.Partial("_SuccessAlert")

<div id="mainPageContent"
     hx-get="@Url.Action("GetParentNopCategories", "Categories")"
     hx-target="this"
     hx-swap="innerHTML"
     hx-trigger="load"
     hx-indicator="#spinner">

</div>
@model Datafeed_v2.Models.ProductsAdmin.GetProductSourceWithNoMappingsModel

<button type="button"
        class="mt-10 mb-20 w-full flex items-center justify-center px-5 py-2 text-md text-gray-700 transition-colors duration-200 bg-white border rounded-lg gap-x-2 sm:w-auto"
        hx-get="@Url.Action("GetProductSourcesWithNoMappings", "ProductsAdmin")"
        hx-target="#mainPageContent"
        hx-swap="innerHTML"
        hx-trigger="click"
        hx-indicator="#spinner">
    <svg class="w-7 h-7 rtl:rotate-180" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 15.75L3 12m0 0l3.75-3.75M3 12h18"/>
    </svg>
    <span>Go back</span>
</button>

<div class="mt-10 gap-6 w-3/4 mx-auto"
     hx-boost="true"
     hx-target="#mainPageContent"
     hx-swap="innerHTML"
     hx-trigger="change"
     hx-indicator="#spinner">
    <div class="bg-slate-50">
        <div class="bg-gray-700 rounded-xl shadow">
            <img class="text-gray-200 pt-5 pb-5 mx-auto max-h-80"
                 src="data:image/png;base64,@Convert.ToBase64String(Model.ProductSourceImages.FirstOrDefault()?.ImageBytes ?? Array.Empty<byte>())"
                 alt="No product image available"/>
        </div>

        <div class="p-5">
            <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-500 text-center">
                (@Model.ProductSource.ProductSku) - @Model.ProductSource.ProductTitle
            </h5>
        </div>

        <div class="flex flex-row mt-10 gap-6"
             _="on load call initNopCategoryMappings(@Model.ProductSource.ID)">
            <div class="basis-1/2 flex-auto bg-slate-50 cursor-pointer">
                <div class="flex-col">
                    <h1 class="text-3xl font-bold text-center">Mapped Nop Categories</h1>
                </div>

                <ul class="divide-gray-200 draggable" id="mappedNopCategories">
                    @foreach (var category in Model.MappedNopCategories)
                    {
                        <li id="@category.ID" class="pb-3 sm:pb-4 cursor-pointer draggable">
                            <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                <div class="flex-1 min-w-0">
                                    <p class="text-2xl text-gray-900 truncate select-none">@category.Name</p>
                                </div>
                            </div>
                        </li>
                    }
                </ul>
            </div>

            <div class="basis-1/2 flex-auto bg-slate-50">
                <div class="flex-col">
                    <h1 class="text-3xl font-bold text-center">Unmapped Nop Categories</h1>
                </div>

                <ul class="divide-gray-200" id="unmappedNopCategories">
                    @foreach (var category in Model.UnmappedNopCategories)
                    {
                        <li id="@category.ID" class="pb-3 sm:pb-4 cursor-pointer draggable">
                            <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                <div class="flex-1 min-w-0">
                                    <p class="text-2xl text-gray-900 truncate select-none">@category.Name</p>
                                </div>
                            </div>
                        </li>
                    }
                </ul>
            </div>
        </div>
    </div>
</div>
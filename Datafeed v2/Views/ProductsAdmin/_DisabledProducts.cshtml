@{
    ViewBag.Title = "Disabled Products";
}

<style>
    /* Table loading overlay styles */
    .table-loading-overlay {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.9);
        z-index: 100;
        border-radius: 0.5rem;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 1rem;
    }

    .table-loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }

    .table-loading-text {
        font-size: 1.125rem;
        font-weight: 500;
        color: #374151;
    }

    /* Ensure table container has relative positioning for overlay */
    .table-container {
        position: relative;
    }
</style>

<div class="mb-6 flex items-center justify-between bg-white p-4 rounded-lg shadow-sm">
    <div class="flex items-center gap-4">
        <sl-select id="reasonFilter" class="w-[250px]" size="medium" placeholder="Filter by reason" clearable>
            <sl-option value="">All reasons</sl-option>
            <sl-option value="Missing-description">Missing description</sl-option>
            <sl-option value="Missing-images">Missing images</sl-option>
            <sl-option value="Missing-pricing">Missing competitor price</sl-option>
        </sl-select>
    </div>
</div>

<div class="overflow-x-auto rounded-lg shadow bg-white table-container">
    <!-- Loading overlay for table -->
    <div id="tableLoadingOverlay" class="table-loading-overlay">
        <div class="table-loading-content">
            <sl-spinner style="font-size: 2rem; --track-width: 4px;"></sl-spinner>
            <div class="table-loading-text">Loading...</div>
        </div>
    </div>

    <div id="tableAlertContainer"></div>
    <table id="disabledProductsTable" class="w-full table-auto">
        <thead class="bg-gray-50">
        <tr>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">SKU</th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Title</th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Short
                Description
            </th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Long
                Description
            </th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Source</th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Reason</th>
            <th class="px-4 py-3 text-right text-sm font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
        </thead>

        <tbody>
        @* DataTables will populate this dynamically *@
        </tbody>

    </table>
</div>

<div class="mt-6 flex justify-end gap-4 bg-white p-4 rounded-lg shadow-sm">
    <sl-tooltip content="Export disabled products to a CSV file">
        <sl-button hx-get="@Url.Action("GenerateDisabledProductSourcesCsv", "ProductsAdmin")"
                   hx-vals="js:{ reason: document.getElementById('reasonFilter').value }"
                   hx-on::after-request="htmx.find('.htmx-indicator').classList.toggle('htmx-request');"
                   size="medium"
                   class="bg-white border border-gray-300 text-gray-700 hover:bg-gray-50">
            <sl-icon slot="prefix" name="download"></sl-icon>
            Export
        </sl-button>
    </sl-tooltip>
    <sl-tooltip content="Import products from a CSV file. Allows mass updating product descriptions.">
        <sl-button size="medium"
                   id="csvUploadButton"
                   class="bg-white border border-gray-300 text-gray-700 hover:bg-gray-50">
            <sl-icon slot="prefix" name="upload"></sl-icon>
            Import
        </sl-button>
    </sl-tooltip>
    <input type="file" id="csvFileInput" name="csvFile" class="hidden" accept=".csv"
           hx-post="@Url.Action("ImportCsv", "ProductsAdmin")"
           hx-encoding="multipart/form-data"
           hx-target="body"
           hx-swap="innerHTML"
           hx-trigger="change">
</div>

@* Add the modal dialog structure here *@
<sl-dialog id="applyFixDialog" label="Apply Fix">
    <div id="applyFixAlertContainer"></div>
    <div id="applyFixDialogContent"></div>
    <sl-button slot="footer" variant="primary" id="applyFixButton">Apply</sl-button>
    <sl-button slot="footer" variant="default" id="closeFixDialogButton">Close</sl-button>
</sl-dialog>

<script>
    // Initialise close button for the dialog
    let closeButton1 = getCloseFixDialogButton();
    if (closeButton1) {
        closeButton1.addEventListener('click', closeDialog);
    }

    // Initialise DataTable with server-side processing
    initDatatable('disabledProductsTable', {
        columnDefs: [
            {width: '200px', targets: [5]}, // UPDATE index for Reason column width (was 4)
            {orderable: false, searchable: false, targets: [6]} // UPDATE index for Actions column (was 5)
        ],
        stateSave: true,
        serverSide: true, // Enable server-side processing
        processing: true, // Show processing indicator during all operations
        ajax: {
            url: '@(Url.Action("GetDisabledProductsData", "ProductsAdmin"))', // URL for data endpoint
            type: 'POST', // Use POST method
            error: function (xhr, error, thrown) { // Basic error handling
                console.error("DataTables Error:", error, thrown);
                alert('Error loading disabled products data. Please try again.');
            }
        },
        columns: [ // Define columns and their data sources
            {data: 'Sku'},
            {data: 'Title'},
            {data: 'ShortDescription', render: DataTable.render.text()}, // ADD this column definition
            {
                data: 'LongDescription', render: DataTable.render.text() // UPDATE data property (was Description)
            },
            {data: 'Source', render: DataTable.render.text()}, // ADD Source column
            {
                data: 'ReasonHtml', orderable: true, searchable: true, render: function (data) {
                    return data;
                }
            }, // Render HTML directly
            {
                data: 'ActionsHtml', orderable: false, searchable: false, render: function (data) {
                    return data;
                }
            } // Render HTML directly
        ],
        language: {
            processing: '' // Disable default processing display - we use custom overlay
        },
        // Add this createdRow callback
        createdRow: function (row, data, dataIndex) {
            $(row).attr('data-product-id', data.Id);
        }
    });

    // Custom loading overlay control
    const tableLoadingOverlay = document.getElementById('tableLoadingOverlay');

    // Show overlay when DataTable starts processing
    $('#disabledProductsTable').on('processing.dt', function (e, settings, processing) {
        if (processing) {
            tableLoadingOverlay.style.display = 'flex';
        } else {
            tableLoadingOverlay.style.display = 'none';
        }
    });

    function getDialog() {
        return document.getElementById('applyFixDialog');
    }

    function getCloseFixDialogButton() {
        return document.getElementById('closeFixDialogButton');
    }

    function displayModalAlert(message, variant = 'danger', modalId = 'applyFixDialog') {
        if (!message) {
            return;
        }
        const container = document.getElementById(modalId).querySelector('#applyFixAlertContainer');
        if (!container) return;

        let alert = document.createElement('sl-alert');
        alert.setAttribute('variant', variant);
        alert.setAttribute('open', '');
        alert.setAttribute('closable', '');
        alert.innerHTML = `<sl-icon slot='icon' name='${variant === 'success' ? 'check-circle' : 'exclamation-triangle'}'></sl-icon>${message}`;
        container.innerHTML = ''; // Clear previous alerts
        container.prepend(alert);
    }

    // Debounce function
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function initFixesMenu() {
        const kqmMenus = document.querySelectorAll('.kqmFixesMenu');
        for (let i = 0; i < kqmMenus.length; i++) {
            const menu = kqmMenus[i];
            menu.addEventListener('sl-select', event => {
                const item = event.detail.item;
                const productId = item.closest('tr').getAttribute('data-product-id');

                fixProductSource(item.value, productId);
            });
        }

        const priceBookMenus = document.querySelectorAll('.priceBookFixesMenu');
        for (let i = 0; i < priceBookMenus.length; i++) {
            const menu = priceBookMenus[i];
            menu.addEventListener('sl-select', event => {
                const item = event.detail.item;
                const productId = item.closest('tr').getAttribute('data-product-id');

                fixPriceBook(item.value, productId);
            });
        }
    }

    function closeDialog() {
        const applyFixButton = document.getElementById('applyFixButton');
        applyFixButton.onclick = null;
        getDialog().hide();
    }

    function getApplyFixDialogContent() {
        return document.getElementById('applyFixDialogContent');
    }

    // Initialise menus after DataTables draws the table AND set initial filter state
    $('#disabledProductsTable').on('init.dt draw.dt', function (e) {
        if (e.type === 'init') {
            // Run this only once on initialisation after state is loaded
            setReasonFilterFromState();
        }
        // Run this on init and every subsequent draw
        initFixesMenu();
    });

    function reset() {
        tinymce.remove();

        let applyFixDialogContent = getApplyFixDialogContent();
        while (applyFixDialogContent.firstChild) {
            applyFixDialogContent.removeChild(applyFixDialogContent.lastChild);
        }

        document.body.removeEventListener('successfulUpdate', closeDialog);
    }

    function fixPriceBook(type, productId) {
        const applyFixButton = document.getElementById('applyFixButton');
        getDialog().setAttribute('data-fix-type', type);
        getDialog().setAttribute('data-product-id', productId);

        reset();

        if (type === 'description') {
            getDialog().setAttribute('label', 'Add Description');

            const shortDescription = document.createElement('sl-textarea');
            shortDescription.setAttribute('name', 'shortDescription');
            shortDescription.setAttribute('label', 'Short Description');
            shortDescription.setAttribute('help-text', 'Enter the short description for this product');
            shortDescription.setAttribute('resize', 'auto');
            shortDescription.setAttribute('rows', '2');
            shortDescription.setAttribute('autofocus', '');

            const descriptionContainer = document.createElement('div');
            descriptionContainer.setAttribute('class', 'mb-4');

            const descriptionLabel = document.createElement('label');
            descriptionLabel.setAttribute('class', 'block text-(size:--sl-input-label-font-size-medium) font-medium text-gray-700 mb-1 mt-2');
            descriptionLabel.textContent = 'Description';

            const descriptionHelpText = document.createElement('p');
            descriptionHelpText.setAttribute('class', 'text-(size:--sl-input-help-text-font-size-medium) text-gray-500 mt-1');
            descriptionHelpText.textContent = 'Enter the description for this product';

            const description = document.createElement('div');
            description.setAttribute('id', 'description-editor');

            descriptionContainer.appendChild(descriptionLabel);
            descriptionContainer.appendChild(description);
            descriptionContainer.appendChild(descriptionHelpText);

            // Create a table to hold the features and specifications
            const featuresTable = document.createElement('table');
            featuresTable.setAttribute('id', 'featuresTable');
            featuresTable.setAttribute('class', 'min-w-full divide-y divide-gray-200 mt-5');
            const featuresTableHeader = document.createElement('thead');
            featuresTableHeader.setAttribute('class', 'bg-gray-50');
            const featuresTableHeaderRow = document.createElement('tr');
            featuresTableHeaderRow.innerHTML = `
                <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase w-2/5">Features</th>
                <th class="px-4 py-3 text-center text-sm font-medium text-gray-500 uppercase w-1/5">Edit</th>
                <th class="px-4 py-3 text-center text-sm font-medium text-gray-500 uppercase w-1/5">Disable</th>`;
            featuresTableHeader.appendChild(featuresTableHeaderRow);
            featuresTable.appendChild(featuresTableHeader);

            const specsTable = document.createElement('table');
            specsTable.setAttribute('id', 'specsTable');
            specsTable.setAttribute('class', 'min-w-full divide-y divide-gray-200 mt-5 mb-5');
            const specsTableHeader = document.createElement('thead');
            specsTableHeader.setAttribute('class', 'bg-gray-50');
            const specsTableHeaderRow = document.createElement('tr');
            specsTableHeaderRow.innerHTML = `
                <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase w-2/5">Specs</th>
                <th class="px-4 py-3 text-center text-sm font-medium text-gray-500 uppercase w-1/5">Edit</th>
                <th class="px-4 py-3 text-center text-sm font-medium text-gray-500 uppercase w-1/5">Disable</th>`;
            specsTableHeader.appendChild(specsTableHeaderRow);
            specsTable.appendChild(specsTableHeader);

            // Inside the fix function where the first button is created
            const generateDescriptionUrl = `@Url.Action("GenerateDescriptionForPriceBookProduct", "ProductsAdmin")`;
            const generateDescriptionButton = document.createElement('sl-button');
            generateDescriptionButton.setAttribute('name', 'generateDescription');
            generateDescriptionButton.setAttribute('size', 'large');
            generateDescriptionButton.setAttribute('hx-post', generateDescriptionUrl);
            generateDescriptionButton.setAttribute('hx-indicator', '#modal-loader');
            generateDescriptionButton.setAttribute('hx-vals', `js:{ "productId": ${productId}, "model": "Gemini" }`); // Pass the product ID and model
            generateDescriptionButton.setAttribute('hx-swap', `none`);
            generateDescriptionButton.textContent = 'Gemini';
            generateDescriptionButton.addEventListener('aiDescription', function (evt) {
                shortDescription.value = evt.detail.shortDescription;
                tinymce.get('description-editor').setContent(evt.detail.description);
                evt.detail.features.forEach(feature => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = feature;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.setAttribute('class', 'text-center');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.setAttribute('class', 'text-center');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    featuresTable.appendChild(tr);
                });
                evt.detail.specs.forEach(spec => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = spec;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.setAttribute('class', 'text-center');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.setAttribute('class', 'text-center');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    specsTable.appendChild(tr);
                });
            });

            // Create the second generate description button
            const generateDescriptionButton2 = document.createElement('sl-button');
            generateDescriptionButton2.setAttribute('name', 'generateDescription2');
            generateDescriptionButton2.setAttribute('size', 'large');
            generateDescriptionButton2.setAttribute('hx-post', generateDescriptionUrl);
            generateDescriptionButton2.setAttribute('hx-indicator', '#modal-loader');
            generateDescriptionButton2.setAttribute('hx-vals', `js:{ "productId": ${productId}, "model": "Perplexity" }`); // Pass the product ID and model
            generateDescriptionButton2.setAttribute('hx-swap', `none`);
            generateDescriptionButton2.textContent = 'Perplexity';
            generateDescriptionButton2.addEventListener('aiDescription', function (evt) {
                shortDescription.value = evt.detail.shortDescription;
                tinymce.get('description-editor').setContent(evt.detail.description);
                evt.detail.features.forEach(feature => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = feature;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    featuresTable.appendChild(tr);
                });
                evt.detail.specs.forEach(spec => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = spec;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    specsTable.appendChild(tr);
                });
            });

            // Create the kaseya generate description button
            const generateKaseyaDescriptionButton = document.createElement('sl-button');
            generateKaseyaDescriptionButton.setAttribute('name', 'generateKaseyaDescription');
            generateKaseyaDescriptionButton.setAttribute('size', 'large');
            generateKaseyaDescriptionButton.setAttribute('hx-post', '@(Url.Action("ScrapeDescriptionForProductSource", "ProductsAdmin"))');
            generateKaseyaDescriptionButton.setAttribute('hx-indicator', '#modal-loader');
            generateKaseyaDescriptionButton.setAttribute('hx-vals', `js:{ "sourceId": ${productId} }`); // Pass the product source ID
            generateKaseyaDescriptionButton.setAttribute('hx-swap', `none`);
            generateKaseyaDescriptionButton.textContent = 'Kaseya';
            generateKaseyaDescriptionButton.addEventListener('kaseyaDescription', function (evt) {
                shortDescription.value = evt.detail.shortDescription;
                tinymce.get('description-editor').setContent(evt.detail.description);
                evt.detail.features.forEach(feature => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = feature;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    featuresTable.appendChild(tr);
                });
                evt.detail.specs.forEach(spec => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = spec;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    specsTable.appendChild(tr);
                });
            });

            // Create the icecat generate description button
            const generateIcecatDescriptionButton = document.createElement('sl-button');
            generateIcecatDescriptionButton.setAttribute('name', 'generateIcecatDescription');
            generateIcecatDescriptionButton.setAttribute('size', 'large');
            generateIcecatDescriptionButton.setAttribute('hx-post', '@(Url.Action("GetIcecatProductInfoForPriceBookProduct", "ProductsAdmin"))');
            generateIcecatDescriptionButton.setAttribute('hx-indicator', '#modal-loader');
            generateIcecatDescriptionButton.setAttribute('hx-vals', `js:{ "productId": ${productId} }`); // Pass the product ID
            generateIcecatDescriptionButton.setAttribute('hx-swap', `none`);
            generateIcecatDescriptionButton.textContent = 'Icecat';
            generateIcecatDescriptionButton.addEventListener('icecatDescription', function (evt) {
                shortDescription.value = evt.detail.shortDescription;
                tinymce.get('description-editor').setContent(evt.detail.description);
                evt.detail.features.forEach(feature => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = feature;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    featuresTable.appendChild(tr);
                });
                evt.detail.specs.forEach(spec => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = spec;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    specsTable.appendChild(tr);
                });
            });

            // Create icons for the buttons
            const generateDescriptionButtonIcon = document.createElement('sl-icon');
            generateDescriptionButtonIcon.setAttribute('name', 'magic');
            generateDescriptionButtonIcon.setAttribute('slot', 'prefix');
            generateDescriptionButton.appendChild(generateDescriptionButtonIcon);

            const generateDescriptionButton2Icon = document.createElement('sl-icon');
            generateDescriptionButton2Icon.setAttribute('name', 'magic');
            generateDescriptionButton2Icon.setAttribute('slot', 'prefix');
            generateDescriptionButton2.appendChild(generateDescriptionButton2Icon);

            // Wrap the buttons in tooltips
            const tooltip1 = document.createElement('sl-tooltip');
            tooltip1.setAttribute('content', 'Click to generate a product description using AI (Gemini)');
            tooltip1.appendChild(generateDescriptionButton);

            const tooltip2 = document.createElement('sl-tooltip');
            tooltip2.setAttribute('content', 'Click to generate a product description using AI (Perplexity)');
            tooltip2.appendChild(generateDescriptionButton2);

            const tooltip3 = document.createElement('sl-tooltip');
            tooltip3.setAttribute('content', 'Click to generate a product description using the Kaseya Estore');
            tooltip3.appendChild(generateKaseyaDescriptionButton);

            const tooltip4 = document.createElement('sl-tooltip');
            tooltip4.setAttribute('content', 'Click to generate a product description using the Icecat API');
            tooltip4.appendChild(generateIcecatDescriptionButton);

            // Append the textarea and the tooltips (with the buttons) to the dialog content
            getApplyFixDialogContent().appendChild(shortDescription);
            getApplyFixDialogContent().appendChild(descriptionContainer);
            getApplyFixDialogContent().appendChild(featuresTable);
            getApplyFixDialogContent().appendChild(specsTable);
            getApplyFixDialogContent().appendChild(tooltip1);
            getApplyFixDialogContent().appendChild(tooltip2);
            getApplyFixDialogContent().appendChild(tooltip3);
            getApplyFixDialogContent().appendChild(tooltip4);

            applyFixButton.setAttribute('hx-post', '@(Url.Action("SetDescriptionForPriceBookProduct", "ProductsAdmin"))');
            applyFixButton.setAttribute('hx-target', '#applyFixDialogContent');
            applyFixButton.setAttribute('hx-swap', 'afterbegin');
            applyFixButton.setAttribute('hx-vals', `js:{
                    "productId": ${productId},
                    "shortDescription": document.querySelector('sl-textarea[name="shortDescription"]').value,
                    "description": tinymce.get('description-editor').getContent(),
                    "features": Array.from(document.querySelectorAll('#featuresTable td:first-child')).filter(td => td.style.textDecoration !== 'line-through').map(td => td.textContent),
                    "specs": Array.from(document.querySelectorAll('#specsTable td:first-child')).filter(td => td.style.textDecoration !== 'line-through').map(td => td.textContent)
                }`);
            document.body.addEventListener('successfulUpdate', closeDialog);

            // Initialise TinyMCE
            tinymce.init({
                selector: '#description-editor',
                height: 300,
                menubar: false,
                readonly: false,
                plugins: [
                    // Core editing features
                    'anchor', 'autolink', 'charmap', 'codesample', 'emoticons', 'image', 'link', 'lists', 'media', 'searchreplace', 'table', 'visualblocks', 'wordcount'//,
                    // Your account includes a free trial of TinyMCE premium features
                    // Try the most popular premium features until Apr 1, 2025:
                    //'checklist', 'mediaembed', 'casechange', 'export', 'formatpainter', 'pageembed', 'a11ychecker', 'tinymcespellchecker', 'permanentpen', 'powerpaste', 'advtable', 'advcode', 'editimage', 'advtemplate', 'ai', 'mentions', 'tinycomments', 'tableofcontents', 'footnotes', 'mergetags', 'autocorrect', 'typography', 'inlinecss', 'markdown','importword', 'exportword', 'exportpdf'
                ],
                toolbar: 'undo redo | blocks | ' +
                    'bold italic backcolor | alignleft aligncenter ' +
                    'alignright alignjustify | bullist numlist outdent indent | ' +
                    'removeformat | help',
                content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; }',
                tinycomments_mode: 'embedded',
                tinycomments_author: 'Author name',
                mergetags_list: [
                    {value: 'First.Name', title: 'First Name'},
                    {value: 'Email', title: 'Email'},
                ],
                ai_request: (request, respondWith) => respondWith.string(() => Promise.reject('See docs to implement AI Assistant')),
            });

            htmx.process(applyFixButton);
            htmx.process(generateDescriptionButton);
            htmx.process(generateDescriptionButton2);
            htmx.process(generateKaseyaDescriptionButton);
            htmx.process(generateIcecatDescriptionButton);
            getDialog().show();
        } else if (type === 'images') {
            getDialog().setAttribute('label', 'Add Images');

            // Create a form element for file uploads
            const form = document.createElement('form');
            form.setAttribute('id', 'uploadImagesForm');
            form.setAttribute('class', 'mb-4');

            const fileInput = document.createElement('input');
            fileInput.setAttribute('type', 'file');
            fileInput.setAttribute('name', 'images');
            fileInput.setAttribute('multiple', ''); // Allow multiple file uploads
            fileInput.setAttribute('required', '');
            fileInput.setAttribute('class', 'mb-2');

            form.appendChild(fileInput);

            // Create URL input section
            const urlContainer = document.createElement('div');
            urlContainer.setAttribute('class', 'flex flex-col gap-4 mt-4');

            // URL input and add button
            const urlInputGroup = document.createElement('div');
            urlInputGroup.setAttribute('class', 'flex gap-2 items-center');

            const urlInput = document.createElement('sl-input');
            urlInput.setAttribute('id', 'imageUrlInput');
            urlInput.setAttribute('placeholder', 'Enter image URL');
            urlInput.setAttribute('class', 'flex-1');

            const addUrlButton = document.createElement('sl-button');
            addUrlButton.setAttribute('variant', 'neutral');
            addUrlButton.setAttribute('class', 'self-end');
            addUrlButton.textContent = 'Add URL';
            addUrlButton.addEventListener('click', () => addUrlToTable());

            urlInputGroup.appendChild(urlInput);
            urlInputGroup.appendChild(addUrlButton);

            // URL table
            const urlTable = document.createElement('table');
            urlTable.setAttribute('id', 'urlsTable');
            urlTable.setAttribute('class', 'table table-striped w-full mt-2');
            urlTable.innerHTML = `
                <thead class="thead-light">
                    <tr>
                        <th>URL</th>
                        <th style="width: 50px;">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- URLs will be added here -->
                </tbody>
            `;

            urlContainer.appendChild(urlInputGroup);
            urlContainer.appendChild(urlTable);

            // Add both sections to dialog
            getApplyFixDialogContent().appendChild(form);
            getApplyFixDialogContent().appendChild(urlContainer);

            // URL handling functions
            function addUrlToTable() {
                const url = urlInput.value.trim();
                if (!url) return;

                // Validate URL format
                try {
                    new URL(url);
                } catch {
                    displayModalAlert('Invalid URL format', 'danger', 'applyFixDialog');
                    return;
                }

                // Check for duplicates
                const existingUrls = Array.from(urlTable.querySelectorAll('tbody tr td:first-child'))
                    .map(td => td.textContent.trim());
                if (existingUrls.includes(url)) {
                    displayModalAlert('URL already in list', 'warning', 'applyFixDialog');
                    return;
                }

                // Add to table
                const newRow = urlTable.querySelector('tbody').insertRow();
                newRow.innerHTML = `
                    <td class="truncate max-w-[300px]">${url}</td>
                    <td class="text-center">
                        <sl-icon-button name="trash" 
                                      class="text-danger"
                                      onclick="this.closest('tr').remove()">
                        </sl-icon-button>
                    </td>
                `;

                urlInput.value = '';
            }

            // Existing file upload setup
            applyFixButton.setAttribute('hx-post', '@(Url.Action("UploadImagesForPriceBookProduct", "ProductsAdmin"))');
            applyFixButton.setAttribute('hx-encoding', 'multipart/form-data');
            applyFixButton.setAttribute('hx-target', '#applyFixDialogContent');
            applyFixButton.setAttribute('hx-swap', 'afterbegin');
            applyFixButton.setAttribute('hx-include', '#uploadImagesForm');
            applyFixButton.setAttribute('hx-vals', `js:{ "productId": ${productId} }`);
            document.body.addEventListener('successfulUpdate', closeDialog);

            // New URL upload button
            const urlUploadButton = document.createElement('sl-button');
            urlUploadButton.setAttribute('variant', 'success');
            urlUploadButton.setAttribute('class', 'mt-4');
            urlUploadButton.textContent = 'Upload All URLs';
            urlUploadButton.setAttribute('hx-post', '@Url.Action("UploadImageFromUrlForPriceBookProduct", "ProductsAdmin")');
            urlUploadButton.setAttribute('hx-vals', `js:{ 
                "productId": ${productId},
                "urls": Array.from(document.querySelectorAll('#urlsTable tbody tr td:first-child')).map(td => td.textContent.trim())
            }`);
            urlUploadButton.setAttribute('hx-target', '#applyFixDialogContent');
            urlUploadButton.setAttribute('hx-swap', 'afterbegin');

            urlContainer.appendChild(urlUploadButton);

            document.body.addEventListener('successfulUpdate', closeDialog);
            htmx.process(applyFixButton);
            htmx.process(urlUploadButton);
            getDialog().show();
        }
    }

    function fixProductSource(type, productId) {
        const applyFixButton = document.getElementById('applyFixButton');
        getDialog().setAttribute('data-fix-type', type);
        getDialog().setAttribute('data-product-id', productId);

        reset();

        if (type === 'description') {
            getDialog().setAttribute('label', 'Add Description');

            const shortDescription = document.createElement('sl-textarea');
            shortDescription.setAttribute('name', 'shortDescription');
            shortDescription.setAttribute('label', 'Short Description');
            shortDescription.setAttribute('help-text', 'Enter the short description for this product');
            shortDescription.setAttribute('resize', 'auto');
            shortDescription.setAttribute('rows', '2');
            shortDescription.setAttribute('autofocus', '');

            const descriptionContainer = document.createElement('div');
            descriptionContainer.setAttribute('class', 'mb-4');

            const descriptionLabel = document.createElement('label');
            descriptionLabel.setAttribute('class', 'block text-(size:--sl-input-label-font-size-medium) font-medium text-gray-700 mb-1 mt-2');
            descriptionLabel.textContent = 'Description';

            const descriptionHelpText = document.createElement('p');
            descriptionHelpText.setAttribute('class', 'text-(size:--sl-input-help-text-font-size-medium) text-gray-500 mt-1');
            descriptionHelpText.textContent = 'Enter the description for this product';

            const description = document.createElement('div');
            description.setAttribute('id', 'description-editor');

            descriptionContainer.appendChild(descriptionLabel);
            descriptionContainer.appendChild(description);
            descriptionContainer.appendChild(descriptionHelpText);

            // Create a table to hold the features and specifications
            const featuresTable = document.createElement('table');
            featuresTable.setAttribute('id', 'featuresTable');
            featuresTable.setAttribute('class', 'min-w-full divide-y divide-gray-200 mt-5');
            const featuresTableHeader = document.createElement('thead');
            featuresTableHeader.setAttribute('class', 'bg-gray-50');
            const featuresTableHeaderRow = document.createElement('tr');
            featuresTableHeaderRow.innerHTML = `
                <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase w-2/5">Features</th>
                <th class="px-4 py-3 text-center text-sm font-medium text-gray-500 uppercase w-1/5">Edit</th>
                <th class="px-4 py-3 text-center text-sm font-medium text-gray-500 uppercase w-1/5">Disable</th>`;
            featuresTableHeader.appendChild(featuresTableHeaderRow);
            featuresTable.appendChild(featuresTableHeader);

            const specsTable = document.createElement('table');
            specsTable.setAttribute('id', 'specsTable');
            specsTable.setAttribute('class', 'min-w-full divide-y divide-gray-200 mt-5 mb-5');
            const specsTableHeader = document.createElement('thead');
            specsTableHeader.setAttribute('class', 'bg-gray-50');
            const specsTableHeaderRow = document.createElement('tr');
            specsTableHeaderRow.innerHTML = `
                <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase w-2/5">Specs</th>
                <th class="px-4 py-3 text-center text-sm font-medium text-gray-500 uppercase w-1/5">Edit</th>
                <th class="px-4 py-3 text-center text-sm font-medium text-gray-500 uppercase w-1/5">Disable</th>`;
            specsTableHeader.appendChild(specsTableHeaderRow);
            specsTable.appendChild(specsTableHeader);

            // Inside the fix function where the first button is created
            const generateDescriptionUrl = `@Url.Action("GenerateDescriptionForProductSource", "ProductsAdmin")`;
            const generateDescriptionButton = document.createElement('sl-button');
            generateDescriptionButton.setAttribute('name', 'generateDescription');
            generateDescriptionButton.setAttribute('size', 'large');
            generateDescriptionButton.setAttribute('hx-post', generateDescriptionUrl);
            generateDescriptionButton.setAttribute('hx-indicator', '#modal-loader');
            generateDescriptionButton.setAttribute('hx-vals', `js:{ "sourceId": ${productId}, "model": "Gemini" }`); // Pass the product source ID and model
            generateDescriptionButton.setAttribute('hx-swap', `none`);
            generateDescriptionButton.textContent = 'Gemini';
            generateDescriptionButton.addEventListener('aiDescription', function (evt) {
                shortDescription.value = evt.detail.shortDescription;
                tinymce.get('description-editor').setContent(evt.detail.description);
                evt.detail.features.forEach(feature => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = feature;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.setAttribute('class', 'text-center');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.setAttribute('class', 'text-center');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    featuresTable.appendChild(tr);
                });
                evt.detail.specs.forEach(spec => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = spec;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.setAttribute('class', 'text-center');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.setAttribute('class', 'text-center');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    specsTable.appendChild(tr);
                });
            });

            // Create the second generate description button
            const generateDescriptionButton2 = document.createElement('sl-button');
            generateDescriptionButton2.setAttribute('name', 'generateDescription2');
            generateDescriptionButton2.setAttribute('size', 'large');
            generateDescriptionButton2.setAttribute('hx-post', generateDescriptionUrl);
            generateDescriptionButton2.setAttribute('hx-indicator', '#modal-loader');
            generateDescriptionButton2.setAttribute('hx-vals', `js:{ "productId": ${productId}, "model": "Perplexity" }`); // Pass the product ID and model
            generateDescriptionButton2.setAttribute('hx-swap', `none`);
            generateDescriptionButton2.textContent = 'Perplexity';
            generateDescriptionButton2.addEventListener('aiDescription', function (evt) {
                shortDescription.value = evt.detail.shortDescription;
                tinymce.get('description-editor').setContent(evt.detail.description);
                evt.detail.features.forEach(feature => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = feature;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    featuresTable.appendChild(tr);
                });
                evt.detail.specs.forEach(spec => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = spec;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    specsTable.appendChild(tr);
                });
            });

            // Create the kaseya description button
            const generateKaseyaDescriptionButton = document.createElement('sl-button');
            generateKaseyaDescriptionButton.setAttribute('name', 'generateKaseyaDescription');
            generateKaseyaDescriptionButton.setAttribute('size', 'large');
            generateKaseyaDescriptionButton.setAttribute('hx-post', '@(Url.Action("ScrapeDescriptionForProductSource", "ProductsAdmin"))');
            generateKaseyaDescriptionButton.setAttribute('hx-indicator', '#modal-loader');
            generateKaseyaDescriptionButton.setAttribute('hx-vals', `js:{ "sourceId": ${productId} }`); // Pass the product source ID
            generateKaseyaDescriptionButton.setAttribute('hx-swap', `none`);
            generateKaseyaDescriptionButton.textContent = 'Kaseya';
            generateKaseyaDescriptionButton.addEventListener('kaseyaDescription', function (evt) {
                shortDescription.value = evt.detail.shortDescription;
                tinymce.get('description-editor').setContent(evt.detail.description);
                evt.detail.features.forEach(feature => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = feature;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    featuresTable.appendChild(tr);
                });
                evt.detail.specs.forEach(spec => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = spec;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    specsTable.appendChild(tr);
                });
            });

            // Create the icecat generate description button
            const generateIcecatDescriptionButton = document.createElement('sl-button');
            generateIcecatDescriptionButton.setAttribute('name', 'generateIcecatDescription');
            generateIcecatDescriptionButton.setAttribute('size', 'large');
            generateIcecatDescriptionButton.setAttribute('hx-post', '@(Url.Action("GetIcecatProductInfoForProductSource", "ProductsAdmin"))');
            generateIcecatDescriptionButton.setAttribute('hx-indicator', '#modal-loader');
            generateIcecatDescriptionButton.setAttribute('hx-vals', `js:{ "sourceId": ${productId} }`); // Pass the product ID
            generateIcecatDescriptionButton.setAttribute('hx-swap', `none`);
            generateIcecatDescriptionButton.textContent = 'Icecat';
            generateIcecatDescriptionButton.addEventListener('icecatDescription', function (evt) {
                shortDescription.value = evt.detail.shortDescription;
                tinymce.get('description-editor').setContent(evt.detail.description);
                evt.detail.features.forEach(feature => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = feature;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    featuresTable.appendChild(tr);
                });
                evt.detail.specs.forEach(spec => {
                    const tr = document.createElement('tr');
                    const td1 = document.createElement('td');
                    td1.textContent = spec;
                    const editBtn = document.createElement('sl-icon-button');
                    editBtn.setAttribute('name', 'pencil');
                    editBtn.addEventListener('click', () => {
                        if (editBtn.getAttribute('name') === 'pencil') {
                            const input = document.createElement('input');
                            input.type = 'text';
                            input.value = td1.textContent;
                            td1.textContent = '';
                            td1.appendChild(input);
                            input.focus();
                            editBtn.setAttribute('name', 'floppy');
                        } else {
                            const input = td1.querySelector('input');
                            td1.textContent = input.value;
                            editBtn.setAttribute('name', 'pencil');
                        }
                    });
                    tr.appendChild(td1);
                    const td2 = document.createElement('td');
                    td2.appendChild(editBtn);
                    tr.appendChild(td2);

                    const disableBtn = document.createElement('sl-icon-button');
                    disableBtn.setAttribute('name', 'ban');
                    disableBtn.addEventListener('click', () => {
                        if (td1.style.textDecoration === 'line-through') {
                            td1.style.textDecoration = '';
                        } else {
                            td1.style.textDecoration = 'line-through';
                        }
                    });
                    const td3 = document.createElement('td');
                    td3.appendChild(disableBtn);
                    tr.appendChild(td3);

                    specsTable.appendChild(tr);
                });
            });

            // Create icons for the buttons
            const generateDescriptionButtonIcon = document.createElement('sl-icon');
            generateDescriptionButtonIcon.setAttribute('name', 'magic');
            generateDescriptionButtonIcon.setAttribute('slot', 'prefix');
            generateDescriptionButton.appendChild(generateDescriptionButtonIcon);

            const generateDescriptionButton2Icon = document.createElement('sl-icon');
            generateDescriptionButton2Icon.setAttribute('name', 'magic');
            generateDescriptionButton2Icon.setAttribute('slot', 'prefix');
            generateDescriptionButton2.appendChild(generateDescriptionButton2Icon);

            // Wrap the buttons in tooltips
            const tooltip1 = document.createElement('sl-tooltip');
            tooltip1.setAttribute('content', 'Click to generate a product description using AI (Gemini)');
            tooltip1.appendChild(generateDescriptionButton);

            const tooltip2 = document.createElement('sl-tooltip');
            tooltip2.setAttribute('content', 'Click to generate a product description using AI (Perplexity)');
            tooltip2.appendChild(generateDescriptionButton2);

            const tooltip3 = document.createElement('sl-tooltip');
            tooltip3.setAttribute('content', 'Click to generate a product description using the Kaseya Estore');
            tooltip3.appendChild(generateKaseyaDescriptionButton);

            const tooltip4 = document.createElement('sl-tooltip');
            tooltip4.setAttribute('content', 'Click to generate a product description using the Icecat API');
            tooltip4.appendChild(generateIcecatDescriptionButton);

            // Append the textarea and the tooltips (with the buttons) to the dialog content
            getApplyFixDialogContent().appendChild(shortDescription);
            getApplyFixDialogContent().appendChild(descriptionContainer);
            getApplyFixDialogContent().appendChild(featuresTable);
            getApplyFixDialogContent().appendChild(specsTable);
            getApplyFixDialogContent().appendChild(tooltip1);
            getApplyFixDialogContent().appendChild(tooltip2);
            getApplyFixDialogContent().appendChild(tooltip3);
            getApplyFixDialogContent().appendChild(tooltip4);

            applyFixButton.setAttribute('hx-post', '@(Url.Action("SetDescriptionForProductSource", "ProductsAdmin"))');
            applyFixButton.setAttribute('hx-target', '#applyFixDialogContent');
            applyFixButton.setAttribute('hx-swap', 'afterbegin');
            applyFixButton.setAttribute('hx-vals', `js:{
                    "sourceId": ${productId},
                    "shortDescription": document.querySelector('sl-textarea[name="shortDescription"]').value,
                    "description": tinymce.get('description-editor').getContent(),
                    "features": Array.from(document.querySelectorAll('#featuresTable td:first-child')).filter(td => td.style.textDecoration !== 'line-through').map(td => td.textContent),
                    "specs": Array.from(document.querySelectorAll('#specsTable td:first-child')).filter(td => td.style.textDecoration !== 'line-through').map(td => td.textContent)
                }`);
            document.body.addEventListener('successfulUpdate', closeDialog);

            // Initialise TinyMCE
            tinymce.init({
                selector: '#description-editor',
                height: 300,
                menubar: false,
                readonly: false,
                plugins: [
                    // Core editing features
                    'anchor', 'autolink', 'charmap', 'codesample', 'emoticons', 'image', 'link', 'lists', 'media', 'searchreplace', 'table', 'visualblocks', 'wordcount'//,
                    // Your account includes a free trial of TinyMCE premium features
                    // Try the most popular premium features until Apr 1, 2025:
                    //'checklist', 'mediaembed', 'casechange', 'export', 'formatpainter', 'pageembed', 'a11ychecker', 'tinymcespellchecker', 'permanentpen', 'powerpaste', 'advtable', 'advcode', 'editimage', 'advtemplate', 'ai', 'mentions', 'tinycomments', 'tableofcontents', 'footnotes', 'mergetags', 'autocorrect', 'typography', 'inlinecss', 'markdown','importword', 'exportword', 'exportpdf'
                ],
                toolbar: 'undo redo | blocks | ' +
                    'bold italic backcolor | alignleft aligncenter ' +
                    'alignright alignjustify | bullist numlist outdent indent | ' +
                    'removeformat | help',
                content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; }',
                tinycomments_mode: 'embedded',
                tinycomments_author: 'Author name',
                mergetags_list: [
                    {value: 'First.Name', title: 'First Name'},
                    {value: 'Email', title: 'Email'},
                ],
                ai_request: (request, respondWith) => respondWith.string(() => Promise.reject('See docs to implement AI Assistant')),
            });

            htmx.process(applyFixButton);
            htmx.process(generateDescriptionButton);
            htmx.process(generateDescriptionButton2);
            htmx.process(generateKaseyaDescriptionButton);
            htmx.process(generateIcecatDescriptionButton);
            getDialog().show();
        } else if (type === 'images') {
            getDialog().setAttribute('label', 'Add Images');

            // Create a form element for file uploads
            const form = document.createElement('form');
            form.setAttribute('id', 'uploadImagesForm');
            form.setAttribute('class', 'mb-4');

            const fileInput = document.createElement('input');
            fileInput.setAttribute('type', 'file');
            fileInput.setAttribute('name', 'images');
            fileInput.setAttribute('multiple', ''); // Allow multiple file uploads
            fileInput.setAttribute('required', '');
            fileInput.setAttribute('class', 'mb-2');

            form.appendChild(fileInput);

            // Create URL input section
            const urlContainer = document.createElement('div');
            urlContainer.setAttribute('class', 'flex flex-col gap-4 mt-4');

            // URL input and add button
            const urlInputGroup = document.createElement('div');
            urlInputGroup.setAttribute('class', 'flex gap-2 items-center');

            const urlInput = document.createElement('sl-input');
            urlInput.setAttribute('id', 'imageUrlInput');
            urlInput.setAttribute('placeholder', 'Enter image URL');
            urlInput.setAttribute('class', 'flex-1');

            const addUrlButton = document.createElement('sl-button');
            addUrlButton.setAttribute('variant', 'neutral');
            addUrlButton.setAttribute('class', 'self-end');
            addUrlButton.textContent = 'Add URL';
            addUrlButton.addEventListener('click', () => addUrlToTable());

            urlInputGroup.appendChild(urlInput);
            urlInputGroup.appendChild(addUrlButton);

            // URL table
            const urlTable = document.createElement('table');
            urlTable.setAttribute('id', 'urlsTable');
            urlTable.setAttribute('class', 'table table-striped w-full mt-2');
            urlTable.innerHTML = `
                <thead class="thead-light">
                    <tr>
                        <th>URL</th>
                        <th style="width: 50px;">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- URLs will be added here -->
                </tbody>
            `;

            urlContainer.appendChild(urlInputGroup);
            urlContainer.appendChild(urlTable);

            // Add both sections to dialog
            getApplyFixDialogContent().appendChild(form);
            getApplyFixDialogContent().appendChild(urlContainer);

            // URL handling functions
            function addUrlToTable() {
                const url = urlInput.value.trim();
                if (!url) return;

                // Validate URL format
                try {
                    new URL(url);
                } catch {
                    displayModalAlert('Invalid URL format', 'danger', 'applyFixDialogContent');
                    return;
                }

                // Check for duplicates
                const existingUrls = Array.from(urlTable.querySelectorAll('tbody tr td:first-child'))
                    .map(td => td.textContent.trim());
                if (existingUrls.includes(url)) {
                    displayModalAlert('URL already in list', 'warning', 'applyFixDialogContent');
                    return;
                }

                // Add to table
                const newRow = urlTable.querySelector('tbody').insertRow();
                newRow.innerHTML = `
                    <td class="truncate max-w-[300px]">${url}</td>
                    <td class="text-center">
                        <sl-icon-button name="trash" 
                                      class="text-danger"
                                      onclick="this.closest('tr').remove()">
                        </sl-icon-button>
                    </td>
                `;

                urlInput.value = '';
            }

            // Existing file upload setup
            applyFixButton.setAttribute('hx-post', '@(Url.Action("UploadImagesForProductSource", "ProductsAdmin"))');
            applyFixButton.setAttribute('hx-encoding', 'multipart/form-data');
            applyFixButton.setAttribute('hx-target', '#applyFixDialogContent');
            applyFixButton.setAttribute('hx-swap', 'afterbegin');
            applyFixButton.setAttribute('hx-include', '#uploadImagesForm');
            applyFixButton.setAttribute('hx-vals', `js:{ "sourceId": ${productId} }`);
            document.body.addEventListener('successfulUpdate', closeDialog);

            // New URL upload button
            const urlUploadButton = document.createElement('sl-button');
            urlUploadButton.setAttribute('variant', 'success');
            urlUploadButton.setAttribute('class', 'mt-4');
            urlUploadButton.textContent = 'Upload All URLs';
            urlUploadButton.setAttribute('hx-post', '@Url.Action("UploadImageFromUrlForProductSource", "ProductsAdmin")');
            urlUploadButton.setAttribute('hx-vals', `js:{ 
                "sourceId": ${productId},
                "urls": Array.from(document.querySelectorAll('#urlsTable tbody tr td:first-child')).map(td => td.textContent.trim())
            }`);
            urlUploadButton.setAttribute('hx-target', '#applyFixDialogContent');
            urlUploadButton.setAttribute('hx-swap', 'afterbegin');

            urlContainer.appendChild(urlUploadButton);

            document.body.addEventListener('successfulUpdate', closeDialog);
            htmx.process(applyFixButton);
            htmx.process(urlUploadButton);
            getDialog().show();
        }
    }

    // Set up reason filter functionality
    function getReasonFilter() {
        return document.getElementById('reasonFilter');
    }

    // Function to set the filter dropdown based on DataTable state
    function setReasonFilterFromState() {
        const table = $('#disabledProductsTable').DataTable();
        const reasonFilter = getReasonFilter(); // Ensure getReasonFilter() is defined above this
        if (!reasonFilter) return; // Exit if filter element not found

        const state = table.state();
        let filterValue = ''; // Default to 'All reasons'

        // UPDATE column index to 4 for Reason column state
        if (state && state.columns && state.columns[4] && state.columns[4].search && state.columns[4].search.search) {
            const savedSearch = state.columns[4].search.search.toLowerCase(); // Get saved search for column 4

            // Map saved search term back to dropdown value
            if (savedSearch === 'missing description') {
                filterValue = 'Missing-description';
            } else if (savedSearch === 'missing images') {
                filterValue = 'Missing-images';
            } else if (savedSearch === 'missing competitor price') {
                filterValue = 'Missing-competitor-price';
            }
            // Add more mappings here if other specific reason filters are added
        }

        // Set the dropdown value, ensuring it exists as an option
        const optionExists = Array.from(reasonFilter.childNodes).some(opt => opt.value === filterValue);
        reasonFilter.value = optionExists ? filterValue : ''; // Fallback to 'All reasons' if value not found
    }

    // Debounce the search function to avoid excessive requests
    var debouncedSearch = debounce((searchValue) => {
        $('#disabledProductsTable').DataTable()
            .column(4) // UPDATE index for Reason column (was 3)
            .search(searchValue.replaceAll('-', ' '))
            .draw();
    }, 300); // 300ms delay

    getReasonFilter().addEventListener('sl-change', (event) => {
        const selectedReason = event.target.value;
        debouncedSearch(selectedReason);
    });

    // Initialise CSV upload button
    document.getElementById('csvUploadButton').addEventListener('click', function () {
        document.getElementById('csvFileInput').click();
    });
</script>

@{
    ViewBag.Title = "Products Admin";
}

<style>
    /* Loader styles */
    .loader-container {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        z-index: 50;
        border-radius: 0.375rem;
        justify-content: center;
        align-items: center;
    }

    .loader {
        width: 48px;
        height: 48px;
        border: 5px solid #3b82f6;
        border-bottom-color: transparent;
        border-radius: 50%;
        display: inline-block;
        box-sizing: border-box;
        animation: rotation 1s linear infinite;
    }

    @@keyframes rotation {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
</style>

@Html.Partial("_ErrorAlert")
@Html.Partial("_SuccessAlert")

<h1 class="text-2xl font-semibold mb-4">@ViewBag.Title</h1>

<sl-dialog id="applyFixDialog" label="Apply fix" style="--width: 500px;">
    <div id="applyFixAlertContainer"></div> <!-- Container for alerts inside the modal -->
    <!-- Loader that will be shown during HTMX requests -->
    <div id="modal-loader" class="loader-container">
        <div class="loader"></div>
    </div>
    <div id="applyFixDialogContent">
    </div>

    <div slot="footer" class="space-x-2">
        <sl-button id="applyFixButton" size="large" variant="success" pill>Apply</sl-button>
        <sl-button id="closeFixDialogButton" size="large" variant="primary" pill>Close</sl-button>
    </div>
</sl-dialog>

<div id="mainPageContent"
     hx-get="@Url.Action("GetDisabledProducts", "ProductsAdmin")"
     hx-target="this"
     hx-swap="innerHTML"
     hx-trigger="load"
     hx-indicator="#spinner">
</div>

<script>
    // Add event listeners for htmx events to manually show/hide the loader
    document.addEventListener('htmx:beforeRequest', function (event) {
        if (event.detail.elt.getAttribute('hx-indicator') === '#modal-loader') {
            document.getElementById('modal-loader').style.display = 'flex';
        }
    });

    document.addEventListener('htmx:afterRequest', function (event) {
        if (event.detail.elt.getAttribute('hx-indicator') === '#modal-loader') {
            document.getElementById('modal-loader').style.display = 'none';
        }
    });

    document.addEventListener('htmx:responseError', function (event) {
        document.getElementById('modal-loader').style.display = 'none';
    });
</script>
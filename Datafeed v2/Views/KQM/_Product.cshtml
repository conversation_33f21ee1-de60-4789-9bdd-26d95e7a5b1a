@model Datafeed_v2.Models.KQM.GetProductModel
@{
    var firstProduct = Model.Products.First();
}

<button type="button"
        class="mt-10 w-full flex items-center justify-center px-5 py-2 text-md text-gray-700 transition-colors duration-200 bg-white border rounded-lg gap-x-2 sm:w-auto"
        hx-get="@(Model.IsViewAllProducts ? Url.Action("GetAllProducts", "KQM") : Url.Action("GetProductsOrChildrenCategoriesForCategory", "KQM", new { nopCategoryId = firstProduct.CategoryID }))"
        hx-target="#mainPageContent"
        hx-swap="innerHTML"
        hx-trigger="click"
        hx-indicator="#spinner">
    <svg class="w-7 h-7 rtl:rotate-180" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
         stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 15.75L3 12m0 0l3.75-3.75M3 12h18"/>
    </svg>
    <span>Go back</span>
</button>

<div class="flex flex-row mt-10 gap-6">
    <div class="basis-4/6 flex-auto bg-slate-50">
        <div class="bg-gray-700 rounded-xl shadow">
            <img class="text-gray-200 pt-5 pb-5 mx-auto max-h-80" src="data:image/png;base64,@Convert.ToBase64String(Model.KqmProductImages.FirstOrDefault()?.Image ?? Array.Empty<byte>())"
                 alt="No product image available"/>

            <div class="p-5">
                <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-300 text-center">
                    (@firstProduct.SKU) - @firstProduct.ProductName
                </h5>

                <p class="mb-3 font-normal text-gray-300">@firstProduct.ProductDescription</p>
            </div>

            <div class="bg-gray-600 shadow">
                <table class="table">
                    <thead>
                    <tr class="text-gray-200">
                        <th>Supplier Quantity</th>
                        <th>Supplier Cost (ex. GST)</th>
                        <th>Supplier Cost With Transformation Applied</th>
                    </tr>
                    </thead>

                    <tbody>
                    <tr class="odd:bg-gray-100 even:bg-gray-300">
                        <td>@firstProduct.Quantity</td>
                        <td>@Html.Raw($"{firstProduct.Price:C}")</td>
                        <td>@Html.Raw($"{firstProduct.Price * (1.00m + (firstProduct.AppliedTransformationPackageClass.HasValue ? firstProduct.AppliedTransformationPackageClass.Value.PercentMarkup / 100 : 0.00m)):C}")</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="basis-2/6 flex-auto bg-slate-50">
        <div class="flex flex-col gap-6">
            <div class="basis-1/3 flex-auto bg-slate-50">
                <div class="p-5">
                    <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-500 text-center">
                        Currently applied transform:
                        <i>@(firstProduct.AppliedTransformationPackageClass.GetValueOrDefault()?.PackageName ?? "None")</i>
                        (+@Html.Raw($"{firstProduct.AppliedTransformationPackageClass.GetValueOrDefault()?.PercentMarkup ?? 0:F2}%")
                        )
                    </h5>
                </div>
            </div>

            <div class="basis-2/3 flex-auto bg-slate-50">
                <label for="transformationPackageId">Select a Transformation Package</label>
                <select id="transformationPackageId" name="transformationPackageId"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-md rounded-lg focus:ring-blue-500 block w-full col-lg-pull-2.5"
                        hx-post="@Url.Action("ApplyTransformationPackageToProduct", "KQM")"
                        hx-target="#mainPageContent"
                        hx-swap="innerHTML"
                        hx-trigger="change"
                        hx-indicator="#spinner"
                        hx-vals='{ "productId": @firstProduct.ProductID }'>
                    <option value="-1" selected="selected" disabled="disabled">None</option>
                    @foreach (var transformationPackage in Model.TransformationPackages)
                    {
                        <option value="@transformationPackage.ID"
                                @Html.Raw($"{(firstProduct.AppliedTransformationPackage.GetValueOrDefault(-1) == transformationPackage.ID ? "selected='selected'" : "")}")>
                            @transformationPackage.PackageName (@Math.Round(transformationPackage.PercentMarkup, 2)%)
                        </option>
                    }
                </select>
            </div>
        </div>
    </div>
</div>
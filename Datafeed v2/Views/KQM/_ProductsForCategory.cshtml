@using Datafeed_v2.Models.DbModels.Datafeed
@model Datafeed_v2.Models.KQM.GetProductsForCategoryModel

<button type="button"
        class="mt-10 mb-20 w-full flex items-center justify-center px-5 py-2 text-md text-gray-700 transition-colors duration-200 bg-white border rounded-lg gap-x-2 sm:w-auto"
        hx-get="@Url.Action("GetCategories", "KQM")"
        hx-target="#mainPageContent"
        hx-swap="innerHTML"
        hx-trigger="click"
        hx-indicator="#spinner">
    <svg class="w-7 h-7 rtl:rotate-180" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 15.75L3 12m0 0l3.75-3.75M3 12h18"/>
    </svg>
    <span>Go back</span>
</button>

<div class="flex space-x-4 max-w-lg mx-auto">
    @foreach (var category in Model.ChildrenCategories.GetValueOrDefault(Array.Empty<NopCategories>()))
    {
        <div class="bg-white rounded-lg px-10 py-10 ring-1 ring-slate-900/5 shadow-xl cursor-pointer hover:bg-gray-100"
             hx-get="@Url.Action("GetProductsOrChildrenCategoriesForCategory", "KQM", new { nopCategoryId = category.ID })"
             hx-target="#mainPageContent"
             hx-swap="innerHTML"
             hx-trigger="click"
             hx-indicator="#spinner">
            <h3 class="text-slate-900 mt-5 text-3xl tracking-tight">@category.Name</h3>
        </div>
    }
</div>

<div class="flex flex-row mt-10 gap-6">
    <div class="basis-4/6 flex-auto bg-slate-50">
        <table id="productsForCategoryTable" class="table"
               _="on load call initDatatable('productsForCategoryTable')">
            <thead>
            <tr>
                <th>Product ID</th>
                <th>Product Title</th>
                <th>Product SKU</th>
            </tr>
            </thead>

            <tbody>
            @foreach (var productGroup in Model.Products)
            {
                var firstProduct = productGroup.AsEnumerable().First();

                <tr class="odd:bg-white even:bg-slate-100 cursor-pointer"
                    @* hx-get="@Url.Action("GetProduct", "KQM", new { productId = firstProduct.ProductID, datafeedCategoryId = firstProduct.CategoryID })" *@
                    hx-get="@Url.Action("GetProduct", "KQM", new { productId = firstProduct.ProductID, nopCategoryId = firstProduct.CategoryID, isViewAllProducts = Model.IsViewAllProducts })"
                    hx-target="#mainPageContent"
                    hx-swap="innerHTML"
                    hx-trigger="click"
                    hx-indicator="#spinner">
                    <td>@firstProduct.ProductID</td>
                    <td>@firstProduct.ProductName</td>
                    <td>@firstProduct.SKU</td>
                </tr>
            }
            </tbody>

            <tfoot>
            <tr>
                <td
                    _="on load wait 100ms then call makeFooterSpanTable('productsForCategoryTable')">
                    <button type="submit"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-md w-full sm:w-auto px-5 py-2.5 text-center"
                            hx-get="@Url.Action("GetAddNewProductModal", "KQM")"
                            hx-target="#addNewProductModal"
                            hx-swap="innerHTML"
                            hx-trigger="click"
                            hx-indicator="#spinner"
                            data-toggle="modal"
                            data-target="#addNewProductModal">
                        Add New
                    </button>
                </td>
            </tr>
            </tfoot>
        </table>
    </div>

    <div class="basis-2/6 flex-auto bg-slate-50">
        <div class="flex flex-col gap-6">
            <div class="basis-1/3 flex-auto bg-slate-50">
                <div class="p-5">
                    <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-500 text-center">
                        Currently applied transform:
                        <i>@Model.AppliedTransformationPackageNameForCategory.GetValueOrDefault("None")</i>
                    </h5>
                </div>
            </div>

            <div class="basis-2/3 flex-auto bg-slate-50">
                <label for="transformationPackageId">Select a Transformation Package</label>
                <select id="transformationPackageId" name="transformationPackageId"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-md rounded-lg focus:ring-blue-500 block w-full col-lg-pull-2.5"
                        hx-post="@Url.Action("ApplyTransformationPackageToCategory", "KQM")"
                        hx-target="#mainPageContent"
                        hx-swap="innerHTML"
                        hx-trigger="change"
                        hx-indicator="#spinner"
                        hx-vals='{ "categoryId": @(Model.Products.FirstOrDefault()?.FirstOrDefault()?.CategoryID) }'>
                    <option value="-1" selected="selected" disabled="disabled">None</option>
                    @foreach (var transformationPackage in Model.TransformationPackages)
                    {
                        <option value="@transformationPackage.ID"
                                @Html.Raw($"{(Model.AppliedTransformationPackageForCategory.GetValueOrDefault(-1) == transformationPackage.ID ? "selected='selected'" : "")}")>
                            @transformationPackage.PackageName (@Math.Round(transformationPackage.PercentMarkup, 2)%)
                        </option>
                    }
                </select>
            </div>
        </div>
    </div>
</div>
@model Datafeed_v2.Models.PriceBook.GetImportedItemModel

<button type="button"
        class="mt-10 w-full flex items-center justify-center px-5 py-2 text-md text-gray-700 transition-colors duration-200 bg-white border rounded-lg gap-x-2 sm:w-auto"
        hx-get="@Url.Action("GetItemsImportedForPriceBook", "PriceBook", new { id = Model.PriceBookId })"
        hx-target="#mainPageContent"
        hx-swap="innerHTML"
        hx-trigger="click"
        hx-indicator="#spinner">
    <svg class="w-7 h-7 rtl:rotate-180" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
         stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 15.75L3 12m0 0l3.75-3.75M3 12h18"/>
    </svg>
    <span>Go back</span>
</button>

<div class="flex flex-row mt-10 gap-6">
    @if (Model.PriceBookItem.IsPublished)
    {
        <p class="p-5">This price book item is published</p>
        <button type="button"
                class="w-full h-1/2 mt-3 flex px-5 py-2 text-md text-gray-900 transition-colors duration-200 bg-red-500 border rounded-lg sm:w-auto"
                hx-get="@Url.Action("UnpublishPriceBookItem", "PriceBook", new { priceBookId = Model.PriceBookId, priceBookItemId = Model.PriceBookItem.ID })"
                hx-target="#mainPageContent"
                hx-swap="innerHTML"
                hx-trigger="click"
                hx-indicator="#spinner">
            Unpublish
        </button>
    }
    else
    {
        <p class="p-5">This price book item is currently not published</p>
        <button type="button"
                class="w-full h-1/2 mt-3 flex px-5 py-2 text-md text-gray-900 transition-colors duration-200 bg-green-500 border rounded-lg sm:w-auto"
                hx-get="@Url.Action("PublishPriceBookItem", "PriceBook", new { priceBookId = Model.PriceBookId, priceBookItemId = Model.PriceBookItem.ID })"
                hx-target="#mainPageContent"
                hx-swap="innerHTML"
                hx-trigger="click"
                hx-indicator="#spinner">
            Publish
        </button>
    }
</div>

<div class="flex flex-row mt-10 gap-6"
     _="on load call initCategoryMappingsForPriceBookItem(@Model.PriceBookItem.ID, @Model.PriceBookId)">
    <div class="basis-4/6 flex-auto bg-slate-50 cursor-pointer">
        <div class="flex-col">
            <h1 class="text-3xl font-bold text-center">Datafeed Categories</h1>
        </div>

        <ul class="divide-gray-200 draggable" id="mappedDatafeedCategories">
            @* @foreach (var mappedCategory in Model.MappedDatafeedCategories) *@
            @foreach (var mappedCategory in Model.MappedNopCategories)
            {
                <li id="@mappedCategory.ID" class="pb-3 sm:pb-4 cursor-pointer draggable">
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <div class="flex-1 min-w-0">
                            @* <p class="text-2xl text-gray-900 truncate select-none">@mappedCategory.CategoryName</p> *@
                            <p class="text-2xl text-gray-900 truncate select-none">@mappedCategory.Name</p>
                        </div>
                    </div>
                </li>
            }
        </ul>
    </div>

    <div class="basis-4/6 flex-auto bg-slate-50">
        <div class="flex-col">
            <h1 class="text-3xl font-bold text-center">Unmapped Datafeed Categories</h1>
        </div>

        <ul class="divide-gray-200" id="unmappedDatafeedCategories">
            @* @foreach (var datafeedCategory in Model.UnmappedDatafeedCategories) *@
            @foreach (var datafeedCategory in Model.UnmappedNopCategories)
            {
                <li id="@datafeedCategory.ID" class="pb-3 sm:pb-4 cursor-pointer draggable">
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <div class="flex-1 min-w-0">
                            @* <p class="text-2xl text-gray-900 truncate select-none">@datafeedCategory.CategoryName</p> *@
                            <p class="text-2xl text-gray-900 truncate select-none">@datafeedCategory.Name</p>
                        </div>
                    </div>
                </li>
            }
        </ul>
    </div>
</div>
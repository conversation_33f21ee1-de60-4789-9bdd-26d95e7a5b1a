@model Datafeed_v2.Models.PriceBook.GetImportedPriceBooksModel

<sl-dialog label="Import Price Book" style="--width: 800px;">
    <form class="flex flex-col gap-y-10"
          hx-encoding="multipart/form-data"
          hx-post="@(Url.Action("UploadPriceBook", "PriceBook"))"
          hx-on:htmx:xhr:progress="htmx.find('#progress').setAttribute('value', event.detail.loaded / event.detail.total * 100)"
          hx-vals="js:{ priceBookSupplier: document.querySelector('#priceBookSupplier').value, priceBook: document.querySelector('#priceBook').shadowRoot.querySelector('input[type=\'file\']').files[0], priceBookName: document.querySelector('#priceBookName').value }"
          hx-on:htmx:beforeSwap="document.getElementById('priceBook').value = '';">
        <sl-select id="priceBookSupplier" label="Supplier" help-text="Select the supplier of the price book" required>
            <sl-option value="generic">Generic</sl-option>
        </sl-select>
        <sl-input type="text" id="priceBookName" name="priceBookName" label="Name"
                  help-text="The name of the price book" required clearable></sl-input>
        <sl-input type="file" id="priceBook" name="priceBook" size="medium" label="File"
                  help-text="Select the price book file you wish to import" clearable required></sl-input>
        <sl-progress-bar id="progress" value="0" max="100" label="Progress"></sl-progress-bar>
        <sl-button type="submit" variant="success" size="medium" pill>Upload</sl-button>
    </form>

    <div slot="footer" class="space-x-2">
        <sl-button variant="primary" size="large" pill hx-on:click="document.querySelector('sl-dialog').hide();">Close
        </sl-button>
    </div>
</sl-dialog>

<div class="mb-6 flex items-center justify-between bg-white p-4 rounded-lg shadow-sm">
    <div class="flex items-center gap-4">
        <sl-checkbox id="showArchived">Show Archived</sl-checkbox>
    </div>
</div>

<table id="importedPriceBooksTable" class="table table-striped"
       _="on load call initDatatable('importedPriceBooksTable')">
    <thead>
    <tr>
        <th>Price Book Name</th>
        <th>Imported</th>
        <th>Supplier</th>
        <th>Archived</th>
        <th>Actions</th>
    </tr>
    </thead>

    <tbody>
    @foreach (var priceBook in Model.PriceBooks)
    {
        @Html.Partial("_ImportedPriceBookRow", priceBook)
    }
    </tbody>

    <tfoot>
    <tr>
        <td colspan="5">
            <sl-button class="float-right" variant="primary" size="medium" pill
                       hx-on:click="document.querySelector('sl-dialog').show();">Import
            </sl-button>
        </td>
    </tr>
    </tfoot>
</table>

<script>
    function initArchivedFilter() {
        const showArchivedCheckbox = document.getElementById('showArchived');
        const table = $('#importedPriceBooksTable').DataTable();

        showArchivedCheckbox.addEventListener('sl-change', function () {
            const showArchived = this.checked;
            table.column(3).search(showArchived ? 'True' : 'False').draw();
        });
    }

    window.onload = initArchivedFilter;
</script>

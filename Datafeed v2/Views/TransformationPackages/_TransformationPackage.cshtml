@model Datafeed_v2.Models.TransformationPackages.GetTransformationPackageModel

<button type="button"
        class="mt-10 mb-20 w-full flex items-center justify-center px-5 py-2 text-md text-gray-700 transition-colors duration-200 bg-white border rounded-lg gap-x-2 sm:w-auto"
        hx-get="@Url.Action("GetTransformationPackages", "TransformationPackages")"
        hx-target="#mainPageContent"
        hx-swap="innerHTML"
        hx-trigger="click"
        hx-indicator="#spinner">
    <svg class="w-7 h-7 rtl:rotate-180" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
         stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 15.75L3 12m0 0l3.75-3.75M3 12h18"/>
    </svg>
    <span>Go back</span>
</button>

<div class="h-full flex items-center justify-center">
    <form class="flex flex-col w-1/2 justify-center bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4"
          hx-post="@Url.Action("UpdateTransformationPackage", "TransformationPackages")"
          hx-target="#mainPageContent"
          hx-swap="innerHTML"
          hx-trigger="submit"
          hx-indicator="#spinner"
          hx-vals="js:{ transformationPackageId: @Model.TransformationPackage.ID,
                        EnablePercentMarkup: $('#enablePercentMarkup').is(':checked') ? 'true' : 'false',
                        EnableAI: $('#enableAI').is(':checked') ? 'true' : 'false' }">
        <div class="mb-4 self-center w-1/2">
            <label class="block text-gray-700 text-md font-bold mb-2" for="packageName">Package Name</label>
            <input
                class="max-w-none shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="packageName" name="PackageName" type="text" placeholder="Package Name"
                value="@Model.TransformationPackage.PackageName">
        </div>

        <div class="mb-4 self-center w-1/2">
            <label class="block text-gray-700 text-md font-bold mb-2" for="packageDescription">
                Package
                Description
            </label>
            <textarea
                class="max-w-none shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="packageDescription" name="PackageDescription" placeholder="Package Description"
                rows="3">@Model.TransformationPackage.PackageDescription</textarea>
        </div>

        <div class="mb-4 self-center w-1/2">
            <label class="block text-gray-700 text-md font-bold mb-2" for="percentMarkup">Percent Markup</label>
            <input
                class="max-w-none shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="percentMarkup" name="PercentMarkup" type="number" placeholder="Percent Markup"
                value="@Model.TransformationPackage.PercentMarkup">
        </div>

        <div class="mb-4 self-center w-1/2 flex flex-row">
            <label class="block text-gray-700 text-md font-bold mb-2 w-full" for="enablePercentMarkup">
                Enable Percent
                Markup
            </label>
            <input
                class="max-w-none shadow border rounded w-full h-1/2 py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="enablePercentMarkup" name="EnablePercentMarkup"
                @Html.Raw($"{(Model.TransformationPackage.EnablePercentMarkup ? "checked='checked'" : "")}")
                type="checkbox" value="@Model.TransformationPackage.EnablePercentMarkup">
        </div>

        <div class="mb-4 self-center w-1/2 flex flex-row">
            <label class="block text-gray-700 text-md font-bold mb-2 w-full" for="enableAI">Enable AI</label>
            <input
                class="max-w-none shadow border rounded w-full h-1/2 py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="enableAI" name="EnableAI"
                @Html.Raw($"{(Model.TransformationPackage.EnableAI ? "checked='checked'" : "")}") type="checkbox"
                value="@Model.TransformationPackage.EnableAI">
        </div>

        <button type="submit"
                class="self-center text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-md w-1/2 px-5 py-2.5 text-center">
            Save
        </button>
    </form>
</div>
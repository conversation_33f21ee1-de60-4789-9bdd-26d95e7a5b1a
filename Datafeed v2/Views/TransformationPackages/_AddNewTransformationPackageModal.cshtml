<div id="addNewTransformationPackageModal" class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">Create a new transformation package</h5>
        </div>
        <div class="modal-body h-full flex items-center justify-center w-full">
            <form id="addNewTransformationPackageForm"
                  class="flex flex-col w-full justify-center bg-white rounded px-8 pt-6 pb-8 mb-4">
                <div class="mb-4 self-center w-4/6">
                    <label class="block text-gray-700 text-md font-bold mb-2" for="packageName">Package Name</label>
                    <input
                        class="max-w-none shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="packageName" name="PackageName" type="text" placeholder="Package Name">
                </div>

                <div class="mb-4 self-center w-4/6">
                    <label class="block text-gray-700 text-md font-bold mb-2" for="packageDescription">
                        Package
                        Description
                    </label>
                    <textarea
                        class="max-w-none shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="packageDescription" name="PackageDescription" placeholder="Package Description"
                        rows="3"></textarea>
                </div>

                <div class="mb-4 self-center w-4/6">
                    <label class="block text-gray-700 text-md font-bold mb-2" for="percentMarkup">Percent Markup</label>
                    <input
                        class="max-w-none shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="percentMarkup" name="PercentMarkup" type="number" placeholder="Percent Markup">
                </div>

                <div class="mb-4 self-center w-4/6 flex flex-row">
                    <label class="block text-gray-700 text-md font-bold mb-2 w-full" for="enablePercentMarkup">
                        Enable
                        Percent Markup
                    </label>
                    <input
                        class="max-w-none shadow border rounded w-full h-1/2 py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="enablePercentMarkup" name="EnablePercentMarkup" type="checkbox">
                </div>

                <div class="mb-4 self-center w-4/6 flex flex-row">
                    <label class="block text-gray-700 text-md font-bold mb-2 w-full" for="enableAI">Enable AI</label>
                    <input
                        class="max-w-none shadow border rounded w-full h-1/2 py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="enableAI" name="EnableAI" type="checkbox">
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button"
                    class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-md w-full sm:w-auto px-5 py-2.5 text-center"
                    hx-post="@Url.Action("AddTransformationPackage", "TransformationPackages")"
                    hx-target="#mainPageContent"
                    hx-swap="innerHTML"
                    hx-trigger="click"
                    hx-include="#addNewTransformationPackageForm"
                    hx-indicator="#spinner"
                    hx-vals="js:{ EnablePercentMarkup: $('#enablePercentMarkup').is(':checked') ? 'true' : 'false',
                                  EnableAI: $('#enableAI').is(':checked') ? 'true' : 'false' }"
                    data-dismiss="modal">
                Add
            </button>
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        </div>
    </div>
</div>
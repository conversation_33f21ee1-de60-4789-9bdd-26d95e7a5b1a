@{
    ViewBag.Title = "Transformation Packages";
}

<div id="addNewTransformationPackageModal"
     class="modal modal-blur fade"
     style="display: none"
     aria-hidden="false"
     tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content"></div>
    </div>
</div>

<h1 class="text-2xl font-semibold mb-4">@ViewBag.Title</h1>

<div id="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mt-10 hidden"
     role="alert"
     _="on error from body remove .hidden then add .hidden to #success">
    <strong class="font-bold">Error!</strong>
    <span id="errorMessage"
          class="block sm:inline select-all">
    </span>
    <span class="absolute top-0 bottom-0 right-0 px-4 py-3"
          _="on click toggle .hidden on #error">
        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg"
             viewBox="0 0 20 20">
            <title>Close</title>
            <path
                d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
        </svg>
    </span>
</div>

<div id="success"
     class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative mt-10 hidden"
     role="alert"
     _="on success(message) from body remove .hidden then add .hidden to #error then set #successMessage.innerHTML to message">
    <span id="successMessage"
          class="block sm:inline">
    </span>
    <span class="absolute top-0 bottom-0 right-0 px-4 py-3"
          _="on click toggle .hidden on #success">
        <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg"
             viewBox="0 0 20 20">
            <title>Close</title>
            <path
                d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
        </svg>
    </span>
</div>

<div id="mainPageContent"
     hx-get="@Url.Action("GetTransformationPackages", "TransformationPackages")"
     hx-target="this"
     hx-swap="innerHTML"
     hx-trigger="load"
     hx-indicator="#spinner">

</div>
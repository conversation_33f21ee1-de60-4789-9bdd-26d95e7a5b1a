@model Datafeed_v2.Models.TransformationPackages.GetTransformationPackagesModel

<table id="transformationPackagesTable" class="table mt-10 border border-gray-200 rounded-lg shadow-md">
    <thead>
    <tr>
        <th>Package Name</th>
        <th>Package Description</th>
        <th>Percent Markup</th>
        <th>Percent Markup Enabled</th>
        <th>AI Enabled</th>
    </tr>
    </thead>

    <tbody>
    @foreach (var transformationPackage in Model.TransformationPackages)
    {
        <tr class="odd:bg-white even:bg-slate-100 cursor-pointer"
            hx-get="@Url.Action("GetTransformationPackage", "TransformationPackages", new { transformationPackageId = transformationPackage.ID })"
            hx-target="#mainPageContent"
            hx-trigger="click"
            hx-indicator="#spinner">
            <td>@transformationPackage.PackageName</td>
            <td>@transformationPackage.PackageDescription</td>
            <td>@Html.Raw($"{transformationPackage.PercentMarkup:F2}%")</td>
            <td>@transformationPackage.EnablePercentMarkup</td>
            <td>@transformationPackage.EnableAI</td>
        </tr>
    }
    </tbody>

    <tfoot>
    <tr>
        <td
            _="on load call makeFooterSpanTable('transformationPackagesTable')">
            <button type="submit"
                    class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-md w-full sm:w-auto px-5 py-2.5 text-center"
                    hx-get="@Url.Action("GetAddNewTransformationPackageModal", "TransformationPackages")"
                    hx-target="#addNewTransformationPackageModal"
                    hx-swap="innerHTML"
                    hx-trigger="click"
                    hx-indicator="#spinner"
                    data-toggle="modal"
                    data-target="#addNewTransformationPackageModal">
                Add New
            </button>
        </td>
    </tr>
    </tfoot>
</table>
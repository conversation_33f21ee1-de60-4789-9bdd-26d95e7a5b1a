using System;
using System.Diagnostics;
using System.Net;
using System.Web;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;
using Datafeed_v2.Hangfire;
using Datafeed_v2.Hangfire.Tasks;
using Hangfire;

namespace Datafeed_v2
{
    public class MvcApplication : HttpApplication
    {
        protected void Application_Start()
        {
            // TeamsNotifier.SendMessage("test", "704b08fd-7777-47b0-8a75-abbaf4ee5e5e", "19:qi-sMQZz3IXp7gh8u2oPBGrChLr4Ig9EZUgNX7410PU1@thread.tacv2").ConfigureAwait(false).GetAwaiter().GetResult();

            AreaRegistration.RegisterAllAreas();
            FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
            RouteConfig.RegisterRoutes(RouteTable.Routes);
            BundleConfig.RegisterBundles(BundleTable.Bundles);

            #region Configure Hangfire

            HangfireBootstrapper.Instance.Start();
            GlobalJobFilters.Filters.Add(new AutomaticRetryAttribute { Attempts = 0 });
            GlobalJobFilters.Filters.Add(new DisableConcurrentExecutionAttribute(60 * 60 * 2));

            // if (Debugger.IsAttached)
            if (!Debugger.IsAttached)
            {
                const string ingramFeedPath = "152840.TXT.zip";
                const string ingramCategoriesPath = "X4STD_FULL.csv.zip";
                const string teamId = "a94d9e95-cd5d-43e0-b1f2-0c64b114f18b";
                const string channelId = "19:<EMAIL>";

                RecurringJob.AddOrUpdate("GetNopCategories",
                    () => GetNopCategories.Run(null),
                    Cron.Never(), TimeZoneInfo.FindSystemTimeZoneById("AUS Eastern Standard Time"));

                RecurringJob.AddOrUpdate("GetIngramProducts",
                    () => GetIngramProducts.Run(ingramFeedPath, ingramCategoriesPath, null, null),
                    Cron.Never(), TimeZoneInfo.FindSystemTimeZoneById("AUS Eastern Standard Time"));

                RecurringJob.AddOrUpdate("GetPriceBookItemImages",
                    () => GetPriceBookItemImages.Run(null, null),
                    Cron.Daily(5, 21),
                    TimeZoneInfo.FindSystemTimeZoneById(
                        "AUS Eastern Standard Time"));

                RecurringJob.AddOrUpdate("RequestPricing",
                    () => RequestPricing.Run(null, null, null),
                    "7 8 * * *",
                    TimeZoneInfo.FindSystemTimeZoneById(
                        "AUS Eastern Standard Time"));

                RecurringJob.AddOrUpdate("GetProductImagesFromIcecat",
                    () => GetProductImagesFromIcecat.Run(null, null, null, null),
                    Cron.Daily(0),
                    TimeZoneInfo.FindSystemTimeZoneById("AUS Eastern Standard Time"));

                RecurringJob.AddOrUpdate("GetProductDescriptionsFromIcecat",
                    () => GetProductDescriptionsFromIcecat.Run(null, null, null),
                    Cron.Daily(1),
                    TimeZoneInfo.FindSystemTimeZoneById("AUS Eastern Standard Time"));

                RecurringJob.AddOrUpdate("TruncateProductSyncLogs",
                    () => TruncateProductSyncLogs.Run(null, null),
                    Cron.Weekly(DayOfWeek.Sunday), // Runs every Sunday at 12am AEST
                    TimeZoneInfo.FindSystemTimeZoneById("AUS Eastern Standard Time"));

                RecurringJob.AddOrUpdate("PreflightQueueRunner",
                    () => PreflightQueueRunner.Run(null, null, null, null, null, null, null, false),
                    "0 */2 * * *", TimeZoneInfo.FindSystemTimeZoneById("AUS Eastern Standard Time"));

                RecurringJob.AddOrUpdate("GetDescriptionsFromKqm",
                    () => GetDescriptionsFromKqm.Run(null, null, null),
                    "0 */12 * * *",
                    TimeZoneInfo.FindSystemTimeZoneById("AUS Eastern Standard Time"));

                RecurringJob.AddOrUpdate("UpdateProductInformationFromKqm",
                    () => UpdateProductInformationFromKqm.Run(null, null, null),
                    "0 */12 * * *",
                    TimeZoneInfo.FindSystemTimeZoneById("AUS Eastern Standard Time"));
            }
            else
            {
                RecurringJob.RemoveIfExists("GetNopCategories");
                RecurringJob.RemoveIfExists("GetIngramProducts");
                RecurringJob.RemoveIfExists("GetPriceBookItemImages");
                RecurringJob.RemoveIfExists("RequestPricing");
                RecurringJob.RemoveIfExists("GetProductImagesFromIcecat");
                RecurringJob.RemoveIfExists("GetProductDescriptionsFromIcecat");
                RecurringJob.RemoveIfExists("TruncateProductSyncLogs");
                RecurringJob.RemoveIfExists("PreflightQueueRunner");
                RecurringJob.RemoveIfExists("GetDescriptionsFromKqm");
                RecurringJob.RemoveIfExists("UpdateProductInformationFromKqm");
            }

            #endregion
        }

        protected void Application_End()
        {
            HangfireBootstrapper.Instance.Stop(true);
        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {
            if (Context.Request is { IsSecureConnection: false, IsLocal: false })
            {
                Response.Clear();
                Response.StatusCode = (int)HttpStatusCode.MovedPermanently;
                Response.AddHeader("Location", Context.Request.Url.ToString().Insert(4, "s"));
                Response.End();
            }
        }
    }
}
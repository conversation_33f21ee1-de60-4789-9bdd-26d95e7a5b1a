#nullable enable
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models;
using Sylvan.Data.Excel;

namespace Datafeed_v2.PriceBooks
{
    public interface IExcelDataReader : IDisposable
    {
        Task<bool> ReadAsync();
        string GetString(int ordinal);
        decimal GetDecimal(int ordinal);
        int GetInt32(int ordinal);
    }

    public class DiExcelDataReader(ExcelDataReader reader) : IExcelDataReader
    {
        public Task<bool> ReadAsync()
        {
            return reader.ReadAsync();
        }

        public string GetString(int ordinal)
        {
            return reader.GetString(ordinal);
        }

        public decimal GetDecimal(int ordinal)
        {
            return reader.GetDecimal(ordinal);
        }

        public int GetInt32(int ordinal)
        {
            return reader.GetInt32(ordinal);
        }

        public void Dispose()
        {
            reader.Dispose();
        }
    }

    public class GenericPriceBookParser
    {
        private static class ExcelColumns
        {
            public const int ProductSku = 0;
            public const int ProductTitle = 1;
            public const int Brand = 2;
            public const int BuyPrice = 3;
            public const int ProductDescription = 4;
            public const int LongDescription = 5;
            public const int Soh = 6;
            public const int Rrp = 7;
            public const int ImageUrl = 8;
        }

        /// <summary>
        /// Parses an Excel file stream into a list of GenericPriceBook objects.
        /// </summary>
        /// <param name="fileStream">The Excel file stream to parse</param>
        /// <param name="diReader">Optional custom Excel reader for dependency injection</param>
        /// <param name="diParseExcelRowAsync">Optional custom row parser for dependency injection</param>
        /// <returns>A Result containing either a List of GenericPriceBook objects or a failure message</returns>
        public static async Task<Result<List<GenericPriceBook>>> ParseExcelFileAsync(Stream fileStream,
            IExcelDataReader? diReader = null,
            Func<IExcelDataReader, Task<Result<GenericPriceBook>>>? diParseExcelRowAsync = null)
        {
            if (fileStream is not { CanRead: true } || fileStream.Length == 0)
            {
                return Result.Failure<List<GenericPriceBook>>("Invalid or empty file stream.");
            }

            try
            {
                const int batchSize = 1000;
                var priceBooks = new List<GenericPriceBook>();
                var batch = new List<GenericPriceBook>(batchSize);

                using var reader = diReader ??
                                   new DiExcelDataReader(ExcelDataReader.Create(fileStream,
                                       ExcelWorkbookType.ExcelXml));

                while (await reader.ReadAsync()) // This call also skips the header row
                {
                    var rowResult = await (diParseExcelRowAsync == null
                        ? ParseExcelRowAsync(reader)
                        : diParseExcelRowAsync(reader));
                    if (rowResult.IsFailure)
                    {
                        return Result.Failure<List<GenericPriceBook>>("Failed to parse Excel file");
                    }

                    batch.Add(rowResult.Value);

                    if (batch.Count >= batchSize)
                    {
                        priceBooks.AddRange(batch);
                        batch.Clear();
                    }
                }

                if (batch.Count > 0)
                {
                    priceBooks.AddRange(batch);
                }

                return Result.Success(priceBooks);
            }
            catch (Exception ex)
            {
                return Result.Failure<List<GenericPriceBook>>($"Failed to parse Excel file: {ex.Message}");
            }
        }

        private static Task<Result<GenericPriceBook>> ParseExcelRowAsync(IExcelDataReader reader)
        {
            var productSku = Result.Try(() => reader.GetString(ExcelColumns.ProductSku));
            var productTitle = Result.Try(() => reader.GetString(ExcelColumns.ProductTitle));
            var brand = Result.Try(() => reader.GetString(ExcelColumns.Brand));
            var buyPrice = Result.Try(() => reader.GetDecimal(ExcelColumns.BuyPrice));
            var productDescription = Result.Try(() => reader.GetString(ExcelColumns.ProductDescription));
            var longDescription = Result.Try(() => reader.GetString(ExcelColumns.LongDescription));
            var soh = Result.Try(() => reader.GetInt32(ExcelColumns.Soh));
            var rrp = Result.Try(() => reader.GetDecimal(ExcelColumns.Rrp));
            var imageUrl = Result.Try(() => reader.GetString(ExcelColumns.ImageUrl));

            if (productSku.IsFailure)
            {
                return Task.FromResult(Result.Failure<GenericPriceBook>("Failed to parse row values"));
            }

            var priceBook = new GenericPriceBook
            {
                ProductSku = productSku.Value,
                ProductTitle = productTitle.GetValueOrDefault(),
                Brand = brand.GetValueOrDefault(),
                BuyPrice = buyPrice.GetValueOrDefault(),
                ProductDescription = productDescription.GetValueOrDefault(),
                LongDescription = longDescription.GetValueOrDefault(),
                Soh = soh.GetValueOrDefault(),
                Rrp = rrp.GetValueOrDefault(),
                ImageUrl = imageUrl.GetValueOrDefault()
            };

            return Task.FromResult(Result.Success(priceBook));
        }
    }
}
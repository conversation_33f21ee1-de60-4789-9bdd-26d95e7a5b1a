using System.Collections.Generic;
using Datafeed_v2.Models.DbModels.Datafeed;

namespace Datafeed_v2.Models.Categories;

public class GetMappedProductsForNopCategoryModel
{
    public bool UpdateTransformationPackageSelection { get; set; }
    public uint? ProductIdUpdatedTransformationPackageOrPriceOverride { get; set; }
    public bool UnmappedProducts { get; set; }

    public uint SelectedNopCategoryId { get; set; }
    public IEnumerable<MappedKqmProductsForNopCategory> MappedKqmProducts { get; set; }
    public IEnumerable<KqmQuotes> MappedQuotes { get; set; }
    public IEnumerable<KqmQuotes> UnmappedQuotes { get; set; }
    public CommerceTransformationPackages SelectedTransformationPackage { get; set; }
    public IEnumerable<CommerceTransformationPackages> TransformationPackages { get; set; }

    public IEnumerable<ImportedPriceBooks> MappedPriceBooks { get; set; }
    public IEnumerable<ImportedPriceBooks> UnmappedPriceBooks { get; set; }

    public IEnumerable<MappedPriceBookItemsForNopCategory> MappedPriceBookItems { get; set; }
}
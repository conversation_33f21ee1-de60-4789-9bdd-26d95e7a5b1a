using CsvHelper.Configuration;

namespace Datafeed_v2.Models.Jim2Api;

public class GetDataModel : IJim2ApiModel
{
    public ResultSets[] resultSets { get; set; }
}

public class GetDataCsvModel
{
    // public string StockNo { get; set; }
    public string SKU { get; set; }
    public decimal RRP_Ex { get; set; }
    public decimal RRP_Inc { get; set; } // todo: dunno if this field is actually GST inclusive or not
    public string Product_Description { get; set; }
    public string Manufacturer { get; set; }

    // public string LongDescription { get; set; }
    // public string BuyUnit { get; set; }
    public decimal Stock { get; set; }
    public string Image_URL { get; set; }

    // public decimal QtyCommitted { get; set; }
    // public decimal QtyAvailable { get; set; }
    // public decimal QtyOnPO { get; set; }
    // public decimal QtyBackOrder { get; set; }
    public decimal Price_Inc { get; set; }
    public decimal Price_Ex { get; set; }
    public string Product_Name { get; set; }
    public string Category { get; set; }
}

public class GetDataCsvModelMap : ClassMap<GetDataCsvModel>
{
    public GetDataCsvModelMap()
    {
        Map(m => m.SKU).Name("SKU").Index(0);
        Map(m => m.RRP_Ex).Name("RRP_Ex").Index(1);
        Map(m => m.RRP_Inc).Name("RRP_Inc").Index(2);
        Map(m => m.Price_Ex).Name("Price_Ex").Index(3);
        Map(m => m.Price_Inc).Name("Price_Inc").Index(4);
        Map(m => m.Stock).Name("Stock").Index(5);
        Map(m => m.Image_URL).Name("Image_URL").Index(6);
        Map(m => m.Product_Description).Name("Product_Description").Index(7);
        Map(m => m.Manufacturer).Name("Manufacturer").Index(8);
        Map(m => m.Product_Name).Name("Product_Name").Index(9);
        Map(m => m.Category).Name("Category").Index(10);
    }
}
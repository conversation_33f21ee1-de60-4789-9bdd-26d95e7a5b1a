<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="Jim2AccountingModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="Jim2OAuthTokens">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Token" Type="nvarchar(max)" Nullable="false" />
          <Property Name="TimeStamp" Type="datetime" Nullable="false" />
          <Property Name="jim2_db" Type="nvarchar" MaxLength="50" Nullable="false" />
        </EntityType>
        <EntityContainer Name="Jim2AccountingModelStoreContainer">
          <EntitySet Name="Jim2OAuthTokens" EntityType="Self.Jim2OAuthTokens" Schema="dbo" store:Type="Tables" />
        </EntityContainer>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="Jim2AccountingModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="Jim2OAuthTokens">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Token" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TimeStamp" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="jim2_db" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityContainer Name="Jim2AccountingEntities" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="Jim2OAuthTokens" EntityType="Self.Jim2OAuthTokens" />
        </EntityContainer>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="Jim2AccountingModelStoreContainer" CdmEntityContainer="Jim2AccountingEntities">
          <EntitySetMapping Name="Jim2OAuthTokens">
            <EntityTypeMapping TypeName="Jim2AccountingModel.Jim2OAuthTokens">
              <MappingFragment StoreEntitySet="Jim2OAuthTokens">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="Token" ColumnName="Token" />
                <ScalarProperty Name="TimeStamp" ColumnName="TimeStamp" />
                <ScalarProperty Name="jim2_db" ColumnName="jim2_db" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="false" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>
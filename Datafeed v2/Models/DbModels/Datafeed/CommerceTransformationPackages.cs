//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Datafeed_v2.Models.DbModels.Datafeed
{
    using System;
    using System.Collections.Generic;
    
    public partial class CommerceTransformationPackages
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public CommerceTransformationPackages()
        {
            this.NopCategories = new HashSet<NopCategories>();
            this.ImportedItemsFromPriceBook = new HashSet<ImportedItemsFromPriceBook>();
        }
    
        public int ID { get; set; }
        public string PackageName { get; set; }
        public string PackageDescription { get; set; }
        public decimal PercentMarkup { get; set; }
        public bool EnablePercentMarkup { get; set; }
        public bool EnableAI { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<NopCategories> NopCategories { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<ImportedItemsFromPriceBook> ImportedItemsFromPriceBook { get; set; }
    }
}

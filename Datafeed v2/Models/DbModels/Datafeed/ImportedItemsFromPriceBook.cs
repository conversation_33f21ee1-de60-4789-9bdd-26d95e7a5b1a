//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Datafeed_v2.Models.DbModels.Datafeed
{
    using System;
    using System.Collections.Generic;
    
    public partial class ImportedItemsFromPriceBook
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public ImportedItemsFromPriceBook()
        {
            this.NewlyImportedPriceBookItems = new HashSet<NewlyImportedPriceBookItems>();
            this.PriceBookImportedItemToNopCategoryMapping = new HashSet<PriceBookImportedItemToNopCategoryMapping>();
            this.PriceBookItemSellPriceOverride = new HashSet<PriceBookItemSellPriceOverride>();
            this.PriceBookItemImages = new HashSet<PriceBookItemImages>();
            this.PriceBookItemCostPriceOverride = new HashSet<PriceBookItemCostPriceOverride>();
        }
    
        public int ID { get; set; }
        public int PriceBookID { get; set; }
        public string ProductName { get; set; }
        public string ProductSKU { get; set; }
        public string ProductDescription { get; set; }
        public decimal ProductPrice { get; set; }
        public Nullable<int> AppliedTransformationPackage { get; set; }
        public bool IsPublished { get; set; }
        public string ProductShortDescription { get; set; }
        public string ImageURLs { get; set; }
        public string Brand { get; set; }
        public Nullable<decimal> CostPrice { get; set; }
        public Nullable<int> StockOnHand { get; set; }
    
        public virtual CommerceTransformationPackages CommerceTransformationPackages { get; set; }
        public virtual ImportedPriceBooks ImportedPriceBooks { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<NewlyImportedPriceBookItems> NewlyImportedPriceBookItems { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PriceBookImportedItemToNopCategoryMapping> PriceBookImportedItemToNopCategoryMapping { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PriceBookItemSellPriceOverride> PriceBookItemSellPriceOverride { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PriceBookItemImages> PriceBookItemImages { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PriceBookItemCostPriceOverride> PriceBookItemCostPriceOverride { get; set; }
    }
}

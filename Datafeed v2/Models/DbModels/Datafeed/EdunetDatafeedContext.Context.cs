//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Datafeed_v2.Models.DbModels.Datafeed
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    using System.Data.Entity.Core.Objects;
    using System.Linq;
    
    public partial class EdunetDatafeedsEntities : DbContext
    {
        public EdunetDatafeedsEntities()
            : base("name=EdunetDatafeedsEntities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<KQMProductSyncs> KQMProductSyncs { get; set; }
        public virtual DbSet<CommerceTransformationPackages> CommerceTransformationPackages { get; set; }
        public virtual DbSet<KQMPendingProductSKUs> KQMPendingProductSKUs { get; set; }
        public virtual DbSet<KQMBrands> KQMBrands { get; set; }
        public virtual DbSet<PriceBookAndKQMMultiplexedProducts> PriceBookAndKQMMultiplexedProducts { get; set; }
        public virtual DbSet<KqmProductImages> KqmProductImages { get; set; }
        public virtual DbSet<KqmQuoteToNopCategoryMapping> KqmQuoteToNopCategoryMapping { get; set; }
        public virtual DbSet<KqmProductToNopCategoryMapping> KqmProductToNopCategoryMapping { get; set; }
        public virtual DbSet<KqmProductSuppliers> KqmProductSuppliers { get; set; }
        public virtual DbSet<IngramProductToNopCategoryMapping> IngramProductToNopCategoryMapping { get; set; }
        public virtual DbSet<NopCategories> NopCategories { get; set; }
        public virtual DbSet<KqmProductPriceOverride> KqmProductPriceOverride { get; set; }
        public virtual DbSet<IngramProductPriceOverride> IngramProductPriceOverride { get; set; }
        public virtual DbSet<PriceBookImportedItemToNopCategoryMapping> PriceBookImportedItemToNopCategoryMapping { get; set; }
        public virtual DbSet<PriceBookToNopCategoryMapping> PriceBookToNopCategoryMapping { get; set; }
        public virtual DbSet<IngramProducts> IngramProducts { get; set; }
        public virtual DbSet<IngramCategoryToNopCategoryMapping> IngramCategoryToNopCategoryMapping { get; set; }
        public virtual DbSet<MappedIngramProductsForNopCategory> MappedIngramProductsForNopCategory { get; set; }
        public virtual DbSet<KqmDeletedProductsFromQuote> KqmDeletedProductsFromQuote { get; set; }
        public virtual DbSet<KQMProducts> KQMProducts { get; set; }
        public virtual DbSet<KqmQuotes> KqmQuotes { get; set; }
        public virtual DbSet<MappedKqmProductsForNopCategory> MappedKqmProductsForNopCategory { get; set; }
        public virtual DbSet<ProductsCheaperAtCompetitor> ProductsCheaperAtCompetitor { get; set; }
        public virtual DbSet<ProductSyncLog> ProductSyncLog { get; set; }
        public virtual DbSet<ImportedPriceBooks> ImportedPriceBooks { get; set; }
        public virtual DbSet<ProductSourceList> ProductSourceList { get; set; }
        public virtual DbSet<ProductSourceTypeToNopCategoryMapping> ProductSourceTypeToNopCategoryMapping { get; set; }
        public virtual DbSet<ProductSourceStatus> ProductSourceStatus { get; set; }
        public virtual DbSet<ProductSourceImages> ProductSourceImages { get; set; }
        public virtual DbSet<FandoogleReflectorCache> FandoogleReflectorCache { get; set; }
        public virtual DbSet<PriceBookItemSellPriceOverride> PriceBookItemSellPriceOverride { get; set; }
        public virtual DbSet<AIGeneratedProductInfo> AIGeneratedProductInfo { get; set; }
        public virtual DbSet<PreflightLog> PreflightLog { get; set; }
        public virtual DbSet<TakeoffLog> TakeoffLog { get; set; }
        public virtual DbSet<ProductSourceTypesForNopCategory> ProductSourceTypesForNopCategory { get; set; }
        public virtual DbSet<ProductSourceSellPriceOverride> ProductSourceSellPriceOverride { get; set; }
        public virtual DbSet<DisabledProductsSanitised> DisabledProductsSanitised { get; set; }
        public virtual DbSet<DisabledPriceBookProductsSanitised> DisabledPriceBookProductsSanitised { get; set; }
        public virtual DbSet<NewlyImportedPriceBookItems> NewlyImportedPriceBookItems { get; set; }
        public virtual DbSet<MappedPriceBookItemsForNopCategory> MappedPriceBookItemsForNopCategory { get; set; }
        public virtual DbSet<ImportedItemsFromPriceBook> ImportedItemsFromPriceBook { get; set; }
        public virtual DbSet<PreflightFlagStatus> PreflightFlagStatus { get; set; }
        public virtual DbSet<PriceBookItemImages> PriceBookItemImages { get; set; }
        public virtual DbSet<ProductsCheaperAtCompetitorSanitised> ProductsCheaperAtCompetitorSanitised { get; set; }
        public virtual DbSet<PriceBookItemCostPriceOverride> PriceBookItemCostPriceOverride { get; set; }
        public virtual DbSet<ProductSourceCostPriceOverride> ProductSourceCostPriceOverride { get; set; }
        public virtual DbSet<ProductSource> ProductSource { get; set; }
        public virtual DbSet<ProductSourcesForNopCategory> ProductSourcesForNopCategory { get; set; }
        public virtual DbSet<EolProductLog> EolProductLog { get; set; }
    
        public virtual ObjectResult<Nullable<int>> GetProductsCheaperAtCompetitorCount()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetProductsCheaperAtCompetitorCount");
        }
    
        public virtual ObjectResult<Nullable<int>> GetDisabledProductSourceCount()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetDisabledProductSourceCount");
        }
    }
}

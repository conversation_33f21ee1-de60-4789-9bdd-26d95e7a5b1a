//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Datafeed_v2.Models.DbModels.Datafeed
{
    using System;
    using System.Collections.Generic;
    
    public partial class MappedKqmProductsForNopCategory
    {
        public int ID { get; set; }
        public bool IsPulished { get; set; }
        public int supplierID { get; set; }
        public int productID { get; set; }
        public int quantity { get; set; }
        public decimal cost { get; set; }
        public System.DateTime createdDate { get; set; }
        public System.DateTime modifiedDate { get; set; }
        public int recurringType { get; set; }
        public string productNumber { get; set; }
        public string url { get; set; }
        public string manufacturerPartNumber { get; set; }
        public int type { get; set; }
        public string title { get; set; }
        public string description { get; set; }
        public string ShortDescription { get; set; }
        public int brandID { get; set; }
        public int weight { get; set; }
        public int categoryID { get; set; }
        public decimal price { get; set; }
        public decimal retailPrice { get; set; }
        public bool isHidden { get; set; }
        public bool isActive { get; set; }
        public bool isRecommended { get; set; }
        public bool isSerialized { get; set; }
        public bool isRecurring { get; set; }
        public Nullable<int> KqmQuoteId { get; set; }
        public Nullable<int> AppliedTransformationPackage { get; set; }
        public string MappingType { get; set; }
        public int NopCategoryId { get; set; }
        public Nullable<decimal> PriceOverride { get; set; }
    }
}

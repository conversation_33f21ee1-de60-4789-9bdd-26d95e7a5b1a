<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="EdunetDatafeedsModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="AIGeneratedProductInfo">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ProductId" Type="int" Nullable="false" />
          <Property Name="ProductSource" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="Attribute" Type="nvarchar" MaxLength="20" Nullable="false" />
          <Property Name="Value" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="CommerceTransformationPackages">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PackageName" Type="varchar" MaxLength="150" Nullable="false" />
          <Property Name="PackageDescription" Type="varchar(max)" Nullable="false" />
          <Property Name="PercentMarkup" Type="money" Nullable="false" />
          <Property Name="EnablePercentMarkup" Type="bit" Nullable="false" />
          <Property Name="EnableAI" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="FandoogleReflectorCache">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ProductSku" Type="nvarchar" MaxLength="200" Nullable="false" />
          <Property Name="ExpiresAt" Type="datetime" Nullable="false" />
          <Property Name="RandomisedPrice" Type="money" Nullable="false" />
          <Property Name="OriginalPrice" Type="money" Nullable="false" />
        </EntityType>
        <EntityType Name="ImportedItemsFromPriceBook">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PriceBookID" Type="int" Nullable="false" />
          <Property Name="ProductName" Type="nvarchar" MaxLength="250" Nullable="false" />
          <Property Name="ProductSKU" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="ProductDescription" Type="nvarchar(max)" Nullable="false" />
          <Property Name="ProductPrice" Type="money" Nullable="false" />
          <Property Name="AppliedTransformationPackage" Type="int" />
          <Property Name="IsPublished" Type="bit" Nullable="false" />
          <Property Name="ProductShortDescription" Type="nvarchar(max)" />
          <Property Name="ImageURLs" Type="nvarchar(max)" />
          <Property Name="Brand" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="CostPrice" Type="money" />
          <Property Name="StockOnHand" Type="int" />
        </EntityType>
        <EntityType Name="ImportedPriceBooks">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="250" Nullable="false" />
          <Property Name="ImportedDateTime" Type="datetime" Nullable="false" />
          <Property Name="Supplier" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="AutoMapNewItems" Type="bit" Nullable="false" />
          <Property Name="Archived" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="IngramCategoryToNopCategoryMapping">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IngramCategoryName" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="NopCategoryId" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="IngramProductPriceOverride">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IngramProductId" Type="int" Nullable="false" />
          <Property Name="Price" Type="money" Nullable="false" />
        </EntityType>
        <EntityType Name="IngramProducts">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IngramPartNumber" Type="nvarchar" MaxLength="255" />
          <Property Name="IngramPartDescription" Type="nvarchar(max)" />
          <Property Name="CustomerPartNumber" Type="nvarchar" MaxLength="255" />
          <Property Name="VendorPartNumber" Type="nvarchar" MaxLength="255" />
          <Property Name="EANUPCCode" Type="nvarchar" MaxLength="255" />
          <Property Name="Plant" Type="nvarchar" MaxLength="255" />
          <Property Name="VendorNumber" Type="int" />
          <Property Name="VendorName" Type="nvarchar" MaxLength="255" />
          <Property Name="Size" Type="nvarchar" MaxLength="255" />
          <Property Name="Weight" Type="float" />
          <Property Name="Volume" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Unit" Type="nvarchar" MaxLength="255" />
          <Property Name="CategoryID" Type="nvarchar" MaxLength="255" />
          <Property Name="CustomerPrice" Type="decimal" Precision="18" Scale="2" />
          <Property Name="RetailPrice" Type="decimal" Precision="18" Scale="2" />
          <Property Name="AvailabilityFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="AvailableQuantity" Type="int" />
          <Property Name="BacklogInformation" Type="int" />
          <Property Name="BacklogETA" Type="nvarchar" MaxLength="255" />
          <Property Name="LicenseFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="BOMFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="WarrantyFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="BulkFreightFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="MaterialLongDescription" Type="nvarchar(max)" />
          <Property Name="Length" Type="float" />
          <Property Name="Width" Type="float" />
          <Property Name="Height" Type="float" />
          <Property Name="DimensionUnit" Type="nvarchar" MaxLength="255" />
          <Property Name="WeightUnit" Type="nvarchar" MaxLength="255" />
          <Property Name="VolumeUnit" Type="nvarchar" MaxLength="255" />
          <Property Name="Category" Type="int" />
          <Property Name="MaterialCreationReasonCode" Type="nvarchar" MaxLength="255" />
          <Property Name="MediaCode" Type="nvarchar" MaxLength="255" />
          <Property Name="MaterialLanguageCode" Type="nvarchar" MaxLength="255" />
          <Property Name="SubstituteMaterial" Type="nvarchar" MaxLength="255" />
          <Property Name="SupersededMaterial" Type="nvarchar" MaxLength="255" />
          <Property Name="ManufacturerVendorNumber" Type="int" />
          <Property Name="SubCategory" Type="nvarchar" MaxLength="255" />
          <Property Name="ProductFamily" Type="nvarchar" MaxLength="255" />
          <Property Name="PurchasingVendor" Type="nvarchar" MaxLength="255" />
          <Property Name="MaterialChangeCode" Type="nvarchar" MaxLength="255" />
          <Property Name="ActionCode" Type="nvarchar" MaxLength="255" />
          <Property Name="PriceStatus" Type="nvarchar" MaxLength="255" />
          <Property Name="NewMaterialFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="VendorSubrange" Type="nvarchar" MaxLength="255" />
          <Property Name="CaseQty" Type="int" />
          <Property Name="PalletQty" Type="int" />
          <Property Name="DirectOrderIdentifier" Type="nvarchar" MaxLength="255" />
          <Property Name="MaterialStatus" Type="nvarchar" MaxLength="255" />
          <Property Name="Discontinued" Type="nvarchar(max)" />
          <Property Name="ReleaseDate" Type="nvarchar" MaxLength="255" />
          <Property Name="FulfilmentType" Type="nvarchar" MaxLength="255" />
          <Property Name="MusicCopyrightFees" Type="int" />
          <Property Name="RecyclingFees" Type="int" />
          <Property Name="DocumentCopyrightFees" Type="int" />
          <Property Name="BatteryFees" Type="int" />
          <Property Name="CustomerPriceWithTax" Type="decimal" Precision="18" Scale="2" />
          <Property Name="RetailPriceWithTax" Type="decimal" Precision="18" Scale="2" />
          <Property Name="TaxPercent" Type="decimal" Precision="18" Scale="2" />
          <Property Name="DiscountInPercent" Type="nvarchar" MaxLength="255" />
          <Property Name="CustomerReservationNumber" Type="nvarchar" MaxLength="255" />
          <Property Name="CustomerReservationQty" Type="nvarchar" MaxLength="255" />
          <Property Name="AgreementID" Type="nvarchar" MaxLength="255" />
          <Property Name="LevelID" Type="nvarchar" MaxLength="255" />
          <Property Name="Period" Type="nvarchar" MaxLength="255" />
          <Property Name="Points" Type="nvarchar" MaxLength="255" />
          <Property Name="CompanyCode" Type="int" />
          <Property Name="CompanyCodeCurrency" Type="nvarchar" MaxLength="255" />
          <Property Name="CustomerCurrencyCode" Type="nvarchar" MaxLength="255" />
          <Property Name="CustomerPriceChangeFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="SubstituteFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="CreationReasonType" Type="nvarchar" MaxLength="255" />
          <Property Name="CreationReasonValue" Type="nvarchar" MaxLength="255" />
          <Property Name="Plant01AvailableQuantity" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Plant02AvailableQuantity" Type="nvarchar" MaxLength="255" />
          <Property Name="AppliedTransformationPackage" Type="int" />
          <Property Name="CategoryName" Type="nvarchar" MaxLength="150" />
          <Property Name="SubCategoryName" Type="nvarchar" MaxLength="150" />
        </EntityType>
        <EntityType Name="IngramProductToNopCategoryMapping">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IngramProductId" Type="int" Nullable="false" />
          <Property Name="NopCategoryId" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="KQMBrands">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="brandID" Type="int" Nullable="false" />
          <Property Name="name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="imageFilename" Type="nvarchar(max)" Nullable="false" />
          <Property Name="createDate" Type="datetime" Nullable="false" />
          <Property Name="modifiedDate" Type="datetime" Nullable="false" />
        </EntityType>
        <EntityType Name="KqmDeletedProductsFromQuote">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="KqmProductId" Type="int" Nullable="false" />
          <Property Name="KqmQuoteId" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="KQMPendingProductSKUs">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ProductSKU" Type="nvarchar" MaxLength="200" Nullable="false" />
          <Property Name="RequestingUser" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="RequestedDateTime" Type="datetime" Nullable="false" />
          <Property Name="Accepted" Type="bit" Nullable="false" />
          <Property Name="Rejected" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="KqmProductImages">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="KqmProductId" Type="int" Nullable="false" />
          <Property Name="Image" Type="varbinary(max)" Nullable="false" />
          <Property Name="ImageUrl" Type="nvarchar(max)" Nullable="false" />
        </EntityType>
        <EntityType Name="KqmProductPriceOverride">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="KqmProductId" Type="int" Nullable="false" />
          <Property Name="Price" Type="money" Nullable="false" />
        </EntityType>
        <EntityType Name="KQMProducts">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IsPulished" Type="bit" Nullable="false" />
          <Property Name="supplierID" Type="int" Nullable="false" />
          <Property Name="productID" Type="int" Nullable="false" />
          <Property Name="quantity" Type="int" Nullable="false" />
          <Property Name="cost" Type="money" Nullable="false" />
          <Property Name="createdDate" Type="datetime" Nullable="false" />
          <Property Name="modifiedDate" Type="datetime" Nullable="false" />
          <Property Name="recurringType" Type="int" Nullable="false" />
          <Property Name="productNumber" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="url" Type="nvarchar" MaxLength="250" Nullable="false" />
          <Property Name="manufacturerPartNumber" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="type" Type="int" Nullable="false" />
          <Property Name="title" Type="nvarchar(max)" Nullable="false" />
          <Property Name="description" Type="nvarchar(max)" />
          <Property Name="brandID" Type="int" Nullable="false" />
          <Property Name="weight" Type="int" Nullable="false" />
          <Property Name="categoryID" Type="int" Nullable="false" />
          <Property Name="price" Type="money" Nullable="false" />
          <Property Name="retailPrice" Type="money" Nullable="false" />
          <Property Name="isHidden" Type="bit" Nullable="false" />
          <Property Name="isActive" Type="bit" Nullable="false" />
          <Property Name="isRecommended" Type="bit" Nullable="false" />
          <Property Name="isSerialized" Type="bit" Nullable="false" />
          <Property Name="isRecurring" Type="bit" Nullable="false" />
          <Property Name="AppliedTransformationPackage" Type="int" />
          <Property Name="KqmQuoteId" Type="int" />
          <Property Name="ShortDescription" Type="nvarchar" MaxLength="2000" />
        </EntityType>
        <EntityType Name="KqmProductSuppliers">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="productID" Type="int" Nullable="false" />
          <Property Name="supplierID" Type="int" Nullable="false" />
          <Property Name="quantity" Type="int" Nullable="false" />
          <Property Name="cost" Type="money" Nullable="false" />
          <Property Name="productNumber" Type="nvarchar" MaxLength="200" Nullable="false" />
          <Property Name="createdDate" Type="datetime" Nullable="false" />
          <Property Name="modifiedDate" Type="datetime" />
        </EntityType>
        <EntityType Name="KQMProductSyncs">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="SyncDateTime" Type="datetime" Nullable="false" />
          <Property Name="NewProductsSynced" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="KqmProductToNopCategoryMapping">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="KqmProductId" Type="int" Nullable="false" />
          <Property Name="NopCategoryId" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="KqmQuotes">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="quoteId" Type="int" Nullable="false" />
          <Property Name="salesOrderId" Type="int" Nullable="false" />
          <Property Name="code" Type="nvarchar" MaxLength="250" Nullable="false" />
          <Property Name="quoteNumber" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="title" Type="nvarchar(max)" Nullable="false" />
          <Property Name="status" Type="int" Nullable="false" />
          <Property Name="privateNote" Type="nvarchar(max)" />
          <Property Name="ownerEmployeeID" Type="int" Nullable="false" />
          <Property Name="customerID" Type="int" Nullable="false" />
          <Property Name="contactName" Type="nvarchar" MaxLength="100" />
          <Property Name="contactEmail" Type="nvarchar" MaxLength="100" />
          <Property Name="deliveryType" Type="int" Nullable="false" />
          <Property Name="deliveryCompany" Type="nvarchar" MaxLength="100" />
          <Property Name="deliveryContact" Type="nvarchar" MaxLength="250" />
          <Property Name="deliveryPhone" Type="nvarchar" MaxLength="100" />
          <Property Name="deliveryAddress" Type="nvarchar" MaxLength="250" />
          <Property Name="deliveryCity" Type="nvarchar" MaxLength="50" />
          <Property Name="deliveryState" Type="nvarchar" MaxLength="50" />
          <Property Name="deliveryPostalCode" Type="nvarchar" MaxLength="50" />
          <Property Name="deliveryCountry" Type="nvarchar" MaxLength="50" />
          <Property Name="deliveryInstruction" Type="nvarchar" MaxLength="250" />
          <Property Name="deliveryAmount" Type="money" Nullable="false" />
          <Property Name="deliveryTax" Type="money" Nullable="false" />
          <Property Name="deliveryTaxRate" Type="int" Nullable="false" />
          <Property Name="expiryDate" Type="datetime" />
          <Property Name="createdDate" Type="datetime" Nullable="false" />
          <Property Name="modifiedDate" Type="datetime" />
          <Property Name="AutoMapNewProducts" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="KqmQuoteToNopCategoryMapping">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="KqmQuoteId" Type="int" Nullable="false" />
          <Property Name="NopCategoryId" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="NewlyImportedPriceBookItems">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PriceBookItemId" Type="int" Nullable="false" />
          <Property Name="ImportedOn" Type="datetime" Nullable="false" />
        </EntityType>
        <EntityType Name="NopCategories">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CategoryId" Type="int" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="Deleted" Type="bit" Nullable="false" />
          <Property Name="Published" Type="bit" Nullable="false" />
          <Property Name="CreatedOnUtc" Type="datetime" Nullable="false" />
          <Property Name="UpdatedOnUtc" Type="datetime" Nullable="false" />
          <Property Name="ParentCategoryId" Type="int" Nullable="false" />
          <Property Name="AppliedTransformationPackage" Type="int" />
        </EntityType>
        <EntityType Name="PreflightFlagStatus">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="Html" Type="nvarchar(max)" Nullable="false" />
        </EntityType>
        <EntityType Name="PreflightLog">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ProductSourceId" Type="int" />
          <Property Name="LogDateTime" Type="datetime" Nullable="false" />
          <Property Name="Message" Type="nvarchar(max)" Nullable="false" />
        </EntityType>
        <EntityType Name="PriceBookImportedItemToNopCategoryMapping">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PriceBookItemId" Type="int" Nullable="false" />
          <Property Name="NopCategoryId" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="PriceBookItemCostPriceOverride">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PriceBookItemId" Type="int" Nullable="false" />
          <Property Name="CostPrice" Type="money" Nullable="false" />
          <Property Name="ExpiryDate" Type="datetime" Nullable="false" />
          <Property Name="Note" Type="nvarchar(max)" Nullable="false" />
          <Property Name="ActionedBy" Type="nvarchar" MaxLength="200" Nullable="false" />
        </EntityType>
        <EntityType Name="PriceBookItemImages">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PriceBookItemID" Type="int" Nullable="false" />
          <Property Name="ImageURL" Type="nvarchar(max)" />
          <Property Name="Image" Type="varbinary(max)" Nullable="false" />
        </EntityType>
        <EntityType Name="PriceBookItemSellPriceOverride">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PriceBookItemId" Type="int" Nullable="false" />
          <Property Name="SellPrice" Type="money" Nullable="false" />
        </EntityType>
        <EntityType Name="PriceBookToNopCategoryMapping">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PriceBookId" Type="int" Nullable="false" />
          <Property Name="NopCategoryId" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="ProductsCheaperAtCompetitor">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ProductSku" Type="nvarchar" MaxLength="200" Nullable="false" />
          <Property Name="CompetitorPrice" Type="money" Nullable="false" />
        </EntityType>
        <EntityType Name="ProductSource">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="SourceTypeIds" Type="nvarchar" MaxLength="250" Nullable="false" />
          <Property Name="ProductSku" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="ProductTitle" Type="nvarchar(max)" Nullable="false" />
          <Property Name="ProductLongDescription" Type="nvarchar(max)" />
          <Property Name="ProductShortDescription" Type="nvarchar(max)" />
          <Property Name="CostPrice" Type="money" Nullable="false" />
          <Property Name="SellPrice" Type="money" />
          <Property Name="Status" Type="int" Nullable="false" />
          <Property Name="QtyAvailable" Type="int" Nullable="false" />
          <Property Name="Brand" Type="nvarchar" MaxLength="100" />
          <Property Name="KaseyaShortDescriptionPreflightFlag" Type="int" Nullable="false" />
          <Property Name="KaseyaLongDescriptionPreflightFlag" Type="int" Nullable="false" />
          <Property Name="CompetitorPricePreflightFlag" Type="int" Nullable="false" />
          <Property Name="KaseyaRecommendedSellPricePreflightFlag" Type="int" Nullable="false" />
          <Property Name="KaseyaImagesPreflightFlag" Type="int" Nullable="false" />
          <Property Name="KaseyaCostPricePreflightFlag" Type="int" Nullable="false" />
          <Property Name="IcecatShortDescriptionPreflightFlag" Type="int" Nullable="false" />
          <Property Name="IcecatLongDescriptionPreflightFlag" Type="int" Nullable="false" />
          <Property Name="IcecatImagesPreflightFlag" Type="int" Nullable="false" />
          <Property Name="Archived" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="ProductSourceCostPriceOverride">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ProductSourceId" Type="int" Nullable="false" />
          <Property Name="CostPrice" Type="money" Nullable="false" />
          <Property Name="ExpiryDate" Type="datetime" Nullable="false" />
          <Property Name="Note" Type="nvarchar(max)" Nullable="false" />
          <Property Name="ActionedBy" Type="nvarchar" MaxLength="200" Nullable="false" />
        </EntityType>
        <EntityType Name="ProductSourceImages">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ProductSourceId" Type="int" Nullable="false" />
          <Property Name="ImageBytes" Type="varbinary(max)" Nullable="false" />
          <Property Name="ImageUrl" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="ProductSourceList">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="FileName" Type="nvarchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="ProductSourceSellPriceOverride">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ProductSourceId" Type="int" Nullable="false" />
          <Property Name="SellPrice" Type="money" Nullable="false" />
          <Property Name="ExpiryDate" Type="datetime" Nullable="false" />
          <Property Name="Note" Type="nvarchar(max)" Nullable="false" />
          <Property Name="ActionedBy" Type="nvarchar" MaxLength="200" Nullable="false" />
        </EntityType>
        <EntityType Name="ProductSourceStatus">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="Publishable" Type="bit" Nullable="false" />
          <Property Name="Active" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="ProductSourceTypeToNopCategoryMapping">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="SourceTypeId" Type="int" Nullable="false" />
          <Property Name="NopCategoryId" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="ProductSyncLog">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ProductId" Type="int" />
          <Property Name="SyncDateTime" Type="datetime" Nullable="false" />
          <Property Name="Message" Type="nvarchar(max)" Nullable="false" />
        </EntityType>
        <EntityType Name="TakeoffLog">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ProductSourceId" Type="int" />
          <Property Name="LogDateTime" Type="datetime" Nullable="false" />
          <Property Name="Message" Type="nvarchar(max)" Nullable="false" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'EdunetDatafeeds.dbo.DisabledPriceBookProductsSanitised' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="DisabledPriceBookProductsSanitised">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="PriceBookID" />
            <PropertyRef Name="ProductName" />
            <PropertyRef Name="ProductSKU" />
            <PropertyRef Name="ProductDescription" />
            <PropertyRef Name="ProductPrice" />
            <PropertyRef Name="IsPublished" />
            <PropertyRef Name="Brand" />
            <PropertyRef Name="Reason" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PriceBookID" Type="int" Nullable="false" />
          <Property Name="ProductName" Type="nvarchar" MaxLength="250" Nullable="false" />
          <Property Name="ProductSKU" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="ProductDescription" Type="nvarchar(max)" Nullable="false" />
          <Property Name="ProductPrice" Type="money" Nullable="false" />
          <Property Name="AppliedTransformationPackage" Type="int" />
          <Property Name="IsPublished" Type="bit" Nullable="false" />
          <Property Name="ProductShortDescription" Type="nvarchar" MaxLength="500" />
          <Property Name="ImageURLs" Type="nvarchar(max)" />
          <Property Name="Brand" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="CostPrice" Type="money" />
          <Property Name="StockOnHand" Type="int" />
          <Property Name="Reason" Type="varchar" MaxLength="52" Nullable="false" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'EdunetDatafeeds.dbo.DisabledProductsSanitised' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="DisabledProductsSanitised">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="SourceTypeIds" />
            <PropertyRef Name="ProductSku" />
            <PropertyRef Name="ProductTitle" />
            <PropertyRef Name="CostPrice" />
            <PropertyRef Name="Status" />
            <PropertyRef Name="QtyAvailable" />
            <PropertyRef Name="Reason" />
            <PropertyRef Name="ImageCount" />
            <PropertyRef Name="DoogleCount" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="SourceTypeIds" Type="nvarchar" MaxLength="250" Nullable="false" />
          <Property Name="ProductSku" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="ProductTitle" Type="nvarchar" MaxLength="200" Nullable="false" />
          <Property Name="ProductLongDescription" Type="nvarchar(max)" />
          <Property Name="ProductShortDescription" Type="nvarchar(max)" />
          <Property Name="CostPrice" Type="money" Nullable="false" />
          <Property Name="SellPrice" Type="money" />
          <Property Name="Status" Type="int" Nullable="false" />
          <Property Name="QtyAvailable" Type="int" Nullable="false" />
          <Property Name="Brand" Type="nvarchar" MaxLength="100" />
          <Property Name="Reason" Type="varchar" MaxLength="52" Nullable="false" />
          <Property Name="ImageCount" Type="int" Nullable="false" />
          <Property Name="DoogleCount" Type="int" Nullable="false" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'EdunetDatafeeds.dbo.MappedIngramProductsForNopCategory' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="MappedIngramProductsForNopCategory">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="MappingType" />
            <PropertyRef Name="NopCategoryId" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="IngramPartNumber" Type="nvarchar" MaxLength="255" />
          <Property Name="IngramPartDescription" Type="nvarchar(max)" />
          <Property Name="CustomerPartNumber" Type="nvarchar" MaxLength="255" />
          <Property Name="VendorPartNumber" Type="nvarchar" MaxLength="255" />
          <Property Name="EANUPCCode" Type="nvarchar" MaxLength="255" />
          <Property Name="Plant" Type="nvarchar" MaxLength="255" />
          <Property Name="VendorNumber" Type="int" />
          <Property Name="VendorName" Type="nvarchar" MaxLength="255" />
          <Property Name="Size" Type="nvarchar" MaxLength="255" />
          <Property Name="Weight" Type="float" />
          <Property Name="Volume" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Unit" Type="nvarchar" MaxLength="255" />
          <Property Name="CategoryID" Type="nvarchar" MaxLength="255" />
          <Property Name="CustomerPrice" Type="decimal" Precision="18" Scale="2" />
          <Property Name="RetailPrice" Type="decimal" Precision="18" Scale="2" />
          <Property Name="AvailabilityFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="AvailableQuantity" Type="int" />
          <Property Name="BacklogInformation" Type="int" />
          <Property Name="BacklogETA" Type="nvarchar" MaxLength="255" />
          <Property Name="LicenseFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="BOMFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="WarrantyFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="BulkFreightFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="MaterialLongDescription" Type="nvarchar(max)" />
          <Property Name="Length" Type="float" />
          <Property Name="Width" Type="float" />
          <Property Name="Height" Type="float" />
          <Property Name="DimensionUnit" Type="nvarchar" MaxLength="255" />
          <Property Name="WeightUnit" Type="nvarchar" MaxLength="255" />
          <Property Name="VolumeUnit" Type="nvarchar" MaxLength="255" />
          <Property Name="Category" Type="int" />
          <Property Name="MaterialCreationReasonCode" Type="nvarchar" MaxLength="255" />
          <Property Name="MediaCode" Type="nvarchar" MaxLength="255" />
          <Property Name="MaterialLanguageCode" Type="nvarchar" MaxLength="255" />
          <Property Name="SubstituteMaterial" Type="nvarchar" MaxLength="255" />
          <Property Name="SupersededMaterial" Type="nvarchar" MaxLength="255" />
          <Property Name="ManufacturerVendorNumber" Type="int" />
          <Property Name="SubCategory" Type="nvarchar" MaxLength="255" />
          <Property Name="ProductFamily" Type="nvarchar" MaxLength="255" />
          <Property Name="PurchasingVendor" Type="nvarchar" MaxLength="255" />
          <Property Name="MaterialChangeCode" Type="nvarchar" MaxLength="255" />
          <Property Name="ActionCode" Type="nvarchar" MaxLength="255" />
          <Property Name="PriceStatus" Type="nvarchar" MaxLength="255" />
          <Property Name="NewMaterialFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="VendorSubrange" Type="nvarchar" MaxLength="255" />
          <Property Name="CaseQty" Type="int" />
          <Property Name="PalletQty" Type="int" />
          <Property Name="DirectOrderIdentifier" Type="nvarchar" MaxLength="255" />
          <Property Name="MaterialStatus" Type="nvarchar" MaxLength="255" />
          <Property Name="Discontinued" Type="nvarchar(max)" />
          <Property Name="ReleaseDate" Type="nvarchar" MaxLength="255" />
          <Property Name="FulfilmentType" Type="nvarchar" MaxLength="255" />
          <Property Name="MusicCopyrightFees" Type="int" />
          <Property Name="RecyclingFees" Type="int" />
          <Property Name="DocumentCopyrightFees" Type="int" />
          <Property Name="BatteryFees" Type="int" />
          <Property Name="CustomerPriceWithTax" Type="decimal" Precision="18" Scale="2" />
          <Property Name="RetailPriceWithTax" Type="decimal" Precision="18" Scale="2" />
          <Property Name="TaxPercent" Type="decimal" Precision="18" Scale="2" />
          <Property Name="DiscountInPercent" Type="nvarchar" MaxLength="255" />
          <Property Name="CustomerReservationNumber" Type="nvarchar" MaxLength="255" />
          <Property Name="CustomerReservationQty" Type="nvarchar" MaxLength="255" />
          <Property Name="AgreementID" Type="nvarchar" MaxLength="255" />
          <Property Name="LevelID" Type="nvarchar" MaxLength="255" />
          <Property Name="Period" Type="nvarchar" MaxLength="255" />
          <Property Name="Points" Type="nvarchar" MaxLength="255" />
          <Property Name="CompanyCode" Type="int" />
          <Property Name="CompanyCodeCurrency" Type="nvarchar" MaxLength="255" />
          <Property Name="CustomerCurrencyCode" Type="nvarchar" MaxLength="255" />
          <Property Name="CustomerPriceChangeFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="SubstituteFlag" Type="nvarchar" MaxLength="255" />
          <Property Name="CreationReasonType" Type="nvarchar" MaxLength="255" />
          <Property Name="CreationReasonValue" Type="nvarchar" MaxLength="255" />
          <Property Name="Plant01AvailableQuantity" Type="decimal" Precision="18" Scale="2" />
          <Property Name="Plant02AvailableQuantity" Type="nvarchar" MaxLength="255" />
          <Property Name="CategoryName" Type="nvarchar" MaxLength="150" />
          <Property Name="SubCategoryName" Type="nvarchar" MaxLength="150" />
          <Property Name="AppliedTransformationPackage" Type="int" />
          <Property Name="MappingType" Type="varchar" MaxLength="10" Nullable="false" />
          <Property Name="NopCategoryId" Type="int" Nullable="false" />
          <Property Name="PriceOverride" Type="money" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'EdunetDatafeeds.dbo.MappedKqmProductsForNopCategory' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="MappedKqmProductsForNopCategory">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="IsPulished" />
            <PropertyRef Name="supplierID" />
            <PropertyRef Name="productID" />
            <PropertyRef Name="quantity" />
            <PropertyRef Name="cost" />
            <PropertyRef Name="createdDate" />
            <PropertyRef Name="modifiedDate" />
            <PropertyRef Name="recurringType" />
            <PropertyRef Name="productNumber" />
            <PropertyRef Name="url" />
            <PropertyRef Name="manufacturerPartNumber" />
            <PropertyRef Name="type" />
            <PropertyRef Name="title" />
            <PropertyRef Name="brandID" />
            <PropertyRef Name="weight" />
            <PropertyRef Name="categoryID" />
            <PropertyRef Name="price" />
            <PropertyRef Name="retailPrice" />
            <PropertyRef Name="isHidden" />
            <PropertyRef Name="isActive" />
            <PropertyRef Name="isRecommended" />
            <PropertyRef Name="isSerialized" />
            <PropertyRef Name="isRecurring" />
            <PropertyRef Name="MappingType" />
            <PropertyRef Name="NopCategoryId" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="IsPulished" Type="bit" Nullable="false" />
          <Property Name="supplierID" Type="int" Nullable="false" />
          <Property Name="productID" Type="int" Nullable="false" />
          <Property Name="quantity" Type="int" Nullable="false" />
          <Property Name="cost" Type="money" Nullable="false" />
          <Property Name="createdDate" Type="datetime" Nullable="false" />
          <Property Name="modifiedDate" Type="datetime" Nullable="false" />
          <Property Name="recurringType" Type="int" Nullable="false" />
          <Property Name="productNumber" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="url" Type="nvarchar" MaxLength="250" Nullable="false" />
          <Property Name="manufacturerPartNumber" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="type" Type="int" Nullable="false" />
          <Property Name="title" Type="nvarchar(max)" Nullable="false" />
          <Property Name="description" Type="nvarchar(max)" />
          <Property Name="ShortDescription" Type="nvarchar" MaxLength="2000" />
          <Property Name="brandID" Type="int" Nullable="false" />
          <Property Name="weight" Type="int" Nullable="false" />
          <Property Name="categoryID" Type="int" Nullable="false" />
          <Property Name="price" Type="money" Nullable="false" />
          <Property Name="retailPrice" Type="money" Nullable="false" />
          <Property Name="isHidden" Type="bit" Nullable="false" />
          <Property Name="isActive" Type="bit" Nullable="false" />
          <Property Name="isRecommended" Type="bit" Nullable="false" />
          <Property Name="isSerialized" Type="bit" Nullable="false" />
          <Property Name="isRecurring" Type="bit" Nullable="false" />
          <Property Name="KqmQuoteId" Type="int" />
          <Property Name="AppliedTransformationPackage" Type="int" />
          <Property Name="MappingType" Type="nvarchar(max)" Nullable="false" />
          <Property Name="NopCategoryId" Type="int" Nullable="false" />
          <Property Name="PriceOverride" Type="money" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'EdunetDatafeeds.dbo.MappedPriceBookItemsForNopCategory' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="MappedPriceBookItemsForNopCategory">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="PriceBookID" />
            <PropertyRef Name="ProductName" />
            <PropertyRef Name="ProductSKU" />
            <PropertyRef Name="ProductDescription" />
            <PropertyRef Name="ProductPrice" />
            <PropertyRef Name="Brand" />
            <PropertyRef Name="PriceBookArchived" />
            <PropertyRef Name="NopCategoryId" />
            <PropertyRef Name="IsPublished" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="PriceBookID" Type="int" Nullable="false" />
          <Property Name="ProductName" Type="nvarchar" MaxLength="250" Nullable="false" />
          <Property Name="ProductSKU" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="ProductDescription" Type="nvarchar(max)" Nullable="false" />
          <Property Name="ProductShortDescription" Type="nvarchar(max)" />
          <Property Name="ProductPrice" Type="money" Nullable="false" />
          <Property Name="CostPrice" Type="money" />
          <Property Name="AppliedTransformationPackage" Type="int" />
          <Property Name="IsPublished" Type="bit" Nullable="false" />
          <Property Name="Brand" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="StockOnHand" Type="int" />
          <Property Name="PriceBookArchived" Type="bit" Nullable="false" />
          <Property Name="CategoryAppliedTransformationPackage" Type="int" />
          <Property Name="MappingType" Type="nvarchar" MaxLength="4000" />
          <Property Name="NopCategoryId" Type="int" Nullable="false" />
          <Property Name="PriceOverride" Type="money" />
          <Property Name="ImageUrls" Type="nvarchar(max)" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'EdunetDatafeeds.dbo.PriceBookAndKQMMultiplexedProducts' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="PriceBookAndKQMMultiplexedProducts">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="ProductID" />
            <PropertyRef Name="SKU" />
            <PropertyRef Name="ProductName" />
            <PropertyRef Name="Price" />
            <PropertyRef Name="CategoryID" />
            <PropertyRef Name="SupplierID" />
            <PropertyRef Name="Brand" />
            <PropertyRef Name="Quantity" />
            <PropertyRef Name="IsPublished" />
            <PropertyRef Name="Source" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="ProductID" Type="int" Nullable="false" />
          <Property Name="SKU" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="ProductName" Type="nvarchar(max)" Nullable="false" />
          <Property Name="ProductDescription" Type="nvarchar(max)" />
          <Property Name="Price" Type="money" Nullable="false" />
          <Property Name="CategoryID" Type="int" Nullable="false" />
          <Property Name="SupplierID" Type="int" Nullable="false" />
          <Property Name="Brand" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="Quantity" Type="int" Nullable="false" />
          <Property Name="IsPublished" Type="bit" Nullable="false" />
          <Property Name="AppliedTransformationPackage" Type="int" />
          <Property Name="Source" Type="int" Nullable="false" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'EdunetDatafeeds.dbo.ProductsCheaperAtCompetitorSanitised' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="ProductsCheaperAtCompetitorSanitised">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="ProductSku" />
            <PropertyRef Name="CompetitorPrice" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="ProductSku" Type="nvarchar" MaxLength="200" Nullable="false" />
          <Property Name="CompetitorPrice" Type="money" Nullable="false" />
          <Property Name="OurCostPrice" Type="money" />
          <Property Name="Brand" Type="nvarchar" MaxLength="100" />
          <Property Name="ProductTitle" Type="nvarchar(max)" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'EdunetDatafeeds.dbo.ProductSourcesForNopCategory' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="ProductSourcesForNopCategory">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="CategoryId" />
            <PropertyRef Name="CategoryName" />
            <PropertyRef Name="SourceTypeId" />
            <PropertyRef Name="SourceTypeName" />
            <PropertyRef Name="ProductTitle" />
            <PropertyRef Name="ProductSku" />
            <PropertyRef Name="QtyAvailable" />
            <PropertyRef Name="StatusId" />
            <PropertyRef Name="StatusName" />
            <PropertyRef Name="StatusPublishable" />
            <PropertyRef Name="StatusActive" />
            <PropertyRef Name="Archived" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="CategoryId" Type="int" Nullable="false" />
          <Property Name="CategoryName" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="SourceTypeId" Type="int" Nullable="false" />
          <Property Name="SourceTypeName" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="Brand" Type="nvarchar" MaxLength="100" />
          <Property Name="ProductTitle" Type="nvarchar(max)" Nullable="false" />
          <Property Name="ProductSku" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="ProductShortDescription" Type="nvarchar(max)" />
          <Property Name="ProductLongDescription" Type="nvarchar(max)" />
          <Property Name="CostPrice" Type="money" />
          <Property Name="SellPrice" Type="money" />
          <Property Name="QtyAvailable" Type="int" Nullable="false" />
          <Property Name="StatusId" Type="int" Nullable="false" />
          <Property Name="StatusName" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="StatusPublishable" Type="bit" Nullable="false" />
          <Property Name="StatusActive" Type="bit" Nullable="false" />
          <Property Name="PriceOverride" Type="money" />
          <Property Name="DisabledReason" Type="varchar" MaxLength="52" />
          <Property Name="DisabledProductDoogleCount" Type="int" />
          <Property Name="DisabledProductImageCount" Type="int" />
          <Property Name="Archived" Type="bit" Nullable="false" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'EdunetDatafeeds.dbo.ProductSourceTypesForNopCategory' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="ProductSourceTypesForNopCategory">
          <Key>
            <PropertyRef Name="SourceTypeId" />
            <PropertyRef Name="SourceTypeName" />
            <PropertyRef Name="CategoryId" />
            <PropertyRef Name="CategoryName" />
          </Key>
          <Property Name="SourceTypeId" Type="int" Nullable="false" />
          <Property Name="SourceTypeName" Type="nvarchar" MaxLength="150" Nullable="false" />
          <Property Name="SourceTypeFileName" Type="nvarchar" MaxLength="100" />
          <Property Name="CategoryId" Type="int" Nullable="false" />
          <Property Name="CategoryName" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <Association Name="ImportedItemsFromPriceBook_CommerceTransformationPackages_ID_fk">
          <End Role="CommerceTransformationPackages" Type="Self.CommerceTransformationPackages" Multiplicity="0..1" />
          <End Role="ImportedItemsFromPriceBook" Type="Self.ImportedItemsFromPriceBook" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="CommerceTransformationPackages">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ImportedItemsFromPriceBook">
              <PropertyRef Name="AppliedTransformationPackage" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ImportedItemsFromPriceBook_ImportedPriceBooks_ID_fk">
          <End Role="ImportedPriceBooks" Type="Self.ImportedPriceBooks" Multiplicity="1" />
          <End Role="ImportedItemsFromPriceBook" Type="Self.ImportedItemsFromPriceBook" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ImportedPriceBooks">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ImportedItemsFromPriceBook">
              <PropertyRef Name="PriceBookID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <!--Errors Found During Generation:
warning 6035: The relationship 'IngramCategoryToNopCategoryMapping_NopCategories_CategoryId_fk' has columns that are not part of the key of the table on the primary side of the relationship. The relationship was excluded.
        <Association Name="IngramCategoryToNopCategoryMapping_NopCategories_CategoryId_fk" />-->
        <Association Name="IngramProductPriceOverride_IngramProducts_ID_fk">
          <End Role="IngramProducts" Type="Self.IngramProducts" Multiplicity="1" />
          <End Role="IngramProductPriceOverride" Type="Self.IngramProductPriceOverride" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="IngramProducts">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="IngramProductPriceOverride">
              <PropertyRef Name="IngramProductId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="IngramProductToNopCategoryMapping_IngramProducts_ID_fk">
          <End Role="IngramProducts" Type="Self.IngramProducts" Multiplicity="1" />
          <End Role="IngramProductToNopCategoryMapping" Type="Self.IngramProductToNopCategoryMapping" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="IngramProducts">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="IngramProductToNopCategoryMapping">
              <PropertyRef Name="IngramProductId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <!--Errors Found During Generation:
warning 6035: The relationship 'IngramProductToNopCategoryMapping_NopCategories_CategoryId_fk' has columns that are not part of the key of the table on the primary side of the relationship. The relationship was excluded.
        <Association Name="IngramProductToNopCategoryMapping_NopCategories_CategoryId_fk" />-->
        <Association Name="KqmProductPriceOverride_KQMProducts_ID_fk">
          <End Role="KQMProducts" Type="Self.KQMProducts" Multiplicity="1" />
          <End Role="KqmProductPriceOverride" Type="Self.KqmProductPriceOverride" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="KQMProducts">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="KqmProductPriceOverride">
              <PropertyRef Name="KqmProductId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <!--Errors Found During Generation:
warning 6035: The relationship 'KQMProducts_KqmQuotes_quoteID_fk' has columns that are not part of the key of the table on the primary side of the relationship. The relationship was excluded.
        <Association Name="KQMProducts_KqmQuotes_quoteID_fk" />-->
        <Association Name="KqmProductToNopCategoryMapping_KQMProducts_ID_fk">
          <End Role="KQMProducts" Type="Self.KQMProducts" Multiplicity="1" />
          <End Role="KqmProductToNopCategoryMapping" Type="Self.KqmProductToNopCategoryMapping" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="KQMProducts">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="KqmProductToNopCategoryMapping">
              <PropertyRef Name="KqmProductId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <!--Errors Found During Generation:
warning 6035: The relationship 'KqmProductToNopCategoryMapping_NopCategories_categoryID_fk' has columns that are not part of the key of the table on the primary side of the relationship. The relationship was excluded.
        <Association Name="KqmProductToNopCategoryMapping_NopCategories_categoryID_fk" />-->
        <!--Errors Found During Generation:
warning 6035: The relationship 'KqmQuoteToNopCategoryMapping_KqmQuotes_quoteID_fk' has columns that are not part of the key of the table on the primary side of the relationship. The relationship was excluded.
        <Association Name="KqmQuoteToNopCategoryMapping_KqmQuotes_quoteID_fk" />-->
        <!--Errors Found During Generation:
warning 6035: The relationship 'KqmQuoteToNopCategoryMapping_NopCategories_categoryID_fk' has columns that are not part of the key of the table on the primary side of the relationship. The relationship was excluded.
        <Association Name="KqmQuoteToNopCategoryMapping_NopCategories_categoryID_fk" />-->
        <Association Name="NewlyImportedPriceBookItems_ImportedItemsFromPriceBook_ID_fk">
          <End Role="ImportedItemsFromPriceBook" Type="Self.ImportedItemsFromPriceBook" Multiplicity="1" />
          <End Role="NewlyImportedPriceBookItems" Type="Self.NewlyImportedPriceBookItems" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ImportedItemsFromPriceBook">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="NewlyImportedPriceBookItems">
              <PropertyRef Name="PriceBookItemId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="NopCategories_CommerceTransformationPackages_ID_fk">
          <End Role="CommerceTransformationPackages" Type="Self.CommerceTransformationPackages" Multiplicity="0..1" />
          <End Role="NopCategories" Type="Self.NopCategories" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="CommerceTransformationPackages">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="NopCategories">
              <PropertyRef Name="AppliedTransformationPackage" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="PreflightLog_ProductSource_ID_fk">
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="0..1" />
          <End Role="PreflightLog" Type="Self.PreflightLog" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ProductSource">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="PreflightLog">
              <PropertyRef Name="ProductSourceId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="PriceBookImportedItemToNopCategoryMapping_ImportedItemsFromPriceBook_ID_fk">
          <End Role="ImportedItemsFromPriceBook" Type="Self.ImportedItemsFromPriceBook" Multiplicity="1" />
          <End Role="PriceBookImportedItemToNopCategoryMapping" Type="Self.PriceBookImportedItemToNopCategoryMapping" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ImportedItemsFromPriceBook">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="PriceBookImportedItemToNopCategoryMapping">
              <PropertyRef Name="PriceBookItemId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <!--Errors Found During Generation:
warning 6035: The relationship 'PriceBookImportedItemToNopCategoryMapping_NopCategories_CategoryId_fk' has columns that are not part of the key of the table on the primary side of the relationship. The relationship was excluded.
        <Association Name="PriceBookImportedItemToNopCategoryMapping_NopCategories_CategoryId_fk" />-->
        <Association Name="PriceBookItemCostPriceOverride_ImportedItemsFromPriceBook_ID_fk">
          <End Role="ImportedItemsFromPriceBook" Type="Self.ImportedItemsFromPriceBook" Multiplicity="1" />
          <End Role="PriceBookItemCostPriceOverride" Type="Self.PriceBookItemCostPriceOverride" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ImportedItemsFromPriceBook">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="PriceBookItemCostPriceOverride">
              <PropertyRef Name="PriceBookItemId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="PriceBookItemImages_ImportedItemsFromPriceBook_ID_fk">
          <End Role="ImportedItemsFromPriceBook" Type="Self.ImportedItemsFromPriceBook" Multiplicity="1" />
          <End Role="PriceBookItemImages" Type="Self.PriceBookItemImages" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ImportedItemsFromPriceBook">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="PriceBookItemImages">
              <PropertyRef Name="PriceBookItemID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="PriceBookItemSellPriceOverride_ImportedItemsFromPriceBook_ID_fk">
          <End Role="ImportedItemsFromPriceBook" Type="Self.ImportedItemsFromPriceBook" Multiplicity="1" />
          <End Role="PriceBookItemSellPriceOverride" Type="Self.PriceBookItemSellPriceOverride" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ImportedItemsFromPriceBook">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="PriceBookItemSellPriceOverride">
              <PropertyRef Name="PriceBookItemId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="PriceBookToNopCategoryMapping_ImportedPriceBooks_ID_fk">
          <End Role="ImportedPriceBooks" Type="Self.ImportedPriceBooks" Multiplicity="1" />
          <End Role="PriceBookToNopCategoryMapping" Type="Self.PriceBookToNopCategoryMapping" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ImportedPriceBooks">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="PriceBookToNopCategoryMapping">
              <PropertyRef Name="PriceBookId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <!--Errors Found During Generation:
warning 6035: The relationship 'PriceBookToNopCategoryMapping_NopCategories_CategoryId_fk' has columns that are not part of the key of the table on the primary side of the relationship. The relationship was excluded.
        <Association Name="PriceBookToNopCategoryMapping_NopCategories_CategoryId_fk" />-->
        <Association Name="ProductSouceImages_ProductSource_ID_fk">
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="1" />
          <End Role="ProductSourceImages" Type="Self.ProductSourceImages" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ProductSource">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSourceImages">
              <PropertyRef Name="ProductSourceId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk">
          <End Role="PreflightFlagStatus" Type="Self.PreflightFlagStatus" Multiplicity="1" />
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="KaseyaShortDescriptionPreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_2">
          <End Role="PreflightFlagStatus" Type="Self.PreflightFlagStatus" Multiplicity="1" />
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="KaseyaLongDescriptionPreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_3">
          <End Role="PreflightFlagStatus" Type="Self.PreflightFlagStatus" Multiplicity="1" />
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="CompetitorPricePreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_4">
          <End Role="PreflightFlagStatus" Type="Self.PreflightFlagStatus" Multiplicity="1" />
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="KaseyaRecommendedSellPricePreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_5">
          <End Role="PreflightFlagStatus" Type="Self.PreflightFlagStatus" Multiplicity="1" />
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="KaseyaImagesPreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_6">
          <End Role="PreflightFlagStatus" Type="Self.PreflightFlagStatus" Multiplicity="1" />
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="KaseyaCostPricePreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_7">
          <End Role="PreflightFlagStatus" Type="Self.PreflightFlagStatus" Multiplicity="1" />
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="IcecatShortDescriptionPreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_8">
          <End Role="PreflightFlagStatus" Type="Self.PreflightFlagStatus" Multiplicity="1" />
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="IcecatLongDescriptionPreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_9">
          <End Role="PreflightFlagStatus" Type="Self.PreflightFlagStatus" Multiplicity="1" />
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="IcecatImagesPreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_ProductSourceStatus_ID_fk">
          <End Role="ProductSourceStatus" Type="Self.ProductSourceStatus" Multiplicity="1" />
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ProductSourceStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="Status" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSourceCostPriceOverride_ProductSource_ID_fk">
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="1" />
          <End Role="ProductSourceCostPriceOverride" Type="Self.ProductSourceCostPriceOverride" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ProductSource">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSourceCostPriceOverride">
              <PropertyRef Name="ProductSourceId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSourceSellPriceOverride_ProductSource_ID_fk">
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="1" />
          <End Role="ProductSourceSellPriceOverride" Type="Self.ProductSourceSellPriceOverride" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ProductSource">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSourceSellPriceOverride">
              <PropertyRef Name="ProductSourceId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <!--Errors Found During Generation:
warning 6035: The relationship 'ProductSourceTypeToNopCategoryMapping_NopCategories_CategoryId_fk' has columns that are not part of the key of the table on the primary side of the relationship. The relationship was excluded.
        <Association Name="ProductSourceTypeToNopCategoryMapping_NopCategories_CategoryId_fk" />-->
        <Association Name="ProductSourceTypeToNopCategoryMapping_ProductSourceList_ID_fk">
          <End Role="ProductSourceList" Type="Self.ProductSourceList" Multiplicity="1" />
          <End Role="ProductSourceTypeToNopCategoryMapping" Type="Self.ProductSourceTypeToNopCategoryMapping" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ProductSourceList">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSourceTypeToNopCategoryMapping">
              <PropertyRef Name="SourceTypeId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="TakeoffLog_ProductSource_ID_fk">
          <End Role="ProductSource" Type="Self.ProductSource" Multiplicity="0..1" />
          <End Role="TakeoffLog" Type="Self.TakeoffLog" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ProductSource">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="TakeoffLog">
              <PropertyRef Name="ProductSourceId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Function Name="GetDisabledProductSourceCount" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" />
        <Function Name="GetProductsCheaperAtCompetitorCount" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" />
        <EntityContainer Name="EdunetDatafeedsModelStoreContainer">
          <EntitySet Name="AIGeneratedProductInfo" EntityType="Self.AIGeneratedProductInfo" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CommerceTransformationPackages" EntityType="Self.CommerceTransformationPackages" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="FandoogleReflectorCache" EntityType="Self.FandoogleReflectorCache" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ImportedItemsFromPriceBook" EntityType="Self.ImportedItemsFromPriceBook" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ImportedPriceBooks" EntityType="Self.ImportedPriceBooks" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="IngramCategoryToNopCategoryMapping" EntityType="Self.IngramCategoryToNopCategoryMapping" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="IngramProductPriceOverride" EntityType="Self.IngramProductPriceOverride" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="IngramProducts" EntityType="Self.IngramProducts" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="IngramProductToNopCategoryMapping" EntityType="Self.IngramProductToNopCategoryMapping" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="KQMBrands" EntityType="Self.KQMBrands" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="KqmDeletedProductsFromQuote" EntityType="Self.KqmDeletedProductsFromQuote" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="KQMPendingProductSKUs" EntityType="Self.KQMPendingProductSKUs" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="KqmProductImages" EntityType="Self.KqmProductImages" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="KqmProductPriceOverride" EntityType="Self.KqmProductPriceOverride" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="KQMProducts" EntityType="Self.KQMProducts" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="KqmProductSuppliers" EntityType="Self.KqmProductSuppliers" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="KQMProductSyncs" EntityType="Self.KQMProductSyncs" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="KqmProductToNopCategoryMapping" EntityType="Self.KqmProductToNopCategoryMapping" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="KqmQuotes" EntityType="Self.KqmQuotes" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="KqmQuoteToNopCategoryMapping" EntityType="Self.KqmQuoteToNopCategoryMapping" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="NewlyImportedPriceBookItems" EntityType="Self.NewlyImportedPriceBookItems" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="NopCategories" EntityType="Self.NopCategories" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="PreflightFlagStatus" EntityType="Self.PreflightFlagStatus" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="PreflightLog" EntityType="Self.PreflightLog" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="PriceBookImportedItemToNopCategoryMapping" EntityType="Self.PriceBookImportedItemToNopCategoryMapping" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="PriceBookItemCostPriceOverride" EntityType="Self.PriceBookItemCostPriceOverride" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="PriceBookItemImages" EntityType="Self.PriceBookItemImages" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="PriceBookItemSellPriceOverride" EntityType="Self.PriceBookItemSellPriceOverride" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="PriceBookToNopCategoryMapping" EntityType="Self.PriceBookToNopCategoryMapping" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ProductsCheaperAtCompetitor" EntityType="Self.ProductsCheaperAtCompetitor" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ProductSource" EntityType="Self.ProductSource" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ProductSourceCostPriceOverride" EntityType="Self.ProductSourceCostPriceOverride" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ProductSourceImages" EntityType="Self.ProductSourceImages" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ProductSourceList" EntityType="Self.ProductSourceList" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ProductSourceSellPriceOverride" EntityType="Self.ProductSourceSellPriceOverride" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ProductSourceStatus" EntityType="Self.ProductSourceStatus" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ProductSourceTypeToNopCategoryMapping" EntityType="Self.ProductSourceTypeToNopCategoryMapping" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ProductSyncLog" EntityType="Self.ProductSyncLog" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TakeoffLog" EntityType="Self.TakeoffLog" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DisabledPriceBookProductsSanitised" EntityType="Self.DisabledPriceBookProductsSanitised" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [DisabledPriceBookProductsSanitised].[ID] AS [ID], 
    [DisabledPriceBookProductsSanitised].[PriceBookID] AS [PriceBookID], 
    [DisabledPriceBookProductsSanitised].[ProductName] AS [ProductName], 
    [DisabledPriceBookProductsSanitised].[ProductSKU] AS [ProductSKU], 
    [DisabledPriceBookProductsSanitised].[ProductDescription] AS [ProductDescription], 
    [DisabledPriceBookProductsSanitised].[ProductPrice] AS [ProductPrice], 
    [DisabledPriceBookProductsSanitised].[AppliedTransformationPackage] AS [AppliedTransformationPackage], 
    [DisabledPriceBookProductsSanitised].[IsPublished] AS [IsPublished], 
    [DisabledPriceBookProductsSanitised].[ProductShortDescription] AS [ProductShortDescription], 
    [DisabledPriceBookProductsSanitised].[ImageURLs] AS [ImageURLs], 
    [DisabledPriceBookProductsSanitised].[Brand] AS [Brand], 
    [DisabledPriceBookProductsSanitised].[CostPrice] AS [CostPrice], 
    [DisabledPriceBookProductsSanitised].[StockOnHand] AS [StockOnHand], 
    [DisabledPriceBookProductsSanitised].[Reason] AS [Reason]
    FROM [dbo].[DisabledPriceBookProductsSanitised] AS [DisabledPriceBookProductsSanitised]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="DisabledProductsSanitised" EntityType="Self.DisabledProductsSanitised" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [DisabledProductsSanitised].[ID] AS [ID], 
    [DisabledProductsSanitised].[SourceTypeIds] AS [SourceTypeIds], 
    [DisabledProductsSanitised].[ProductSku] AS [ProductSku], 
    [DisabledProductsSanitised].[ProductTitle] AS [ProductTitle], 
    [DisabledProductsSanitised].[ProductLongDescription] AS [ProductLongDescription], 
    [DisabledProductsSanitised].[ProductShortDescription] AS [ProductShortDescription], 
    [DisabledProductsSanitised].[CostPrice] AS [CostPrice], 
    [DisabledProductsSanitised].[SellPrice] AS [SellPrice], 
    [DisabledProductsSanitised].[Status] AS [Status], 
    [DisabledProductsSanitised].[QtyAvailable] AS [QtyAvailable], 
    [DisabledProductsSanitised].[Brand] AS [Brand], 
    [DisabledProductsSanitised].[Reason] AS [Reason], 
    [DisabledProductsSanitised].[ImageCount] AS [ImageCount], 
    [DisabledProductsSanitised].[DoogleCount] AS [DoogleCount]
    FROM [dbo].[DisabledProductsSanitised] AS [DisabledProductsSanitised]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="MappedIngramProductsForNopCategory" EntityType="Self.MappedIngramProductsForNopCategory" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [MappedIngramProductsForNopCategory].[ID] AS [ID], 
    [MappedIngramProductsForNopCategory].[IngramPartNumber] AS [IngramPartNumber], 
    [MappedIngramProductsForNopCategory].[IngramPartDescription] AS [IngramPartDescription], 
    [MappedIngramProductsForNopCategory].[CustomerPartNumber] AS [CustomerPartNumber], 
    [MappedIngramProductsForNopCategory].[VendorPartNumber] AS [VendorPartNumber], 
    [MappedIngramProductsForNopCategory].[EANUPCCode] AS [EANUPCCode], 
    [MappedIngramProductsForNopCategory].[Plant] AS [Plant], 
    [MappedIngramProductsForNopCategory].[VendorNumber] AS [VendorNumber], 
    [MappedIngramProductsForNopCategory].[VendorName] AS [VendorName], 
    [MappedIngramProductsForNopCategory].[Size] AS [Size], 
    [MappedIngramProductsForNopCategory].[Weight] AS [Weight], 
    [MappedIngramProductsForNopCategory].[Volume] AS [Volume], 
    [MappedIngramProductsForNopCategory].[Unit] AS [Unit], 
    [MappedIngramProductsForNopCategory].[CategoryID] AS [CategoryID], 
    [MappedIngramProductsForNopCategory].[CustomerPrice] AS [CustomerPrice], 
    [MappedIngramProductsForNopCategory].[RetailPrice] AS [RetailPrice], 
    [MappedIngramProductsForNopCategory].[AvailabilityFlag] AS [AvailabilityFlag], 
    [MappedIngramProductsForNopCategory].[AvailableQuantity] AS [AvailableQuantity], 
    [MappedIngramProductsForNopCategory].[BacklogInformation] AS [BacklogInformation], 
    [MappedIngramProductsForNopCategory].[BacklogETA] AS [BacklogETA], 
    [MappedIngramProductsForNopCategory].[LicenseFlag] AS [LicenseFlag], 
    [MappedIngramProductsForNopCategory].[BOMFlag] AS [BOMFlag], 
    [MappedIngramProductsForNopCategory].[WarrantyFlag] AS [WarrantyFlag], 
    [MappedIngramProductsForNopCategory].[BulkFreightFlag] AS [BulkFreightFlag], 
    [MappedIngramProductsForNopCategory].[MaterialLongDescription] AS [MaterialLongDescription], 
    [MappedIngramProductsForNopCategory].[Length] AS [Length], 
    [MappedIngramProductsForNopCategory].[Width] AS [Width], 
    [MappedIngramProductsForNopCategory].[Height] AS [Height], 
    [MappedIngramProductsForNopCategory].[DimensionUnit] AS [DimensionUnit], 
    [MappedIngramProductsForNopCategory].[WeightUnit] AS [WeightUnit], 
    [MappedIngramProductsForNopCategory].[VolumeUnit] AS [VolumeUnit], 
    [MappedIngramProductsForNopCategory].[Category] AS [Category], 
    [MappedIngramProductsForNopCategory].[MaterialCreationReasonCode] AS [MaterialCreationReasonCode], 
    [MappedIngramProductsForNopCategory].[MediaCode] AS [MediaCode], 
    [MappedIngramProductsForNopCategory].[MaterialLanguageCode] AS [MaterialLanguageCode], 
    [MappedIngramProductsForNopCategory].[SubstituteMaterial] AS [SubstituteMaterial], 
    [MappedIngramProductsForNopCategory].[SupersededMaterial] AS [SupersededMaterial], 
    [MappedIngramProductsForNopCategory].[ManufacturerVendorNumber] AS [ManufacturerVendorNumber], 
    [MappedIngramProductsForNopCategory].[SubCategory] AS [SubCategory], 
    [MappedIngramProductsForNopCategory].[ProductFamily] AS [ProductFamily], 
    [MappedIngramProductsForNopCategory].[PurchasingVendor] AS [PurchasingVendor], 
    [MappedIngramProductsForNopCategory].[MaterialChangeCode] AS [MaterialChangeCode], 
    [MappedIngramProductsForNopCategory].[ActionCode] AS [ActionCode], 
    [MappedIngramProductsForNopCategory].[PriceStatus] AS [PriceStatus], 
    [MappedIngramProductsForNopCategory].[NewMaterialFlag] AS [NewMaterialFlag], 
    [MappedIngramProductsForNopCategory].[VendorSubrange] AS [VendorSubrange], 
    [MappedIngramProductsForNopCategory].[CaseQty] AS [CaseQty], 
    [MappedIngramProductsForNopCategory].[PalletQty] AS [PalletQty], 
    [MappedIngramProductsForNopCategory].[DirectOrderIdentifier] AS [DirectOrderIdentifier], 
    [MappedIngramProductsForNopCategory].[MaterialStatus] AS [MaterialStatus], 
    [MappedIngramProductsForNopCategory].[Discontinued] AS [Discontinued], 
    [MappedIngramProductsForNopCategory].[ReleaseDate] AS [ReleaseDate], 
    [MappedIngramProductsForNopCategory].[FulfilmentType] AS [FulfilmentType], 
    [MappedIngramProductsForNopCategory].[MusicCopyrightFees] AS [MusicCopyrightFees], 
    [MappedIngramProductsForNopCategory].[RecyclingFees] AS [RecyclingFees], 
    [MappedIngramProductsForNopCategory].[DocumentCopyrightFees] AS [DocumentCopyrightFees], 
    [MappedIngramProductsForNopCategory].[BatteryFees] AS [BatteryFees], 
    [MappedIngramProductsForNopCategory].[CustomerPriceWithTax] AS [CustomerPriceWithTax], 
    [MappedIngramProductsForNopCategory].[RetailPriceWithTax] AS [RetailPriceWithTax], 
    [MappedIngramProductsForNopCategory].[TaxPercent] AS [TaxPercent], 
    [MappedIngramProductsForNopCategory].[DiscountInPercent] AS [DiscountInPercent], 
    [MappedIngramProductsForNopCategory].[CustomerReservationNumber] AS [CustomerReservationNumber], 
    [MappedIngramProductsForNopCategory].[CustomerReservationQty] AS [CustomerReservationQty], 
    [MappedIngramProductsForNopCategory].[AgreementID] AS [AgreementID], 
    [MappedIngramProductsForNopCategory].[LevelID] AS [LevelID], 
    [MappedIngramProductsForNopCategory].[Period] AS [Period], 
    [MappedIngramProductsForNopCategory].[Points] AS [Points], 
    [MappedIngramProductsForNopCategory].[CompanyCode] AS [CompanyCode], 
    [MappedIngramProductsForNopCategory].[CompanyCodeCurrency] AS [CompanyCodeCurrency], 
    [MappedIngramProductsForNopCategory].[CustomerCurrencyCode] AS [CustomerCurrencyCode], 
    [MappedIngramProductsForNopCategory].[CustomerPriceChangeFlag] AS [CustomerPriceChangeFlag], 
    [MappedIngramProductsForNopCategory].[SubstituteFlag] AS [SubstituteFlag], 
    [MappedIngramProductsForNopCategory].[CreationReasonType] AS [CreationReasonType], 
    [MappedIngramProductsForNopCategory].[CreationReasonValue] AS [CreationReasonValue], 
    [MappedIngramProductsForNopCategory].[Plant01AvailableQuantity] AS [Plant01AvailableQuantity], 
    [MappedIngramProductsForNopCategory].[Plant02AvailableQuantity] AS [Plant02AvailableQuantity], 
    [MappedIngramProductsForNopCategory].[CategoryName] AS [CategoryName], 
    [MappedIngramProductsForNopCategory].[SubCategoryName] AS [SubCategoryName], 
    [MappedIngramProductsForNopCategory].[AppliedTransformationPackage] AS [AppliedTransformationPackage], 
    [MappedIngramProductsForNopCategory].[MappingType] AS [MappingType], 
    [MappedIngramProductsForNopCategory].[NopCategoryId] AS [NopCategoryId], 
    [MappedIngramProductsForNopCategory].[PriceOverride] AS [PriceOverride]
    FROM [dbo].[MappedIngramProductsForNopCategory] AS [MappedIngramProductsForNopCategory]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="MappedKqmProductsForNopCategory" EntityType="Self.MappedKqmProductsForNopCategory" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [MappedKqmProductsForNopCategory].[ID] AS [ID], 
    [MappedKqmProductsForNopCategory].[IsPulished] AS [IsPulished], 
    [MappedKqmProductsForNopCategory].[supplierID] AS [supplierID], 
    [MappedKqmProductsForNopCategory].[productID] AS [productID], 
    [MappedKqmProductsForNopCategory].[quantity] AS [quantity], 
    [MappedKqmProductsForNopCategory].[cost] AS [cost], 
    [MappedKqmProductsForNopCategory].[createdDate] AS [createdDate], 
    [MappedKqmProductsForNopCategory].[modifiedDate] AS [modifiedDate], 
    [MappedKqmProductsForNopCategory].[recurringType] AS [recurringType], 
    [MappedKqmProductsForNopCategory].[productNumber] AS [productNumber], 
    [MappedKqmProductsForNopCategory].[url] AS [url], 
    [MappedKqmProductsForNopCategory].[manufacturerPartNumber] AS [manufacturerPartNumber], 
    [MappedKqmProductsForNopCategory].[type] AS [type], 
    [MappedKqmProductsForNopCategory].[title] AS [title], 
    [MappedKqmProductsForNopCategory].[description] AS [description], 
    [MappedKqmProductsForNopCategory].[ShortDescription] AS [ShortDescription], 
    [MappedKqmProductsForNopCategory].[brandID] AS [brandID], 
    [MappedKqmProductsForNopCategory].[weight] AS [weight], 
    [MappedKqmProductsForNopCategory].[categoryID] AS [categoryID], 
    [MappedKqmProductsForNopCategory].[price] AS [price], 
    [MappedKqmProductsForNopCategory].[retailPrice] AS [retailPrice], 
    [MappedKqmProductsForNopCategory].[isHidden] AS [isHidden], 
    [MappedKqmProductsForNopCategory].[isActive] AS [isActive], 
    [MappedKqmProductsForNopCategory].[isRecommended] AS [isRecommended], 
    [MappedKqmProductsForNopCategory].[isSerialized] AS [isSerialized], 
    [MappedKqmProductsForNopCategory].[isRecurring] AS [isRecurring], 
    [MappedKqmProductsForNopCategory].[KqmQuoteId] AS [KqmQuoteId], 
    [MappedKqmProductsForNopCategory].[AppliedTransformationPackage] AS [AppliedTransformationPackage], 
    [MappedKqmProductsForNopCategory].[MappingType] AS [MappingType], 
    [MappedKqmProductsForNopCategory].[NopCategoryId] AS [NopCategoryId], 
    [MappedKqmProductsForNopCategory].[PriceOverride] AS [PriceOverride]
    FROM [dbo].[MappedKqmProductsForNopCategory] AS [MappedKqmProductsForNopCategory]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="MappedPriceBookItemsForNopCategory" EntityType="Self.MappedPriceBookItemsForNopCategory" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [MappedPriceBookItemsForNopCategory].[ID] AS [ID], 
    [MappedPriceBookItemsForNopCategory].[PriceBookID] AS [PriceBookID], 
    [MappedPriceBookItemsForNopCategory].[ProductName] AS [ProductName], 
    [MappedPriceBookItemsForNopCategory].[ProductSKU] AS [ProductSKU], 
    [MappedPriceBookItemsForNopCategory].[ProductDescription] AS [ProductDescription], 
    [MappedPriceBookItemsForNopCategory].[ProductShortDescription] AS [ProductShortDescription], 
    [MappedPriceBookItemsForNopCategory].[ProductPrice] AS [ProductPrice], 
    [MappedPriceBookItemsForNopCategory].[CostPrice] AS [CostPrice], 
    [MappedPriceBookItemsForNopCategory].[AppliedTransformationPackage] AS [AppliedTransformationPackage], 
    [MappedPriceBookItemsForNopCategory].[IsPublished] AS [IsPublished], 
    [MappedPriceBookItemsForNopCategory].[Brand] AS [Brand], 
    [MappedPriceBookItemsForNopCategory].[StockOnHand] AS [StockOnHand], 
    [MappedPriceBookItemsForNopCategory].[PriceBookArchived] AS [PriceBookArchived], 
    [MappedPriceBookItemsForNopCategory].[CategoryAppliedTransformationPackage] AS [CategoryAppliedTransformationPackage], 
    [MappedPriceBookItemsForNopCategory].[MappingType] AS [MappingType], 
    [MappedPriceBookItemsForNopCategory].[NopCategoryId] AS [NopCategoryId], 
    [MappedPriceBookItemsForNopCategory].[PriceOverride] AS [PriceOverride], 
    [MappedPriceBookItemsForNopCategory].[ImageUrls] AS [ImageUrls]
    FROM [dbo].[MappedPriceBookItemsForNopCategory] AS [MappedPriceBookItemsForNopCategory]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="PriceBookAndKQMMultiplexedProducts" EntityType="Self.PriceBookAndKQMMultiplexedProducts" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [PriceBookAndKQMMultiplexedProducts].[ID] AS [ID], 
    [PriceBookAndKQMMultiplexedProducts].[ProductID] AS [ProductID], 
    [PriceBookAndKQMMultiplexedProducts].[SKU] AS [SKU], 
    [PriceBookAndKQMMultiplexedProducts].[ProductName] AS [ProductName], 
    [PriceBookAndKQMMultiplexedProducts].[ProductDescription] AS [ProductDescription], 
    [PriceBookAndKQMMultiplexedProducts].[Price] AS [Price], 
    [PriceBookAndKQMMultiplexedProducts].[CategoryID] AS [CategoryID], 
    [PriceBookAndKQMMultiplexedProducts].[SupplierID] AS [SupplierID], 
    [PriceBookAndKQMMultiplexedProducts].[Brand] AS [Brand], 
    [PriceBookAndKQMMultiplexedProducts].[Quantity] AS [Quantity], 
    [PriceBookAndKQMMultiplexedProducts].[IsPublished] AS [IsPublished], 
    [PriceBookAndKQMMultiplexedProducts].[AppliedTransformationPackage] AS [AppliedTransformationPackage], 
    [PriceBookAndKQMMultiplexedProducts].[Source] AS [Source]
    FROM [dbo].[PriceBookAndKQMMultiplexedProducts] AS [PriceBookAndKQMMultiplexedProducts]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="ProductsCheaperAtCompetitorSanitised" EntityType="Self.ProductsCheaperAtCompetitorSanitised" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [ProductsCheaperAtCompetitorSanitised].[ID] AS [ID], 
    [ProductsCheaperAtCompetitorSanitised].[ProductSku] AS [ProductSku], 
    [ProductsCheaperAtCompetitorSanitised].[CompetitorPrice] AS [CompetitorPrice], 
    [ProductsCheaperAtCompetitorSanitised].[OurCostPrice] AS [OurCostPrice], 
    [ProductsCheaperAtCompetitorSanitised].[Brand] AS [Brand], 
    [ProductsCheaperAtCompetitorSanitised].[ProductTitle] AS [ProductTitle]
    FROM [dbo].[ProductsCheaperAtCompetitorSanitised] AS [ProductsCheaperAtCompetitorSanitised]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="ProductSourcesForNopCategory" EntityType="Self.ProductSourcesForNopCategory" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [ProductSourcesForNopCategory].[ID] AS [ID], 
    [ProductSourcesForNopCategory].[CategoryId] AS [CategoryId], 
    [ProductSourcesForNopCategory].[CategoryName] AS [CategoryName], 
    [ProductSourcesForNopCategory].[SourceTypeId] AS [SourceTypeId], 
    [ProductSourcesForNopCategory].[SourceTypeName] AS [SourceTypeName], 
    [ProductSourcesForNopCategory].[Brand] AS [Brand], 
    [ProductSourcesForNopCategory].[ProductTitle] AS [ProductTitle], 
    [ProductSourcesForNopCategory].[ProductSku] AS [ProductSku], 
    [ProductSourcesForNopCategory].[ProductShortDescription] AS [ProductShortDescription], 
    [ProductSourcesForNopCategory].[ProductLongDescription] AS [ProductLongDescription], 
    [ProductSourcesForNopCategory].[CostPrice] AS [CostPrice], 
    [ProductSourcesForNopCategory].[SellPrice] AS [SellPrice], 
    [ProductSourcesForNopCategory].[QtyAvailable] AS [QtyAvailable], 
    [ProductSourcesForNopCategory].[StatusId] AS [StatusId], 
    [ProductSourcesForNopCategory].[StatusName] AS [StatusName], 
    [ProductSourcesForNopCategory].[StatusPublishable] AS [StatusPublishable], 
    [ProductSourcesForNopCategory].[StatusActive] AS [StatusActive], 
    [ProductSourcesForNopCategory].[PriceOverride] AS [PriceOverride], 
    [ProductSourcesForNopCategory].[DisabledReason] AS [DisabledReason], 
    [ProductSourcesForNopCategory].[DisabledProductDoogleCount] AS [DisabledProductDoogleCount], 
    [ProductSourcesForNopCategory].[DisabledProductImageCount] AS [DisabledProductImageCount], 
    [ProductSourcesForNopCategory].[Archived] AS [Archived]
    FROM [dbo].[ProductSourcesForNopCategory] AS [ProductSourcesForNopCategory]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="ProductSourceTypesForNopCategory" EntityType="Self.ProductSourceTypesForNopCategory" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [ProductSourceTypesForNopCategory].[SourceTypeId] AS [SourceTypeId], 
    [ProductSourceTypesForNopCategory].[SourceTypeName] AS [SourceTypeName], 
    [ProductSourceTypesForNopCategory].[SourceTypeFileName] AS [SourceTypeFileName], 
    [ProductSourceTypesForNopCategory].[CategoryId] AS [CategoryId], 
    [ProductSourceTypesForNopCategory].[CategoryName] AS [CategoryName]
    FROM [dbo].[ProductSourceTypesForNopCategory] AS [ProductSourceTypesForNopCategory]</DefiningQuery>
          </EntitySet>
          <AssociationSet Name="ImportedItemsFromPriceBook_CommerceTransformationPackages_ID_fk" Association="Self.ImportedItemsFromPriceBook_CommerceTransformationPackages_ID_fk">
            <End Role="CommerceTransformationPackages" EntitySet="CommerceTransformationPackages" />
            <End Role="ImportedItemsFromPriceBook" EntitySet="ImportedItemsFromPriceBook" />
          </AssociationSet>
          <AssociationSet Name="ImportedItemsFromPriceBook_ImportedPriceBooks_ID_fk" Association="Self.ImportedItemsFromPriceBook_ImportedPriceBooks_ID_fk">
            <End Role="ImportedPriceBooks" EntitySet="ImportedPriceBooks" />
            <End Role="ImportedItemsFromPriceBook" EntitySet="ImportedItemsFromPriceBook" />
          </AssociationSet>
          <AssociationSet Name="IngramProductPriceOverride_IngramProducts_ID_fk" Association="Self.IngramProductPriceOverride_IngramProducts_ID_fk">
            <End Role="IngramProducts" EntitySet="IngramProducts" />
            <End Role="IngramProductPriceOverride" EntitySet="IngramProductPriceOverride" />
          </AssociationSet>
          <AssociationSet Name="IngramProductToNopCategoryMapping_IngramProducts_ID_fk" Association="Self.IngramProductToNopCategoryMapping_IngramProducts_ID_fk">
            <End Role="IngramProducts" EntitySet="IngramProducts" />
            <End Role="IngramProductToNopCategoryMapping" EntitySet="IngramProductToNopCategoryMapping" />
          </AssociationSet>
          <AssociationSet Name="KqmProductPriceOverride_KQMProducts_ID_fk" Association="Self.KqmProductPriceOverride_KQMProducts_ID_fk">
            <End Role="KQMProducts" EntitySet="KQMProducts" />
            <End Role="KqmProductPriceOverride" EntitySet="KqmProductPriceOverride" />
          </AssociationSet>
          <AssociationSet Name="KqmProductToNopCategoryMapping_KQMProducts_ID_fk" Association="Self.KqmProductToNopCategoryMapping_KQMProducts_ID_fk">
            <End Role="KQMProducts" EntitySet="KQMProducts" />
            <End Role="KqmProductToNopCategoryMapping" EntitySet="KqmProductToNopCategoryMapping" />
          </AssociationSet>
          <AssociationSet Name="NewlyImportedPriceBookItems_ImportedItemsFromPriceBook_ID_fk" Association="Self.NewlyImportedPriceBookItems_ImportedItemsFromPriceBook_ID_fk">
            <End Role="ImportedItemsFromPriceBook" EntitySet="ImportedItemsFromPriceBook" />
            <End Role="NewlyImportedPriceBookItems" EntitySet="NewlyImportedPriceBookItems" />
          </AssociationSet>
          <AssociationSet Name="NopCategories_CommerceTransformationPackages_ID_fk" Association="Self.NopCategories_CommerceTransformationPackages_ID_fk">
            <End Role="CommerceTransformationPackages" EntitySet="CommerceTransformationPackages" />
            <End Role="NopCategories" EntitySet="NopCategories" />
          </AssociationSet>
          <AssociationSet Name="PreflightLog_ProductSource_ID_fk" Association="Self.PreflightLog_ProductSource_ID_fk">
            <End Role="ProductSource" EntitySet="ProductSource" />
            <End Role="PreflightLog" EntitySet="PreflightLog" />
          </AssociationSet>
          <AssociationSet Name="PriceBookImportedItemToNopCategoryMapping_ImportedItemsFromPriceBook_ID_fk" Association="Self.PriceBookImportedItemToNopCategoryMapping_ImportedItemsFromPriceBook_ID_fk">
            <End Role="ImportedItemsFromPriceBook" EntitySet="ImportedItemsFromPriceBook" />
            <End Role="PriceBookImportedItemToNopCategoryMapping" EntitySet="PriceBookImportedItemToNopCategoryMapping" />
          </AssociationSet>
          <AssociationSet Name="PriceBookItemCostPriceOverride_ImportedItemsFromPriceBook_ID_fk" Association="Self.PriceBookItemCostPriceOverride_ImportedItemsFromPriceBook_ID_fk">
            <End Role="ImportedItemsFromPriceBook" EntitySet="ImportedItemsFromPriceBook" />
            <End Role="PriceBookItemCostPriceOverride" EntitySet="PriceBookItemCostPriceOverride" />
          </AssociationSet>
          <AssociationSet Name="PriceBookItemImages_ImportedItemsFromPriceBook_ID_fk" Association="Self.PriceBookItemImages_ImportedItemsFromPriceBook_ID_fk">
            <End Role="ImportedItemsFromPriceBook" EntitySet="ImportedItemsFromPriceBook" />
            <End Role="PriceBookItemImages" EntitySet="PriceBookItemImages" />
          </AssociationSet>
          <AssociationSet Name="PriceBookItemSellPriceOverride_ImportedItemsFromPriceBook_ID_fk" Association="Self.PriceBookItemSellPriceOverride_ImportedItemsFromPriceBook_ID_fk">
            <End Role="ImportedItemsFromPriceBook" EntitySet="ImportedItemsFromPriceBook" />
            <End Role="PriceBookItemSellPriceOverride" EntitySet="PriceBookItemSellPriceOverride" />
          </AssociationSet>
          <AssociationSet Name="PriceBookToNopCategoryMapping_ImportedPriceBooks_ID_fk" Association="Self.PriceBookToNopCategoryMapping_ImportedPriceBooks_ID_fk">
            <End Role="ImportedPriceBooks" EntitySet="ImportedPriceBooks" />
            <End Role="PriceBookToNopCategoryMapping" EntitySet="PriceBookToNopCategoryMapping" />
          </AssociationSet>
          <AssociationSet Name="ProductSouceImages_ProductSource_ID_fk" Association="Self.ProductSouceImages_ProductSource_ID_fk">
            <End Role="ProductSource" EntitySet="ProductSource" />
            <End Role="ProductSourceImages" EntitySet="ProductSourceImages" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk" Association="Self.ProductSource_PreflightFlagStatus_ID_fk">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_2" Association="Self.ProductSource_PreflightFlagStatus_ID_fk_2">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_3" Association="Self.ProductSource_PreflightFlagStatus_ID_fk_3">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_4" Association="Self.ProductSource_PreflightFlagStatus_ID_fk_4">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_5" Association="Self.ProductSource_PreflightFlagStatus_ID_fk_5">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_6" Association="Self.ProductSource_PreflightFlagStatus_ID_fk_6">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_7" Association="Self.ProductSource_PreflightFlagStatus_ID_fk_7">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_8" Association="Self.ProductSource_PreflightFlagStatus_ID_fk_8">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_9" Association="Self.ProductSource_PreflightFlagStatus_ID_fk_9">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_ProductSourceStatus_ID_fk" Association="Self.ProductSource_ProductSourceStatus_ID_fk">
            <End Role="ProductSourceStatus" EntitySet="ProductSourceStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSourceCostPriceOverride_ProductSource_ID_fk" Association="Self.ProductSourceCostPriceOverride_ProductSource_ID_fk">
            <End Role="ProductSource" EntitySet="ProductSource" />
            <End Role="ProductSourceCostPriceOverride" EntitySet="ProductSourceCostPriceOverride" />
          </AssociationSet>
          <AssociationSet Name="ProductSourceSellPriceOverride_ProductSource_ID_fk" Association="Self.ProductSourceSellPriceOverride_ProductSource_ID_fk">
            <End Role="ProductSource" EntitySet="ProductSource" />
            <End Role="ProductSourceSellPriceOverride" EntitySet="ProductSourceSellPriceOverride" />
          </AssociationSet>
          <AssociationSet Name="ProductSourceTypeToNopCategoryMapping_ProductSourceList_ID_fk" Association="Self.ProductSourceTypeToNopCategoryMapping_ProductSourceList_ID_fk">
            <End Role="ProductSourceList" EntitySet="ProductSourceList" />
            <End Role="ProductSourceTypeToNopCategoryMapping" EntitySet="ProductSourceTypeToNopCategoryMapping" />
          </AssociationSet>
          <AssociationSet Name="TakeoffLog_ProductSource_ID_fk" Association="Self.TakeoffLog_ProductSource_ID_fk">
            <End Role="ProductSource" EntitySet="ProductSource" />
            <End Role="TakeoffLog" EntitySet="TakeoffLog" />
          </AssociationSet>
        </EntityContainer>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="EdunetDatafeedsModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="KQMProductSyncs">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="SyncDateTime" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="NewProductsSynced" Type="Int32" Nullable="false" />
        </EntityType>
        <EntityContainer Name="EdunetDatafeedsEntities" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="KQMProductSyncs" EntityType="Self.KQMProductSyncs" />
          <EntitySet Name="CommerceTransformationPackages" EntityType="EdunetDatafeedsModel.CommerceTransformationPackages" />
          <EntitySet Name="KQMPendingProductSKUs" EntityType="EdunetDatafeedsModel.KQMPendingProductSKUs" />
          <EntitySet Name="KQMBrands" EntityType="EdunetDatafeedsModel.KQMBrands" />
          <EntitySet Name="PriceBookAndKQMMultiplexedProducts" EntityType="EdunetDatafeedsModel.PriceBookAndKQMMultiplexedProducts" />
          <EntitySet Name="KqmProductImages" EntityType="EdunetDatafeedsModel.KqmProductImages" />
          <EntitySet Name="KqmQuoteToNopCategoryMapping" EntityType="EdunetDatafeedsModel.KqmQuoteToNopCategoryMapping" />
          <EntitySet Name="KqmProductToNopCategoryMapping" EntityType="EdunetDatafeedsModel.KqmProductToNopCategoryMapping" />
          <EntitySet Name="KqmProductSuppliers" EntityType="EdunetDatafeedsModel.KqmProductSuppliers" />
          <EntitySet Name="IngramProductToNopCategoryMapping" EntityType="EdunetDatafeedsModel.IngramProductToNopCategoryMapping" />
          <EntitySet Name="NopCategories" EntityType="EdunetDatafeedsModel.NopCategories" />
          <AssociationSet Name="NopCategories_CommerceTransformationPackages_ID_fk" Association="EdunetDatafeedsModel.NopCategories_CommerceTransformationPackages_ID_fk">
            <End Role="CommerceTransformationPackages" EntitySet="CommerceTransformationPackages" />
            <End Role="NopCategories" EntitySet="NopCategories" />
          </AssociationSet>
          <EntitySet Name="KqmProductPriceOverride" EntityType="EdunetDatafeedsModel.KqmProductPriceOverride" />
          <EntitySet Name="IngramProductPriceOverride" EntityType="EdunetDatafeedsModel.IngramProductPriceOverride" />
          <EntitySet Name="PriceBookImportedItemToNopCategoryMapping" EntityType="EdunetDatafeedsModel.PriceBookImportedItemToNopCategoryMapping" />
          <EntitySet Name="PriceBookToNopCategoryMapping" EntityType="EdunetDatafeedsModel.PriceBookToNopCategoryMapping" />
          <EntitySet Name="IngramProducts" EntityType="EdunetDatafeedsModel.IngramProducts" />
          <AssociationSet Name="IngramProductPriceOverride_IngramProducts_ID_fk" Association="EdunetDatafeedsModel.IngramProductPriceOverride_IngramProducts_ID_fk">
            <End Role="IngramProducts" EntitySet="IngramProducts" />
            <End Role="IngramProductPriceOverride" EntitySet="IngramProductPriceOverride" />
          </AssociationSet>
          <AssociationSet Name="IngramProductToNopCategoryMapping_IngramProducts_ID_fk" Association="EdunetDatafeedsModel.IngramProductToNopCategoryMapping_IngramProducts_ID_fk">
            <End Role="IngramProducts" EntitySet="IngramProducts" />
            <End Role="IngramProductToNopCategoryMapping" EntitySet="IngramProductToNopCategoryMapping" />
          </AssociationSet>
          <EntitySet Name="IngramCategoryToNopCategoryMapping" EntityType="EdunetDatafeedsModel.IngramCategoryToNopCategoryMapping" />
          <EntitySet Name="MappedIngramProductsForNopCategory" EntityType="EdunetDatafeedsModel.MappedIngramProductsForNopCategory" />
          <EntitySet Name="KqmDeletedProductsFromQuote" EntityType="EdunetDatafeedsModel.KqmDeletedProductsFromQuote" />
          <EntitySet Name="KQMProducts" EntityType="EdunetDatafeedsModel.KQMProducts" />
          <AssociationSet Name="KqmProductPriceOverride_KQMProducts_ID_fk" Association="EdunetDatafeedsModel.KqmProductPriceOverride_KQMProducts_ID_fk">
            <End Role="KQMProducts" EntitySet="KQMProducts" />
            <End Role="KqmProductPriceOverride" EntitySet="KqmProductPriceOverride" />
          </AssociationSet>
          <AssociationSet Name="KqmProductToNopCategoryMapping_KQMProducts_ID_fk" Association="EdunetDatafeedsModel.KqmProductToNopCategoryMapping_KQMProducts_ID_fk">
            <End Role="KQMProducts" EntitySet="KQMProducts" />
            <End Role="KqmProductToNopCategoryMapping" EntitySet="KqmProductToNopCategoryMapping" />
          </AssociationSet>
          <EntitySet Name="KqmQuotes" EntityType="EdunetDatafeedsModel.KqmQuotes" />
          <EntitySet Name="MappedKqmProductsForNopCategory" EntityType="EdunetDatafeedsModel.MappedKqmProductsForNopCategory" />
          <EntitySet Name="ProductsCheaperAtCompetitor" EntityType="EdunetDatafeedsModel.ProductsCheaperAtCompetitor" />
          <EntitySet Name="ProductSyncLog" EntityType="EdunetDatafeedsModel.ProductSyncLog" />
          <EntitySet Name="ImportedPriceBooks" EntityType="EdunetDatafeedsModel.ImportedPriceBooks" />
          <AssociationSet Name="PriceBookToNopCategoryMapping_ImportedPriceBooks_ID_fk" Association="EdunetDatafeedsModel.PriceBookToNopCategoryMapping_ImportedPriceBooks_ID_fk">
            <End Role="ImportedPriceBooks" EntitySet="ImportedPriceBooks" />
            <End Role="PriceBookToNopCategoryMapping" EntitySet="PriceBookToNopCategoryMapping" />
          </AssociationSet>
          <EntitySet Name="ProductSourceList" EntityType="EdunetDatafeedsModel.ProductSourceList" />
          <EntitySet Name="ProductSourceTypeToNopCategoryMapping" EntityType="EdunetDatafeedsModel.ProductSourceTypeToNopCategoryMapping" />
          <AssociationSet Name="ProductSourceTypeToNopCategoryMapping_ProductSourceList_ID_fk" Association="EdunetDatafeedsModel.ProductSourceTypeToNopCategoryMapping_ProductSourceList_ID_fk">
            <End Role="ProductSourceList" EntitySet="ProductSourceList" />
            <End Role="ProductSourceTypeToNopCategoryMapping" EntitySet="ProductSourceTypeToNopCategoryMapping" />
          </AssociationSet>
          <EntitySet Name="ProductSourceStatus" EntityType="EdunetDatafeedsModel.ProductSourceStatus" />
          <EntitySet Name="ProductSourceImages" EntityType="EdunetDatafeedsModel.ProductSourceImages" />
          <EntitySet Name="FandoogleReflectorCache" EntityType="EdunetDatafeedsModel.FandoogleReflectorCache" />
          <EntitySet Name="PriceBookItemSellPriceOverride" EntityType="EdunetDatafeedsModel.PriceBookItemSellPriceOverride" />
          <EntitySet Name="AIGeneratedProductInfo" EntityType="EdunetDatafeedsModel.AIGeneratedProductInfo" />
          <EntitySet Name="PreflightLog" EntityType="EdunetDatafeedsModel.PreflightLog" />
          <EntitySet Name="TakeoffLog" EntityType="EdunetDatafeedsModel.TakeoffLog" />
          <EntitySet Name="ProductSourceTypesForNopCategory" EntityType="EdunetDatafeedsModel.ProductSourceTypesForNopCategory" />
          <EntitySet Name="ProductSourceSellPriceOverride" EntityType="EdunetDatafeedsModel.ProductSourceSellPriceOverride" />
          <FunctionImport Name="GetProductsCheaperAtCompetitorCount" ReturnType="Collection(Int32)" />
          <FunctionImport Name="GetDisabledProductSourceCount" ReturnType="Collection(Int32)" />
          <EntitySet Name="DisabledProductsSanitised" EntityType="EdunetDatafeedsModel.DisabledProductsSanitised" />
          <EntitySet Name="DisabledPriceBookProductsSanitised" EntityType="EdunetDatafeedsModel.DisabledPriceBookProductsSanitised" />
          <EntitySet Name="NewlyImportedPriceBookItems" EntityType="EdunetDatafeedsModel.NewlyImportedPriceBookItems" />
          <EntitySet Name="MappedPriceBookItemsForNopCategory" EntityType="EdunetDatafeedsModel.MappedPriceBookItemsForNopCategory" />
          <EntitySet Name="ImportedItemsFromPriceBook" EntityType="EdunetDatafeedsModel.ImportedItemsFromPriceBook" />
          <AssociationSet Name="ImportedItemsFromPriceBook_CommerceTransformationPackages_ID_fk" Association="EdunetDatafeedsModel.ImportedItemsFromPriceBook_CommerceTransformationPackages_ID_fk">
            <End Role="CommerceTransformationPackages" EntitySet="CommerceTransformationPackages" />
            <End Role="ImportedItemsFromPriceBook" EntitySet="ImportedItemsFromPriceBook" />
          </AssociationSet>
          <AssociationSet Name="ImportedItemsFromPriceBook_ImportedPriceBooks_ID_fk" Association="EdunetDatafeedsModel.ImportedItemsFromPriceBook_ImportedPriceBooks_ID_fk">
            <End Role="ImportedPriceBooks" EntitySet="ImportedPriceBooks" />
            <End Role="ImportedItemsFromPriceBook" EntitySet="ImportedItemsFromPriceBook" />
          </AssociationSet>
          <AssociationSet Name="NewlyImportedPriceBookItems_ImportedItemsFromPriceBook_ID_fk" Association="EdunetDatafeedsModel.NewlyImportedPriceBookItems_ImportedItemsFromPriceBook_ID_fk">
            <End Role="ImportedItemsFromPriceBook" EntitySet="ImportedItemsFromPriceBook" />
            <End Role="NewlyImportedPriceBookItems" EntitySet="NewlyImportedPriceBookItems" />
          </AssociationSet>
          <AssociationSet Name="PriceBookImportedItemToNopCategoryMapping_ImportedItemsFromPriceBook_ID_fk" Association="EdunetDatafeedsModel.PriceBookImportedItemToNopCategoryMapping_ImportedItemsFromPriceBook_ID_fk">
            <End Role="ImportedItemsFromPriceBook" EntitySet="ImportedItemsFromPriceBook" />
            <End Role="PriceBookImportedItemToNopCategoryMapping" EntitySet="PriceBookImportedItemToNopCategoryMapping" />
          </AssociationSet>
          <AssociationSet Name="PriceBookItemSellPriceOverride_ImportedItemsFromPriceBook_ID_fk" Association="EdunetDatafeedsModel.PriceBookItemSellPriceOverride_ImportedItemsFromPriceBook_ID_fk">
            <End Role="ImportedItemsFromPriceBook" EntitySet="ImportedItemsFromPriceBook" />
            <End Role="PriceBookItemSellPriceOverride" EntitySet="PriceBookItemSellPriceOverride" />
          </AssociationSet>
          <EntitySet Name="PreflightFlagStatus" EntityType="EdunetDatafeedsModel.PreflightFlagStatus" />
          <EntitySet Name="PriceBookItemImages" EntityType="EdunetDatafeedsModel.PriceBookItemImages" />
          <AssociationSet Name="PriceBookItemImages_ImportedItemsFromPriceBook_ID_fk" Association="EdunetDatafeedsModel.PriceBookItemImages_ImportedItemsFromPriceBook_ID_fk">
            <End Role="ImportedItemsFromPriceBook" EntitySet="ImportedItemsFromPriceBook" />
            <End Role="PriceBookItemImages" EntitySet="PriceBookItemImages" />
          </AssociationSet>
          <EntitySet Name="ProductsCheaperAtCompetitorSanitised" EntityType="EdunetDatafeedsModel.ProductsCheaperAtCompetitorSanitised" />
          <EntitySet Name="PriceBookItemCostPriceOverride" EntityType="EdunetDatafeedsModel.PriceBookItemCostPriceOverride" />
          <EntitySet Name="ProductSourceCostPriceOverride" EntityType="EdunetDatafeedsModel.ProductSourceCostPriceOverride" />
          <AssociationSet Name="PriceBookItemCostPriceOverride_ImportedItemsFromPriceBook_ID_fk" Association="EdunetDatafeedsModel.PriceBookItemCostPriceOverride_ImportedItemsFromPriceBook_ID_fk">
            <End Role="ImportedItemsFromPriceBook" EntitySet="ImportedItemsFromPriceBook" />
            <End Role="PriceBookItemCostPriceOverride" EntitySet="PriceBookItemCostPriceOverride" />
          </AssociationSet>
          <EntitySet Name="ProductSource" EntityType="EdunetDatafeedsModel.ProductSource" />
          <EntitySet Name="ProductSourcesForNopCategory" EntityType="EdunetDatafeedsModel.ProductSourcesForNopCategory" />
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk" Association="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_2" Association="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_2">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_3" Association="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_3">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_4" Association="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_4">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_5" Association="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_5">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_6" Association="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_6">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_7" Association="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_7">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_8" Association="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_8">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_PreflightFlagStatus_ID_fk_9" Association="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_9">
            <End Role="PreflightFlagStatus" EntitySet="PreflightFlagStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="PreflightLog_ProductSource_ID_fk" Association="EdunetDatafeedsModel.PreflightLog_ProductSource_ID_fk">
            <End Role="ProductSource" EntitySet="ProductSource" />
            <End Role="PreflightLog" EntitySet="PreflightLog" />
          </AssociationSet>
          <AssociationSet Name="ProductSouceImages_ProductSource_ID_fk" Association="EdunetDatafeedsModel.ProductSouceImages_ProductSource_ID_fk">
            <End Role="ProductSource" EntitySet="ProductSource" />
            <End Role="ProductSourceImages" EntitySet="ProductSourceImages" />
          </AssociationSet>
          <AssociationSet Name="ProductSource_ProductSourceStatus_ID_fk" Association="EdunetDatafeedsModel.ProductSource_ProductSourceStatus_ID_fk">
            <End Role="ProductSourceStatus" EntitySet="ProductSourceStatus" />
            <End Role="ProductSource" EntitySet="ProductSource" />
          </AssociationSet>
          <AssociationSet Name="ProductSourceCostPriceOverride_ProductSource_ID_fk" Association="EdunetDatafeedsModel.ProductSourceCostPriceOverride_ProductSource_ID_fk">
            <End Role="ProductSource" EntitySet="ProductSource" />
            <End Role="ProductSourceCostPriceOverride" EntitySet="ProductSourceCostPriceOverride" />
          </AssociationSet>
          <AssociationSet Name="ProductSourceSellPriceOverride_ProductSource_ID_fk" Association="EdunetDatafeedsModel.ProductSourceSellPriceOverride_ProductSource_ID_fk">
            <End Role="ProductSource" EntitySet="ProductSource" />
            <End Role="ProductSourceSellPriceOverride" EntitySet="ProductSourceSellPriceOverride" />
          </AssociationSet>
          <AssociationSet Name="TakeoffLog_ProductSource_ID_fk" Association="EdunetDatafeedsModel.TakeoffLog_ProductSource_ID_fk">
            <End Role="ProductSource" EntitySet="ProductSource" />
            <End Role="TakeoffLog" EntitySet="TakeoffLog" />
          </AssociationSet>
          </EntityContainer>
        <EntityType Name="CommerceTransformationPackages">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PackageName" Type="String" Nullable="false" MaxLength="150" FixedLength="false" Unicode="false" />
          <Property Name="PackageDescription" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="false" />
          <Property Name="PercentMarkup" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="EnablePercentMarkup" Type="Boolean" Nullable="false" />
          <Property Name="EnableAI" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="NopCategories" Relationship="EdunetDatafeedsModel.NopCategories_CommerceTransformationPackages_ID_fk" FromRole="CommerceTransformationPackages" ToRole="NopCategories" />
          <NavigationProperty Name="ImportedItemsFromPriceBook" Relationship="EdunetDatafeedsModel.ImportedItemsFromPriceBook_CommerceTransformationPackages_ID_fk" FromRole="CommerceTransformationPackages" ToRole="ImportedItemsFromPriceBook" />
        </EntityType>
        <EntityType Name="KQMPendingProductSKUs">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ProductSKU" Type="String" Nullable="false" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="RequestingUser" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="RequestedDateTime" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Accepted" Type="Boolean" Nullable="false" />
          <Property Name="Rejected" Type="Boolean" Nullable="false" />
        </EntityType>
        <EntityType Name="KQMBrands">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="brandID" Type="Int32" Nullable="false" />
          <Property Name="name" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="imageFilename" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="createDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="modifiedDate" Type="DateTime" Nullable="false" Precision="3" />
        </EntityType>
        <EntityType Name="PriceBookAndKQMMultiplexedProducts">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="ProductID" />
            <PropertyRef Name="SKU" />
            <PropertyRef Name="ProductName" />
            <PropertyRef Name="Price" />
            <PropertyRef Name="CategoryID" />
            <PropertyRef Name="SupplierID" />
            <PropertyRef Name="Brand" />
            <PropertyRef Name="Quantity" />
            <PropertyRef Name="IsPublished" />
            <PropertyRef Name="Source" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" />
          <Property Name="ProductID" Type="Int32" Nullable="false" />
          <Property Name="SKU" Type="String" Nullable="false" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="ProductName" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ProductDescription" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Price" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="CategoryID" Type="Int32" Nullable="false" />
          <Property Name="SupplierID" Type="Int32" Nullable="false" />
          <Property Name="Brand" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="Quantity" Type="Int32" Nullable="false" />
          <Property Name="IsPublished" Type="Boolean" Nullable="false" />
          <Property Name="AppliedTransformationPackage" Type="Int32" />
          <Property Name="Source" Type="Int32" Nullable="false" />
        </EntityType>
        <EntityType Name="KqmProductImages">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="KqmProductId" Type="Int32" Nullable="false" />
          <Property Name="Image" Type="Binary" Nullable="false" MaxLength="Max" FixedLength="false" />
          <Property Name="ImageUrl" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="KqmQuoteToNopCategoryMapping">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="KqmQuoteId" Type="Int32" Nullable="false" />
          <Property Name="NopCategoryId" Type="Int32" Nullable="false" />
        </EntityType>
        <EntityType Name="KqmProductToNopCategoryMapping">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="KqmProductId" Type="Int32" Nullable="false" />
          <Property Name="NopCategoryId" Type="Int32" Nullable="false" />
          <NavigationProperty Name="KQMProducts" Relationship="EdunetDatafeedsModel.KqmProductToNopCategoryMapping_KQMProducts_ID_fk" FromRole="KqmProductToNopCategoryMapping" ToRole="KQMProducts" />
        </EntityType>
        <EntityType Name="KqmProductSuppliers">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="productID" Type="Int32" Nullable="false" />
          <Property Name="supplierID" Type="Int32" Nullable="false" />
          <Property Name="quantity" Type="Int32" Nullable="false" />
          <Property Name="cost" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="productNumber" Type="String" Nullable="false" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="createdDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="modifiedDate" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="IngramProductToNopCategoryMapping">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IngramProductId" Type="Int32" Nullable="false" />
          <Property Name="NopCategoryId" Type="Int32" Nullable="false" />
          <NavigationProperty Name="IngramProducts" Relationship="EdunetDatafeedsModel.IngramProductToNopCategoryMapping_IngramProducts_ID_fk" FromRole="IngramProductToNopCategoryMapping" ToRole="IngramProducts" />
        </EntityType>
        <EntityType Name="NopCategories">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CategoryId" Type="Int32" Nullable="false" />
          <Property Name="Name" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="Deleted" Type="Boolean" Nullable="false" />
          <Property Name="Published" Type="Boolean" Nullable="false" />
          <Property Name="CreatedOnUtc" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UpdatedOnUtc" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="ParentCategoryId" Type="Int32" Nullable="false" />
          <Property Name="AppliedTransformationPackage" Type="Int32" />
          <NavigationProperty Name="CommerceTransformationPackages" Relationship="EdunetDatafeedsModel.NopCategories_CommerceTransformationPackages_ID_fk" FromRole="NopCategories" ToRole="CommerceTransformationPackages" />
        </EntityType>
        <Association Name="NopCategories_CommerceTransformationPackages_ID_fk">
          <End Type="EdunetDatafeedsModel.CommerceTransformationPackages" Role="CommerceTransformationPackages" Multiplicity="0..1" />
          <End Type="EdunetDatafeedsModel.NopCategories" Role="NopCategories" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="CommerceTransformationPackages">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="NopCategories">
              <PropertyRef Name="AppliedTransformationPackage" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="KqmProductPriceOverride">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="KqmProductId" Type="Int32" Nullable="false" />
          <Property Name="Price" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <NavigationProperty Name="KQMProducts" Relationship="EdunetDatafeedsModel.KqmProductPriceOverride_KQMProducts_ID_fk" FromRole="KqmProductPriceOverride" ToRole="KQMProducts" />
        </EntityType>
        <EntityType Name="IngramProductPriceOverride">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IngramProductId" Type="Int32" Nullable="false" />
          <Property Name="Price" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <NavigationProperty Name="IngramProducts" Relationship="EdunetDatafeedsModel.IngramProductPriceOverride_IngramProducts_ID_fk" FromRole="IngramProductPriceOverride" ToRole="IngramProducts" />
        </EntityType>
        <EntityType Name="PriceBookImportedItemToNopCategoryMapping">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PriceBookItemId" Type="Int32" Nullable="false" />
          <Property Name="NopCategoryId" Type="Int32" Nullable="false" />
          <NavigationProperty Name="ImportedItemsFromPriceBook" Relationship="EdunetDatafeedsModel.PriceBookImportedItemToNopCategoryMapping_ImportedItemsFromPriceBook_ID_fk" FromRole="PriceBookImportedItemToNopCategoryMapping" ToRole="ImportedItemsFromPriceBook" />
        </EntityType>
        <EntityType Name="PriceBookToNopCategoryMapping">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PriceBookId" Type="Int32" Nullable="false" />
          <Property Name="NopCategoryId" Type="Int32" Nullable="false" />
          <NavigationProperty Name="ImportedPriceBooks" Relationship="EdunetDatafeedsModel.PriceBookToNopCategoryMapping_ImportedPriceBooks_ID_fk" FromRole="PriceBookToNopCategoryMapping" ToRole="ImportedPriceBooks" />
        </EntityType>
        <EntityType Name="IngramProducts">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IngramPartNumber" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="IngramPartDescription" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="CustomerPartNumber" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="VendorPartNumber" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="EANUPCCode" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Plant" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="VendorNumber" Type="Int32" />
          <Property Name="VendorName" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Size" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Weight" Type="Double" />
          <Property Name="Volume" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Unit" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CategoryID" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CustomerPrice" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="RetailPrice" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="AvailabilityFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="AvailableQuantity" Type="Int32" />
          <Property Name="BacklogInformation" Type="Int32" />
          <Property Name="BacklogETA" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="LicenseFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="BOMFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="WarrantyFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="BulkFreightFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MaterialLongDescription" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Length" Type="Double" />
          <Property Name="Width" Type="Double" />
          <Property Name="Height" Type="Double" />
          <Property Name="DimensionUnit" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="WeightUnit" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="VolumeUnit" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Category" Type="Int32" />
          <Property Name="MaterialCreationReasonCode" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MediaCode" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MaterialLanguageCode" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="SubstituteMaterial" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="SupersededMaterial" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="ManufacturerVendorNumber" Type="Int32" />
          <Property Name="SubCategory" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="ProductFamily" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="PurchasingVendor" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MaterialChangeCode" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="ActionCode" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="PriceStatus" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="NewMaterialFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="VendorSubrange" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CaseQty" Type="Int32" />
          <Property Name="PalletQty" Type="Int32" />
          <Property Name="DirectOrderIdentifier" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MaterialStatus" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Discontinued" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ReleaseDate" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="FulfilmentType" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MusicCopyrightFees" Type="Int32" />
          <Property Name="RecyclingFees" Type="Int32" />
          <Property Name="DocumentCopyrightFees" Type="Int32" />
          <Property Name="BatteryFees" Type="Int32" />
          <Property Name="CustomerPriceWithTax" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="RetailPriceWithTax" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="TaxPercent" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="DiscountInPercent" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CustomerReservationNumber" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CustomerReservationQty" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="AgreementID" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="LevelID" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Period" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Points" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CompanyCode" Type="Int32" />
          <Property Name="CompanyCodeCurrency" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CustomerCurrencyCode" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CustomerPriceChangeFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="SubstituteFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CreationReasonType" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CreationReasonValue" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Plant01AvailableQuantity" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Plant02AvailableQuantity" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="AppliedTransformationPackage" Type="Int32" />
          <Property Name="CategoryName" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="SubCategoryName" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="IngramProductPriceOverride" Relationship="EdunetDatafeedsModel.IngramProductPriceOverride_IngramProducts_ID_fk" FromRole="IngramProducts" ToRole="IngramProductPriceOverride" />
          <NavigationProperty Name="IngramProductToNopCategoryMapping" Relationship="EdunetDatafeedsModel.IngramProductToNopCategoryMapping_IngramProducts_ID_fk" FromRole="IngramProducts" ToRole="IngramProductToNopCategoryMapping" />
        </EntityType>
        <Association Name="IngramProductPriceOverride_IngramProducts_ID_fk">
          <End Type="EdunetDatafeedsModel.IngramProducts" Role="IngramProducts" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.IngramProductPriceOverride" Role="IngramProductPriceOverride" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="IngramProducts">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="IngramProductPriceOverride">
              <PropertyRef Name="IngramProductId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="IngramProductToNopCategoryMapping_IngramProducts_ID_fk">
          <End Type="EdunetDatafeedsModel.IngramProducts" Role="IngramProducts" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.IngramProductToNopCategoryMapping" Role="IngramProductToNopCategoryMapping" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="IngramProducts">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="IngramProductToNopCategoryMapping">
              <PropertyRef Name="IngramProductId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="IngramCategoryToNopCategoryMapping">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IngramCategoryName" Type="String" Nullable="false" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="NopCategoryId" Type="Int32" Nullable="false" />
        </EntityType>
        <EntityType Name="MappedIngramProductsForNopCategory">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="MappingType" />
            <PropertyRef Name="NopCategoryId" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" />
          <Property Name="IngramPartNumber" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="IngramPartDescription" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="CustomerPartNumber" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="VendorPartNumber" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="EANUPCCode" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Plant" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="VendorNumber" Type="Int32" />
          <Property Name="VendorName" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Size" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Weight" Type="Double" />
          <Property Name="Volume" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Unit" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CategoryID" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CustomerPrice" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="RetailPrice" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="AvailabilityFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="AvailableQuantity" Type="Int32" />
          <Property Name="BacklogInformation" Type="Int32" />
          <Property Name="BacklogETA" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="LicenseFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="BOMFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="WarrantyFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="BulkFreightFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MaterialLongDescription" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Length" Type="Double" />
          <Property Name="Width" Type="Double" />
          <Property Name="Height" Type="Double" />
          <Property Name="DimensionUnit" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="WeightUnit" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="VolumeUnit" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Category" Type="Int32" />
          <Property Name="MaterialCreationReasonCode" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MediaCode" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MaterialLanguageCode" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="SubstituteMaterial" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="SupersededMaterial" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="ManufacturerVendorNumber" Type="Int32" />
          <Property Name="SubCategory" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="ProductFamily" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="PurchasingVendor" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MaterialChangeCode" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="ActionCode" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="PriceStatus" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="NewMaterialFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="VendorSubrange" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CaseQty" Type="Int32" />
          <Property Name="PalletQty" Type="Int32" />
          <Property Name="DirectOrderIdentifier" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MaterialStatus" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Discontinued" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ReleaseDate" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="FulfilmentType" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MusicCopyrightFees" Type="Int32" />
          <Property Name="RecyclingFees" Type="Int32" />
          <Property Name="DocumentCopyrightFees" Type="Int32" />
          <Property Name="BatteryFees" Type="Int32" />
          <Property Name="CustomerPriceWithTax" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="RetailPriceWithTax" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="TaxPercent" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="DiscountInPercent" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CustomerReservationNumber" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CustomerReservationQty" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="AgreementID" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="LevelID" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Period" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Points" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CompanyCode" Type="Int32" />
          <Property Name="CompanyCodeCurrency" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CustomerCurrencyCode" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CustomerPriceChangeFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="SubstituteFlag" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CreationReasonType" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CreationReasonValue" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Plant01AvailableQuantity" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="Plant02AvailableQuantity" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CategoryName" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="SubCategoryName" Type="String" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="AppliedTransformationPackage" Type="Int32" />
          <Property Name="MappingType" Type="String" Nullable="false" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="NopCategoryId" Type="Int32" Nullable="false" />
          <Property Name="PriceOverride" Type="Decimal" Precision="19" Scale="4" />
        </EntityType>
        <EntityType Name="KqmDeletedProductsFromQuote">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="KqmProductId" Type="Int32" Nullable="false" />
          <Property Name="KqmQuoteId" Type="Int32" Nullable="false" />
        </EntityType>
        <EntityType Name="KQMProducts">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IsPulished" Type="Boolean" Nullable="false" />
          <Property Name="supplierID" Type="Int32" Nullable="false" />
          <Property Name="productID" Type="Int32" Nullable="false" />
          <Property Name="quantity" Type="Int32" Nullable="false" />
          <Property Name="cost" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="createdDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="modifiedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="recurringType" Type="Int32" Nullable="false" />
          <Property Name="productNumber" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="url" Type="String" Nullable="false" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="manufacturerPartNumber" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="type" Type="Int32" Nullable="false" />
          <Property Name="title" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="brandID" Type="Int32" Nullable="false" />
          <Property Name="weight" Type="Int32" Nullable="false" />
          <Property Name="categoryID" Type="Int32" Nullable="false" />
          <Property Name="price" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="retailPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="isHidden" Type="Boolean" Nullable="false" />
          <Property Name="isActive" Type="Boolean" Nullable="false" />
          <Property Name="isRecommended" Type="Boolean" Nullable="false" />
          <Property Name="isSerialized" Type="Boolean" Nullable="false" />
          <Property Name="isRecurring" Type="Boolean" Nullable="false" />
          <Property Name="AppliedTransformationPackage" Type="Int32" />
          <Property Name="KqmQuoteId" Type="Int32" />
          <Property Name="ShortDescription" Type="String" MaxLength="2000" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="KqmProductPriceOverride" Relationship="EdunetDatafeedsModel.KqmProductPriceOverride_KQMProducts_ID_fk" FromRole="KQMProducts" ToRole="KqmProductPriceOverride" />
          <NavigationProperty Name="KqmProductToNopCategoryMapping" Relationship="EdunetDatafeedsModel.KqmProductToNopCategoryMapping_KQMProducts_ID_fk" FromRole="KQMProducts" ToRole="KqmProductToNopCategoryMapping" />
        </EntityType>
        <Association Name="KqmProductPriceOverride_KQMProducts_ID_fk">
          <End Type="EdunetDatafeedsModel.KQMProducts" Role="KQMProducts" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.KqmProductPriceOverride" Role="KqmProductPriceOverride" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="KQMProducts">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="KqmProductPriceOverride">
              <PropertyRef Name="KqmProductId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="KqmProductToNopCategoryMapping_KQMProducts_ID_fk">
          <End Type="EdunetDatafeedsModel.KQMProducts" Role="KQMProducts" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.KqmProductToNopCategoryMapping" Role="KqmProductToNopCategoryMapping" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="KQMProducts">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="KqmProductToNopCategoryMapping">
              <PropertyRef Name="KqmProductId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="KqmQuotes">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="quoteId" Type="Int32" Nullable="false" />
          <Property Name="salesOrderId" Type="Int32" Nullable="false" />
          <Property Name="code" Type="String" Nullable="false" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="quoteNumber" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="title" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="status" Type="Int32" Nullable="false" />
          <Property Name="privateNote" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ownerEmployeeID" Type="Int32" Nullable="false" />
          <Property Name="customerID" Type="Int32" Nullable="false" />
          <Property Name="contactName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="contactEmail" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="deliveryType" Type="Int32" Nullable="false" />
          <Property Name="deliveryCompany" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="deliveryContact" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="deliveryPhone" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="deliveryAddress" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="deliveryCity" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="deliveryState" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="deliveryPostalCode" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="deliveryCountry" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="deliveryInstruction" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="deliveryAmount" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="deliveryTax" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="deliveryTaxRate" Type="Int32" Nullable="false" />
          <Property Name="expiryDate" Type="DateTime" Precision="3" />
          <Property Name="createdDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="modifiedDate" Type="DateTime" Precision="3" />
          <Property Name="AutoMapNewProducts" Type="Boolean" Nullable="false" />
        </EntityType>
        <EntityType Name="MappedKqmProductsForNopCategory">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="IsPulished" />
            <PropertyRef Name="supplierID" />
            <PropertyRef Name="productID" />
            <PropertyRef Name="quantity" />
            <PropertyRef Name="cost" />
            <PropertyRef Name="createdDate" />
            <PropertyRef Name="modifiedDate" />
            <PropertyRef Name="recurringType" />
            <PropertyRef Name="productNumber" />
            <PropertyRef Name="url" />
            <PropertyRef Name="manufacturerPartNumber" />
            <PropertyRef Name="type" />
            <PropertyRef Name="title" />
            <PropertyRef Name="brandID" />
            <PropertyRef Name="weight" />
            <PropertyRef Name="categoryID" />
            <PropertyRef Name="price" />
            <PropertyRef Name="retailPrice" />
            <PropertyRef Name="isHidden" />
            <PropertyRef Name="isActive" />
            <PropertyRef Name="isRecommended" />
            <PropertyRef Name="isSerialized" />
            <PropertyRef Name="isRecurring" />
            <PropertyRef Name="MappingType" />
            <PropertyRef Name="NopCategoryId" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" />
          <Property Name="IsPulished" Type="Boolean" Nullable="false" />
          <Property Name="supplierID" Type="Int32" Nullable="false" />
          <Property Name="productID" Type="Int32" Nullable="false" />
          <Property Name="quantity" Type="Int32" Nullable="false" />
          <Property Name="cost" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="createdDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="modifiedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="recurringType" Type="Int32" Nullable="false" />
          <Property Name="productNumber" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="url" Type="String" Nullable="false" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="manufacturerPartNumber" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="type" Type="Int32" Nullable="false" />
          <Property Name="title" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="description" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ShortDescription" Type="String" MaxLength="2000" FixedLength="false" Unicode="true" />
          <Property Name="brandID" Type="Int32" Nullable="false" />
          <Property Name="weight" Type="Int32" Nullable="false" />
          <Property Name="categoryID" Type="Int32" Nullable="false" />
          <Property Name="price" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="retailPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="isHidden" Type="Boolean" Nullable="false" />
          <Property Name="isActive" Type="Boolean" Nullable="false" />
          <Property Name="isRecommended" Type="Boolean" Nullable="false" />
          <Property Name="isSerialized" Type="Boolean" Nullable="false" />
          <Property Name="isRecurring" Type="Boolean" Nullable="false" />
          <Property Name="KqmQuoteId" Type="Int32" />
          <Property Name="AppliedTransformationPackage" Type="Int32" />
          <Property Name="MappingType" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="NopCategoryId" Type="Int32" Nullable="false" />
          <Property Name="PriceOverride" Type="Decimal" Precision="19" Scale="4" />
        </EntityType>
        <EntityType Name="ProductsCheaperAtCompetitor">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ProductSku" Type="String" Nullable="false" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="CompetitorPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
        </EntityType>
        <EntityType Name="ProductSyncLog">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ProductId" Type="Int32" />
          <Property Name="SyncDateTime" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Message" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="ImportedPriceBooks">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" Nullable="false" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="ImportedDateTime" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Supplier" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="AutoMapNewItems" Type="Boolean" Nullable="false" />
          <Property Name="Archived" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="PriceBookToNopCategoryMapping" Relationship="EdunetDatafeedsModel.PriceBookToNopCategoryMapping_ImportedPriceBooks_ID_fk" FromRole="ImportedPriceBooks" ToRole="PriceBookToNopCategoryMapping" />
          <NavigationProperty Name="ImportedItemsFromPriceBook" Relationship="EdunetDatafeedsModel.ImportedItemsFromPriceBook_ImportedPriceBooks_ID_fk" FromRole="ImportedPriceBooks" ToRole="ImportedItemsFromPriceBook" />
        </EntityType>
        <Association Name="PriceBookToNopCategoryMapping_ImportedPriceBooks_ID_fk">
          <End Type="EdunetDatafeedsModel.ImportedPriceBooks" Role="ImportedPriceBooks" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.PriceBookToNopCategoryMapping" Role="PriceBookToNopCategoryMapping" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ImportedPriceBooks">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="PriceBookToNopCategoryMapping">
              <PropertyRef Name="PriceBookId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="ProductSourceList">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" Nullable="false" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="FileName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="ProductSourceTypeToNopCategoryMapping" Relationship="EdunetDatafeedsModel.ProductSourceTypeToNopCategoryMapping_ProductSourceList_ID_fk" FromRole="ProductSourceList" ToRole="ProductSourceTypeToNopCategoryMapping" />
        </EntityType>
        <EntityType Name="ProductSourceTypeToNopCategoryMapping">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="SourceTypeId" Type="Int32" Nullable="false" />
          <Property Name="NopCategoryId" Type="Int32" Nullable="false" />
          <NavigationProperty Name="ProductSourceList" Relationship="EdunetDatafeedsModel.ProductSourceTypeToNopCategoryMapping_ProductSourceList_ID_fk" FromRole="ProductSourceTypeToNopCategoryMapping" ToRole="ProductSourceList" />
        </EntityType>
        <Association Name="ProductSourceTypeToNopCategoryMapping_ProductSourceList_ID_fk">
          <End Type="EdunetDatafeedsModel.ProductSourceList" Role="ProductSourceList" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ProductSourceTypeToNopCategoryMapping" Role="ProductSourceTypeToNopCategoryMapping" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ProductSourceList">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSourceTypeToNopCategoryMapping">
              <PropertyRef Name="SourceTypeId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="ProductSourceStatus">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="Publishable" Type="Boolean" Nullable="false" />
          <Property Name="Active" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="ProductSource" Relationship="EdunetDatafeedsModel.ProductSource_ProductSourceStatus_ID_fk" FromRole="ProductSourceStatus" ToRole="ProductSource" />
        </EntityType>
        <EntityType Name="ProductSourceImages">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ProductSourceId" Type="Int32" Nullable="false" />
          <Property Name="ImageBytes" Type="Binary" Nullable="false" MaxLength="Max" FixedLength="false" />
          <Property Name="ImageUrl" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="ProductSource" Relationship="EdunetDatafeedsModel.ProductSouceImages_ProductSource_ID_fk" FromRole="ProductSourceImages" ToRole="ProductSource" />
        </EntityType>
        <EntityType Name="FandoogleReflectorCache">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ProductSku" Type="String" Nullable="false" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="ExpiresAt" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="RandomisedPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="OriginalPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
        </EntityType>
        <EntityType Name="PriceBookItemSellPriceOverride">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PriceBookItemId" Type="Int32" Nullable="false" />
          <Property Name="SellPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <NavigationProperty Name="ImportedItemsFromPriceBook" Relationship="EdunetDatafeedsModel.PriceBookItemSellPriceOverride_ImportedItemsFromPriceBook_ID_fk" FromRole="PriceBookItemSellPriceOverride" ToRole="ImportedItemsFromPriceBook" />
        </EntityType>
        <EntityType Name="AIGeneratedProductInfo">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ProductId" Type="Int32" Nullable="false" />
          <Property Name="ProductSource" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Attribute" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Value" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="PreflightLog">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ProductSourceId" Type="Int32" />
          <Property Name="LogDateTime" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Message" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="ProductSource" Relationship="EdunetDatafeedsModel.PreflightLog_ProductSource_ID_fk" FromRole="PreflightLog" ToRole="ProductSource" />
        </EntityType>
        <EntityType Name="TakeoffLog">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ProductSourceId" Type="Int32" />
          <Property Name="LogDateTime" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Message" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="ProductSource" Relationship="EdunetDatafeedsModel.TakeoffLog_ProductSource_ID_fk" FromRole="TakeoffLog" ToRole="ProductSource" />
        </EntityType>
        <EntityType Name="ProductSourceTypesForNopCategory">
          <Key>
            <PropertyRef Name="SourceTypeId" />
            <PropertyRef Name="SourceTypeName" />
            <PropertyRef Name="CategoryId" />
            <PropertyRef Name="CategoryName" />
          </Key>
          <Property Name="SourceTypeId" Type="Int32" Nullable="false" />
          <Property Name="SourceTypeName" Type="String" Nullable="false" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="SourceTypeFileName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="CategoryId" Type="Int32" Nullable="false" />
          <Property Name="CategoryName" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="ProductSourceSellPriceOverride">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ProductSourceId" Type="Int32" Nullable="false" />
          <Property Name="SellPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="ExpiryDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Note" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ActionedBy" Type="String" Nullable="false" MaxLength="200" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="ProductSource" Relationship="EdunetDatafeedsModel.ProductSourceSellPriceOverride_ProductSource_ID_fk" FromRole="ProductSourceSellPriceOverride" ToRole="ProductSource" />
        </EntityType>
        <EntityType Name="DisabledProductsSanitised">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="SourceTypeIds" />
            <PropertyRef Name="ProductSku" />
            <PropertyRef Name="ProductTitle" />
            <PropertyRef Name="CostPrice" />
            <PropertyRef Name="Status" />
            <PropertyRef Name="QtyAvailable" />
            <PropertyRef Name="Reason" />
            <PropertyRef Name="ImageCount" />
            <PropertyRef Name="DoogleCount" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="SourceTypeIds" Type="String" Nullable="false" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="ProductSku" Type="String" Nullable="false" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="ProductTitle" Type="String" Nullable="false" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="ProductLongDescription" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ProductShortDescription" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="CostPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="SellPrice" Type="Decimal" Precision="19" Scale="4" />
          <Property Name="Status" Type="Int32" Nullable="false" />
          <Property Name="QtyAvailable" Type="Int32" Nullable="false" />
          <Property Name="Brand" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="Reason" Type="String" Nullable="false" MaxLength="52" FixedLength="false" Unicode="false" />
          <Property Name="ImageCount" Type="Int32" Nullable="false" />
          <Property Name="DoogleCount" Type="Int32" Nullable="false" />
        </EntityType>
        <EntityType Name="DisabledPriceBookProductsSanitised">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="PriceBookID" />
            <PropertyRef Name="ProductName" />
            <PropertyRef Name="ProductSKU" />
            <PropertyRef Name="ProductDescription" />
            <PropertyRef Name="ProductPrice" />
            <PropertyRef Name="IsPublished" />
            <PropertyRef Name="Brand" />
            <PropertyRef Name="Reason" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PriceBookID" Type="Int32" Nullable="false" />
          <Property Name="ProductName" Type="String" Nullable="false" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="ProductSKU" Type="String" Nullable="false" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="ProductDescription" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ProductPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="AppliedTransformationPackage" Type="Int32" />
          <Property Name="IsPublished" Type="Boolean" Nullable="false" />
          <Property Name="ProductShortDescription" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="ImageURLs" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Brand" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="CostPrice" Type="Decimal" Precision="19" Scale="4" />
          <Property Name="StockOnHand" Type="Int32" />
          <Property Name="Reason" Type="String" Nullable="false" MaxLength="52" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="NewlyImportedPriceBookItems">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PriceBookItemId" Type="Int32" Nullable="false" />
          <Property Name="ImportedOn" Type="DateTime" Nullable="false" Precision="3" />
          <NavigationProperty Name="ImportedItemsFromPriceBook" Relationship="EdunetDatafeedsModel.NewlyImportedPriceBookItems_ImportedItemsFromPriceBook_ID_fk" FromRole="NewlyImportedPriceBookItems" ToRole="ImportedItemsFromPriceBook" />
        </EntityType>
        <EntityType Name="MappedPriceBookItemsForNopCategory">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="PriceBookID" />
            <PropertyRef Name="ProductName" />
            <PropertyRef Name="ProductSKU" />
            <PropertyRef Name="ProductDescription" />
            <PropertyRef Name="ProductPrice" />
            <PropertyRef Name="IsPublished" />
            <PropertyRef Name="Brand" />
            <PropertyRef Name="PriceBookArchived" />
            <PropertyRef Name="NopCategoryId" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" />
          <Property Name="PriceBookID" Type="Int32" Nullable="false" />
          <Property Name="ProductName" Type="String" Nullable="false" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="ProductSKU" Type="String" Nullable="false" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="ProductDescription" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ProductShortDescription" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ProductPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="CostPrice" Type="Decimal" Precision="19" Scale="4" />
          <Property Name="AppliedTransformationPackage" Type="Int32" />
          <Property Name="IsPublished" Type="Boolean" Nullable="false" />
          <Property Name="Brand" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="StockOnHand" Type="Int32" />
          <Property Name="PriceBookArchived" Type="Boolean" Nullable="false" />
          <Property Name="CategoryAppliedTransformationPackage" Type="Int32" />
          <Property Name="MappingType" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="NopCategoryId" Type="Int32" Nullable="false" />
          <Property Name="PriceOverride" Type="Decimal" Precision="19" Scale="4" />
          <Property Name="ImageUrls" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="ImportedItemsFromPriceBook">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PriceBookID" Type="Int32" Nullable="false" />
          <Property Name="ProductName" Type="String" Nullable="false" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="ProductSKU" Type="String" Nullable="false" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="ProductDescription" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ProductPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="AppliedTransformationPackage" Type="Int32" />
          <Property Name="IsPublished" Type="Boolean" Nullable="false" />
          <Property Name="ProductShortDescription" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ImageURLs" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Brand" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="CostPrice" Type="Decimal" Precision="19" Scale="4" />
          <Property Name="StockOnHand" Type="Int32" />
          <NavigationProperty Name="CommerceTransformationPackages" Relationship="EdunetDatafeedsModel.ImportedItemsFromPriceBook_CommerceTransformationPackages_ID_fk" FromRole="ImportedItemsFromPriceBook" ToRole="CommerceTransformationPackages" />
          <NavigationProperty Name="ImportedPriceBooks" Relationship="EdunetDatafeedsModel.ImportedItemsFromPriceBook_ImportedPriceBooks_ID_fk" FromRole="ImportedItemsFromPriceBook" ToRole="ImportedPriceBooks" />
          <NavigationProperty Name="NewlyImportedPriceBookItems" Relationship="EdunetDatafeedsModel.NewlyImportedPriceBookItems_ImportedItemsFromPriceBook_ID_fk" FromRole="ImportedItemsFromPriceBook" ToRole="NewlyImportedPriceBookItems" />
          <NavigationProperty Name="PriceBookImportedItemToNopCategoryMapping" Relationship="EdunetDatafeedsModel.PriceBookImportedItemToNopCategoryMapping_ImportedItemsFromPriceBook_ID_fk" FromRole="ImportedItemsFromPriceBook" ToRole="PriceBookImportedItemToNopCategoryMapping" />
          <NavigationProperty Name="PriceBookItemSellPriceOverride" Relationship="EdunetDatafeedsModel.PriceBookItemSellPriceOverride_ImportedItemsFromPriceBook_ID_fk" FromRole="ImportedItemsFromPriceBook" ToRole="PriceBookItemSellPriceOverride" />
          <NavigationProperty Name="PriceBookItemImages" Relationship="EdunetDatafeedsModel.PriceBookItemImages_ImportedItemsFromPriceBook_ID_fk" FromRole="ImportedItemsFromPriceBook" ToRole="PriceBookItemImages" />
          <NavigationProperty Name="PriceBookItemCostPriceOverride" Relationship="EdunetDatafeedsModel.PriceBookItemCostPriceOverride_ImportedItemsFromPriceBook_ID_fk" FromRole="ImportedItemsFromPriceBook" ToRole="PriceBookItemCostPriceOverride" />
        </EntityType>
        <Association Name="ImportedItemsFromPriceBook_CommerceTransformationPackages_ID_fk">
          <End Type="EdunetDatafeedsModel.CommerceTransformationPackages" Role="CommerceTransformationPackages" Multiplicity="0..1" />
          <End Type="EdunetDatafeedsModel.ImportedItemsFromPriceBook" Role="ImportedItemsFromPriceBook" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="CommerceTransformationPackages">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ImportedItemsFromPriceBook">
              <PropertyRef Name="AppliedTransformationPackage" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ImportedItemsFromPriceBook_ImportedPriceBooks_ID_fk">
          <End Type="EdunetDatafeedsModel.ImportedPriceBooks" Role="ImportedPriceBooks" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ImportedItemsFromPriceBook" Role="ImportedItemsFromPriceBook" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ImportedPriceBooks">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ImportedItemsFromPriceBook">
              <PropertyRef Name="PriceBookID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="NewlyImportedPriceBookItems_ImportedItemsFromPriceBook_ID_fk">
          <End Type="EdunetDatafeedsModel.ImportedItemsFromPriceBook" Role="ImportedItemsFromPriceBook" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.NewlyImportedPriceBookItems" Role="NewlyImportedPriceBookItems" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ImportedItemsFromPriceBook">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="NewlyImportedPriceBookItems">
              <PropertyRef Name="PriceBookItemId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="PriceBookImportedItemToNopCategoryMapping_ImportedItemsFromPriceBook_ID_fk">
          <End Type="EdunetDatafeedsModel.ImportedItemsFromPriceBook" Role="ImportedItemsFromPriceBook" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.PriceBookImportedItemToNopCategoryMapping" Role="PriceBookImportedItemToNopCategoryMapping" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ImportedItemsFromPriceBook">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="PriceBookImportedItemToNopCategoryMapping">
              <PropertyRef Name="PriceBookItemId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="PriceBookItemSellPriceOverride_ImportedItemsFromPriceBook_ID_fk">
          <End Type="EdunetDatafeedsModel.ImportedItemsFromPriceBook" Role="ImportedItemsFromPriceBook" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.PriceBookItemSellPriceOverride" Role="PriceBookItemSellPriceOverride" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ImportedItemsFromPriceBook">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="PriceBookItemSellPriceOverride">
              <PropertyRef Name="PriceBookItemId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="PreflightFlagStatus">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Html" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="ProductSource" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk" FromRole="PreflightFlagStatus" ToRole="ProductSource" />
          <NavigationProperty Name="ProductSource1" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_2" FromRole="PreflightFlagStatus" ToRole="ProductSource" />
          <NavigationProperty Name="ProductSource2" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_3" FromRole="PreflightFlagStatus" ToRole="ProductSource" />
          <NavigationProperty Name="ProductSource3" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_4" FromRole="PreflightFlagStatus" ToRole="ProductSource" />
          <NavigationProperty Name="ProductSource4" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_5" FromRole="PreflightFlagStatus" ToRole="ProductSource" />
          <NavigationProperty Name="ProductSource5" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_6" FromRole="PreflightFlagStatus" ToRole="ProductSource" />
          <NavigationProperty Name="ProductSource6" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_7" FromRole="PreflightFlagStatus" ToRole="ProductSource" />
          <NavigationProperty Name="ProductSource7" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_8" FromRole="PreflightFlagStatus" ToRole="ProductSource" />
          <NavigationProperty Name="ProductSource8" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_9" FromRole="PreflightFlagStatus" ToRole="ProductSource" />
          </EntityType>
        <EntityType Name="PriceBookItemImages">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PriceBookItemID" Type="Int32" Nullable="false" />
          <Property Name="ImageURL" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="Image" Type="Binary" Nullable="false" MaxLength="Max" FixedLength="false" />
          <NavigationProperty Name="ImportedItemsFromPriceBook" Relationship="EdunetDatafeedsModel.PriceBookItemImages_ImportedItemsFromPriceBook_ID_fk" FromRole="PriceBookItemImages" ToRole="ImportedItemsFromPriceBook" />
        </EntityType>
        <Association Name="PriceBookItemImages_ImportedItemsFromPriceBook_ID_fk">
          <End Type="EdunetDatafeedsModel.ImportedItemsFromPriceBook" Role="ImportedItemsFromPriceBook" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.PriceBookItemImages" Role="PriceBookItemImages" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ImportedItemsFromPriceBook">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="PriceBookItemImages">
              <PropertyRef Name="PriceBookItemID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="ProductsCheaperAtCompetitorSanitised">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="ProductSku" />
            <PropertyRef Name="CompetitorPrice" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" />
          <Property Name="ProductSku" Type="String" Nullable="false" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="CompetitorPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="OurCostPrice" Type="Decimal" Precision="19" Scale="4" />
          <Property Name="Brand" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="ProductTitle" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="PriceBookItemCostPriceOverride">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PriceBookItemId" Type="Int32" Nullable="false" />
          <Property Name="CostPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="ExpiryDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Note" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ActionedBy" Type="String" Nullable="false" MaxLength="200" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="ImportedItemsFromPriceBook" Relationship="EdunetDatafeedsModel.PriceBookItemCostPriceOverride_ImportedItemsFromPriceBook_ID_fk" FromRole="PriceBookItemCostPriceOverride" ToRole="ImportedItemsFromPriceBook" />
        </EntityType>
        <EntityType Name="ProductSourceCostPriceOverride">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ProductSourceId" Type="Int32" Nullable="false" />
          <Property Name="CostPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="ExpiryDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Note" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ActionedBy" Type="String" Nullable="false" MaxLength="200" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="ProductSource" Relationship="EdunetDatafeedsModel.ProductSourceCostPriceOverride_ProductSource_ID_fk" FromRole="ProductSourceCostPriceOverride" ToRole="ProductSource" />
        </EntityType>
        <Association Name="PriceBookItemCostPriceOverride_ImportedItemsFromPriceBook_ID_fk">
          <End Type="EdunetDatafeedsModel.ImportedItemsFromPriceBook" Role="ImportedItemsFromPriceBook" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.PriceBookItemCostPriceOverride" Role="PriceBookItemCostPriceOverride" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ImportedItemsFromPriceBook">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="PriceBookItemCostPriceOverride">
              <PropertyRef Name="PriceBookItemId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="ProductSource">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="SourceTypeIds" Type="String" Nullable="false" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="ProductSku" Type="String" Nullable="false" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="ProductTitle" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ProductLongDescription" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ProductShortDescription" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="CostPrice" Type="Decimal" Nullable="false" Precision="19" Scale="4" />
          <Property Name="SellPrice" Type="Decimal" Precision="19" Scale="4" />
          <Property Name="Status" Type="Int32" Nullable="false" />
          <Property Name="QtyAvailable" Type="Int32" Nullable="false" />
          <Property Name="Brand" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="KaseyaShortDescriptionPreflightFlag" Type="Int32" Nullable="false" />
          <Property Name="KaseyaLongDescriptionPreflightFlag" Type="Int32" Nullable="false" />
          <Property Name="CompetitorPricePreflightFlag" Type="Int32" Nullable="false" />
          <Property Name="KaseyaRecommendedSellPricePreflightFlag" Type="Int32" Nullable="false" />
          <Property Name="KaseyaImagesPreflightFlag" Type="Int32" Nullable="false" />
          <Property Name="KaseyaCostPricePreflightFlag" Type="Int32" Nullable="false" />
          <Property Name="IcecatShortDescriptionPreflightFlag" Type="Int32" Nullable="false" />
          <Property Name="IcecatLongDescriptionPreflightFlag" Type="Int32" Nullable="false" />
          <Property Name="IcecatImagesPreflightFlag" Type="Int32" Nullable="false" />
          <Property Name="Archived" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="PreflightFlagStatus" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk" FromRole="ProductSource" ToRole="PreflightFlagStatus" />
          <NavigationProperty Name="PreflightFlagStatus1" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_2" FromRole="ProductSource" ToRole="PreflightFlagStatus" />
          <NavigationProperty Name="PreflightFlagStatus2" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_3" FromRole="ProductSource" ToRole="PreflightFlagStatus" />
          <NavigationProperty Name="PreflightFlagStatus3" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_4" FromRole="ProductSource" ToRole="PreflightFlagStatus" />
          <NavigationProperty Name="PreflightFlagStatus4" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_5" FromRole="ProductSource" ToRole="PreflightFlagStatus" />
          <NavigationProperty Name="PreflightFlagStatus5" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_6" FromRole="ProductSource" ToRole="PreflightFlagStatus" />
          <NavigationProperty Name="PreflightFlagStatus6" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_7" FromRole="ProductSource" ToRole="PreflightFlagStatus" />
          <NavigationProperty Name="PreflightFlagStatus7" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_8" FromRole="ProductSource" ToRole="PreflightFlagStatus" />
          <NavigationProperty Name="PreflightFlagStatus8" Relationship="EdunetDatafeedsModel.ProductSource_PreflightFlagStatus_ID_fk_9" FromRole="ProductSource" ToRole="PreflightFlagStatus" />
          <NavigationProperty Name="PreflightLog" Relationship="EdunetDatafeedsModel.PreflightLog_ProductSource_ID_fk" FromRole="ProductSource" ToRole="PreflightLog" />
          <NavigationProperty Name="ProductSourceImages" Relationship="EdunetDatafeedsModel.ProductSouceImages_ProductSource_ID_fk" FromRole="ProductSource" ToRole="ProductSourceImages" />
          <NavigationProperty Name="ProductSourceStatus" Relationship="EdunetDatafeedsModel.ProductSource_ProductSourceStatus_ID_fk" FromRole="ProductSource" ToRole="ProductSourceStatus" />
          <NavigationProperty Name="ProductSourceCostPriceOverride" Relationship="EdunetDatafeedsModel.ProductSourceCostPriceOverride_ProductSource_ID_fk" FromRole="ProductSource" ToRole="ProductSourceCostPriceOverride" />
          <NavigationProperty Name="ProductSourceSellPriceOverride" Relationship="EdunetDatafeedsModel.ProductSourceSellPriceOverride_ProductSource_ID_fk" FromRole="ProductSource" ToRole="ProductSourceSellPriceOverride" />
          <NavigationProperty Name="TakeoffLog" Relationship="EdunetDatafeedsModel.TakeoffLog_ProductSource_ID_fk" FromRole="ProductSource" ToRole="TakeoffLog" />
        </EntityType>
        <EntityType Name="ProductSourcesForNopCategory">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="CategoryId" />
            <PropertyRef Name="CategoryName" />
            <PropertyRef Name="SourceTypeId" />
            <PropertyRef Name="SourceTypeName" />
            <PropertyRef Name="ProductTitle" />
            <PropertyRef Name="ProductSku" />
            <PropertyRef Name="QtyAvailable" />
            <PropertyRef Name="StatusId" />
            <PropertyRef Name="StatusName" />
            <PropertyRef Name="StatusPublishable" />
            <PropertyRef Name="StatusActive" />
            <PropertyRef Name="Archived" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" />
          <Property Name="CategoryId" Type="Int32" Nullable="false" />
          <Property Name="CategoryName" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="SourceTypeId" Type="Int32" Nullable="false" />
          <Property Name="SourceTypeName" Type="String" Nullable="false" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="Brand" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="ProductTitle" Type="String" Nullable="false" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ProductSku" Type="String" Nullable="false" MaxLength="150" FixedLength="false" Unicode="true" />
          <Property Name="ProductShortDescription" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="ProductLongDescription" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="CostPrice" Type="Decimal" Precision="19" Scale="4" />
          <Property Name="SellPrice" Type="Decimal" Precision="19" Scale="4" />
          <Property Name="QtyAvailable" Type="Int32" Nullable="false" />
          <Property Name="StatusId" Type="Int32" Nullable="false" />
          <Property Name="StatusName" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="StatusPublishable" Type="Boolean" Nullable="false" />
          <Property Name="StatusActive" Type="Boolean" Nullable="false" />
          <Property Name="PriceOverride" Type="Decimal" Precision="19" Scale="4" />
          <Property Name="DisabledReason" Type="String" MaxLength="52" FixedLength="false" Unicode="false" />
          <Property Name="DisabledProductDoogleCount" Type="Int32" />
          <Property Name="DisabledProductImageCount" Type="Int32" />
          <Property Name="Archived" Type="Boolean" Nullable="false" />
        </EntityType>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk">
          <End Type="EdunetDatafeedsModel.PreflightFlagStatus" Role="PreflightFlagStatus" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="KaseyaShortDescriptionPreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_2">
          <End Type="EdunetDatafeedsModel.PreflightFlagStatus" Role="PreflightFlagStatus" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="KaseyaLongDescriptionPreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_3">
          <End Type="EdunetDatafeedsModel.PreflightFlagStatus" Role="PreflightFlagStatus" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="CompetitorPricePreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_4">
          <End Type="EdunetDatafeedsModel.PreflightFlagStatus" Role="PreflightFlagStatus" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="KaseyaRecommendedSellPricePreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_5">
          <End Type="EdunetDatafeedsModel.PreflightFlagStatus" Role="PreflightFlagStatus" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="KaseyaImagesPreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_6">
          <End Type="EdunetDatafeedsModel.PreflightFlagStatus" Role="PreflightFlagStatus" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="KaseyaCostPricePreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_7">
          <End Type="EdunetDatafeedsModel.PreflightFlagStatus" Role="PreflightFlagStatus" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="IcecatShortDescriptionPreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_8">
          <End Type="EdunetDatafeedsModel.PreflightFlagStatus" Role="PreflightFlagStatus" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="IcecatLongDescriptionPreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_PreflightFlagStatus_ID_fk_9">
          <End Type="EdunetDatafeedsModel.PreflightFlagStatus" Role="PreflightFlagStatus" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PreflightFlagStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="IcecatImagesPreflightFlag" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="PreflightLog_ProductSource_ID_fk">
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="0..1" />
          <End Type="EdunetDatafeedsModel.PreflightLog" Role="PreflightLog" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ProductSource">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="PreflightLog">
              <PropertyRef Name="ProductSourceId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSouceImages_ProductSource_ID_fk">
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ProductSourceImages" Role="ProductSourceImages" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ProductSource">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSourceImages">
              <PropertyRef Name="ProductSourceId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSource_ProductSourceStatus_ID_fk">
          <End Type="EdunetDatafeedsModel.ProductSourceStatus" Role="ProductSourceStatus" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ProductSourceStatus">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSource">
              <PropertyRef Name="Status" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSourceCostPriceOverride_ProductSource_ID_fk">
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ProductSourceCostPriceOverride" Role="ProductSourceCostPriceOverride" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ProductSource">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSourceCostPriceOverride">
              <PropertyRef Name="ProductSourceId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ProductSourceSellPriceOverride_ProductSource_ID_fk">
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="1" />
          <End Type="EdunetDatafeedsModel.ProductSourceSellPriceOverride" Role="ProductSourceSellPriceOverride" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ProductSource">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="ProductSourceSellPriceOverride">
              <PropertyRef Name="ProductSourceId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="TakeoffLog_ProductSource_ID_fk">
          <End Type="EdunetDatafeedsModel.ProductSource" Role="ProductSource" Multiplicity="0..1" />
          <End Type="EdunetDatafeedsModel.TakeoffLog" Role="TakeoffLog" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ProductSource">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="TakeoffLog">
              <PropertyRef Name="ProductSourceId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="EdunetDatafeedsModelStoreContainer" CdmEntityContainer="EdunetDatafeedsEntities">
          <EntitySetMapping Name="KQMProductSyncs">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.KQMProductSyncs">
              <MappingFragment StoreEntitySet="KQMProductSyncs">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="SyncDateTime" ColumnName="SyncDateTime" />
                <ScalarProperty Name="NewProductsSynced" ColumnName="NewProductsSynced" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CommerceTransformationPackages">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.CommerceTransformationPackages">
              <MappingFragment StoreEntitySet="CommerceTransformationPackages">
                <ScalarProperty Name="EnableAI" ColumnName="EnableAI" />
                <ScalarProperty Name="EnablePercentMarkup" ColumnName="EnablePercentMarkup" />
                <ScalarProperty Name="PercentMarkup" ColumnName="PercentMarkup" />
                <ScalarProperty Name="PackageDescription" ColumnName="PackageDescription" />
                <ScalarProperty Name="PackageName" ColumnName="PackageName" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="KQMPendingProductSKUs">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.KQMPendingProductSKUs">
              <MappingFragment StoreEntitySet="KQMPendingProductSKUs">
                <ScalarProperty Name="Rejected" ColumnName="Rejected" />
                <ScalarProperty Name="Accepted" ColumnName="Accepted" />
                <ScalarProperty Name="RequestedDateTime" ColumnName="RequestedDateTime" />
                <ScalarProperty Name="RequestingUser" ColumnName="RequestingUser" />
                <ScalarProperty Name="ProductSKU" ColumnName="ProductSKU" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="KQMBrands">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.KQMBrands">
              <MappingFragment StoreEntitySet="KQMBrands">
                <ScalarProperty Name="modifiedDate" ColumnName="modifiedDate" />
                <ScalarProperty Name="createDate" ColumnName="createDate" />
                <ScalarProperty Name="imageFilename" ColumnName="imageFilename" />
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="brandID" ColumnName="brandID" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PriceBookAndKQMMultiplexedProducts">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.PriceBookAndKQMMultiplexedProducts">
              <MappingFragment StoreEntitySet="PriceBookAndKQMMultiplexedProducts">
                <ScalarProperty Name="Source" ColumnName="Source" />
                <ScalarProperty Name="AppliedTransformationPackage" ColumnName="AppliedTransformationPackage" />
                <ScalarProperty Name="IsPublished" ColumnName="IsPublished" />
                <ScalarProperty Name="Quantity" ColumnName="Quantity" />
                <ScalarProperty Name="Brand" ColumnName="Brand" />
                <ScalarProperty Name="SupplierID" ColumnName="SupplierID" />
                <ScalarProperty Name="CategoryID" ColumnName="CategoryID" />
                <ScalarProperty Name="Price" ColumnName="Price" />
                <ScalarProperty Name="ProductDescription" ColumnName="ProductDescription" />
                <ScalarProperty Name="ProductName" ColumnName="ProductName" />
                <ScalarProperty Name="SKU" ColumnName="SKU" />
                <ScalarProperty Name="ProductID" ColumnName="ProductID" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="KqmProductImages">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.KqmProductImages">
              <MappingFragment StoreEntitySet="KqmProductImages">
                <ScalarProperty Name="ImageUrl" ColumnName="ImageUrl" />
                <ScalarProperty Name="Image" ColumnName="Image" />
                <ScalarProperty Name="KqmProductId" ColumnName="KqmProductId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="KqmQuoteToNopCategoryMapping">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.KqmQuoteToNopCategoryMapping">
              <MappingFragment StoreEntitySet="KqmQuoteToNopCategoryMapping">
                <ScalarProperty Name="NopCategoryId" ColumnName="NopCategoryId" />
                <ScalarProperty Name="KqmQuoteId" ColumnName="KqmQuoteId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="KqmProductToNopCategoryMapping">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.KqmProductToNopCategoryMapping">
              <MappingFragment StoreEntitySet="KqmProductToNopCategoryMapping">
                <ScalarProperty Name="NopCategoryId" ColumnName="NopCategoryId" />
                <ScalarProperty Name="KqmProductId" ColumnName="KqmProductId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="KqmProductSuppliers">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.KqmProductSuppliers">
              <MappingFragment StoreEntitySet="KqmProductSuppliers">
                <ScalarProperty Name="modifiedDate" ColumnName="modifiedDate" />
                <ScalarProperty Name="createdDate" ColumnName="createdDate" />
                <ScalarProperty Name="productNumber" ColumnName="productNumber" />
                <ScalarProperty Name="cost" ColumnName="cost" />
                <ScalarProperty Name="quantity" ColumnName="quantity" />
                <ScalarProperty Name="supplierID" ColumnName="supplierID" />
                <ScalarProperty Name="productID" ColumnName="productID" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="IngramProductToNopCategoryMapping">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.IngramProductToNopCategoryMapping">
              <MappingFragment StoreEntitySet="IngramProductToNopCategoryMapping">
                <ScalarProperty Name="NopCategoryId" ColumnName="NopCategoryId" />
                <ScalarProperty Name="IngramProductId" ColumnName="IngramProductId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="NopCategories">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.NopCategories">
              <MappingFragment StoreEntitySet="NopCategories">
                <ScalarProperty Name="AppliedTransformationPackage" ColumnName="AppliedTransformationPackage" />
                <ScalarProperty Name="ParentCategoryId" ColumnName="ParentCategoryId" />
                <ScalarProperty Name="UpdatedOnUtc" ColumnName="UpdatedOnUtc" />
                <ScalarProperty Name="CreatedOnUtc" ColumnName="CreatedOnUtc" />
                <ScalarProperty Name="Published" ColumnName="Published" />
                <ScalarProperty Name="Deleted" ColumnName="Deleted" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="CategoryId" ColumnName="CategoryId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="KqmProductPriceOverride">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.KqmProductPriceOverride">
              <MappingFragment StoreEntitySet="KqmProductPriceOverride">
                <ScalarProperty Name="Price" ColumnName="Price" />
                <ScalarProperty Name="KqmProductId" ColumnName="KqmProductId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="IngramProductPriceOverride">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.IngramProductPriceOverride">
              <MappingFragment StoreEntitySet="IngramProductPriceOverride">
                <ScalarProperty Name="Price" ColumnName="Price" />
                <ScalarProperty Name="IngramProductId" ColumnName="IngramProductId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PriceBookImportedItemToNopCategoryMapping">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.PriceBookImportedItemToNopCategoryMapping">
              <MappingFragment StoreEntitySet="PriceBookImportedItemToNopCategoryMapping">
                <ScalarProperty Name="NopCategoryId" ColumnName="NopCategoryId" />
                <ScalarProperty Name="PriceBookItemId" ColumnName="PriceBookItemId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PriceBookToNopCategoryMapping">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.PriceBookToNopCategoryMapping">
              <MappingFragment StoreEntitySet="PriceBookToNopCategoryMapping">
                <ScalarProperty Name="NopCategoryId" ColumnName="NopCategoryId" />
                <ScalarProperty Name="PriceBookId" ColumnName="PriceBookId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="IngramProducts">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.IngramProducts">
              <MappingFragment StoreEntitySet="IngramProducts">
                <ScalarProperty Name="SubCategoryName" ColumnName="SubCategoryName" />
                <ScalarProperty Name="CategoryName" ColumnName="CategoryName" />
                <ScalarProperty Name="AppliedTransformationPackage" ColumnName="AppliedTransformationPackage" />
                <ScalarProperty Name="Plant02AvailableQuantity" ColumnName="Plant02AvailableQuantity" />
                <ScalarProperty Name="Plant01AvailableQuantity" ColumnName="Plant01AvailableQuantity" />
                <ScalarProperty Name="CreationReasonValue" ColumnName="CreationReasonValue" />
                <ScalarProperty Name="CreationReasonType" ColumnName="CreationReasonType" />
                <ScalarProperty Name="SubstituteFlag" ColumnName="SubstituteFlag" />
                <ScalarProperty Name="CustomerPriceChangeFlag" ColumnName="CustomerPriceChangeFlag" />
                <ScalarProperty Name="CustomerCurrencyCode" ColumnName="CustomerCurrencyCode" />
                <ScalarProperty Name="CompanyCodeCurrency" ColumnName="CompanyCodeCurrency" />
                <ScalarProperty Name="CompanyCode" ColumnName="CompanyCode" />
                <ScalarProperty Name="Points" ColumnName="Points" />
                <ScalarProperty Name="Period" ColumnName="Period" />
                <ScalarProperty Name="LevelID" ColumnName="LevelID" />
                <ScalarProperty Name="AgreementID" ColumnName="AgreementID" />
                <ScalarProperty Name="CustomerReservationQty" ColumnName="CustomerReservationQty" />
                <ScalarProperty Name="CustomerReservationNumber" ColumnName="CustomerReservationNumber" />
                <ScalarProperty Name="DiscountInPercent" ColumnName="DiscountInPercent" />
                <ScalarProperty Name="TaxPercent" ColumnName="TaxPercent" />
                <ScalarProperty Name="RetailPriceWithTax" ColumnName="RetailPriceWithTax" />
                <ScalarProperty Name="CustomerPriceWithTax" ColumnName="CustomerPriceWithTax" />
                <ScalarProperty Name="BatteryFees" ColumnName="BatteryFees" />
                <ScalarProperty Name="DocumentCopyrightFees" ColumnName="DocumentCopyrightFees" />
                <ScalarProperty Name="RecyclingFees" ColumnName="RecyclingFees" />
                <ScalarProperty Name="MusicCopyrightFees" ColumnName="MusicCopyrightFees" />
                <ScalarProperty Name="FulfilmentType" ColumnName="FulfilmentType" />
                <ScalarProperty Name="ReleaseDate" ColumnName="ReleaseDate" />
                <ScalarProperty Name="Discontinued" ColumnName="Discontinued" />
                <ScalarProperty Name="MaterialStatus" ColumnName="MaterialStatus" />
                <ScalarProperty Name="DirectOrderIdentifier" ColumnName="DirectOrderIdentifier" />
                <ScalarProperty Name="PalletQty" ColumnName="PalletQty" />
                <ScalarProperty Name="CaseQty" ColumnName="CaseQty" />
                <ScalarProperty Name="VendorSubrange" ColumnName="VendorSubrange" />
                <ScalarProperty Name="NewMaterialFlag" ColumnName="NewMaterialFlag" />
                <ScalarProperty Name="PriceStatus" ColumnName="PriceStatus" />
                <ScalarProperty Name="ActionCode" ColumnName="ActionCode" />
                <ScalarProperty Name="MaterialChangeCode" ColumnName="MaterialChangeCode" />
                <ScalarProperty Name="PurchasingVendor" ColumnName="PurchasingVendor" />
                <ScalarProperty Name="ProductFamily" ColumnName="ProductFamily" />
                <ScalarProperty Name="SubCategory" ColumnName="SubCategory" />
                <ScalarProperty Name="ManufacturerVendorNumber" ColumnName="ManufacturerVendorNumber" />
                <ScalarProperty Name="SupersededMaterial" ColumnName="SupersededMaterial" />
                <ScalarProperty Name="SubstituteMaterial" ColumnName="SubstituteMaterial" />
                <ScalarProperty Name="MaterialLanguageCode" ColumnName="MaterialLanguageCode" />
                <ScalarProperty Name="MediaCode" ColumnName="MediaCode" />
                <ScalarProperty Name="MaterialCreationReasonCode" ColumnName="MaterialCreationReasonCode" />
                <ScalarProperty Name="Category" ColumnName="Category" />
                <ScalarProperty Name="VolumeUnit" ColumnName="VolumeUnit" />
                <ScalarProperty Name="WeightUnit" ColumnName="WeightUnit" />
                <ScalarProperty Name="DimensionUnit" ColumnName="DimensionUnit" />
                <ScalarProperty Name="Height" ColumnName="Height" />
                <ScalarProperty Name="Width" ColumnName="Width" />
                <ScalarProperty Name="Length" ColumnName="Length" />
                <ScalarProperty Name="MaterialLongDescription" ColumnName="MaterialLongDescription" />
                <ScalarProperty Name="BulkFreightFlag" ColumnName="BulkFreightFlag" />
                <ScalarProperty Name="WarrantyFlag" ColumnName="WarrantyFlag" />
                <ScalarProperty Name="BOMFlag" ColumnName="BOMFlag" />
                <ScalarProperty Name="LicenseFlag" ColumnName="LicenseFlag" />
                <ScalarProperty Name="BacklogETA" ColumnName="BacklogETA" />
                <ScalarProperty Name="BacklogInformation" ColumnName="BacklogInformation" />
                <ScalarProperty Name="AvailableQuantity" ColumnName="AvailableQuantity" />
                <ScalarProperty Name="AvailabilityFlag" ColumnName="AvailabilityFlag" />
                <ScalarProperty Name="RetailPrice" ColumnName="RetailPrice" />
                <ScalarProperty Name="CustomerPrice" ColumnName="CustomerPrice" />
                <ScalarProperty Name="CategoryID" ColumnName="CategoryID" />
                <ScalarProperty Name="Unit" ColumnName="Unit" />
                <ScalarProperty Name="Volume" ColumnName="Volume" />
                <ScalarProperty Name="Weight" ColumnName="Weight" />
                <ScalarProperty Name="Size" ColumnName="Size" />
                <ScalarProperty Name="VendorName" ColumnName="VendorName" />
                <ScalarProperty Name="VendorNumber" ColumnName="VendorNumber" />
                <ScalarProperty Name="Plant" ColumnName="Plant" />
                <ScalarProperty Name="EANUPCCode" ColumnName="EANUPCCode" />
                <ScalarProperty Name="VendorPartNumber" ColumnName="VendorPartNumber" />
                <ScalarProperty Name="CustomerPartNumber" ColumnName="CustomerPartNumber" />
                <ScalarProperty Name="IngramPartDescription" ColumnName="IngramPartDescription" />
                <ScalarProperty Name="IngramPartNumber" ColumnName="IngramPartNumber" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="IngramCategoryToNopCategoryMapping">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.IngramCategoryToNopCategoryMapping">
              <MappingFragment StoreEntitySet="IngramCategoryToNopCategoryMapping">
                <ScalarProperty Name="NopCategoryId" ColumnName="NopCategoryId" />
                <ScalarProperty Name="IngramCategoryName" ColumnName="IngramCategoryName" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="MappedIngramProductsForNopCategory">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.MappedIngramProductsForNopCategory">
              <MappingFragment StoreEntitySet="MappedIngramProductsForNopCategory">
                <ScalarProperty Name="PriceOverride" ColumnName="PriceOverride" />
                <ScalarProperty Name="NopCategoryId" ColumnName="NopCategoryId" />
                <ScalarProperty Name="MappingType" ColumnName="MappingType" />
                <ScalarProperty Name="AppliedTransformationPackage" ColumnName="AppliedTransformationPackage" />
                <ScalarProperty Name="SubCategoryName" ColumnName="SubCategoryName" />
                <ScalarProperty Name="CategoryName" ColumnName="CategoryName" />
                <ScalarProperty Name="Plant02AvailableQuantity" ColumnName="Plant02AvailableQuantity" />
                <ScalarProperty Name="Plant01AvailableQuantity" ColumnName="Plant01AvailableQuantity" />
                <ScalarProperty Name="CreationReasonValue" ColumnName="CreationReasonValue" />
                <ScalarProperty Name="CreationReasonType" ColumnName="CreationReasonType" />
                <ScalarProperty Name="SubstituteFlag" ColumnName="SubstituteFlag" />
                <ScalarProperty Name="CustomerPriceChangeFlag" ColumnName="CustomerPriceChangeFlag" />
                <ScalarProperty Name="CustomerCurrencyCode" ColumnName="CustomerCurrencyCode" />
                <ScalarProperty Name="CompanyCodeCurrency" ColumnName="CompanyCodeCurrency" />
                <ScalarProperty Name="CompanyCode" ColumnName="CompanyCode" />
                <ScalarProperty Name="Points" ColumnName="Points" />
                <ScalarProperty Name="Period" ColumnName="Period" />
                <ScalarProperty Name="LevelID" ColumnName="LevelID" />
                <ScalarProperty Name="AgreementID" ColumnName="AgreementID" />
                <ScalarProperty Name="CustomerReservationQty" ColumnName="CustomerReservationQty" />
                <ScalarProperty Name="CustomerReservationNumber" ColumnName="CustomerReservationNumber" />
                <ScalarProperty Name="DiscountInPercent" ColumnName="DiscountInPercent" />
                <ScalarProperty Name="TaxPercent" ColumnName="TaxPercent" />
                <ScalarProperty Name="RetailPriceWithTax" ColumnName="RetailPriceWithTax" />
                <ScalarProperty Name="CustomerPriceWithTax" ColumnName="CustomerPriceWithTax" />
                <ScalarProperty Name="BatteryFees" ColumnName="BatteryFees" />
                <ScalarProperty Name="DocumentCopyrightFees" ColumnName="DocumentCopyrightFees" />
                <ScalarProperty Name="RecyclingFees" ColumnName="RecyclingFees" />
                <ScalarProperty Name="MusicCopyrightFees" ColumnName="MusicCopyrightFees" />
                <ScalarProperty Name="FulfilmentType" ColumnName="FulfilmentType" />
                <ScalarProperty Name="ReleaseDate" ColumnName="ReleaseDate" />
                <ScalarProperty Name="Discontinued" ColumnName="Discontinued" />
                <ScalarProperty Name="MaterialStatus" ColumnName="MaterialStatus" />
                <ScalarProperty Name="DirectOrderIdentifier" ColumnName="DirectOrderIdentifier" />
                <ScalarProperty Name="PalletQty" ColumnName="PalletQty" />
                <ScalarProperty Name="CaseQty" ColumnName="CaseQty" />
                <ScalarProperty Name="VendorSubrange" ColumnName="VendorSubrange" />
                <ScalarProperty Name="NewMaterialFlag" ColumnName="NewMaterialFlag" />
                <ScalarProperty Name="PriceStatus" ColumnName="PriceStatus" />
                <ScalarProperty Name="ActionCode" ColumnName="ActionCode" />
                <ScalarProperty Name="MaterialChangeCode" ColumnName="MaterialChangeCode" />
                <ScalarProperty Name="PurchasingVendor" ColumnName="PurchasingVendor" />
                <ScalarProperty Name="ProductFamily" ColumnName="ProductFamily" />
                <ScalarProperty Name="SubCategory" ColumnName="SubCategory" />
                <ScalarProperty Name="ManufacturerVendorNumber" ColumnName="ManufacturerVendorNumber" />
                <ScalarProperty Name="SupersededMaterial" ColumnName="SupersededMaterial" />
                <ScalarProperty Name="SubstituteMaterial" ColumnName="SubstituteMaterial" />
                <ScalarProperty Name="MaterialLanguageCode" ColumnName="MaterialLanguageCode" />
                <ScalarProperty Name="MediaCode" ColumnName="MediaCode" />
                <ScalarProperty Name="MaterialCreationReasonCode" ColumnName="MaterialCreationReasonCode" />
                <ScalarProperty Name="Category" ColumnName="Category" />
                <ScalarProperty Name="VolumeUnit" ColumnName="VolumeUnit" />
                <ScalarProperty Name="WeightUnit" ColumnName="WeightUnit" />
                <ScalarProperty Name="DimensionUnit" ColumnName="DimensionUnit" />
                <ScalarProperty Name="Height" ColumnName="Height" />
                <ScalarProperty Name="Width" ColumnName="Width" />
                <ScalarProperty Name="Length" ColumnName="Length" />
                <ScalarProperty Name="MaterialLongDescription" ColumnName="MaterialLongDescription" />
                <ScalarProperty Name="BulkFreightFlag" ColumnName="BulkFreightFlag" />
                <ScalarProperty Name="WarrantyFlag" ColumnName="WarrantyFlag" />
                <ScalarProperty Name="BOMFlag" ColumnName="BOMFlag" />
                <ScalarProperty Name="LicenseFlag" ColumnName="LicenseFlag" />
                <ScalarProperty Name="BacklogETA" ColumnName="BacklogETA" />
                <ScalarProperty Name="BacklogInformation" ColumnName="BacklogInformation" />
                <ScalarProperty Name="AvailableQuantity" ColumnName="AvailableQuantity" />
                <ScalarProperty Name="AvailabilityFlag" ColumnName="AvailabilityFlag" />
                <ScalarProperty Name="RetailPrice" ColumnName="RetailPrice" />
                <ScalarProperty Name="CustomerPrice" ColumnName="CustomerPrice" />
                <ScalarProperty Name="CategoryID" ColumnName="CategoryID" />
                <ScalarProperty Name="Unit" ColumnName="Unit" />
                <ScalarProperty Name="Volume" ColumnName="Volume" />
                <ScalarProperty Name="Weight" ColumnName="Weight" />
                <ScalarProperty Name="Size" ColumnName="Size" />
                <ScalarProperty Name="VendorName" ColumnName="VendorName" />
                <ScalarProperty Name="VendorNumber" ColumnName="VendorNumber" />
                <ScalarProperty Name="Plant" ColumnName="Plant" />
                <ScalarProperty Name="EANUPCCode" ColumnName="EANUPCCode" />
                <ScalarProperty Name="VendorPartNumber" ColumnName="VendorPartNumber" />
                <ScalarProperty Name="CustomerPartNumber" ColumnName="CustomerPartNumber" />
                <ScalarProperty Name="IngramPartDescription" ColumnName="IngramPartDescription" />
                <ScalarProperty Name="IngramPartNumber" ColumnName="IngramPartNumber" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="KqmDeletedProductsFromQuote">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.KqmDeletedProductsFromQuote">
              <MappingFragment StoreEntitySet="KqmDeletedProductsFromQuote">
                <ScalarProperty Name="KqmQuoteId" ColumnName="KqmQuoteId" />
                <ScalarProperty Name="KqmProductId" ColumnName="KqmProductId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="KQMProducts">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.KQMProducts">
              <MappingFragment StoreEntitySet="KQMProducts">
                <ScalarProperty Name="ShortDescription" ColumnName="ShortDescription" />
                <ScalarProperty Name="KqmQuoteId" ColumnName="KqmQuoteId" />
                <ScalarProperty Name="AppliedTransformationPackage" ColumnName="AppliedTransformationPackage" />
                <ScalarProperty Name="isRecurring" ColumnName="isRecurring" />
                <ScalarProperty Name="isSerialized" ColumnName="isSerialized" />
                <ScalarProperty Name="isRecommended" ColumnName="isRecommended" />
                <ScalarProperty Name="isActive" ColumnName="isActive" />
                <ScalarProperty Name="isHidden" ColumnName="isHidden" />
                <ScalarProperty Name="retailPrice" ColumnName="retailPrice" />
                <ScalarProperty Name="price" ColumnName="price" />
                <ScalarProperty Name="categoryID" ColumnName="categoryID" />
                <ScalarProperty Name="weight" ColumnName="weight" />
                <ScalarProperty Name="brandID" ColumnName="brandID" />
                <ScalarProperty Name="description" ColumnName="description" />
                <ScalarProperty Name="title" ColumnName="title" />
                <ScalarProperty Name="type" ColumnName="type" />
                <ScalarProperty Name="manufacturerPartNumber" ColumnName="manufacturerPartNumber" />
                <ScalarProperty Name="url" ColumnName="url" />
                <ScalarProperty Name="productNumber" ColumnName="productNumber" />
                <ScalarProperty Name="recurringType" ColumnName="recurringType" />
                <ScalarProperty Name="modifiedDate" ColumnName="modifiedDate" />
                <ScalarProperty Name="createdDate" ColumnName="createdDate" />
                <ScalarProperty Name="cost" ColumnName="cost" />
                <ScalarProperty Name="quantity" ColumnName="quantity" />
                <ScalarProperty Name="productID" ColumnName="productID" />
                <ScalarProperty Name="supplierID" ColumnName="supplierID" />
                <ScalarProperty Name="IsPulished" ColumnName="IsPulished" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="KqmQuotes">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.KqmQuotes">
              <MappingFragment StoreEntitySet="KqmQuotes">
                <ScalarProperty Name="AutoMapNewProducts" ColumnName="AutoMapNewProducts" />
                <ScalarProperty Name="modifiedDate" ColumnName="modifiedDate" />
                <ScalarProperty Name="createdDate" ColumnName="createdDate" />
                <ScalarProperty Name="expiryDate" ColumnName="expiryDate" />
                <ScalarProperty Name="deliveryTaxRate" ColumnName="deliveryTaxRate" />
                <ScalarProperty Name="deliveryTax" ColumnName="deliveryTax" />
                <ScalarProperty Name="deliveryAmount" ColumnName="deliveryAmount" />
                <ScalarProperty Name="deliveryInstruction" ColumnName="deliveryInstruction" />
                <ScalarProperty Name="deliveryCountry" ColumnName="deliveryCountry" />
                <ScalarProperty Name="deliveryPostalCode" ColumnName="deliveryPostalCode" />
                <ScalarProperty Name="deliveryState" ColumnName="deliveryState" />
                <ScalarProperty Name="deliveryCity" ColumnName="deliveryCity" />
                <ScalarProperty Name="deliveryAddress" ColumnName="deliveryAddress" />
                <ScalarProperty Name="deliveryPhone" ColumnName="deliveryPhone" />
                <ScalarProperty Name="deliveryContact" ColumnName="deliveryContact" />
                <ScalarProperty Name="deliveryCompany" ColumnName="deliveryCompany" />
                <ScalarProperty Name="deliveryType" ColumnName="deliveryType" />
                <ScalarProperty Name="contactEmail" ColumnName="contactEmail" />
                <ScalarProperty Name="contactName" ColumnName="contactName" />
                <ScalarProperty Name="customerID" ColumnName="customerID" />
                <ScalarProperty Name="ownerEmployeeID" ColumnName="ownerEmployeeID" />
                <ScalarProperty Name="privateNote" ColumnName="privateNote" />
                <ScalarProperty Name="status" ColumnName="status" />
                <ScalarProperty Name="title" ColumnName="title" />
                <ScalarProperty Name="quoteNumber" ColumnName="quoteNumber" />
                <ScalarProperty Name="code" ColumnName="code" />
                <ScalarProperty Name="salesOrderId" ColumnName="salesOrderId" />
                <ScalarProperty Name="quoteId" ColumnName="quoteId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="MappedKqmProductsForNopCategory">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.MappedKqmProductsForNopCategory">
              <MappingFragment StoreEntitySet="MappedKqmProductsForNopCategory">
                <ScalarProperty Name="PriceOverride" ColumnName="PriceOverride" />
                <ScalarProperty Name="NopCategoryId" ColumnName="NopCategoryId" />
                <ScalarProperty Name="MappingType" ColumnName="MappingType" />
                <ScalarProperty Name="AppliedTransformationPackage" ColumnName="AppliedTransformationPackage" />
                <ScalarProperty Name="KqmQuoteId" ColumnName="KqmQuoteId" />
                <ScalarProperty Name="isRecurring" ColumnName="isRecurring" />
                <ScalarProperty Name="isSerialized" ColumnName="isSerialized" />
                <ScalarProperty Name="isRecommended" ColumnName="isRecommended" />
                <ScalarProperty Name="isActive" ColumnName="isActive" />
                <ScalarProperty Name="isHidden" ColumnName="isHidden" />
                <ScalarProperty Name="retailPrice" ColumnName="retailPrice" />
                <ScalarProperty Name="price" ColumnName="price" />
                <ScalarProperty Name="categoryID" ColumnName="categoryID" />
                <ScalarProperty Name="weight" ColumnName="weight" />
                <ScalarProperty Name="brandID" ColumnName="brandID" />
                <ScalarProperty Name="ShortDescription" ColumnName="ShortDescription" />
                <ScalarProperty Name="description" ColumnName="description" />
                <ScalarProperty Name="title" ColumnName="title" />
                <ScalarProperty Name="type" ColumnName="type" />
                <ScalarProperty Name="manufacturerPartNumber" ColumnName="manufacturerPartNumber" />
                <ScalarProperty Name="url" ColumnName="url" />
                <ScalarProperty Name="productNumber" ColumnName="productNumber" />
                <ScalarProperty Name="recurringType" ColumnName="recurringType" />
                <ScalarProperty Name="modifiedDate" ColumnName="modifiedDate" />
                <ScalarProperty Name="createdDate" ColumnName="createdDate" />
                <ScalarProperty Name="cost" ColumnName="cost" />
                <ScalarProperty Name="quantity" ColumnName="quantity" />
                <ScalarProperty Name="productID" ColumnName="productID" />
                <ScalarProperty Name="supplierID" ColumnName="supplierID" />
                <ScalarProperty Name="IsPulished" ColumnName="IsPulished" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductsCheaperAtCompetitor">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.ProductsCheaperAtCompetitor">
              <MappingFragment StoreEntitySet="ProductsCheaperAtCompetitor">
                <ScalarProperty Name="CompetitorPrice" ColumnName="CompetitorPrice" />
                <ScalarProperty Name="ProductSku" ColumnName="ProductSku" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductSyncLog">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.ProductSyncLog">
              <MappingFragment StoreEntitySet="ProductSyncLog">
                <ScalarProperty Name="Message" ColumnName="Message" />
                <ScalarProperty Name="SyncDateTime" ColumnName="SyncDateTime" />
                <ScalarProperty Name="ProductId" ColumnName="ProductId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ImportedPriceBooks">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.ImportedPriceBooks">
              <MappingFragment StoreEntitySet="ImportedPriceBooks">
                <ScalarProperty Name="Archived" ColumnName="Archived" />
                <ScalarProperty Name="AutoMapNewItems" ColumnName="AutoMapNewItems" />
                <ScalarProperty Name="Supplier" ColumnName="Supplier" />
                <ScalarProperty Name="ImportedDateTime" ColumnName="ImportedDateTime" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductSourceList">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.ProductSourceList">
              <MappingFragment StoreEntitySet="ProductSourceList">
                <ScalarProperty Name="FileName" ColumnName="FileName" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductSourceTypeToNopCategoryMapping">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.ProductSourceTypeToNopCategoryMapping">
              <MappingFragment StoreEntitySet="ProductSourceTypeToNopCategoryMapping">
                <ScalarProperty Name="NopCategoryId" ColumnName="NopCategoryId" />
                <ScalarProperty Name="SourceTypeId" ColumnName="SourceTypeId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductSourceStatus">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.ProductSourceStatus">
              <MappingFragment StoreEntitySet="ProductSourceStatus">
                <ScalarProperty Name="Active" ColumnName="Active" />
                <ScalarProperty Name="Publishable" ColumnName="Publishable" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductSourceImages">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.ProductSourceImages">
              <MappingFragment StoreEntitySet="ProductSourceImages">
                <ScalarProperty Name="ImageUrl" ColumnName="ImageUrl" />
                <ScalarProperty Name="ImageBytes" ColumnName="ImageBytes" />
                <ScalarProperty Name="ProductSourceId" ColumnName="ProductSourceId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="FandoogleReflectorCache">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.FandoogleReflectorCache">
              <MappingFragment StoreEntitySet="FandoogleReflectorCache">
                <ScalarProperty Name="OriginalPrice" ColumnName="OriginalPrice" />
                <ScalarProperty Name="RandomisedPrice" ColumnName="RandomisedPrice" />
                <ScalarProperty Name="ExpiresAt" ColumnName="ExpiresAt" />
                <ScalarProperty Name="ProductSku" ColumnName="ProductSku" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PriceBookItemSellPriceOverride">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.PriceBookItemSellPriceOverride">
              <MappingFragment StoreEntitySet="PriceBookItemSellPriceOverride">
                <ScalarProperty Name="SellPrice" ColumnName="SellPrice" />
                <ScalarProperty Name="PriceBookItemId" ColumnName="PriceBookItemId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="AIGeneratedProductInfo">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.AIGeneratedProductInfo">
              <MappingFragment StoreEntitySet="AIGeneratedProductInfo">
                <ScalarProperty Name="Value" ColumnName="Value" />
                <ScalarProperty Name="Attribute" ColumnName="Attribute" />
                <ScalarProperty Name="ProductSource" ColumnName="ProductSource" />
                <ScalarProperty Name="ProductId" ColumnName="ProductId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PreflightLog">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.PreflightLog">
              <MappingFragment StoreEntitySet="PreflightLog">
                <ScalarProperty Name="Message" ColumnName="Message" />
                <ScalarProperty Name="LogDateTime" ColumnName="LogDateTime" />
                <ScalarProperty Name="ProductSourceId" ColumnName="ProductSourceId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TakeoffLog">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.TakeoffLog">
              <MappingFragment StoreEntitySet="TakeoffLog">
                <ScalarProperty Name="Message" ColumnName="Message" />
                <ScalarProperty Name="LogDateTime" ColumnName="LogDateTime" />
                <ScalarProperty Name="ProductSourceId" ColumnName="ProductSourceId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductSourceTypesForNopCategory">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.ProductSourceTypesForNopCategory">
              <MappingFragment StoreEntitySet="ProductSourceTypesForNopCategory">
                <ScalarProperty Name="CategoryName" ColumnName="CategoryName" />
                <ScalarProperty Name="CategoryId" ColumnName="CategoryId" />
                <ScalarProperty Name="SourceTypeFileName" ColumnName="SourceTypeFileName" />
                <ScalarProperty Name="SourceTypeName" ColumnName="SourceTypeName" />
                <ScalarProperty Name="SourceTypeId" ColumnName="SourceTypeId" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductSourceSellPriceOverride">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.ProductSourceSellPriceOverride">
              <MappingFragment StoreEntitySet="ProductSourceSellPriceOverride">
                <ScalarProperty Name="ActionedBy" ColumnName="ActionedBy" />
                <ScalarProperty Name="Note" ColumnName="Note" />
                <ScalarProperty Name="ExpiryDate" ColumnName="ExpiryDate" />
                <ScalarProperty Name="SellPrice" ColumnName="SellPrice" />
                <ScalarProperty Name="ProductSourceId" ColumnName="ProductSourceId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <FunctionImportMapping FunctionImportName="GetProductsCheaperAtCompetitorCount" FunctionName="EdunetDatafeedsModel.Store.GetProductsCheaperAtCompetitorCount" />
          <FunctionImportMapping FunctionImportName="GetDisabledProductSourceCount" FunctionName="EdunetDatafeedsModel.Store.GetDisabledProductSourceCount" />
          <EntitySetMapping Name="DisabledProductsSanitised">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.DisabledProductsSanitised">
              <MappingFragment StoreEntitySet="DisabledProductsSanitised">
                <ScalarProperty Name="DoogleCount" ColumnName="DoogleCount" />
                <ScalarProperty Name="ImageCount" ColumnName="ImageCount" />
                <ScalarProperty Name="Reason" ColumnName="Reason" />
                <ScalarProperty Name="Brand" ColumnName="Brand" />
                <ScalarProperty Name="QtyAvailable" ColumnName="QtyAvailable" />
                <ScalarProperty Name="Status" ColumnName="Status" />
                <ScalarProperty Name="SellPrice" ColumnName="SellPrice" />
                <ScalarProperty Name="CostPrice" ColumnName="CostPrice" />
                <ScalarProperty Name="ProductShortDescription" ColumnName="ProductShortDescription" />
                <ScalarProperty Name="ProductLongDescription" ColumnName="ProductLongDescription" />
                <ScalarProperty Name="ProductTitle" ColumnName="ProductTitle" />
                <ScalarProperty Name="ProductSku" ColumnName="ProductSku" />
                <ScalarProperty Name="SourceTypeIds" ColumnName="SourceTypeIds" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DisabledPriceBookProductsSanitised">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.DisabledPriceBookProductsSanitised">
              <MappingFragment StoreEntitySet="DisabledPriceBookProductsSanitised">
                <ScalarProperty Name="Reason" ColumnName="Reason" />
                <ScalarProperty Name="StockOnHand" ColumnName="StockOnHand" />
                <ScalarProperty Name="CostPrice" ColumnName="CostPrice" />
                <ScalarProperty Name="Brand" ColumnName="Brand" />
                <ScalarProperty Name="ImageURLs" ColumnName="ImageURLs" />
                <ScalarProperty Name="ProductShortDescription" ColumnName="ProductShortDescription" />
                <ScalarProperty Name="IsPublished" ColumnName="IsPublished" />
                <ScalarProperty Name="AppliedTransformationPackage" ColumnName="AppliedTransformationPackage" />
                <ScalarProperty Name="ProductPrice" ColumnName="ProductPrice" />
                <ScalarProperty Name="ProductDescription" ColumnName="ProductDescription" />
                <ScalarProperty Name="ProductSKU" ColumnName="ProductSKU" />
                <ScalarProperty Name="ProductName" ColumnName="ProductName" />
                <ScalarProperty Name="PriceBookID" ColumnName="PriceBookID" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="NewlyImportedPriceBookItems">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.NewlyImportedPriceBookItems">
              <MappingFragment StoreEntitySet="NewlyImportedPriceBookItems">
                <ScalarProperty Name="ImportedOn" ColumnName="ImportedOn" />
                <ScalarProperty Name="PriceBookItemId" ColumnName="PriceBookItemId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="MappedPriceBookItemsForNopCategory">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.MappedPriceBookItemsForNopCategory">
              <MappingFragment StoreEntitySet="MappedPriceBookItemsForNopCategory">
                <ScalarProperty Name="ImageUrls" ColumnName="ImageUrls" />
                <ScalarProperty Name="PriceOverride" ColumnName="PriceOverride" />
                <ScalarProperty Name="NopCategoryId" ColumnName="NopCategoryId" />
                <ScalarProperty Name="MappingType" ColumnName="MappingType" />
                <ScalarProperty Name="CategoryAppliedTransformationPackage" ColumnName="CategoryAppliedTransformationPackage" />
                <ScalarProperty Name="PriceBookArchived" ColumnName="PriceBookArchived" />
                <ScalarProperty Name="StockOnHand" ColumnName="StockOnHand" />
                <ScalarProperty Name="Brand" ColumnName="Brand" />
                <ScalarProperty Name="IsPublished" ColumnName="IsPublished" />
                <ScalarProperty Name="AppliedTransformationPackage" ColumnName="AppliedTransformationPackage" />
                <ScalarProperty Name="CostPrice" ColumnName="CostPrice" />
                <ScalarProperty Name="ProductPrice" ColumnName="ProductPrice" />
                <ScalarProperty Name="ProductShortDescription" ColumnName="ProductShortDescription" />
                <ScalarProperty Name="ProductDescription" ColumnName="ProductDescription" />
                <ScalarProperty Name="ProductSKU" ColumnName="ProductSKU" />
                <ScalarProperty Name="ProductName" ColumnName="ProductName" />
                <ScalarProperty Name="PriceBookID" ColumnName="PriceBookID" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ImportedItemsFromPriceBook">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.ImportedItemsFromPriceBook">
              <MappingFragment StoreEntitySet="ImportedItemsFromPriceBook">
                <ScalarProperty Name="StockOnHand" ColumnName="StockOnHand" />
                <ScalarProperty Name="CostPrice" ColumnName="CostPrice" />
                <ScalarProperty Name="Brand" ColumnName="Brand" />
                <ScalarProperty Name="ImageURLs" ColumnName="ImageURLs" />
                <ScalarProperty Name="ProductShortDescription" ColumnName="ProductShortDescription" />
                <ScalarProperty Name="IsPublished" ColumnName="IsPublished" />
                <ScalarProperty Name="AppliedTransformationPackage" ColumnName="AppliedTransformationPackage" />
                <ScalarProperty Name="ProductPrice" ColumnName="ProductPrice" />
                <ScalarProperty Name="ProductDescription" ColumnName="ProductDescription" />
                <ScalarProperty Name="ProductSKU" ColumnName="ProductSKU" />
                <ScalarProperty Name="ProductName" ColumnName="ProductName" />
                <ScalarProperty Name="PriceBookID" ColumnName="PriceBookID" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PreflightFlagStatus">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.PreflightFlagStatus">
              <MappingFragment StoreEntitySet="PreflightFlagStatus">
                <ScalarProperty Name="Html" ColumnName="Html" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PriceBookItemImages">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.PriceBookItemImages">
              <MappingFragment StoreEntitySet="PriceBookItemImages">
                <ScalarProperty Name="Image" ColumnName="Image" />
                <ScalarProperty Name="ImageURL" ColumnName="ImageURL" />
                <ScalarProperty Name="PriceBookItemID" ColumnName="PriceBookItemID" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductsCheaperAtCompetitorSanitised">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.ProductsCheaperAtCompetitorSanitised">
              <MappingFragment StoreEntitySet="ProductsCheaperAtCompetitorSanitised">
                <ScalarProperty Name="ProductTitle" ColumnName="ProductTitle" />
                <ScalarProperty Name="Brand" ColumnName="Brand" />
                <ScalarProperty Name="OurCostPrice" ColumnName="OurCostPrice" />
                <ScalarProperty Name="CompetitorPrice" ColumnName="CompetitorPrice" />
                <ScalarProperty Name="ProductSku" ColumnName="ProductSku" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PriceBookItemCostPriceOverride">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.PriceBookItemCostPriceOverride">
              <MappingFragment StoreEntitySet="PriceBookItemCostPriceOverride">
                <ScalarProperty Name="ActionedBy" ColumnName="ActionedBy" />
                <ScalarProperty Name="Note" ColumnName="Note" />
                <ScalarProperty Name="ExpiryDate" ColumnName="ExpiryDate" />
                <ScalarProperty Name="CostPrice" ColumnName="CostPrice" />
                <ScalarProperty Name="PriceBookItemId" ColumnName="PriceBookItemId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductSourceCostPriceOverride">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.ProductSourceCostPriceOverride">
              <MappingFragment StoreEntitySet="ProductSourceCostPriceOverride">
                <ScalarProperty Name="ActionedBy" ColumnName="ActionedBy" />
                <ScalarProperty Name="Note" ColumnName="Note" />
                <ScalarProperty Name="ExpiryDate" ColumnName="ExpiryDate" />
                <ScalarProperty Name="CostPrice" ColumnName="CostPrice" />
                <ScalarProperty Name="ProductSourceId" ColumnName="ProductSourceId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductSource">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.ProductSource">
              <MappingFragment StoreEntitySet="ProductSource">
                <ScalarProperty Name="Archived" ColumnName="Archived" />
                <ScalarProperty Name="IcecatImagesPreflightFlag" ColumnName="IcecatImagesPreflightFlag" />
                <ScalarProperty Name="IcecatLongDescriptionPreflightFlag" ColumnName="IcecatLongDescriptionPreflightFlag" />
                <ScalarProperty Name="IcecatShortDescriptionPreflightFlag" ColumnName="IcecatShortDescriptionPreflightFlag" />
                <ScalarProperty Name="KaseyaCostPricePreflightFlag" ColumnName="KaseyaCostPricePreflightFlag" />
                <ScalarProperty Name="KaseyaImagesPreflightFlag" ColumnName="KaseyaImagesPreflightFlag" />
                <ScalarProperty Name="KaseyaRecommendedSellPricePreflightFlag" ColumnName="KaseyaRecommendedSellPricePreflightFlag" />
                <ScalarProperty Name="CompetitorPricePreflightFlag" ColumnName="CompetitorPricePreflightFlag" />
                <ScalarProperty Name="KaseyaLongDescriptionPreflightFlag" ColumnName="KaseyaLongDescriptionPreflightFlag" />
                <ScalarProperty Name="KaseyaShortDescriptionPreflightFlag" ColumnName="KaseyaShortDescriptionPreflightFlag" />
                <ScalarProperty Name="Brand" ColumnName="Brand" />
                <ScalarProperty Name="QtyAvailable" ColumnName="QtyAvailable" />
                <ScalarProperty Name="Status" ColumnName="Status" />
                <ScalarProperty Name="SellPrice" ColumnName="SellPrice" />
                <ScalarProperty Name="CostPrice" ColumnName="CostPrice" />
                <ScalarProperty Name="ProductShortDescription" ColumnName="ProductShortDescription" />
                <ScalarProperty Name="ProductLongDescription" ColumnName="ProductLongDescription" />
                <ScalarProperty Name="ProductTitle" ColumnName="ProductTitle" />
                <ScalarProperty Name="ProductSku" ColumnName="ProductSku" />
                <ScalarProperty Name="SourceTypeIds" ColumnName="SourceTypeIds" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductSourcesForNopCategory">
            <EntityTypeMapping TypeName="EdunetDatafeedsModel.ProductSourcesForNopCategory">
              <MappingFragment StoreEntitySet="ProductSourcesForNopCategory">
                <ScalarProperty Name="Archived" ColumnName="Archived" />
                <ScalarProperty Name="DisabledProductImageCount" ColumnName="DisabledProductImageCount" />
                <ScalarProperty Name="DisabledProductDoogleCount" ColumnName="DisabledProductDoogleCount" />
                <ScalarProperty Name="DisabledReason" ColumnName="DisabledReason" />
                <ScalarProperty Name="PriceOverride" ColumnName="PriceOverride" />
                <ScalarProperty Name="StatusActive" ColumnName="StatusActive" />
                <ScalarProperty Name="StatusPublishable" ColumnName="StatusPublishable" />
                <ScalarProperty Name="StatusName" ColumnName="StatusName" />
                <ScalarProperty Name="StatusId" ColumnName="StatusId" />
                <ScalarProperty Name="QtyAvailable" ColumnName="QtyAvailable" />
                <ScalarProperty Name="SellPrice" ColumnName="SellPrice" />
                <ScalarProperty Name="CostPrice" ColumnName="CostPrice" />
                <ScalarProperty Name="ProductLongDescription" ColumnName="ProductLongDescription" />
                <ScalarProperty Name="ProductShortDescription" ColumnName="ProductShortDescription" />
                <ScalarProperty Name="ProductSku" ColumnName="ProductSku" />
                <ScalarProperty Name="ProductTitle" ColumnName="ProductTitle" />
                <ScalarProperty Name="Brand" ColumnName="Brand" />
                <ScalarProperty Name="SourceTypeName" ColumnName="SourceTypeName" />
                <ScalarProperty Name="SourceTypeId" ColumnName="SourceTypeId" />
                <ScalarProperty Name="CategoryName" ColumnName="CategoryName" />
                <ScalarProperty Name="CategoryId" ColumnName="CategoryId" />
                <ScalarProperty Name="ID" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="false" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>
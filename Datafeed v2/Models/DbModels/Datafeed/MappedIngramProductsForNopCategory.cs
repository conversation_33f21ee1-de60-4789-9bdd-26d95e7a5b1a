//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Datafeed_v2.Models.DbModels.Datafeed
{
    using System;
    using System.Collections.Generic;
    
    public partial class MappedIngramProductsForNopCategory
    {
        public int ID { get; set; }
        public string IngramPartNumber { get; set; }
        public string IngramPartDescription { get; set; }
        public string CustomerPartNumber { get; set; }
        public string VendorPartNumber { get; set; }
        public string EANUPCCode { get; set; }
        public string Plant { get; set; }
        public Nullable<int> VendorNumber { get; set; }
        public string VendorName { get; set; }
        public string Size { get; set; }
        public Nullable<double> Weight { get; set; }
        public Nullable<decimal> Volume { get; set; }
        public string Unit { get; set; }
        public string CategoryID { get; set; }
        public Nullable<decimal> CustomerPrice { get; set; }
        public Nullable<decimal> RetailPrice { get; set; }
        public string AvailabilityFlag { get; set; }
        public Nullable<int> AvailableQuantity { get; set; }
        public Nullable<int> BacklogInformation { get; set; }
        public string BacklogETA { get; set; }
        public string LicenseFlag { get; set; }
        public string BOMFlag { get; set; }
        public string WarrantyFlag { get; set; }
        public string BulkFreightFlag { get; set; }
        public string MaterialLongDescription { get; set; }
        public Nullable<double> Length { get; set; }
        public Nullable<double> Width { get; set; }
        public Nullable<double> Height { get; set; }
        public string DimensionUnit { get; set; }
        public string WeightUnit { get; set; }
        public string VolumeUnit { get; set; }
        public Nullable<int> Category { get; set; }
        public string MaterialCreationReasonCode { get; set; }
        public string MediaCode { get; set; }
        public string MaterialLanguageCode { get; set; }
        public string SubstituteMaterial { get; set; }
        public string SupersededMaterial { get; set; }
        public Nullable<int> ManufacturerVendorNumber { get; set; }
        public string SubCategory { get; set; }
        public string ProductFamily { get; set; }
        public string PurchasingVendor { get; set; }
        public string MaterialChangeCode { get; set; }
        public string ActionCode { get; set; }
        public string PriceStatus { get; set; }
        public string NewMaterialFlag { get; set; }
        public string VendorSubrange { get; set; }
        public Nullable<int> CaseQty { get; set; }
        public Nullable<int> PalletQty { get; set; }
        public string DirectOrderIdentifier { get; set; }
        public string MaterialStatus { get; set; }
        public string Discontinued { get; set; }
        public string ReleaseDate { get; set; }
        public string FulfilmentType { get; set; }
        public Nullable<int> MusicCopyrightFees { get; set; }
        public Nullable<int> RecyclingFees { get; set; }
        public Nullable<int> DocumentCopyrightFees { get; set; }
        public Nullable<int> BatteryFees { get; set; }
        public Nullable<decimal> CustomerPriceWithTax { get; set; }
        public Nullable<decimal> RetailPriceWithTax { get; set; }
        public Nullable<decimal> TaxPercent { get; set; }
        public string DiscountInPercent { get; set; }
        public string CustomerReservationNumber { get; set; }
        public string CustomerReservationQty { get; set; }
        public string AgreementID { get; set; }
        public string LevelID { get; set; }
        public string Period { get; set; }
        public string Points { get; set; }
        public Nullable<int> CompanyCode { get; set; }
        public string CompanyCodeCurrency { get; set; }
        public string CustomerCurrencyCode { get; set; }
        public string CustomerPriceChangeFlag { get; set; }
        public string SubstituteFlag { get; set; }
        public string CreationReasonType { get; set; }
        public string CreationReasonValue { get; set; }
        public Nullable<decimal> Plant01AvailableQuantity { get; set; }
        public string Plant02AvailableQuantity { get; set; }
        public string CategoryName { get; set; }
        public string SubCategoryName { get; set; }
        public Nullable<int> AppliedTransformationPackage { get; set; }
        public string MappingType { get; set; }
        public int NopCategoryId { get; set; }
        public Nullable<decimal> PriceOverride { get; set; }
    }
}

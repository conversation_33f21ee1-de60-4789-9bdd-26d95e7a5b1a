//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Datafeed_v2.Models.DbModels.Datafeed
{
    using System;
    using System.Collections.Generic;
    
    public partial class ImportedPriceBooks
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public ImportedPriceBooks()
        {
            this.PriceBookToNopCategoryMapping = new HashSet<PriceBookToNopCategoryMapping>();
            this.ImportedItemsFromPriceBook = new HashSet<ImportedItemsFromPriceBook>();
        }
    
        public int ID { get; set; }
        public string Name { get; set; }
        public System.DateTime ImportedDateTime { get; set; }
        public string Supplier { get; set; }
        public bool AutoMapNewItems { get; set; }
        public bool Archived { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PriceBookToNopCategoryMapping> PriceBookToNopCategoryMapping { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<ImportedItemsFromPriceBook> ImportedItemsFromPriceBook { get; set; }
    }
}

using System.Linq;
using CSharpFunctionalExtensions;

namespace Datafeed_v2.Models.DbModels.Datafeed;

public partial class NopCategories
{
    public Maybe<CommerceTransformationPackages>
        AppliedTransformationPackageClass
    {
        get;
        set;
    } // this is set in the GetNopCategories function of NopCategoriesController

    public bool IsParentCategory
    {
        get
        {
            var dbDataFeedsContext = new EdunetDatafeedsEntities();
            return dbDataFeedsContext.NopCategories.Any(q => q.ParentCategoryId == CategoryId);
        }
    }
}
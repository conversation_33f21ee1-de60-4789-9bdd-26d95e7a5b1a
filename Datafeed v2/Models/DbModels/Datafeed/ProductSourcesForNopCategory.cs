//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Datafeed_v2.Models.DbModels.Datafeed
{
    using System;
    using System.Collections.Generic;
    
    public partial class ProductSourcesForNopCategory
    {
        public int ID { get; set; }
        public int CategoryId { get; set; }
        public string CategoryName { get; set; }
        public int SourceTypeId { get; set; }
        public string SourceTypeName { get; set; }
        public string Brand { get; set; }
        public string ProductTitle { get; set; }
        public string ProductSku { get; set; }
        public string ProductShortDescription { get; set; }
        public string ProductLongDescription { get; set; }
        public Nullable<decimal> CostPrice { get; set; }
        public Nullable<decimal> SellPrice { get; set; }
        public int QtyAvailable { get; set; }
        public int StatusId { get; set; }
        public string StatusName { get; set; }
        public bool StatusPublishable { get; set; }
        public bool StatusActive { get; set; }
        public Nullable<decimal> PriceOverride { get; set; }
        public string DisabledReason { get; set; }
        public Nullable<int> DisabledProductDoogleCount { get; set; }
        public Nullable<int> DisabledProductImageCount { get; set; }
        public bool Archived { get; set; }
    }
}

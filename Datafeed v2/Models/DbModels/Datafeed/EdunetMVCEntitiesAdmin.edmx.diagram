<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
 <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <!-- Diagram content (shape and connector positions) -->
    <edmx:Diagrams>
      <Diagram DiagramId="b06a25ee5eb042ab97bd8d151cee31fe" Name="Diagram1">
        <EntityTypeShape EntityType="EdunetMVCAdmin.EmailActionTemplateMap" Width="1.5" PointX="0.75" PointY="0.75" IsExpanded="true" />
        <EntityTypeShape EntityType="EdunetMVCAdmin.EmailActionTemplateMapEndpoints" Width="1.5" PointX="2.75" PointY="0.75" IsExpanded="true" />
        <EntityTypeShape EntityType="EdunetMVCAdmin.EmailTemplates" Width="1.5" PointX="0.75" PointY="3.75" />
        <EntityTypeShape EntityType="EdunetMVCAdmin.EmailActions" Width="1.5" PointX="3.375" PointY="3.75" />
      </Diagram>
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>
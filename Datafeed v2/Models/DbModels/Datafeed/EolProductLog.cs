//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Datafeed_v2.Models.DbModels.Datafeed
{
    using System;
    using System.Collections.Generic;
    
    /// <summary>
    /// Represents a log entry for End of Life (EOL) product checks.
    /// Records the results of checking whether products are still active in KQM.
    /// </summary>
    public partial class EolProductLog
    {
        /// <summary>
        /// Primary key for the EOL product log entry
        /// </summary>
        public int ID { get; set; }
        
        /// <summary>
        /// The SKU that was checked for EOL status
        /// </summary>
        public string SKU { get; set; }
        
        /// <summary>
        /// The source of the product - either "ProductSource" or "ImportedItemsFromPriceBook"
        /// </summary>
        public string ProductSource { get; set; }
        
        /// <summary>
        /// The ID of the source record (ProductSource.ID or ImportedItemsFromPriceBook.ID)
        /// </summary>
        public int ProductSourceId { get; set; }
        
        /// <summary>
        /// Whether the product is End of Life (true) or still active (false)
        /// </summary>
        public bool IsEol { get; set; }
        
        /// <summary>
        /// When the EOL check was performed
        /// </summary>
        public DateTime CheckedDateTime { get; set; }
        
        /// <summary>
        /// The KQM product ID if found, null if not found
        /// </summary>
        public Nullable<int> KqmProductId { get; set; }
        
        /// <summary>
        /// Any error message encountered during the check (null if no error)
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}

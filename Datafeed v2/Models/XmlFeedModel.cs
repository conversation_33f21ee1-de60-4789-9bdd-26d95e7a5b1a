using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml.Serialization;

namespace Datafeed_v2.Models;

public class Utf8StringWriter : StringWriter
{
    public override Encoding Encoding { get; } = Encoding.UTF8;
}

[XmlRoot("Catalogue", Namespace = "http://happen.biz/Jim2_StockFeed.xsd")]
public class XmlFeedModel
{
    [XmlElement("ID")] public string ID { get; set; } = "DATAFEED";

    [XmlElement("IssueDateTime")] public DateTime IssueDateTime { get; set; } = DateTime.Now;

    [XmlElement("CatalogueLine")] public List<CatalogueLine> CatalogueLines { get; set; } = [];
}

public class Groups
{
    [XmlElement("Group1")] public string Group1 { get; set; }
    // [XmlElement("Group2")] public string Group2 { get; set; }
    // [XmlElement("Group3")] public string Group3 { get; set; }
    // [XmlElement("Group4")] public string Group4 { get; set; }
    // [XmlElement("Group5")] public string Group5 { get; set; }
}

public class CatalogueLine
{
    [XmlElement("Vendor")] public string Vendor { get; set; }

    [XmlElement("VendorCode")] public string VendorCode { get; set; }

    [XmlElement("ShortDesc")] public string ShortDesc { get; set; }

    [XmlElement("Manufacturer")] public string Manufacturer { get; set; }

    [XmlElement("ManufPartNo")] public string ManufPartNo { get; set; }

    [XmlElement("CurrentPriceInc")] public decimal CurrentPriceInc { get; set; }

    [XmlElement("Groups")] public Groups Groups { get; set; } = new();
}
using System;

namespace Datafeed_v2.Models.ManageProducts
{
    public class ManageProductDataRow
    {
        public uint Id { get; set; }
        public string Type { get; set; } // "ProductSource" or "PriceBook"
        public string Sku { get; set; }
        public string Title { get; set; }
        public decimal CompetitorPrice { get; set; }
        public decimal SellPrice { get; set; } // Includes override if applicable
        public decimal CostPrice { get; set; }
        public decimal Margin { get; set; }
        public int? StatusId { get; set; } // Nullable for PriceBook items
        public string StatusHtml { get; set; } // Generated HTML for status badge
        public string ActionsHtml { get; set; } // Generated HTML for actions
        public string PriceHtml { get; set; } // Generated HTML for price column (competitor, sell, cost)
        public string MarginHtml { get; set; } // Generated HTML for margin badge
        public string CheckboxHtml { get; set; } // Generated HTML for checkbox

        // Additional data needed for generating HTML/JS interactions
        public decimal OriginalSellPrice { get; set; } // Sell price without override
        public decimal? PriceOverrideValue { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string Note { get; set; }
        public int? ActiveOverrideId { get; set; }
    }
}
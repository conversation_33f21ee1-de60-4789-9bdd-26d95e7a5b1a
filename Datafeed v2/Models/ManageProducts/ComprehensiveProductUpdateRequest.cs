using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Datafeed_v2.Models.ManageProducts
{
    public class ComprehensiveProductUpdateRequest
    {
        [Required] public int ProductId { get; set; }

        [Required] public string ProductType { get; set; }

        // Basic Product Properties
        public string Title { get; set; }
        public string Sku { get; set; }
        public string Brand { get; set; }
        public string ShortDescription { get; set; }
        public string LongDescription { get; set; }
        public string Description { get; set; } // For PriceBookItem

        // Price Overrides
        public decimal? SellPriceOverride { get; set; }
        public DateTime? SellPriceOverrideExpiryDate { get; set; }
        public string SellPriceOverrideNote { get; set; }

        public decimal? CostPriceOverride { get; set; }
        public DateTime? CostPriceOverrideExpiryDate { get; set; }
        public string CostPriceOverrideNote { get; set; }

        // Status Override
        public int? StatusOverride { get; set; }
    }

    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }
}
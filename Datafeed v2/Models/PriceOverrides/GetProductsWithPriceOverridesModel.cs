using System.Collections.Generic;
using Datafeed_v2.Models.DbModels.Datafeed;

namespace Datafeed_v2.Models.PriceOverrides;

public class ProductSourceWithOverride
{
    public ProductSource ProductSource { get; set; }
    public decimal? OverridePrice { get; set; }
}

public class PriceBookItemWithOverride
{
    public ImportedItemsFromPriceBook PriceBookItem { get; set; }
    public decimal? OverridePrice { get; set; }
}

public class GetProductsWithPriceOverridesModel
{
    public IEnumerable<ProductSourceWithOverride> ProductSourcesWithSellPriceOverrides { get; set; }
    public IEnumerable<PriceBookItemWithOverride> PriceBookItemsWithSellPriceOverrides { get; set; }
}
namespace Datafeed_v2.Models.KQM
{
    public class ProductsFeedCsvModel
    {
        public string Sku { get; set; }
        public bool VisibleIndividually { get; set; }
        public string Name { get; set; }
        public string ShortDescription { get; set; }
        public string LongDescription { get; set; }
        public bool ShowOnHomePage { get; set; }
        public int QtyAvailable { get; set; }
        public bool DisplayStockQuantity { get; set; }
        public bool DisplayStockAvailability { get; set; }
        public int OrderMinimumQuantity { get; set; }
        public int OrderMaximumQuantity { get; set; }
        public decimal SellPriceInc { get; set; }
        public bool Published { get; set; }
        public uint NopCategoryId { get; set; }
        public string JsonFeatures { get; set; }
        public string JsonSpecifications { get; set; }
        public string Brand { get; set; }
        public uint ProductSourceId { get; set; }
        public uint PriceBookItemId { get; set; }
        public decimal CostPrice { get; set; }
        public bool Archived { get; set; }
    }
}
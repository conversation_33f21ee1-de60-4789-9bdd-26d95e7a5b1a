using System.Collections.Generic;
using System.Linq;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models.DbModels.Datafeed;

namespace Datafeed_v2.Models.KQM;

public class GetProductsForCategoryModel
{
    public bool IsViewAllProducts = false;
    public Maybe<string> AppliedTransformationPackageNameForCategory { get; set; }

    public Maybe<int> AppliedTransformationPackageForCategory { get; set; }

    // public Maybe<DatafeedCategories[]> ChildrenDatafeedCategories { get; set; }
    public Maybe<NopCategories[]> ChildrenCategories { get; set; }
    public IEnumerable<IGrouping<int, PriceBookAndKQMMultiplexedProducts>> Products { get; set; }
    public IEnumerable<CommerceTransformationPackages> TransformationPackages { get; set; }
}
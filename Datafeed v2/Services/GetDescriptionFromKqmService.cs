using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Properties;
using PuppeteerSharp;
using PuppeteerSharp.Input;

// Required for Keyboard.PressAsync

namespace Datafeed_v2.Services;

public interface IGetDescriptionFromKqmService : IDisposable
{
    Task<Result<IEnumerable<KeyValuePair<string, string>>>> FetchDescriptionsAsync(IEnumerable<string> skus,
        CancellationToken cancellationToken = default);
}

public class GetDescriptionFromKqmService(string dataDirectory = null) : IGetDescriptionFromKqmService
{
    private readonly string _dataDirectory = dataDirectory ??
                                             AppDomain.CurrentDomain.GetData("DataDirectory")?.ToString()
                                             ?? throw new ArgumentException("Data directory not found");

    private IBrowser _browser;
    private readonly SemaphoreSlim _semaphore = new(1, 1);

    private readonly TimeSpan
        _defaultTimeout = TimeSpan.FromSeconds(30);

    private static class Selectors
    {
        // Login Page (Admin)
        public const string LoginEmailInput = "#Email";
        public const string ContinueButton = "form[action*=\"/auth/login\"] button[type=\"submit\"]"; // More specific
        public const string LoginPasswordInput = "#Password";

        public const string
            LoginButton = "form[action*=\"/auth/loginpassword\"] button[type=\"submit\"]"; // More specific

        // Admin Area
        public const string ProductsTabLink = "a.sidebar-link[href=\"/admin/products\"]";
        public const string SearchProductInput = "#Query";
        public const string FirstResultLink = ".table > tbody > tr:first-child > td:nth-child(2) > a.table-hover-link";
        public const string DescriptionElement = ".automated-overview";
        public const string BackToProductsBreadcrumb = "a.breadcrumb[href=\"/admin/products\"]";

        public const string
            ProductEditPageIdentifier = "form[action*='/admin/products/edit']"; // To confirm we are on the edit page
    }

    // JavaScript to extract inner HTML content from the description element
    private const string GetDescriptionText = """
                                              function getDescriptionHtml() {
                                                  const element = document.querySelector('.automated-overview');
                                                  return element ? element.innerHTML.trim() : '';
                                              }
                                              """;

    private const string KeyA = "KeyA";

    private const string DescriptionIframeSelector =
        "div.card:nth-child(5) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(3) > iframe:nth-child(1)";


    public async Task<Result<IEnumerable<KeyValuePair<string, string>>>> FetchDescriptionsAsync(
        IEnumerable<string> skus,
        CancellationToken cancellationToken = default)
    {
        var skuList = skus?.ToList();
        if (skuList == null || !skuList.Any() || skuList.Any(string.IsNullOrWhiteSpace))
        {
            return Result.Failure<IEnumerable<KeyValuePair<string, string>>>(
                "SKU list cannot be null, empty, or contain empty SKUs");
        }

        var descriptions = new Dictionary<string, string>();

        try
        {
            await _semaphore.WaitAsync(cancellationToken);
            var browserResult = await InitialiseBrowserAsync(cancellationToken);
            if (browserResult.IsFailure)
            {
                return Result.Failure<IEnumerable<KeyValuePair<string, string>>>(browserResult.Error);
            }

            var page = await _browser.NewPageAsync().ConfigureAwait(false);
            try
            {
                await ConfigurePageAsync(page);
                var loginResult = await LoginAsync(page, cancellationToken);
                if (loginResult.IsFailure)
                {
                    return Result.Failure<IEnumerable<KeyValuePair<string, string>>>(loginResult.Error);
                }

                var navResult = await NavigateToProductsPageAsync(page, cancellationToken);
                if (navResult.IsFailure)
                {
                    return Result.Failure<IEnumerable<KeyValuePair<string, string>>>(navResult.Error);
                }

                foreach (var sku in skuList)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var searchResult = await SearchAndNavigateToProductAsync(page, sku, cancellationToken);
                    if (searchResult.IsFailure)
                    {
                        continue;
                    }

                    var descriptionResult = await ExtractDescriptionAsync(page).ConfigureAwait(false);
                    if (descriptionResult.IsFailure)
                    {
                        return Result.Failure<IEnumerable<KeyValuePair<string, string>>>(
                            $"Failed to extract description for SKU {sku}: {descriptionResult.Error}");
                    }

                    descriptions[sku] = descriptionResult.Value;

                    // Navigate back to the product search results page
                    var backNavResult = await NavigateBackToProductsAsync(page, cancellationToken);
                    if (backNavResult.IsFailure)
                    {
                        return Result.Failure<IEnumerable<KeyValuePair<string, string>>>(
                            $"Failed to navigate back for SKU {sku}: {backNavResult.Error}");
                    }
                }

                return Result.Success(descriptions.ToList().AsEnumerable());
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                // Consider taking a screenshot on error
                // await page.ScreenshotAsync($@"{_dataDirectory}\error_screenshot_{DateTime.Now:yyyyMMddHHmmss}.png");
                return Result.Failure<IEnumerable<KeyValuePair<string, string>>>(
                    $"Failed to fetch descriptions: {ex.Message}");
            }
            finally
            {
                if (page is { IsClosed: false })
                {
                    await page.CloseAsync().ConfigureAwait(false);
                }

                // Consider if browser should be closed here or kept open if service is reused frequently.
                // For Hangfire tasks, closing after each run is safer.
                await CloseBrowserAsync();
            }
        }
        finally
        {
            _semaphore.Release();
        }
    }

    private async Task<Result> InitialiseBrowserAsync(CancellationToken cancellationToken)
    {
        if (_browser is { IsConnected: true }) // Check if connected
        {
            return Result.Success();
        }

        try
        {
            _browser = await Puppeteer.LaunchAsync(new LaunchOptions
            {
                HeadlessMode = HeadlessMode.True,
                ExecutablePath = $@"{_dataDirectory}\chrome-win\chrome.exe",
                Args =
                [
                    "--no-sandbox", "--disable-setuid-sandbox", "--window-size=1920,1080", "--disable-gpu",
                    "--single-process"
                ],
                DefaultViewport = new ViewPortOptions { Width = 1920, Height = 1080 },
            }).ConfigureAwait(false);

            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to initialise browser: {ex.Message}");
        }
    }

    private async Task CloseBrowserAsync()
    {
        if (_browser != null)
        {
            try
            {
                if (_browser.IsConnected)
                {
                    await _browser.CloseAsync().ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error closing browser: {ex.Message}"); // Log error, but don't throw
            }
            finally
            {
                _browser.Dispose(); // Ensure disposal
                _browser = null;
            }
        }
    }

    private async Task ConfigurePageAsync(IPage page)
    {
        page.DefaultNavigationTimeout = (int)_defaultTimeout.TotalMilliseconds;
        page.DefaultTimeout = (int)_defaultTimeout.TotalMilliseconds;
        await page.SetUserAgentAsync(
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" // Use a recent UA
        ).ConfigureAwait(false);
        // Optional: Set viewport
        // await page.SetViewportAsync(new ViewPortOptions { Width = 1920, Height = 1080 });
    }

    private async Task<Result> LoginAsync(IPage page, CancellationToken cancellationToken)
    {
        try
        {
            const string adminLoginUrl = "https://edunet.mykaseyaquotemanager.com/auth/login?returnUrl=%2Fadmin";
            await page.GoToAsync(adminLoginUrl, WaitUntilNavigation.Networkidle2).ConfigureAwait(false);

            // Enter email
            await page.WaitForSelectorAsync(Selectors.LoginEmailInput).ConfigureAwait(false);
            await page.TypeAsync(Selectors.LoginEmailInput, Settings.Default.KqmEstoreUsername).ConfigureAwait(false);
            await page.ClickAsync(Selectors.ContinueButton).ConfigureAwait(false);

            // Enter password
            await page.WaitForSelectorAsync(Selectors.LoginPasswordInput).ConfigureAwait(false);
            await page.TypeAsync(Selectors.LoginPasswordInput, Settings.Default.KqmEstorePassword)
                .ConfigureAwait(false);
            await page.ClickAsync(Selectors.LoginButton).ConfigureAwait(false);

            // Wait for a known element on the admin dashboard after login, e.g., the Products tab link
            await page.WaitForSelectorAsync(Selectors.ProductsTabLink,
                new WaitForSelectorOptions { Timeout = (int)_defaultTimeout.TotalMilliseconds }).ConfigureAwait(false);
            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to login to KQM Admin: {ex.Message}");
        }
    }

    private async Task<Result> NavigateToProductsPageAsync(IPage page, CancellationToken cancellationToken)
    {
        try
        {
            await page.WaitForSelectorAsync(Selectors.ProductsTabLink).ConfigureAwait(false);
            await page.ClickAsync(Selectors.ProductsTabLink).ConfigureAwait(false);

            // Wait for the search input on the products page to confirm navigation
            await page.WaitForSelectorAsync(Selectors.SearchProductInput,
                new WaitForSelectorOptions { Timeout = (int)_defaultTimeout.TotalMilliseconds }).ConfigureAwait(false);
            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to navigate to Products page: {ex.Message}");
        }
    }

    private async Task<Result> SearchAndNavigateToProductAsync(IPage page, string sku,
        CancellationToken cancellationToken)
    {
        const int maxRetries = 3;
        const int retryDelayMs = 1500; // 1.5 second delay
        try
        {
            // Ensure we are on the products page (check for search input)
            await page.WaitForSelectorAsync(Selectors.SearchProductInput).ConfigureAwait(false);

            // Clear existing search (if any) and type SKU
            await page.FocusAsync(Selectors.SearchProductInput).ConfigureAwait(false);
            // Select all text and delete - more reliable than clicking multiple times
            await page.Keyboard.DownAsync(Key.Control).ConfigureAwait(false);
            await page.Keyboard.PressAsync(KeyA).ConfigureAwait(false);
            await page.Keyboard.UpAsync(Key.Control).ConfigureAwait(false);
            await page.Keyboard.PressAsync(Key.Backspace).ConfigureAwait(false);

            await page.TypeAsync(Selectors.SearchProductInput, sku).ConfigureAwait(false);
            var waitForSearchResults = page.WaitForNavigationAsync(new NavigationOptions
            {
                WaitUntil = [WaitUntilNavigation.Networkidle2]
            }).ConfigureAwait(false);
            await page.Keyboard.PressAsync(Key.Enter).ConfigureAwait(false);
            await waitForSearchResults;

            // Wait for search results to load and the first result link to appear, with retries
            var foundResultLink = false;
            for (var attempt = 1; attempt <= maxRetries; attempt++)
            {
                cancellationToken.ThrowIfCancellationRequested();
                try
                {
                    await page.WaitForSelectorAsync(Selectors.FirstResultLink,
                            new WaitForSelectorOptions
                            {
                                Timeout = (int)_defaultTimeout.TotalMilliseconds / 2
                            }) // Use half timeout per attempt
                        .ConfigureAwait(false);
                    foundResultLink = true;
                    break; // Success, exit loop
                }
                catch (WaitTaskTimeoutException)
                {
                    if (attempt == maxRetries)
                    {
                        // Last attempt failed, return failure
                        return Result.Failure(
                            $"Failed to find search result link for SKU {sku} after {maxRetries} attempts.");
                    }

                    // Log retry? Optional.
                    await Task.Delay(retryDelayMs, cancellationToken).ConfigureAwait(false); // Wait before next attempt
                }
            }

            if (!foundResultLink)
            {
                // This should technically be caught by the loop's failure condition, but acts as a safeguard.
                return Result.Failure($"Search result link for SKU {sku} not found after retries.");
            }

            var waitForProductPage = page.WaitForNavigationAsync(new NavigationOptions
            {
                WaitUntil = [WaitUntilNavigation.Networkidle2]
            }).ConfigureAwait(false);
            await page.ClickAsync(Selectors.FirstResultLink).ConfigureAwait(false);
            await waitForProductPage;

            // Wait for the iframe to be present
            await page.WaitForSelectorAsync(DescriptionIframeSelector,
                    new WaitForSelectorOptions { Timeout = (int)_defaultTimeout.TotalMilliseconds / 2 })
                .ConfigureAwait(false);

            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to search/navigate for SKU {sku}: {ex.Message}");
        }
    }

    private async Task<Result<string>> ExtractDescriptionAsync(IPage page)
    {
        try
        {
            // Wait for the iframe to be present
            var initialIframeElement = await page.QuerySelectorAsync(DescriptionIframeSelector).ConfigureAwait(false);
            if (initialIframeElement == null)
            {
                return Result.Failure<string>("Could not find the description iframe.");
            }

            // Get the initial content frame of the iframe
            var initialFrame = await initialIframeElement.ContentFrameAsync().ConfigureAwait(false);
            if (initialFrame == null)
            {
                return Result.Failure<string>(
                    "Could not get the content frame of the description iframe (initial attempt).");
            }

            // Check if the description element exists before waiting
            var descriptionElementExists = await initialFrame.EvaluateExpressionAsync<bool>(
                $"document.querySelector('{Selectors.DescriptionElement}') != null"
            ).ConfigureAwait(false);

            if (!descriptionElementExists)
            {
                // If the element doesn't exist at all, waiting won't help.
                // Return success with empty string as per current behaviour for missing elements.
                // Or, return failure if this is considered an error state:
                // return Result.Failure<string>("Description element selector not found within the iframe.");
                return Result.Success(string.Empty);
            }

            // Now that we know it exists, wait for it to be potentially ready/stable
            // Ensure the description element is present within the initial iframe
            await initialFrame.WaitForSelectorAsync(Selectors.DescriptionElement,
                    new WaitForSelectorOptions { Timeout = (int)_defaultTimeout.TotalMilliseconds / 2 })
                .ConfigureAwait(false);

            // Re-acquire the iframe element and frame right before evaluation
            // This helps ensure the frame reference is still valid if it reloaded or detached.
            var iframeElement = await page.QuerySelectorAsync(DescriptionIframeSelector).ConfigureAwait(false);
            if (iframeElement == null)
            {
                // If it disappeared after the wait, something is odd.
                return Result.Failure<string>("Description iframe disappeared before evaluation.");
            }

            var frame = await iframeElement.ContentFrameAsync().ConfigureAwait(false);
            if (frame == null)
            {
                // If the frame is suddenly null here, it likely detached or reloaded.
                return Result.Failure<string>(
                    "Could not get the content frame of the description iframe (before evaluation). Frame might have detached.");
            }

            // Evaluate the function directly within the iframe's context
            var descriptionHtml = await frame.EvaluateFunctionAsync<string>(
                """
                () => {
                                    const element = document.querySelector('.automated-overview');
                                    return element ? element.innerHTML.trim() : '';
                                }
                """
            ).ConfigureAwait(false);

            return Result.Success(descriptionHtml ?? string.Empty);
        }
        catch (Exception ex)
        {
            // Add specific check for TargetClosedException if needed for logging/debugging
            // if (ex is PuppeteerSharp.TargetClosedException) { ... }
            return Result.Failure<string>($"Failed to extract description HTML: {ex.Message}");
        }
    }

    private async Task<Result> NavigateBackToProductsAsync(IPage page, CancellationToken cancellationToken)
    {
        try
        {
            // Use the breadcrumb link to go back
            await page.WaitForSelectorAsync(Selectors.BackToProductsBreadcrumb).ConfigureAwait(false);
            await page.ClickAsync(Selectors.BackToProductsBreadcrumb).ConfigureAwait(false);

            // Wait for the search input to reappear to confirm we are back
            await page.WaitForSelectorAsync(Selectors.SearchProductInput,
                new WaitForSelectorOptions { Timeout = (int)_defaultTimeout.TotalMilliseconds }).ConfigureAwait(false);
            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to navigate back to products page: {ex.Message}");
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            _semaphore?.Dispose();
            // Ensure browser is closed and disposed on service disposal
            CloseBrowserAsync().ConfigureAwait(false).GetAwaiter().GetResult();
        }
    }

    // Finaliser as a safeguard
    ~GetDescriptionFromKqmService()
    {
        Dispose(false);
    }
}
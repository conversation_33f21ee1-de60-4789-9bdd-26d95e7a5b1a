using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Properties;
using PuppeteerSharp;
using PuppeteerSharp.Input;

namespace Datafeed_v2.Services;

/// <summary>
/// Product information model for KQM service results
/// Contains comprehensive product data extracted from KQM admin backend
/// </summary>
public class KqmProductInformation
{
    public int Id { get; set; }
    public string Title { get; set; }
    public string LongDescription { get; set; }
    public decimal? RecommendedSellPrice { get; set; }
    public decimal? CostPrice { get; set; }
    public int? TotalStockCount { get; set; }
}

/// <summary>
/// Interface for the new GetProductInformationFromKqmService
/// Follows the same pattern as IGetDescriptionFromKqmService but returns comprehensive product information
/// </summary>
public interface IGetProductInformationFromKqmService : IDisposable
{
    Task<Result<IEnumerable<KeyValuePair<string, KqmProductInformation>>>> FetchProductInformationAsync(
        IEnumerable<string> skus, CancellationToken cancellationToken = default);
}

/// <summary>
/// Service for extracting comprehensive product information from the KQM admin backend
/// Follows the same architectural pattern as GetDescriptionFromKqmService but extracts
/// additional product data including ID, Title, and Long Description
/// </summary>
public class GetProductInformationFromKqmService(string dataDirectory = null) : IGetProductInformationFromKqmService
{
    private readonly string _dataDirectory = dataDirectory ??
                                             AppDomain.CurrentDomain.GetData("DataDirectory")?.ToString()
                                             ?? throw new ArgumentException("Data directory not found");

    private IBrowser _browser;
    private readonly SemaphoreSlim _semaphore = new(1, 1);

    private readonly TimeSpan _defaultTimeout = TimeSpan.FromSeconds(30);

    // Selectors for KQM admin backend - based on GetDescriptionFromKqmService patterns
    private static class Selectors
    {
        // Login Page (Admin)
        public const string LoginEmailInput = "#Email";
        public const string ContinueButton = "form[action*=\"/auth/login\"] button[type=\"submit\"]";
        public const string LoginPasswordInput = "#Password";
        public const string LoginButton = "form[action*=\"/auth/loginpassword\"] button[type=\"submit\"]";

        // Admin Area
        public const string ProductsTabLink = "a.sidebar-link[href=\"/admin/products\"]";
        public const string SearchProductInput = "#Query";
        public const string FirstResultLink = ".table > tbody > tr:first-child > td:nth-child(2) > a.table-hover-link";
        public const string BackToProductsBreadcrumb = "a.breadcrumb[href=\"/admin/products\"]";

        // Product detail selectors - based on actual KQM admin page structure
        public const string ProductIdElement = "#ID, input[name='ID']";
        public const string ProductTitleElement = "#Title, input[name='Title']";
        public const string ProductDescriptionTextarea = "textarea[name='Description']";

        // Pricing selectors - based on actual page structure
        public const string RecommendedSellPriceElement = ".product-price";
        public const string AllSupplierCostElements = "input[name*='Suppliers'][name*='Cost']";

        // Stock selectors - based on supplier quantity fields
        public const string AllSupplierStockElements = "input[name*='Suppliers'][name*='Quantity']";

        // Form identifier to confirm we are on the edit page
        public const string ProductEditPageIdentifier = "form#product-form";
    }

    public async Task<Result<IEnumerable<KeyValuePair<string, KqmProductInformation>>>> FetchProductInformationAsync(
        IEnumerable<string> skus,
        CancellationToken cancellationToken = default)
    {
        var skuList = skus?.ToList();
        if (skuList == null || !skuList.Any() || skuList.Any(string.IsNullOrWhiteSpace))
        {
            return Result.Failure<IEnumerable<KeyValuePair<string, KqmProductInformation>>>(
                "SKU list cannot be null, empty, or contain empty SKUs");
        }

        // Remove duplicates and preserve order - ensures only one result per unique SKU
        var uniqueSkus = skuList.Distinct().ToList();
        var productInformation = new Dictionary<string, KqmProductInformation>();

        try
        {
            await _semaphore.WaitAsync(cancellationToken);
            var browserResult = await InitialiseBrowserAsync(cancellationToken);
            if (browserResult.IsFailure)
            {
                return Result.Failure<IEnumerable<KeyValuePair<string, KqmProductInformation>>>(browserResult.Error);
            }

            var page = await _browser.NewPageAsync().ConfigureAwait(false);
            try
            {
                await ConfigurePageAsync(page);
                var loginResult = await LoginAsync(page, cancellationToken);
                if (loginResult.IsFailure)
                {
                    return Result.Failure<IEnumerable<KeyValuePair<string, KqmProductInformation>>>(loginResult.Error);
                }

                var navResult = await NavigateToProductsPageAsync(page, cancellationToken);
                if (navResult.IsFailure)
                {
                    return Result.Failure<IEnumerable<KeyValuePair<string, KqmProductInformation>>>(navResult.Error);
                }

                // Process each unique SKU only once
                foreach (var sku in uniqueSkus)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    // Skip if we already have information for this SKU
                    if (productInformation.ContainsKey(sku))
                    {
                        continue;
                    }

                    var searchResult = await SearchAndNavigateToProductAsync(page, sku, cancellationToken);
                    if (searchResult.IsFailure)
                    {
                        // Log the failure but continue with other SKUs - this handles "no results" scenario
                        continue;
                    }

                    var extractResult = await ExtractProductInformationAsync(page, sku, cancellationToken);
                    if (extractResult.IsFailure)
                    {
                        // Log the failure but continue with other SKUs
                        continue;
                    }

                    // Store exactly one result per SKU - this ensures when multiple search results
                    // are found for a SKU, only the first one (which we navigated to) is returned
                    productInformation[sku] = extractResult.Value;

                    // Navigate back to the product search results page
                    var backNavResult = await NavigateBackToProductsAsync(page, cancellationToken);
                    if (backNavResult.IsFailure)
                    {
                        return Result.Failure<IEnumerable<KeyValuePair<string, KqmProductInformation>>>(
                            $"Failed to navigate back for SKU {sku}: {backNavResult.Error}");
                    }
                }

                // Return results ensuring exactly one entry per unique SKU
                return Result.Success(productInformation.ToList().AsEnumerable());
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                return Result.Failure<IEnumerable<KeyValuePair<string, KqmProductInformation>>>(
                    $"Failed to fetch product information: {ex.Message}");
            }
            finally
            {
                await page.CloseAsync().ConfigureAwait(false);
            }
        }
        catch (OperationCanceledException)
        {
            return Result.Failure<IEnumerable<KeyValuePair<string, KqmProductInformation>>>(
                "Operation was cancelled");
        }
        catch (Exception ex)
        {
            return Result.Failure<IEnumerable<KeyValuePair<string, KqmProductInformation>>>(
                $"Unexpected error: {ex.Message}");
        }
        finally
        {
            _semaphore.Release();
        }
    }

    private async Task<Result> InitialiseBrowserAsync(CancellationToken cancellationToken)
    {
        try
        {
            _browser = await Puppeteer.LaunchAsync(new LaunchOptions
            {
                HeadlessMode = HeadlessMode.False,
                ExecutablePath = $@"{_dataDirectory}\chrome-win\chrome.exe",
                Args =
                [
                    "--no-sandbox", "--disable-setuid-sandbox", "--window-size=1920,1080", "--disable-gpu",
                    "--single-process"
                ],
                DefaultViewport = new ViewPortOptions { Width = 1920, Height = 1080 },
            }).ConfigureAwait(false);

            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to initialise browser: {ex.Message}");
        }
    }

    private static async Task ConfigurePageAsync(IPage page)
    {
        await page.SetViewportAsync(new ViewPortOptions { Width = 1920, Height = 1080 }).ConfigureAwait(false);
        await page.SetUserAgentAsync(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
            .ConfigureAwait(false);
    }

    private async Task<Result> LoginAsync(IPage page, CancellationToken cancellationToken)
    {
        try
        {
            const string adminLoginUrl = "https://edunet.mykaseyaquotemanager.com/auth/login?returnUrl=%2Fadmin";
            await page.GoToAsync(adminLoginUrl, WaitUntilNavigation.Networkidle2).ConfigureAwait(false);

            // Enter email
            await page.WaitForSelectorAsync(Selectors.LoginEmailInput).ConfigureAwait(false);
            await page.TypeAsync(Selectors.LoginEmailInput, Settings.Default.KqmEstoreUsername).ConfigureAwait(false);
            await page.ClickAsync(Selectors.ContinueButton).ConfigureAwait(false);

            // Enter password
            await page.WaitForSelectorAsync(Selectors.LoginPasswordInput).ConfigureAwait(false);
            await page.TypeAsync(Selectors.LoginPasswordInput, Settings.Default.KqmEstorePassword)
                .ConfigureAwait(false);
            await page.ClickAsync(Selectors.LoginButton).ConfigureAwait(false);

            // Wait for a known element on the admin dashboard after login
            await page.WaitForSelectorAsync(Selectors.ProductsTabLink,
                new WaitForSelectorOptions { Timeout = (int)_defaultTimeout.TotalMilliseconds }).ConfigureAwait(false);

            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to login to KQM Admin: {ex.Message}");
        }
    }

    private async Task<Result> NavigateToProductsPageAsync(IPage page, CancellationToken cancellationToken)
    {
        try
        {
            await page.WaitForSelectorAsync(Selectors.ProductsTabLink).ConfigureAwait(false);
            await page.ClickAsync(Selectors.ProductsTabLink).ConfigureAwait(false);

            // Wait for the search input on the products page to confirm navigation
            await page.WaitForSelectorAsync(Selectors.SearchProductInput,
                new WaitForSelectorOptions { Timeout = (int)_defaultTimeout.TotalMilliseconds }).ConfigureAwait(false);
            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to navigate to Products page: {ex.Message}");
        }
    }

    private async Task<Result> SearchAndNavigateToProductAsync(IPage page, string sku,
        CancellationToken cancellationToken)
    {
        const int maxRetries = 3;
        const int retryDelayMs = 1500;
        try
        {
            // Ensure we are on the products page (check for search input)
            await page.WaitForSelectorAsync(Selectors.SearchProductInput).ConfigureAwait(false);

            // Clear existing search and type SKU
            await page.FocusAsync(Selectors.SearchProductInput).ConfigureAwait(false);
            await page.Keyboard.DownAsync(Key.Control).ConfigureAwait(false);
            await page.Keyboard.PressAsync("KeyA").ConfigureAwait(false);
            await page.Keyboard.UpAsync(Key.Control).ConfigureAwait(false);
            await page.Keyboard.PressAsync(Key.Backspace).ConfigureAwait(false);

            await page.TypeAsync(Selectors.SearchProductInput, sku).ConfigureAwait(false);
            var waitForSearchResults = page.WaitForNavigationAsync(new NavigationOptions
            {
                WaitUntil = [WaitUntilNavigation.Networkidle2]
            }).ConfigureAwait(false);
            await page.Keyboard.PressAsync(Key.Enter).ConfigureAwait(false);
            await waitForSearchResults;

            // Wait for search results with retries
            var foundResultLink = false;
            for (var attempt = 1; attempt <= maxRetries; attempt++)
            {
                cancellationToken.ThrowIfCancellationRequested();
                try
                {
                    await page.WaitForSelectorAsync(Selectors.FirstResultLink,
                            new WaitForSelectorOptions { Timeout = (int)_defaultTimeout.TotalMilliseconds / 2 })
                        .ConfigureAwait(false);
                    foundResultLink = true;
                    break;
                }
                catch (WaitTaskTimeoutException)
                {
                    if (attempt == maxRetries)
                    {
                        return Result.Failure($"No search results found for SKU {sku} after {maxRetries} attempts.");
                    }

                    await Task.Delay(retryDelayMs, cancellationToken).ConfigureAwait(false);
                }
            }

            if (!foundResultLink)
            {
                return Result.Failure($"Search result link for SKU {sku} not found after retries.");
            }

            // Navigate to the first result (handles both single and multiple results)
            var waitForProductPage = page.WaitForNavigationAsync(new NavigationOptions
            {
                WaitUntil = [WaitUntilNavigation.Networkidle2]
            }).ConfigureAwait(false);
            await page.ClickAsync(Selectors.FirstResultLink).ConfigureAwait(false);
            await waitForProductPage;

            // Wait for the product edit form to confirm we're on the right page
            await page.WaitForSelectorAsync(Selectors.ProductEditPageIdentifier,
                    new WaitForSelectorOptions { Timeout = (int)_defaultTimeout.TotalMilliseconds / 2 })
                .ConfigureAwait(false);

            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to search/navigate for SKU {sku}: {ex.Message}");
        }
    }

    private async Task<Result<KqmProductInformation>> ExtractProductInformationAsync(IPage page, string sku,
        CancellationToken cancellationToken)
    {
        try
        {
            // Extract Product ID
            var productId = 0;
            try
            {
                var idElement = await page.QuerySelectorAsync(Selectors.ProductIdElement).ConfigureAwait(false);
                if (idElement != null)
                {
                    var idValue = await idElement.EvaluateFunctionAsync<string>("el => el.value").ConfigureAwait(false);
                    if (int.TryParse(idValue, out var parsedId))
                    {
                        productId = parsedId;
                    }
                }
            }
            catch
            {
                // If ID extraction fails, continue with default value
            }

            // Extract Product Title
            var title = string.Empty;
            try
            {
                var titleElement = await page.QuerySelectorAsync(Selectors.ProductTitleElement).ConfigureAwait(false);
                if (titleElement != null)
                {
                    title = await titleElement.EvaluateFunctionAsync<string>("el => el.value").ConfigureAwait(false);
                }
            }
            catch
            {
                // If title extraction fails, continue with empty string
            }

            // Extract Long Description from textarea
            var longDescription = string.Empty;
            try
            {
                var descriptionTextarea = await page.QuerySelectorAsync(Selectors.ProductDescriptionTextarea)
                    .ConfigureAwait(false);
                if (descriptionTextarea != null)
                {
                    var htmlContent = await descriptionTextarea.EvaluateFunctionAsync<string>("el => el.value")
                        .ConfigureAwait(false);
                    if (!string.IsNullOrWhiteSpace(htmlContent))
                    {
                        // Extract content inside body tags, removing DOCTYPE and HTML wrapper
                        var bodyStartIndex = htmlContent.IndexOf("<body>", StringComparison.OrdinalIgnoreCase);
                        var bodyEndIndex = htmlContent.IndexOf("</body>", StringComparison.OrdinalIgnoreCase);

                        if (bodyStartIndex >= 0 && bodyEndIndex > bodyStartIndex)
                        {
                            // Extract content between <body> and </body> tags
                            var bodyStartTag = bodyStartIndex + "<body>".Length;
                            longDescription = htmlContent.Substring(bodyStartTag, bodyEndIndex - bodyStartTag).Trim();
                        }
                        else
                        {
                            // Fallback: use the full content if body tags are not found
                            longDescription = htmlContent.Trim();
                        }
                    }
                }
            }
            catch
            {
                // If description extraction fails, continue with empty string
            }

            // Extract Recommended Sell Price (from the calculated price display)
            decimal? recommendedSellPrice = null;
            try
            {
                var priceElement = await page.QuerySelectorAsync(Selectors.RecommendedSellPriceElement)
                    .ConfigureAwait(false);
                if (priceElement != null)
                {
                    var priceText = await priceElement.EvaluateFunctionAsync<string>("el => el.textContent")
                        .ConfigureAwait(false);
                    // Remove currency symbols and parse (e.g., "$176.00" -> "176.00")
                    var cleanPriceText = priceText?.Replace("$", "").Replace(",", "").Trim();
                    if (decimal.TryParse(cleanPriceText, out var parsedPrice) && parsedPrice > 0)
                    {
                        recommendedSellPrice = parsedPrice;
                    }
                }
            }
            catch
            {
                // If price extraction fails, continue with null
            }

            // Extract Cost Price (cheapest from all suppliers)
            decimal? costPrice = null;
            try
            {
                // Get all supplier cost fields
                var costElements = await page.QuerySelectorAllAsync(Selectors.AllSupplierCostElements)
                    .ConfigureAwait(false);
                var cheapestCost = decimal.MaxValue;
                var hasCost = false;

                foreach (var costElement in costElements)
                {
                    var costValue = await costElement.EvaluateFunctionAsync<string>("el => el.value")
                        .ConfigureAwait(false);
                    if (decimal.TryParse(costValue, out var parsedCost) && parsedCost > 0)
                    {
                        if (parsedCost < cheapestCost)
                        {
                            cheapestCost = parsedCost;
                            hasCost = true;
                        }
                    }
                }

                if (hasCost)
                {
                    costPrice = cheapestCost;
                }
            }
            catch
            {
                // If cost extraction fails, continue with null
            }

            // Extract Total Stock Count (aggregate from all suppliers)
            int? totalStockCount = null;
            try
            {
                // Get all supplier quantity fields
                var stockElements = await page.QuerySelectorAllAsync(Selectors.AllSupplierStockElements)
                    .ConfigureAwait(false);
                var totalStock = 0;
                var hasStock = false;

                foreach (var stockElement in stockElements)
                {
                    var stockValue = await stockElement.EvaluateFunctionAsync<string>("el => el.value")
                        .ConfigureAwait(false);
                    if (int.TryParse(stockValue, out var parsedStock) && parsedStock > 0)
                    {
                        totalStock += parsedStock;
                        hasStock = true;
                    }
                }

                if (hasStock)
                {
                    totalStockCount = totalStock;
                }
            }
            catch
            {
                // If stock extraction fails, continue with null
            }

            var productInfo = new KqmProductInformation
            {
                Id = productId,
                Title = title?.Trim() ?? string.Empty,
                LongDescription = longDescription?.Trim() ?? string.Empty,
                RecommendedSellPrice = recommendedSellPrice,
                CostPrice = costPrice,
                TotalStockCount = totalStockCount
            };

            return Result.Success(productInfo);
        }
        catch (Exception ex)
        {
            return Result.Failure<KqmProductInformation>(
                $"Failed to extract product information for SKU {sku}: {ex.Message}");
        }
    }

    private async Task<Result> NavigateBackToProductsAsync(IPage page, CancellationToken cancellationToken)
    {
        try
        {
            // Use the breadcrumb link to go back
            await page.WaitForSelectorAsync(Selectors.BackToProductsBreadcrumb).ConfigureAwait(false);
            await page.ClickAsync(Selectors.BackToProductsBreadcrumb).ConfigureAwait(false);

            // Wait for the search input to reappear to confirm we are back
            await page.WaitForSelectorAsync(Selectors.SearchProductInput,
                new WaitForSelectorOptions { Timeout = (int)_defaultTimeout.TotalMilliseconds }).ConfigureAwait(false);
            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to navigate back to products page: {ex.Message}");
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            _semaphore?.Dispose();
            _browser?.Dispose();
        }
    }
}
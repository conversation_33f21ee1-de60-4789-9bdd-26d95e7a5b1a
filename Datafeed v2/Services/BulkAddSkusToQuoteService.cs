#nullable enable
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Properties;
using PuppeteerSharp;

namespace Datafeed_v2.Services;

public interface IBulkAddSkusToQuoteService
{
    Task<Result> AddSkusToQuoteAsync(string quoteName, IEnumerable<string> skus,
        CancellationToken cancellationToken = default);
}

public class BulkAddSkusToQuoteService : IBulkAddSkusToQuoteService, IDisposable
{
    private readonly Func<Task<IBrowser>>? _browserProviderFunc;
    private IBrowser? _browser;

    private readonly string _dataDirectory = AppDomain.CurrentDomain.GetData("DataDirectory")?.ToString()
                                             ?? throw new InvalidOperationException(
                                                 "Data directory not found. Ensure 'DataDirectory' is set for the AppDomain.");

    public BulkAddSkusToQuoteService(Func<Task<IBrowser>>? browserProviderFunc = null)
    {
        _browserProviderFunc = browserProviderFunc;
    }

    private readonly SemaphoreSlim _semaphore = new(1, 1);
    private readonly TimeSpan _defaultTimeout = TimeSpan.FromSeconds(30);
    private readonly TimeSpan _shortTimeout = TimeSpan.FromSeconds(15); // For modal interactions

    private readonly TimeSpan
        _longTimeout = TimeSpan.FromMinutes(5); // For interactions with quotes containing a large amount of products

    private static class Selectors
    {
        // Login
        public const string LoginButton = "header a[href*=\"/auth/login\"]"; // Assuming same login page as estore
        public const string FormEmailInput = "body form[action=\"/auth/login\"] input[type=\"email\"]";
        public const string ContinueButton = "body form[action=\"/auth/login\"] button[type=\"submit\"]";
        public const string FormPasswordInput = "body form[action=\"/auth/loginpassword\"] input[type=\"password\"]";
        public const string FormLoginButton = "body form[action=\"/auth/loginpassword\"] button[type=\"submit\"]";

        // Navigation & Quote Search
        public const string NavSidebarLink = "nav a[class*=\"sidebar-link\"]"; // General sidebar link for wait

        public const string
            QuotesNavButton = "#sidebar > ul:nth-child(2) > li:nth-child(2) > a"; // More specific than just span

        public const string SearchQuotesInput = "#Query";

        public const string FirstQuoteLink =
            "div.table-responsive > table > tbody > tr:nth-child(1) > td:nth-child(1) > a";

        // Quote Editing & Product Search
        public const string ProductSearchInput = "input[name=\"product-search\"][ks-function=\"modal-search-input\"]";
        public const string ProductSearchModal = "div.modal.show"; // General selector for the modal when visible
        public const string FirstProductCheckbox = "#Result_0__IsSelected";

        public const string
            AddProductButton =
                "div.modal.show button[ks-function=\"modal-search-add-button\"]"; // Scoped to visible modal

        public const string SaveQuoteButton = "button[ks-function=\"adminlist-save-button\"]";
    }

    private async Task<Result<IBrowser>> InitialiseBrowserAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Check if the internally managed browser instance is already valid
            if (_browser is { IsConnected: true })
            {
                return Result.Success(_browser);
            }

            // Explicitly dispose of any existing disconnected internal browser instance
            if (_browser != null)
            {
                await _browser.DisposeAsync().ConfigureAwait(false);
                _browser = null;
            }

            var launchOptions = new LaunchOptions
            {
                Headless = true,
                Args = ["--no-sandbox", "--disable-setuid-sandbox"],
                // SlowMo = 20 // Uncomment for debugging if needed
            };

            var executablePath = $@"{_dataDirectory}\chrome-win\chrome.exe";
            if (!string.IsNullOrWhiteSpace(executablePath))
            {
                launchOptions.ExecutablePath = executablePath;
            }
            else
            {
                // Download browser only if no specific path is provided
                try
                {
                    var browserFetcher = new BrowserFetcher();
                    Console.WriteLine("Checking/Downloading browser revision...");
                    await browserFetcher.DownloadAsync().ConfigureAwait(false);
                    Console.WriteLine("Browser check/download complete.");
                }
                catch (Exception downloadEx)
                {
                    return Result.Failure<IBrowser>($"Failed to download or verify browser: {downloadEx.Message}");
                }
            }

            // Launch and assign to the internal field
            _browser = await Puppeteer.LaunchAsync(launchOptions).ConfigureAwait(false);

            if (_browser is not { IsConnected: true })
            {
                // Ensure _browser is null if launch failed
                _browser = null;
                return Result.Failure<IBrowser>("Failed to launch or connect to the browser.");
            }

            Console.WriteLine($"Browser launched. Endpoint: {_browser.WebSocketEndpoint}");
            return Result.Success(_browser);
        }
        catch (Exception ex)
        {
            // Ensure browser is null if initialisation fails
            _browser = null;
            return Result.Failure<IBrowser>($"Browser initialisation failed: {ex.Message}");
        }
    }

    public async Task<Result> AddSkusToQuoteAsync(string quoteName, IEnumerable<string> skus,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(quoteName))
        {
            return Result.Failure("Quote name cannot be empty");
        }

        var skuList = skus?.ToList() ?? [];
        if (!skuList.Any())
        {
            return Result.Failure("SKU list cannot be empty");
        }

        var isExternalBrowser = false; // Track if the browser came from the provider

        try
        {
            await _semaphore.WaitAsync(cancellationToken);

            // --- Browser Acquisition Logic ---
            IBrowser? browserInstance = null; // Local variable for the browser used in this specific call
            if (_browserProviderFunc != null)
            {
                // Using external provider
                try
                {
                    browserInstance = await _browserProviderFunc().ConfigureAwait(false);
                    if (browserInstance is not { IsConnected: true })
                    {
                        return Result.Failure("Failed to get a connected browser instance from provider.");
                    }

                    isExternalBrowser = true;
                    Console.WriteLine("Using externally provided browser instance.");
                }
                catch (Exception ex)
                {
                    return Result.Failure($"Failed to obtain browser instance from provider: {ex.Message}");
                }
            }
            else
            {
                // Using internal initialisation
                Console.WriteLine("No external browser provider. Initializing internal browser...");
                var browserResult = await InitialiseBrowserAsync(cancellationToken).ConfigureAwait(false);
                if (browserResult.IsFailure)
                {
                    return Result.Failure(browserResult.Error); // Error already logged in InitialiseBrowserAsync
                }

                // Assign to both the class field (_browser) and the local variable (browserInstance)
                _browser = browserResult.Value;
                browserInstance = _browser;
                isExternalBrowser = false;
                Console.WriteLine("Internal browser initialised successfully.");
            }
            // --- End Browser Acquisition Logic ---

            // Safety check - should have a browser instance by now
            if (browserInstance == null)
            {
                return Result.Failure("Fatal error: Browser instance could not be determined.");
            }

            var page = await browserInstance.NewPageAsync().ConfigureAwait(false);
            try
            {
                await ConfigurePageAsync(page);
                var loginResult = await LoginAsync(page, cancellationToken);
                if (loginResult.IsFailure) return Result.Failure(loginResult.Error);

                var navResult = await NavigateToQuotesPageAsync(page, cancellationToken);
                if (navResult.IsFailure) return Result.Failure(navResult.Error);

                var openQuoteResult = await SearchAndOpenQuoteAsync(page, quoteName, cancellationToken);
                if (openQuoteResult.IsFailure) return Result.Failure(openQuoteResult.Error);

                foreach (var sku in skuList)
                {
                    var addSkuResult = await AddSkuToQuoteAsync(page, browserInstance, sku, cancellationToken);
                    if (addSkuResult.IsFailure)
                    {
                        return Result.Failure($"Failed to add SKU '{sku}': {addSkuResult.Error}");
                    }
                }

                var saveResult = await SaveQuoteAsync(page, cancellationToken);
                if (saveResult.IsFailure) return Result.Failure(saveResult.Error);

                return Result.Success();
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                // Take a screenshot on error for debugging
                var screenshotPath = Path.Combine(_dataDirectory, $"error_{DateTime.Now:yyyyMMddHHmmss}.png");
                try
                {
                    if (page is { IsClosed: false }) // Check if page is usable
                    {
                        await page.ScreenshotAsync(screenshotPath).ConfigureAwait(false);
                        Console.WriteLine($"Error screenshot saved to {screenshotPath}");
                    }
                }
                catch (Exception scEx)
                {
                    Console.WriteLine($"Failed to save error screenshot: {scEx.Message}");
                }

                return Result.Failure($"Failed during bulk SKU add: {ex.Message}");
            }
            finally
            {
                if (page is { IsClosed: false })
                {
                    await page.CloseAsync().ConfigureAwait(false);
                }
            }
        }
        finally
        {
            // Only close the internally managed browser instance
            if (!isExternalBrowser && _browser != null)
            {
                Console.WriteLine("Closing internally managed browser instance.");
                await _browser.CloseAsync().ConfigureAwait(false);
                _browser = null;
            }
            else if (isExternalBrowser)
            {
                Console.WriteLine("Skipping close for externally provided browser instance.");
            }

            _semaphore.Release();
        }
    }


    private async Task ConfigurePageAsync(IPage page)
    {
        page.DefaultNavigationTimeout = (int)_defaultTimeout.TotalMilliseconds;
        page.DefaultTimeout = (int)_defaultTimeout.TotalMilliseconds;
        await page.SetUserAgentAsync(
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ).ConfigureAwait(false);
        // Consider setting viewport if needed for layout consistency
        // await page.SetViewportAsync(new ViewPortOptions { Width = 1920, Height = 1080 });
    }

    private static async Task<Result> LoginAsync(IPage page, CancellationToken cancellationToken)
    {
        try
        {
            // Use the admin URL base
            const string adminLoginUrl = "https://edunet.mykaseyaquotemanager.com/admin";
            await page.GoToAsync(adminLoginUrl, WaitUntilNavigation.Networkidle0).ConfigureAwait(false);

            // Check if already logged in by looking for a known element on the admin dashboard, e.g., sidebar
            var sidebarElement = await page.QuerySelectorAsync(Selectors.NavSidebarLink);
            if (sidebarElement != null)
            {
                return Result.Success(); // Already logged in
            }

            await page.WaitForSelectorAsync(Selectors.FormEmailInput).ConfigureAwait(false);
            await page.TypeAsync(Selectors.FormEmailInput, Settings.Default.KqmEstoreUsername).ConfigureAwait(false);
            await page.ClickAsync(Selectors.ContinueButton).ConfigureAwait(false);

            await page.WaitForSelectorAsync(Selectors.FormPasswordInput).ConfigureAwait(false);
            await page.TypeAsync(Selectors.FormPasswordInput, Settings.Default.KqmEstorePassword).ConfigureAwait(false);
            await page.ClickAsync(Selectors.FormLoginButton).ConfigureAwait(false);

            // Wait for navigation to the admin dashboard after login
            await page.WaitForSelectorAsync(Selectors.NavSidebarLink).ConfigureAwait(false);
            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to login to admin: {ex.Message}");
        }
    }

    private static async Task<Result> NavigateToQuotesPageAsync(IPage page, CancellationToken cancellationToken)
    {
        try
        {
            await page.WaitForSelectorAsync(Selectors.QuotesNavButton).ConfigureAwait(false);
            await page.ClickAsync(Selectors.QuotesNavButton).ConfigureAwait(false);
            // Wait for the quotes page to load, identified by the search input
            await page.WaitForSelectorAsync(Selectors.SearchQuotesInput).ConfigureAwait(false);
            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to navigate to Quotes page: {ex.Message}");
        }
    }

    private async Task<Result> SearchAndOpenQuoteAsync(IPage page, string quoteName,
        CancellationToken cancellationToken)
    {
        try
        {
            await page.WaitForSelectorAsync(Selectors.SearchQuotesInput).ConfigureAwait(false);
            await page.TypeAsync(Selectors.SearchQuotesInput, quoteName).ConfigureAwait(false);
            // Press Enter to trigger search
            await page.Keyboard.PressAsync("Enter").ConfigureAwait(false);

            // Wait for search results to load - wait for the table row/link to appear
            await page.WaitForSelectorAsync(Selectors.FirstQuoteLink,
                    new WaitForSelectorOptions { Timeout = (int)TimeSpan.FromSeconds(10).TotalMilliseconds })
                .ConfigureAwait(false);

            // Click the link in the first row to open the quote
            await page.ClickAsync(Selectors.FirstQuoteLink).ConfigureAwait(false);

            // Wait for the quote edit page to load, identified by the product search input
            await page.WaitForSelectorAsync(Selectors.ProductSearchInput,
                    new WaitForSelectorOptions { Timeout = (int)_longTimeout.TotalMilliseconds })
                .ConfigureAwait(
                    false); // Using a long delay because quotes with a large amount of products struggles to load in a reasonable time
            return Result.Success();
        }
        catch (WaitTaskTimeoutException)
        {
            return Result.Failure($"Could not find quote '{quoteName}' or timed out waiting for search results.");
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to search for or open quote '{quoteName}': {ex.Message}");
        }
    }

    private async Task<Result> AddSkuToQuoteAsync(IPage page, IBrowser browser, string sku,
        CancellationToken cancellationToken)
    {
        try
        {
            await page.WaitForSelectorAsync(Selectors.ProductSearchInput).ConfigureAwait(false);
            // Clear the input before typing (important if adding multiple SKUs)
            await page.EvaluateFunctionAsync("selector => document.querySelector(selector).value = ''",
                Selectors.ProductSearchInput).ConfigureAwait(false);
            await page.TypeAsync(Selectors.ProductSearchInput, sku).ConfigureAwait(false);

            // Wait for the product search modal to appear and be ready
            await page.WaitForSelectorAsync(Selectors.ProductSearchModal,
                new WaitForSelectorOptions { Timeout = (int)_shortTimeout.TotalMilliseconds }).ConfigureAwait(false);
            // Add a small delay or wait for a specific element inside the modal if needed
            await Task.Delay(500, cancellationToken); // Small delay for modal content rendering
            await page.WaitForSelectorAsync(Selectors.FirstProductCheckbox,
                    new WaitForSelectorOptions { Timeout = (int)_longTimeout.TotalMilliseconds })
                .ConfigureAwait(
                    false); // Using a long delay because quotes with a large amount of products struggles to load in a reasonable time

            // Check the checkbox for the first product
            await page.ClickAsync(Selectors.FirstProductCheckbox).ConfigureAwait(false);

            // Click the 'Add' button within the modal
            await page.WaitForSelectorAsync(Selectors.AddProductButton,
                new WaitForSelectorOptions { Timeout = (int)_shortTimeout.TotalMilliseconds }).ConfigureAwait(false);
            await page.ClickAsync(Selectors.AddProductButton).ConfigureAwait(false);

            // Add checks before waiting for modal disappearance
            if (page.IsClosed)
            {
                return Result.Failure(
                    $"Failed to add SKU '{sku}': Page was closed unexpectedly before modal disappearance check.");
            }

            if (browser?.IsConnected != true)
            {
                return Result.Failure(
                    $"Failed to add SKU '{sku}': Browser disconnected before modal disappearance check.");
            }

            // Wait for the modal to disappear
            await page.WaitForSelectorAsync(Selectors.ProductSearchModal,
                    new WaitForSelectorOptions { Hidden = true, Timeout = (int)_longTimeout.TotalMilliseconds })
                .ConfigureAwait(false);
            // Add a small delay after adding to ensure the page state updates if necessary
            await Task.Delay(500, cancellationToken);

            return Result.Success();
        }
        catch (WaitTaskTimeoutException ex)
        {
            // Check if the modal didn't appear or didn't disappear, or if the product wasn't found
            return Result.Failure(
                $"Timeout waiting for product search modal interaction for SKU '{sku}'. Product might not exist or modal failed. Details: {ex.Message}");
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to add SKU '{sku}' to quote: {ex.Message}");
        }
    }

    private async Task<Result> SaveQuoteAsync(IPage page, CancellationToken cancellationToken)
    {
        try
        {
            await page.WaitForSelectorAsync(Selectors.SaveQuoteButton).ConfigureAwait(false);
            await page.ClickAsync(Selectors.SaveQuoteButton).ConfigureAwait(false);
            // Add a wait here if there's a confirmation message or navigation expected after saving
            // For example, wait for a success notification or for the button to become disabled briefly
            // await Task.Delay(1000, cancellationToken); // Simple delay, replace with a proper wait if possible
            await page.WaitForSelectorAsync(Selectors.SaveQuoteButton,
                    new WaitForSelectorOptions { Hidden = true, Timeout = (int)_longTimeout.TotalMilliseconds })
                .ConfigureAwait(
                    false); // Using a long delay because quotes with a large amount of products struggles to load in a reasonable time
            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to save quote: {ex.Message}");
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            _semaphore?.Dispose();

            // Dispose the internally managed browser instance, if it exists and hasn't been closed/disposed yet
            if (_browser != null)
            {
                Console.WriteLine("Disposing internal browser instance...");
                try
                {
                    // PuppeteerSharp's Browser implements IDisposable explicitly.
                    ((IDisposable)_browser).Dispose();
                }
                catch (ObjectDisposedException)
                {
                    // Ignore if already disposed elsewhere (e.g., CloseAsync in AddSkusToQuoteAsync)
                    Console.WriteLine("Internal browser instance was already disposed.");
                }
                catch (Exception ex)
                {
                    // Log error during disposal if necessary
                    Console.WriteLine($"Error disposing internal browser: {ex.Message}");
                }
                finally
                {
                    _browser = null; // Ensure reference is cleared
                }
            }
        }
    }

    // Optional: Finaliser in case Dispose is not called
    ~BulkAddSkusToQuoteService()
    {
        Dispose(false);
    }
}
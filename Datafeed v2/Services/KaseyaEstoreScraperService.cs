using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Properties;
using Newtonsoft.Json;
using PuppeteerSharp;

namespace Datafeed_v2.Services;

public class ProductSpecification
{
    public string Kind { get; set; }
    public List<string> Values { get; set; }
}

public class ProductInformation
{
    public string ProductDescription { get; set; }
    public List<ProductSpecification> ProductSpecifications { get; set; }
}

public interface IKaseyaEstoreScraperService : IDisposable
{
    Task<Result<ProductInformation>> FetchProductInformationAsync(string sku,
        CancellationToken cancellationToken = default);
}

public class KaseyaEstoreScraperService(string dataDirectory = null) : IKaseyaEstoreScraperService
{
    private readonly string _dataDirectory = dataDirectory ??
                                             AppDomain.CurrentDomain.GetData("DataDirectory")?.ToString()
                                             ?? throw new ArgumentException("Data directory not found");

    private IBrowser _browser;
    private readonly SemaphoreSlim _semaphore = new(1, 1);
    private readonly TimeSpan _defaultTimeout = TimeSpan.FromSeconds(30);

    private static class Selectors
    {
        public const string LoginButton = "header a[href*=\"/auth/login\"]";
        public const string FormEmailInput = "body form[action=\"/auth/login\"] input[type=\"email\"]";
        public const string ContinueButton = "body form[action=\"/auth/login\"] button[type=\"submit\"]";
        public const string FormPasswordInput = "body form[action=\"/auth/loginpassword\"] input[type=\"password\"]";
        public const string FormLoginButton = "body form[action=\"/auth/loginpassword\"] button[type=\"submit\"]";
        public const string NavSidebarLink = "nav a[class*=\"sidebar-link\"]";
        public const string SearchProductInput = "header form[action=\"/search\"] input[id=\"query\"]";
        public const string ProductInfo = "body div[class=\"automated-overview\"]";
        public const string ProductPanel = "div[data-component=\"ProductPanel\"] a";
    }

    private const string GetProductSpecifications = """
                                                    function getProductSpecifications() {
                                                        const specs = [];
                                                        const mainCategories = [...document.querySelectorAll('.automated-specification > ul.automated-specification > li:first-child')];
                                                        
                                                        mainCategories.forEach(category => {
                                                            const categoryName = category.textContent;
                                                            const values = [];
                                                            
                                                            let nextElement = category.nextElementSibling;
                                                            if (nextElement) {
                                                                const subLists = nextElement.querySelectorAll('ul li');
                                                                subLists.forEach(item => {
                                                                    if (item.textContent.trim()) {
                                                                        values.push(item.textContent);
                                                                    }
                                                                });
                                                            }
                                                            
                                                            if (values.length > 0) {
                                                                specs.push({
                                                                    Kind: categoryName,
                                                                    Values: values
                                                                });
                                                            }
                                                        });
                                                        
                                                        return specs;
                                                    }
                                                    """;

    public async Task<Result<ProductInformation>> FetchProductInformationAsync(string sku,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(sku))
        {
            return Result.Failure<ProductInformation>("SKU cannot be empty");
        }

        try
        {
            await _semaphore.WaitAsync(cancellationToken);
            var browserResult = await InitialiseBrowserAsync(cancellationToken);
            if (browserResult.IsFailure)
            {
                return Result.Failure<ProductInformation>(browserResult.Error);
            }

            var page = await _browser.NewPageAsync().ConfigureAwait(false);
            try
            {
                await ConfigurePageAsync(page);
                var loginResult = await LoginAsync(page, cancellationToken);
                if (loginResult.IsFailure)
                {
                    return Result.Failure<ProductInformation>(loginResult.Error);
                }

                var searchResult = await SearchProductAsync(page, sku, cancellationToken);
                if (searchResult.IsFailure)
                {
                    return Result.Failure<ProductInformation>(searchResult.Error);
                }

                var alreadyLoadedProduct = await IsProductDirectlyLoaded(page);
                if (alreadyLoadedProduct)
                {
                    return await ExtractProductInformation(page);
                }

                var navigationResult = await NavigateToProductDetails(page, cancellationToken);
                if (navigationResult.IsFailure)
                {
                    return Result.Failure<ProductInformation>(navigationResult.Error);
                }

                return await ExtractProductInformation(page);
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                return Result.Failure<ProductInformation>($"Failed to fetch product information: {ex.Message}");
            }
            finally
            {
                await _browser.CloseAsync().ConfigureAwait(false);
            }
        }
        finally
        {
            _semaphore.Release();
        }
    }

    private async Task<Result> InitialiseBrowserAsync(CancellationToken cancellationToken)
    {
        if (_browser != null)
        {
            return Result.Success();
        }

        try
        {
            _browser = await Puppeteer.LaunchAsync(new LaunchOptions
            {
                HeadlessMode = HeadlessMode.True,
                ExecutablePath = $@"{_dataDirectory}\chrome-win\chrome.exe",
                Args = ["--no-sandbox", "--disable-setuid-sandbox"]
            }).ConfigureAwait(false);

            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to initialise browser: {ex.Message}");
        }
    }

    private async Task ConfigurePageAsync(IPage page)
    {
        page.DefaultNavigationTimeout = (int)_defaultTimeout.TotalMilliseconds;
        page.DefaultTimeout = (int)_defaultTimeout.TotalMilliseconds;
        await page.SetUserAgentAsync(
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ).ConfigureAwait(false);
    }

    private static async Task<Result> LoginAsync(IPage page, CancellationToken cancellationToken)
    {
        try
        {
            const string estoreUrl = "https://edunet.mykaseyaquotemanager.com/";
            await page.GoToAsync(estoreUrl, WaitUntilNavigation.Networkidle0).ConfigureAwait(false);

            await page.WaitForSelectorAsync(Selectors.LoginButton).ConfigureAwait(false);
            await page.ClickAsync(Selectors.LoginButton).ConfigureAwait(false);

            await page.WaitForSelectorAsync(Selectors.FormEmailInput).ConfigureAwait(false);
            await page.TypeAsync(Selectors.FormEmailInput, Settings.Default.KqmEstoreUsername).ConfigureAwait(false);
            await page.ClickAsync(Selectors.ContinueButton).ConfigureAwait(false);

            await page.WaitForSelectorAsync(Selectors.FormPasswordInput).ConfigureAwait(false);
            await page.TypeAsync(Selectors.FormPasswordInput, Settings.Default.KqmEstorePassword).ConfigureAwait(false);
            await page.ClickAsync(Selectors.FormLoginButton).ConfigureAwait(false);

            await page.WaitForSelectorAsync(Selectors.NavSidebarLink).ConfigureAwait(false);
            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to login: {ex.Message}");
        }
    }

    private static async Task<Result> SearchProductAsync(IPage page, string sku, CancellationToken cancellationToken)
    {
        try
        {
            const string estoreUrl = "https://edunet.mykaseyaquotemanager.com/";
            await page.GoToAsync(estoreUrl).ConfigureAwait(false);

            await page.WaitForSelectorAsync(Selectors.SearchProductInput).ConfigureAwait(false);
            await page.TypeAsync(Selectors.SearchProductInput, sku).ConfigureAwait(false);

            await page.EvaluateExpressionAsync("document.querySelector('header form[action=\"/search\"]').submit()")
                .ConfigureAwait(false);
            await page.WaitForNavigationAsync().ConfigureAwait(false);

            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to search for product: {ex.Message}");
        }
    }

    private static async Task<bool> IsProductDirectlyLoaded(IPage page)
    {
        return await page.EvaluateExpressionAsync<bool>(
            "document.querySelector('body div[ks-content=\"search-result\"]') === null"
        ).ConfigureAwait(false);
    }

    private static async Task<Result> NavigateToProductDetails(IPage page, CancellationToken cancellationToken)
    {
        try
        {
            await page.EvaluateExpressionAsync($"document.querySelector('{Selectors.ProductPanel}').click()")
                .ConfigureAwait(false);
            await page.WaitForNavigationAsync().ConfigureAwait(false);
            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Failed to navigate to product details: {ex.Message}");
        }
    }

    private static async Task<Result<ProductInformation>> ExtractProductInformation(IPage page)
    {
        try
        {
            const string getDescription =
                $"() => [...document.querySelectorAll('{Selectors.ProductInfo} p')].map(p => p.textContent)";
            var descriptionContents =
                (await page.EvaluateFunctionAsync<List<string>>(getDescription).ConfigureAwait(false))
                .Where(q => !string.IsNullOrWhiteSpace(q))
                .ToList();

            var description = string.Join("\n\n", descriptionContents);

            await page.EvaluateExpressionAsync(GetProductSpecifications);
            var specificationsJson =
                await page.EvaluateFunctionAsync<string>("() => JSON.stringify(getProductSpecifications())");
            var specifications = JsonConvert.DeserializeObject<List<ProductSpecification>>(specificationsJson);

            return Result.Success(new ProductInformation
            {
                ProductDescription = description,
                ProductSpecifications = specifications
            });
        }
        catch (Exception ex)
        {
            return Result.Failure<ProductInformation>($"Failed to extract product information: {ex.Message}");
        }
    }

    public void Dispose()
    {
        _semaphore?.Dispose();
        if (_browser == null)
        {
            return;
        }

        _browser.Dispose();
        _browser = null;
    }
}
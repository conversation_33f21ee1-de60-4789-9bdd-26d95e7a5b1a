using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models.Services.IcecatApiService;
using FandoogleReflectorService;
using Newtonsoft.Json;

namespace Datafeed_v2.Services;

public static class IcecatApiContentTypes
{
    public const string All = "All";
    public const string GeneralInfo = "GeneralInfo";
    public const string Image = "Image";
    public const string Gallery = "Gallery";
    public const string Multimedia = "Multimedia";

    public const string ReasonsToBuy = "ReasonsToBuy";
    // Add other content types as needed
}

public class IcecatApiService
{
    private readonly HttpClient _httpClient;
    private readonly RateLimiter _rateLimiter;
    private const string Username = "openIcecat-live";

    public IcecatApiService(RateLimiter rateLimiter, HttpClient? httpClient = null)
    {
        _rateLimiter = rateLimiter;
        _httpClient = httpClient ?? new HttpClient();
        _httpClient.BaseAddress = new Uri("https://live.icecat.biz/api");
    }

    /// <summary>
    /// Retrieves product information from Icecat API using manufacturer code and brand.
    /// </summary>
    /// <param name="brand">The product brand name</param>
    /// <param name="manufacturerCode">The manufacturer's product code</param>
    /// <param name="contentTypes">List of content types to retrieve (see IcecatApiContentTypes)</param>
    /// <returns>
    /// A Result containing ProductInfo if successful, or error message if failed.
    /// Possible failure reasons:
    /// - Rate limit exceeded
    /// - Invalid/missing parameters
    /// - API request failure
    /// - API response error
    /// - Null response data
    /// - Exception during execution
    /// </returns>
    public virtual async Task<Result<ProductInfo>> GetProductInfoByManufacturerCode(
        string brand,
        string manufacturerCode,
        List<string> contentTypes)
    {
        await _rateLimiter.WaitForTokenAsync();

        if (string.IsNullOrWhiteSpace(brand))
        {
            return Result.Failure<ProductInfo>("Brand must be specified");
        }

        if (string.IsNullOrWhiteSpace(manufacturerCode))
        {
            return Result.Failure<ProductInfo>("Manufacturer code must be specified");
        }

        if (contentTypes == null || contentTypes.Count == 0)
        {
            return Result.Failure<ProductInfo>("At least one content type must be specified");
        }

        try
        {
            var queryParams = new Dictionary<string, string>
            {
                { "UserName", Username },
                { "Language", "en" },
                { "Brand", brand },
                { "ProductCode", manufacturerCode },
                { "Content", string.Join(",", contentTypes) }
            };

            var response = await _httpClient.GetAsync(QueryStringHelper.BuildQuery(queryParams));

            if (!response.IsSuccessStatusCode)
            {
                return Result.Failure<ProductInfo>($"API request failed with status: {response.StatusCode}");
            }

            var content = await response.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<ProductInfo>(content);

            if ((result?.Message ?? "OK") != "OK")
            {
                return Result.Failure<ProductInfo>($"API error: {result?.Message ?? "Unknown error"}");
            }

            if (result?.Data == null)
            {
                return Result.Failure<ProductInfo>("API response data is null");
            }

            return Result.Success(result);
        }
        catch (Exception ex)
        {
            return Result.Failure<ProductInfo>($"Exception occurred: {ex.Message}");
        }
    }
}

internal static class QueryStringHelper
{
    public static string BuildQuery(Dictionary<string, string> parameters)
    {
        var query = HttpUtility.ParseQueryString(string.Empty);
        foreach (var param in parameters)
        {
            query[param.Key] = param.Value;
        }

        return "?" + query.ToString();
    }
}
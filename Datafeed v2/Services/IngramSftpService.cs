#nullable enable
using System;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using CSharpFunctionalExtensions;
using CsvHelper;
using CsvHelper.Configuration;
using CsvHelper.Configuration.Attributes;
using Renci.SshNet;

namespace Datafeed_v2.Services;

public class IngramCategoryModel
{
    [Name("Category Description")] public string Category { get; set; }
    [Name("Sub-Category Description")] public string SubCategory { get; set; }
    [Name("Ingram Part Number")] public string IngramPartNumber { get; set; }
}

public class IngramCsvModel
{
    [Name("Ingram Part Number")] public string IngramPartNumber { get; set; }

    [Name("Ingram Part Description")] public string IngramPartDescription { get; set; }

    [Name("Customer Part Number")] public string CustomerPartNumber { get; set; }

    [Name("Vendor Part Number")] public string VendorPartNumber { get; set; }

    [Name("EANUPC Code")] public string EANUPCCode { get; set; }

    [Name("Plant")] public string Plant { get; set; }

    [Name("Vendor Number")] public int VendorNumber { get; set; }

    [Name("Vendor Name")] public string VendorName { get; set; }

    [Name("Size")] public string Size { get; set; }

    [Name("Weight")] public float Weight { get; set; }

    [Name("Volume")] public decimal Volume { get; set; }

    [Name("Unit")] public string Unit { get; set; }

    [Name("Category ID")] public string CategoryID { get; set; }

    [Name("Customer Price")] public decimal CustomerPrice { get; set; }

    [Name("Retail Price")] public decimal RetailPrice { get; set; }

    [Name("Availability Flag")] public string AvailabilityFlag { get; set; }

    [Name("Available Quantity")] public int AvailableQuantity { get; set; }

    [Name("Backlog Information")] public int BacklogInformation { get; set; }

    [Name("Backlog ETA")] public string BacklogETA { get; set; }

    [Name("License Flag")] public string LicenseFlag { get; set; }

    [Name("BOM Flag")] public string BOMFlag { get; set; }

    [Name("Warranty Flag")] public string WarrantyFlag { get; set; }

    [Name("Bulk Freight Flag")] public string BulkFreightFlag { get; set; }

    [Name("Material Long Description")] public string MaterialLongDescription { get; set; }

    [Name("Length")] public float Length { get; set; }

    [Name("Width")] public float Width { get; set; }

    [Name("Height")] public float Height { get; set; }

    [Name("Dimension Unit")] public string DimensionUnit { get; set; }

    [Name("Weight Unit")] public string WeightUnit { get; set; }

    [Name("Volume Unit")] public string VolumeUnit { get; set; }

    [Name("Category")] public int Category { get; set; }

    [Name("Material Creation Reason code")]
    public string MaterialCreationReasonCode { get; set; }

    [Name("Media Code")] public string MediaCode { get; set; }

    [Name("Material Language Code")] public string MaterialLanguageCode { get; set; }

    [Name("Substitute Material")] public string SubstituteMaterial { get; set; }

    [Name("Superseded Material")] public string SupersededMaterial { get; set; }

    [Name("Manufacturer Vendor Number")] public int ManufacturerVendorNumber { get; set; }

    [Name("Sub-Category")] public string SubCategory { get; set; }

    [Name("Product Family")] public string ProductFamily { get; set; }

    [Name("Purchasing Vendor")] public string PurchasingVendor { get; set; }

    [Name("Material Change Code")] public string MaterialChangeCode { get; set; }

    [Name("Action code")] public string ActionCode { get; set; }

    [Name("Price Status")] public string PriceStatus { get; set; }

    [Name("New Material Flag")] public string NewMaterialFlag { get; set; }

    [Name("Vendor Subrange")] public string VendorSubrange { get; set; }

    [Name("Case Qty")] public int CaseQty { get; set; }

    [Name("Pallet Qty")] public int PalletQty { get; set; }

    [Name("Direct Order identifier")] public string DirectOrderIdentifier { get; set; }

    [Name("Material Status")] public string MaterialStatus { get; set; }

    [Name("Discontinued / Obsoleted date")]
    public string Discontinued { get; set; }

    [Name("Release Date")] public string ReleaseDate { get; set; }

    [Name("Fulfilment type")] public string FulfilmentType { get; set; }

    [Name("Music Copyright Fees")] public int MusicCopyrightFees { get; set; }

    [Name("Recycling Fees")] public int RecyclingFees { get; set; }

    [Name("Document Copyright Fees")] public int DocumentCopyrightFees { get; set; }

    [Name("Battery Fees")] public int BatteryFees { get; set; }

    [Name("Customer Price with Tax")] public decimal CustomerPriceWithTax { get; set; }

    [Name("Retail Price with Tax")] public decimal RetailPriceWithTax { get; set; }

    [Name("Tax Percent")] public decimal TaxPercent { get; set; }

    [Name("Discount in Percent")] public string DiscountInPercent { get; set; }

    [Name("Customer Reservation Number")] public string CustomerReservationNumber { get; set; }

    [Name("Customer Reservation Qty")] public string CustomerReservationQty { get; set; }

    [Name("Agreement ID")] public string AgreementID { get; set; }

    [Name("Level ID")] public string LevelID { get; set; }

    [Name("Period")] public string Period { get; set; }

    [Name("Points")] public string Points { get; set; }

    [Name("Company code")] public int CompanyCode { get; set; }

    [Name("Company code Currency")] public string CompanyCodeCurrency { get; set; }

    [Name("Customer Currency Code")] public string CustomerCurrencyCode { get; set; }

    [Name("Customer Price Change Flag")] public string CustomerPriceChangeFlag { get; set; }

    [Name("Substitute Flag")] public string SubstituteFlag { get; set; }

    [Name("Creation Reason Type")] public string CreationReasonType { get; set; }

    [Name("Creation Reason Value")] public string CreationReasonValue { get; set; }

    [Name("Plant 01 Available Quantity")] public decimal Plant01AvailableQuantity { get; set; }

    [Name("Plant 02 Available Quantity")] public string Plant02AvailableQuantity { get; set; }
}

public class IngramSftpService(string hostname, string username, string password)
{
    private ISftpClient? _client;
    private bool IsConnected => _client is { IsConnected: true };

    private Result ValidateState(bool validateConnection = true)
    {
        if (string.IsNullOrWhiteSpace(hostname))
        {
            return Result.Failure("Hostname cannot be null or whitespace");
        }

        if (Uri.TryCreate(hostname.Trim(), UriKind.Absolute, out var hostnameUri) == false)
        {
            return Result.Failure("Hostname has to be a valid URL");
        }

        if (!string.Equals(hostnameUri.Scheme.Trim(), "sftp".Trim(), StringComparison.CurrentCultureIgnoreCase))
        {
            return Result.Failure("Hostname has to be a valid SFTP URL");
        }

        if (string.IsNullOrWhiteSpace(username))
        {
            return Result.Failure("Username cannot be null or whitespace");
        }

        if (string.IsNullOrWhiteSpace(password))
        {
            return Result.Failure("Password cannot be null or whitespace");
        }

        if (validateConnection && IsConnected == false)
        {
            return Result.Failure("No valid connection to SFTP server has been established");
        }

        return Result.Success();
    }

    public Result<IngramSftpService> Connect(ISftpClient? clientMock = null)
    {
        if (ValidateState(false) is { IsFailure: true } state)
        {
            return Result.Failure<IngramSftpService>(state.Error);
        }

        _client = clientMock ??
                  new SftpClient(hostname.Replace("sftp://", "").Replace("ftp://", ""), username, password);
        if (Result.Try(() => _client.Connect()) is { IsFailure: true } connectResult)
        {
            return Result.Failure<IngramSftpService>($"Could not connect to SFTP server - {connectResult.Error}");
        }

        return Result.Success(this);
    }

    private static Result<string> UnzipFileFromStream(MemoryStream ms, Encoding encoding)
    {
        var zipResult = Result.Try(() => new ZipArchive(ms, ZipArchiveMode.Read));
        if (zipResult.IsFailure)
        {
            return Result.Failure<string>(zipResult.Error);
        }

        using var zipArchive = zipResult.Value;
        if (zipArchive.Entries.FirstOrDefault() is var entry && entry == null)
        {
            return Result.Failure<string>("No entries found in zip archive");
        }

        var zipMs = new MemoryStream();
        if (Result.Try(() => entry.Open().CopyTo(zipMs)) is { IsFailure: true } copyResult)
        {
            return Result.Failure<string>(copyResult.Error);
        }

        var stringResult = Result.Try(() => encoding.GetString(zipMs.ToArray()));
        if (stringResult.IsFailure)
        {
            return Result.Failure<string>(stringResult.Error);
        }

        return Result.Success(stringResult.Value);
    }

    private Result<string> DownloadFile(string path, Encoding encoding)
    {
        if (string.IsNullOrWhiteSpace(path))
        {
            return Result.Failure<string>("Path cannot be null or whitespace");
        }

        if (ValidateState() is { IsFailure: true } state)
        {
            return Result.Failure<string>(state.Error);
        }

        Debug.Assert(_client != null, $"{nameof(_client)} is null");
        var ms = new MemoryStream();
        if (Result.Try(() => _client?.DownloadFile(path, ms)) is { IsFailure: true } downloadResult)
        {
            return Result.Failure<string>(downloadResult.Error);
        }

        Debug.Assert(ms.Length > 0, $"{nameof(ms)} is empty");
        if (path.Trim().ToLower().EndsWith(".zip"))
        {
            return UnzipFileFromStream(ms, encoding);
        }

        var stringResult = Result.Try(() => encoding.GetString(ms.ToArray()));
        return stringResult.IsFailure
            ? Result.Failure<string>(stringResult.Error)
            : Result.Success(stringResult.Value);
    }

    private static Result<T[]> ParseCsv<T>(string file) where T : class
    {
        if (string.IsNullOrWhiteSpace(file))
        {
            return Result.Failure<T[]>("File cannot be null or whitespace");
        }

        var (_, stringReaderFailed, reader, stringReaderError) = Result.Try(() => new StringReader(file));
        if (stringReaderFailed)
        {
            return Result.Failure<T[]>(stringReaderError);
        }

        var (_, isFailure, csv, error) = Result.Try(() => new CsvReader(reader,
            new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
                HeaderValidated = null,
                MissingFieldFound = null, // Don't throw exception when CsvReader encounters an empty column
                ShouldSkipRecord =
                    args => (args.Row.Parser.Record ?? []).All(string
                        .IsNullOrWhiteSpace), // Skip any entirely empty rows
                BadDataFound = null
            }));
        if (isFailure)
        {
            return Result.Failure<T[]>(error);
        }

        var (_, getRecordsFailed, records, getRecordsError) = Result.Try(() => csv.GetRecordsAsync<T>());
        return getRecordsFailed
            ? Result.Failure<T[]>(getRecordsError)
            : Result.Success(records.ToArrayAsync().ConfigureAwait(false).GetAwaiter().GetResult());
    }

    public Result<IngramCsvModel[]> FetchFeed(string path)
    {
        if (string.IsNullOrWhiteSpace(path))
        {
            return Result.Failure<IngramCsvModel[]>("Path cannot be null or whitespace");
        }

        if (ValidateState() is { IsFailure: true } state)
        {
            return Result.Failure<IngramCsvModel[]>(state.Error);
        }

        var (_, downloadFailed, file, downloadError) = DownloadFile(path, Encoding.UTF8);
        if (downloadFailed)
        {
            return Result.Failure<IngramCsvModel[]>(downloadError);
        }

        var (_, parseFailed, records, parseError) = ParseCsv<IngramCsvModel>(file);
        return parseFailed
            ? Result.Failure<IngramCsvModel[]>(parseError)
            : Result.Success(records);
    }

    public Result<IngramCategoryModel[]> FetchCategories(string folder, string path)
    {
        if (string.IsNullOrWhiteSpace(path))
        {
            return Result.Failure<IngramCategoryModel[]>("Path cannot be null or whitespace");
        }

        if (ValidateState() is { IsFailure: true } state)
        {
            return Result.Failure<IngramCategoryModel[]>(state.Error);
        }

        Debug.Assert(_client != null, $"{nameof(_client)} is null");
        _client.ChangeDirectory(folder);

        var (_, downloadFailed, file, downloadError) = DownloadFile(path, Encoding.UTF8);
        if (downloadFailed)
        {
            return Result.Failure<IngramCategoryModel[]>(downloadError);
        }

        var (_, parseFailed, records, parseError) = ParseCsv<IngramCategoryModel>(file);
        return parseFailed
            ? Result.Failure<IngramCategoryModel[]>(parseError)
            : Result.Success(records);
    }
}
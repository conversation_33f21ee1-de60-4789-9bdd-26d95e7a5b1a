tinymce.Resource.add('tinymce.html-i18n.help-keynav.tr',
    '<h1><PERSON><PERSON><PERSON><PERSON> g<PERSON>yi ba<PERSON></h1>\n' +
    '\n' +
    '<dl>\n' +
    '  <dt>Menü çubuğuna odaklan</dt>\n' +
    '  <dd>Windows veya Linux: Alt+F9</dd>\n' +
    '  <dd>macOS: &#x2325;F9</dd>\n' +
    '  <dt>Araç çubuğuna odaklan</dt>\n' +
    '  <dd>Windows veya Linux: Alt+F10</dd>\n' +
    '  <dd>macOS: &#x2325;F10</dd>\n' +
    '  <dt>Alt bilgiye odaklan</dt>\n' +
    '  <dd>Windows veya Linux: Alt+F11</dd>\n' +
    '  <dd>macOS: &#x2325;F11</dd>\n' +
    '  <dt>Bildirime odakla</dt>\n' +
    '  <dd>Windows veya Linux: Alt+F12</dd>\n' +
    '  <dd>macOS: &#x2325;F12</dd>\n' +
    '  <dt>Bağlamsal araç çubuğuna odaklan</dt>\n' +
    '  <dd>Windows, Linux veya macOS: Ctrl+F9</dd>\n' +
    '</dl>\n' +
    '\n' +
    '<p>Gezinti ilk kullanıcı arabirimi öğesinden başlar, bu öğe vurgulanır ya da ilk öğe, Alt bilgi elemanı\n' +
    '  yolundaysa altı çizilir.</p>\n' +
    '\n' +
    '<h1>Kullanıcı arabirimi bölümleri arasında gezinme</h1>\n' +
    '\n' +
    '<p>Sonraki kullanıcı arabirimi bölümüne gitmek için <strong>Sekme</strong> tuşuna basın.</p>\n' +
    '\n' +
    '<p>Önceki kullanıcı arabirimi bölümüne gitmek için <strong>Shift+Sekme</strong> tuşlarına basın.</p>\n' +
    '\n' +
    '<p>Bu kullanıcı arabirimi bölümlerinin <strong>Sekme</strong> sırası:</p>\n' +
    '\n' +
    '<ol>\n' +
    '  <li>Menü çubuğu</li>\n' +
    '  <li>Her araç çubuğu grubu</li>\n' +
    '  <li>Kenar çubuğu</li>\n' +
    '  <li>Alt bilgide öğe yolu</li>\n' +
    '  <li>Alt bilgide sözcük sayısı geçiş düğmesi</li>\n' +
    '  <li>Alt bilgide marka bağlantısı</li>\n' +
    '  <li>Alt bilgide düzenleyiciyi yeniden boyutlandırma tutamacı</li>\n' +
    '</ol>\n' +
    '\n' +
    '<p>Kullanıcı arabirimi bölümü yoksa atlanır.</p>\n' +
    '\n' +
    '<p>Alt bilgide klavyeyle gezinti odağı yoksa ve görünür bir kenar çubuğu mevcut değilse <strong>Shift+Sekme</strong> tuşlarına basıldığında\n' +
    '  odak son araç çubuğu yerine ilk araç çubuğu grubuna taşınır.</p>\n' +
    '\n' +
    '<h1>Kullanıcı arabirimi bölümleri içinde gezinme</h1>\n' +
    '\n' +
    '<p>Sonraki kullanıcı arabirimi elemanına gitmek için uygun <strong>Ok</strong> tuşuna basın.</p>\n' +
    '\n' +
    '<p><strong>Sol</strong> ve <strong>Sağ</strong> ok tuşları</p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>menü çubuğundaki menüler arasında hareket eder.</li>\n' +
    '  <li>menüde bir alt menü açar.</li>\n' +
    '  <li>araç çubuğu grubundaki düğmeler arasında hareket eder.</li>\n' +
    '  <li>alt bilginin öğe yolundaki öğeler arasında hareket eder.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p><strong>Aşağı</strong> ve <strong>Yukarı</strong> ok tuşları</p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>menüdeki menü öğeleri arasında hareket eder.</li>\n' +
    '  <li>araç çubuğu açılır menüsündeki öğeler arasında hareket eder.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p><strong>Ok</strong> tuşları, odaklanılan kullanıcı arabirimi bölümü içinde döngüsel olarak hareket eder.</p>\n' +
    '\n' +
    '<p>Açık bir menüyü, açık bir alt menüyü veya açık bir açılır menüyü kapatmak için <strong>Esc</strong> tuşuna basın.</p>\n' +
    '\n' +
    '<p>Geçerli odak belirli bir kullanıcı arabirimi bölümünün "üst" kısmındaysa <strong>Esc</strong> tuşuna basıldığında\n' +
    '  klavyeyle gezintiden de tamamen çıkılır.</p>\n' +
    '\n' +
    '<h1>Menü öğesini veya araç çubuğu düğmesini yürütme</h1>\n' +
    '\n' +
    '<p>İstediğiniz menü öğesi veya araç çubuğu düğmesi vurgulandığında <strong>Return</strong>, <strong>Enter</strong>\n' +
    '  veya <strong>Ara çubuğu</strong> tuşuna basın.</p>\n' +
    '\n' +
    '<h1>Sekme bulunmayan iletişim kutularında gezinme</h1>\n' +
    '\n' +
    '<p>Sekme bulunmayan iletişim kutularında, iletişim kutusu açıldığında ilk etkileşimli bileşene odaklanılır.</p>\n' +
    '\n' +
    '<p>Etkileşimli iletişim kutusu bileşenleri arasında gezinmek için <strong>Sekme</strong> veya <strong>Shift+ Sekme</strong> tuşlarına basın.</p>\n' +
    '\n' +
    '<h1>Sekmeli iletişim kutularında gezinme</h1>\n' +
    '\n' +
    '<p>Sekmeli iletişim kutularında, iletişim kutusu açıldığında sekme menüsündeki ilk düğmeye odaklanılır.</p>\n' +
    '\n' +
    '<p>Bu iletişim kutusu sekmesinin etkileşimli bileşenleri arasında gezinmek için <strong>Sekme</strong> veya\n' +
    '  <strong>Shift+Sekme</strong> tuşlarına basın.</p>\n' +
    '\n' +
    '<p>Mevcut sekmeler arasında geçiş yapmak için sekme menüsüne odaklanıp uygun <strong>Ok</strong> tuşuna basarak\n' +
    '  başka bir iletişim kutusu sekmesine geçiş yapın.</p>\n');
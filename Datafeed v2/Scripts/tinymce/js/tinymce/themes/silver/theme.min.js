/**
 * TinyMCE version 7.8.0 (TBD)
 */
!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=e=>()=>e(),x=(e,t)=>(...o)=>e(t.apply(null,o)),y=e=>()=>e,w=e=>e,S=(e,t)=>e===t;function C(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const k=e=>t=>!e(t),O=e=>()=>{throw new Error(e)},_=e=>e(),T=y(!1),E=y(!0);class A{constructor(e,t){this.tag=e,this.value=t}static some(e){return new A(!0,e)}static none(){return A.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?A.some(e(this.value)):A.none()}bind(e){return this.tag?e(this.value):A.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:A.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?A.some(e):A.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}A.singletonNone=new A(!1);const M=Array.prototype.slice,D=Array.prototype.indexOf,B=Array.prototype.push,I=(e,t)=>D.call(e,t),F=(e,t)=>I(e,t)>-1,R=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},N=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},z=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=M.call(e,n,n+t);o.push(s)}return o},L=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},V=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},H=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},P=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},U=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),W=(e,t,o)=>(V(e,((e,n)=>{o=t(o,e,n)})),o),$=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return A.some(s);if(o(s,n))break}return A.none()})(e,t,T),G=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return A.some(o);return A.none()},j=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);B.apply(t,e[o])}return t},q=(e,t)=>j(L(e,t)),X=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},Y=e=>{const t=M.call(e,0);return t.reverse(),t},K=(e,t)=>P(e,(e=>!F(t,e))),J=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],Z=(e,t)=>{const o=M.call(e,0);return o.sort(t),o},ee=(e,t)=>t>=0&&t<e.length?A.some(e[t]):A.none(),te=e=>ee(e,0),oe=e=>ee(e,e.length-1),ne=p(Array.from)?Array.from:e=>M.call(e),se=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return A.none()},re=Object.keys,ae=Object.hasOwnProperty,ie=(e,t)=>{const o=re(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},le=(e,t)=>ce(e,((e,o)=>({k:o,v:t(e,o)}))),ce=(e,t)=>{const o={};return ie(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},de=e=>(t,o)=>{e[o]=t},ue=(e,t,o,n)=>{ie(e,((e,s)=>{(t(e,s)?o:n)(e,s)}))},me=(e,t)=>{const o={};return ue(e,t,de(o),b),o},ge=(e,t)=>{const o=[];return ie(e,((e,n)=>{o.push(t(e,n))})),o},pe=(e,t)=>{const o=re(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return A.some(r)}return A.none()},he=e=>ge(e,w),fe=(e,t)=>be(e,t)?A.from(e[t]):A.none(),be=(e,t)=>ae.call(e,t),ve=(e,t)=>be(e,t)&&void 0!==e[t]&&null!==e[t],xe=(e,t,o=S)=>e.exists((e=>o(e,t))),ye=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},we=(e,t,o)=>e.isSome()&&t.isSome()?A.some(o(e.getOrDie(),t.getOrDie())):A.none(),Se=(e,t)=>null!=e?A.some(t(e)):A.none(),Ce=(e,t)=>e?A.some(t):A.none(),ke=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,Oe=(e,t)=>Te(e,t)?((e,t)=>e.substring(t))(e,t.length):e,_e=(e,t,o=0,n)=>{const s=e.indexOf(t,o);return-1!==s&&(!!u(n)||s+t.length<=n)},Te=(e,t)=>ke(e,t,0),Ee=(e,t)=>ke(e,t,e.length-t.length),Ae=(Eo=/^\s+|\s+$/g,e=>e.replace(Eo,"")),Me=e=>e.length>0,De=e=>!Me(e),Be=e=>void 0!==e.style&&p(e.style.getPropertyValue),Ie=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Fe=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return Ie(o.childNodes[0])},Re=(e,t)=>{const o=(t||document).createElement(e);return Ie(o)},Ne=(e,t)=>{const o=(t||document).createTextNode(e);return Ie(o)},ze=Ie,Le="undefined"!=typeof window?window:Function("return this;")(),Ve=(e,t)=>((e,t)=>{let o=null!=t?t:Le;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),He=Object.getPrototypeOf,Pe=e=>{const t=Ve("ownerDocument.defaultView",e);return a(e)&&((e=>((e,t)=>{const o=((e,t)=>Ve(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(He(e).constructor.name))},Ue=e=>e.dom.nodeName.toLowerCase(),We=e=>t=>(e=>e.dom.nodeType)(t)===e,$e=e=>Ge(e)&&Pe(e.dom),Ge=We(1),je=We(3),qe=We(9),Xe=We(11),Ye=e=>t=>Ge(t)&&Ue(t)===e,Ke=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Je=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Qe=(e,t)=>{const o=void 0===t?document:t.dom;return Je(o)?A.none():A.from(o.querySelector(e)).map(ze)},Ze=(e,t)=>e.dom===t.dom,et=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},tt=e=>ze(e.dom.ownerDocument),ot=e=>qe(e)?e:tt(e),nt=e=>ze(ot(e).dom.documentElement),st=e=>ze(ot(e).dom.defaultView),rt=e=>A.from(e.dom.parentNode).map(ze),at=e=>A.from(e.dom.parentElement).map(ze),it=e=>A.from(e.dom.offsetParent).map(ze),lt=e=>L(e.dom.childNodes,ze),ct=(e,t)=>{const o=e.dom.childNodes;return A.from(o[t]).map(ze)},dt=e=>ct(e,0),ut=(e,t)=>({element:e,offset:t}),mt=(e,t)=>{const o=lt(e);return o.length>0&&t<o.length?ut(o[t],0):ut(e,t)},gt=e=>Xe(e)&&g(e.dom.host),pt=e=>ze(e.dom.getRootNode()),ht=e=>gt(e)?e:ze(ot(e).dom.body),ft=e=>{const t=pt(e);return gt(t)?A.some(t):A.none()},bt=e=>ze(e.dom.host),vt=e=>{const t=je(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return ft(ze(t)).fold((()=>o.body.contains(t)),(n=vt,s=bt,e=>n(s(e))));var n,s},xt=()=>yt(ze(document)),yt=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return ze(t)},wt=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},St=(e,t,o)=>{wt(e.dom,t,o)},Ct=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{wt(o,t,e)}))},kt=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},Ot=(e,t)=>A.from(kt(e,t)),_t=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},Tt=(e,t)=>{e.dom.removeAttribute(t)},Et=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Be(e)&&e.style.setProperty(t,o)},At=(e,t)=>{Be(e)&&e.style.removeProperty(t)},Mt=(e,t,o)=>{const n=e.dom;Et(n,t,o)},Dt=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{Et(o,t,e)}))},Bt=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{e.fold((()=>{At(o,t)}),(e=>{Et(o,t,e)}))}))},It=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||vt(e)?n:Ft(o,t)},Ft=(e,t)=>Be(e)?e.style.getPropertyValue(t):"",Rt=(e,t)=>{const o=e.dom,n=Ft(o,t);return A.from(n).filter((e=>e.length>0))},Nt=e=>{const t={},o=e.dom;if(Be(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},zt=(e,t,o)=>{const n=Re(e);return Mt(n,t,o),Rt(n,t).isSome()},Lt=(e,t)=>{const o=e.dom;At(o,t),xe(Ot(e,"style").map(Ae),"")&&Tt(e,"style")},Vt=e=>e.dom.offsetWidth,Ht=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=It(o,e);return parseFloat(t)||0}return n},n=(e,t)=>W(t,((t,o)=>{const n=It(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Be(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},Pt=Ht("height",(e=>{const t=e.dom;return vt(e)?t.getBoundingClientRect().height:t.offsetHeight})),Ut=e=>Pt.get(e),Wt=e=>Pt.getOuter(e),$t=(e,t)=>({left:e,top:t,translate:(o,n)=>$t(e+o,t+n)}),Gt=$t,jt=(e,t)=>void 0!==e?e:void 0!==t?t:0,qt=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return Gt(o.offsetLeft,o.offsetTop);const r=jt(null==n?void 0:n.pageYOffset,s.scrollTop),a=jt(null==n?void 0:n.pageXOffset,s.scrollLeft),i=jt(s.clientTop,o.clientTop),l=jt(s.clientLeft,o.clientLeft);return Xt(e).translate(a-l,r-i)},Xt=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?Gt(o.offsetLeft,o.offsetTop):vt(e)?(e=>{const t=e.getBoundingClientRect();return Gt(t.left,t.top)})(t):Gt(0,0)},Yt=Ht("width",(e=>e.dom.offsetWidth)),Kt=e=>Yt.get(e),Jt=e=>Yt.getOuter(e),Qt=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},Zt=()=>eo(0,0),eo=(e,t)=>({major:e,minor:t}),to={nu:eo,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?Zt():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return eo(n(1),n(2))})(e,o)},unknown:Zt},oo=(e,t)=>{const o=String(t).toLowerCase();return $(e,(e=>e.search(o)))},no=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,so=e=>t=>_e(t,e),ro=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>_e(e,"edge/")&&_e(e,"chrome")&&_e(e,"safari")&&_e(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,no],search:e=>_e(e,"chrome")&&!_e(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>_e(e,"msie")||_e(e,"trident")},{name:"Opera",versionRegexes:[no,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:so("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:so("firefox")},{name:"Safari",versionRegexes:[no,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(_e(e,"safari")||_e(e,"mobile/"))&&_e(e,"applewebkit")}],ao=[{name:"Windows",search:so("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>_e(e,"iphone")||_e(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:so("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:so("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:so("linux"),versionRegexes:[]},{name:"Solaris",search:so("sunos"),versionRegexes:[]},{name:"FreeBSD",search:so("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:so("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],io={browsers:y(ro),oses:y(ao)},lo="Edge",co="Chromium",uo="Opera",mo="Firefox",go="Safari",po=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(lo),isChromium:n(co),isIE:n("IE"),isOpera:n(uo),isFirefox:n(mo),isSafari:n(go)}},ho=()=>po({current:void 0,version:to.unknown()}),fo=po,bo=(y(lo),y(co),y("IE"),y(uo),y(mo),y(go),"Windows"),vo="Android",xo="Linux",yo="macOS",wo="Solaris",So="FreeBSD",Co="ChromeOS",ko=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(bo),isiOS:n("iOS"),isAndroid:n(vo),isMacOS:n(yo),isLinux:n(xo),isSolaris:n(wo),isFreeBSD:n(So),isChromeOS:n(Co)}},Oo=()=>ko({current:void 0,version:to.unknown()}),_o=ko,To=(y(bo),y("iOS"),y(vo),y(xo),y(yo),y(wo),y(So),y(Co),e=>window.matchMedia(e).matches);var Eo;let Ao=Qt((()=>((e,t,o)=>{const n=io.browsers(),s=io.oses(),r=t.bind((e=>((e,t)=>se(t.brands,(t=>{const o=t.brand.toLowerCase();return $(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:to.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>oo(e,t).map((e=>{const o=to.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(ho,fo),a=((e,t)=>oo(e,t).map((e=>{const o=to.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(Oo,_o),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:y(s),isiPhone:y(r),isTablet:y(l),isPhone:y(c),isTouch:y(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:y(d),isDesktop:y(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(window.navigator.userAgent,A.from(window.navigator.userAgentData),To)));const Mo=()=>Ao(),Do=e=>{const t=ze((e=>{if(g(e.target)){const t=ze(e.target);if(Ge(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return te(t)}}return A.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=x(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},Bo=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(Do(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:C(Io,e,t,r,s)}},Io=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Fo=(e,t)=>{rt(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},Ro=(e,t)=>{const o=(e=>A.from(e.dom.nextSibling).map(ze))(e);o.fold((()=>{rt(e).each((e=>{zo(e,t)}))}),(e=>{Fo(e,t)}))},No=(e,t)=>{dt(e).fold((()=>{zo(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},zo=(e,t)=>{e.dom.appendChild(t.dom)},Lo=(e,t)=>{V(t,(t=>{zo(e,t)}))},Vo=e=>{e.dom.textContent="",V(lt(e),(e=>{Ho(e)}))},Ho=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Po=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return Gt(o,n)},Uo=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},Wo=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),$o=e=>{const t=void 0===e?window:e,o=t.document,n=Po(ze(o));return(e=>{const t=void 0===e?window:e;return Mo().browser.isFirefox()?A.none():A.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return Wo(n.left,n.top,o,s)}),(e=>Wo(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},Go=()=>ze(document),jo=(e,t)=>e.view(t).fold(y([]),(t=>{const o=e.owner(t),n=jo(e,o);return[t].concat(n)}));var qo=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?A.none():A.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(ze)},owner:e=>tt(e)});const Xo=e=>{const t=Go(),o=Po(t),n=((e,t)=>{const o=t.owner(e),n=jo(t,o);return A.some(n)})(e,qo);return n.fold(C(qt,e),(t=>{const n=Xt(e),s=U(t,((e,t)=>{const o=Xt(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return Gt(s.left+n.left+o.left,s.top+n.top+o.top)}))},Yo=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Ko=e=>{const t=qt(e),o=Jt(e),n=Wt(e);return Yo(t.left,t.top,o,n)},Jo=e=>{const t=Xo(e),o=Jt(e),n=Wt(e);return Yo(t.left,t.top,o,n)},Qo=(e,t)=>{const o=Math.max(e.x,t.x),n=Math.max(e.y,t.y),s=Math.min(e.right,t.right),r=Math.min(e.bottom,t.bottom);return Yo(o,n,s-o,r-n)},Zo=()=>$o(window),en=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},tn=e=>{const t=en(A.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(A.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(A.some(e))}}},on=()=>tn((e=>e.unbind())),nn=()=>{const e=tn(b);return{...e,on:t=>e.get().each(t)}};var sn=(e,t,o,n,s)=>e(o,n)?A.some(o):p(s)&&s(o)?A.none():t(o,n,s);const rn=(e,t,o)=>{let n=e.dom;const s=p(o)?o:T;for(;n.parentNode;){n=n.parentNode;const e=ze(n);if(t(e))return A.some(e);if(s(e))break}return A.none()},an=(e,t,o)=>sn(((e,t)=>t(e)),rn,e,t,o),ln=(e,t)=>$(e.dom.childNodes,(e=>t(ze(e)))).map(ze),cn=(e,t,o)=>rn(e,(e=>Ke(e,t)),o),dn=(e,t)=>(e=>{const o=e.dom;return o.parentNode?ln(ze(o.parentNode),(o=>!Ze(e,o)&&Ke(o,t))):A.none()})(e),un=(e,t)=>ln(e,(e=>Ke(e,t))),mn=(e,t)=>Qe(t,e),gn=(e,t,o)=>sn(((e,t)=>Ke(e,t)),cn,e,t,o);var pn=tinymce.util.Tools.resolve("tinymce.ThemeManager");const hn=e=>{const t=t=>t(e),o=y(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:E,isError:T,map:t=>bn.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>A.some(e)};return s},fn=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:T,isError:E,map:t,mapError:t=>bn.error(t(e)),bind:t,exists:T,forall:E,getOr:w,or:w,getOrThunk:_,orThunk:_,getOrDie:O(String(e)),each:b,toOptional:A.none};return o},bn={value:hn,error:fn,fromOption:(e,t)=>e.fold((()=>fn(t)),hn)};var vn;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(vn||(vn={}));const xn=(e,t,o)=>e.stype===vn.Error?t(e.serror):o(e.svalue),yn=e=>({stype:vn.Value,svalue:e}),wn=e=>({stype:vn.Error,serror:e}),Sn=yn,Cn=wn,kn=xn,On=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),_n=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},Tn=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)be(s,t)&&(o[t]=e(o[t],s[t]))}return o},En=Tn(((e,t)=>i(e)&&i(t)?En(e,t):t)),An=Tn(((e,t)=>t)),Mn=e=>({tag:"defaultedThunk",process:e}),Dn=e=>Mn(y(e)),Bn=e=>({tag:"mergeWithThunk",process:e}),In=e=>{const t=(e=>{const t=[],o=[];return V(e,(e=>{xn(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,x(Cn,j)(o)):Sn(t.values);var o},Fn=e=>a(e)&&re(e).length>100?" removed due to size":JSON.stringify(e,null,2),Rn=(e,t)=>Cn([{path:e,getErrorInfo:t}]),Nn=e=>({extract:(t,o)=>((e,t)=>e.stype===vn.Error?t(e.serror):e)(e(o),(e=>((e,t)=>Rn(e,y(t)))(t,e))),toString:y("val")}),zn=Nn(Sn),Ln=(e,t,o,n)=>n(fe(e,t).getOrThunk((()=>o(e)))),Vn=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>Sn(A.none())),(e=>((e,t)=>e.stype===vn.Value?{stype:vn.Value,svalue:t(e.svalue)}:e)(s.extract(t.concat([n]),e),A.some)));switch(e.tag){case"required":return((e,t,o,n)=>fe(t,o).fold((()=>((e,t,o)=>Rn(e,(()=>'Could not find valid *required* value for "'+t+'" in '+Fn(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return Ln(o,n,e.process,r);case"option":return((e,t,o)=>o(fe(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(fe(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return Ln(o,n,y({}),(t=>{const n=En(e.process(o),t);return r(n)}))}},Hn=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),Pn=e=>re(me(e,g)),Un=e=>{const t=Wn(e),o=U(e,((e,t)=>_n(t,(t=>En(e,{[t]:!0})),y(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:Pn(n),r=P(s,(e=>!ve(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>Rn(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},Wn=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)_n(r,((o,r,a,i)=>{const l=Vn(a,e,t,o,i);kn(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?Cn(s):Sn(n)})(t,o,e),toString:()=>"obj{\n"+L(e,(e=>_n(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")")))).join("\n")+"}"}),$n=e=>({extract:(t,o)=>{const n=L(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return In(n)},toString:()=>"array("+e.toString()+")"}),Gn=(e,t)=>{const o=void 0!==t?t:w;return{extract:(t,n)=>{const s=[];for(const r of e){const e=r.extract(t,n);if(e.stype===vn.Value)return{stype:vn.Value,svalue:o(e.svalue)};s.push(e)}return In(s)},toString:()=>"oneOf("+L(e,(e=>e.toString())).join(", ")+")"}},jn=(e,t)=>({extract:(o,n)=>{const s=re(n),r=((t,o)=>$n(Nn(e)).extract(t,o))(o,s);return((e,t)=>e.stype===vn.Value?t(e.svalue):e)(r,(e=>{const s=L(e,(e=>On(e,e,{tag:"required",process:{}},t)));return Wn(s).extract(o,n)}))},toString:()=>"setOf("+t.toString()+")"}),qn=x($n,Wn),Xn=y(zn),Yn=(e,t)=>Nn((o=>{const n=typeof o;return e(o)?Sn(o):Cn(`Expected type: ${t} but got: ${n}`)})),Kn=Yn(h,"number"),Jn=Yn(r,"string"),Qn=Yn(d,"boolean"),Zn=Yn(p,"function"),es=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>es(e[t])));default:return!1}},ts=Nn((e=>es(e)?Sn(e):Cn("Expected value to be acceptable for sending via postMessage"))),os=(e,t)=>({extract:(o,n)=>fe(n,e).fold((()=>((e,t)=>Rn(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>fe(o,n).fold((()=>((e,t,o)=>Rn(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+Fn(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+re(t)}),ns=e=>Nn((t=>e(t).fold(Cn,Sn))),ss=(e,t)=>jn((t=>e(t).fold(wn,yn)),t),rs=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===vn.Error?{stype:vn.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),xn(n,bn.error,bn.value);var n},as=e=>e.fold((e=>{throw new Error(ls(e))}),w),is=(e,t,o)=>as(rs(e,t,o)),ls=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:y("... (only showing first ten failures)")}]):e;return L(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+Fn(e.input),cs=(e,t)=>os(e,le(t,Wn)),ds=(e,t)=>((e,t)=>{const o=Qt(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,t),us=On,ms=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),gs=e=>ns((t=>F(e,t)?bn.value(t):bn.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),ps=e=>us(e,e,{tag:"required",process:{}},Xn()),hs=(e,t)=>us(e,e,{tag:"required",process:{}},t),fs=e=>hs(e,Kn),bs=e=>hs(e,Jn),vs=(e,t)=>us(e,e,{tag:"required",process:{}},gs(t)),xs=e=>hs(e,Zn),ys=(e,t)=>us(e,e,{tag:"required",process:{}},Wn(t)),ws=(e,t)=>us(e,e,{tag:"required",process:{}},qn(t)),Ss=(e,t)=>us(e,e,{tag:"required",process:{}},$n(t)),Cs=e=>us(e,e,{tag:"option",process:{}},Xn()),ks=(e,t)=>us(e,e,{tag:"option",process:{}},t),Os=e=>ks(e,Kn),_s=e=>ks(e,Jn),Ts=(e,t)=>ks(e,gs(t)),Es=e=>ks(e,Zn),As=(e,t)=>ks(e,$n(t)),Ms=(e,t)=>ks(e,Wn(t)),Ds=(e,t)=>us(e,e,Dn(t),Xn()),Bs=(e,t,o)=>us(e,e,Dn(t),o),Is=(e,t)=>Bs(e,t,Kn),Fs=(e,t)=>Bs(e,t,Jn),Rs=(e,t,o)=>Bs(e,t,gs(o)),Ns=(e,t)=>Bs(e,t,Qn),zs=(e,t)=>Bs(e,t,Zn),Ls=(e,t,o)=>Bs(e,t,$n(o)),Vs=(e,t,o)=>Bs(e,t,Wn(o)),Hs=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return V(e,((n,s)=>{const r=re(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=re(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!X(t,(e=>F(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o};Hs([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const Ps=(e,t)=>((e,t)=>({[e]:t}))(e,t),Us=e=>(e=>{const t={};return V(e,(e=>{t[e.key]=e.value})),t})(e),Ws=e=>p(e)?e:T,$s=(e,t,o)=>{let n=e.dom;const s=Ws(o);for(;n.parentNode;){n=n.parentNode;const e=ze(n),o=t(e);if(o.isSome())return o;if(s(e))break}return A.none()},Gs=(e,t,o)=>{const n=t(e),s=Ws(o);return n.orThunk((()=>s(e)?A.none():$s(e,t,s)))},js=(e,t)=>Ze(e.element,t.event.target),qs={can:E,abort:T,run:b},Xs=e=>{if(!ve(e,"can")&&!ve(e,"abort")&&!ve(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...qs,...e}},Ys=y,Ks=Ys("touchstart"),Js=Ys("touchmove"),Qs=Ys("touchend"),Zs=Ys("touchcancel"),er=Ys("mousedown"),tr=Ys("mousemove"),or=Ys("mouseout"),nr=Ys("mouseup"),sr=Ys("mouseover"),rr=Ys("focusin"),ar=Ys("focusout"),ir=Ys("keydown"),lr=Ys("keyup"),cr=Ys("input"),dr=Ys("change"),ur=Ys("click"),mr=Ys("transitioncancel"),gr=Ys("transitionend"),pr=Ys("transitionstart"),hr=Ys("selectstart"),fr=e=>y("alloy."+e),br={tap:fr("tap")},vr=fr("focus"),xr=fr("blur.post"),yr=fr("paste.post"),wr=fr("receive"),Sr=fr("execute"),Cr=fr("focus.item"),kr=br.tap,Or=fr("longpress"),_r=fr("sandbox.close"),Tr=fr("typeahead.cancel"),Er=fr("system.init"),Ar=fr("system.touchmove"),Mr=fr("system.touchend"),Dr=fr("system.scroll"),Br=fr("system.resize"),Ir=fr("system.attached"),Fr=fr("system.detached"),Rr=fr("system.dismissRequested"),Nr=fr("system.repositionRequested"),zr=fr("focusmanager.shifted"),Lr=fr("slotcontainer.visibility"),Vr=fr("system.external.element.scroll"),Hr=fr("change.tab"),Pr=fr("dismiss.tab"),Ur=fr("highlight"),Wr=fr("dehighlight"),$r=(e,t)=>{Xr(e,e.element,t,{})},Gr=(e,t,o)=>{Xr(e,e.element,t,o)},jr=e=>{$r(e,Sr())},qr=(e,t,o)=>{Xr(e,t,o,{})},Xr=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},Yr=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},Kr=e=>Us(e),Jr=(e,t)=>({key:e,value:Xs({abort:t})}),Qr=e=>({key:e,value:Xs({run:(e,t)=>{t.event.prevent()}})}),Zr=(e,t)=>({key:e,value:Xs({run:t})}),ea=(e,t,o)=>({key:e,value:Xs({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),ta=e=>t=>({key:e,value:Xs({run:(e,o)=>{js(e,o)&&t(e,o)}})}),oa=(e,t,o)=>((e,t)=>Zr(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{Yr(t,t.element,e,n)}))})))(e,t.partUids[o]),na=(e,t)=>Zr(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>Gs(n.target,(t=>e.getSystem().getByDom(t).toOptional()),T).getOr(e)));t(e,s,o)})),sa=e=>Zr(e,((e,t)=>{t.cut()})),ra=e=>Zr(e,((e,t)=>{t.stop()})),aa=(e,t)=>ta(e)(t),ia=ta(Ir()),la=ta(Fr()),ca=ta(Er()),da=(li=Sr(),e=>Zr(li,e)),ua=e=>L(e,(e=>Ee(e,"/*")?e.substring(0,e.length-2):e)),ma=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:ua(r)}),e},ga=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),pa=(e,t,o)=>ca(((n,s)=>{o(n,e,t)})),ha=e=>({key:e,value:void 0}),fa=(e,t,o,n,s,r,a)=>{const i=e=>ve(e,o)?e[o]():A.none(),l=le(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:ua(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:y(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...le(r,((e,t)=>ma(e,t))),...l,revoke:C(ha,o),config:t=>{const n=is(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:Qt((()=>is(o+"-config",e,t))),initialConfig:t,state:a}}},schema:y(t),exhibit:(e,t)=>we(i(e),fe(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>ga({}))),name:y(o),handlers:e=>i(e).map((e=>fe(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},ba={init:()=>va({readState:y("No State required")})},va=e=>e,xa=e=>Us(e),ya=Un([ps("fields"),ps("name"),Ds("active",{}),Ds("apis",{}),Ds("state",ba),Ds("extra",{})]),wa=e=>{const t=is("Creating behaviour: "+e.name,ya,e);return((e,t,o,n,s,r)=>{const a=Un(e),i=Ms(t,[(l=e,ks("config",Un(l)))]);var l;return fa(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},Sa=Un([ps("branchKey"),ps("branches"),ps("name"),Ds("active",{}),Ds("apis",{}),Ds("state",ba),Ds("extra",{})]),Ca=e=>{const t=is("Creating behaviour: "+e.name,Sa,e);return((e,t,o,n,s,r)=>{const a=e,i=Ms(t,[ks("config",e)]);return fa(a,i,t,o,n,s,r)})(cs(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},ka=y(void 0),Oa=(e,t)=>{const o=kt(e,t);return void 0===o||""===o?[]:o.split(" ")},_a=e=>void 0!==e.dom.classList,Ta=e=>Oa(e,"class"),Ea=(e,t)=>((e,t,o)=>{const n=Oa(e,t).concat([o]);return St(e,t,n.join(" ")),!0})(e,"class",t),Aa=(e,t)=>((e,t,o)=>{const n=P(Oa(e,t),(e=>e!==o));return n.length>0?St(e,t,n.join(" ")):Tt(e,t),!1})(e,"class",t),Ma=(e,t)=>{_a(e)?e.dom.classList.add(t):Ea(e,t)},Da=e=>{0===(_a(e)?e.dom.classList:Ta(e)).length&&Tt(e,"class")},Ba=(e,t)=>{_a(e)?e.dom.classList.remove(t):Aa(e,t),Da(e)},Ia=(e,t)=>_a(e)&&e.dom.classList.contains(t),Fa=(e,t)=>{V(t,(t=>{Ma(e,t)}))},Ra=(e,t)=>{V(t,(t=>{Ba(e,t)}))},Na=e=>_a(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):Ta(e),za=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},La=(e,t)=>{Bt(e,(e=>({...e,position:A.some(e.position)}))(t))},Va=e=>(xe(Rt(e,"position"),"fixed")?A.none():it(e)).orThunk((()=>{const t=Re("span");return rt(e).bind((e=>{zo(e,t);const o=it(t);return Ho(t),o}))})),Ha=e=>Va(e).map(qt).getOrThunk((()=>Gt(0,0))),Pa=(e,t)=>{const o=e.element;Ma(o,t.transitionClass),Ba(o,t.fadeOutClass),Ma(o,t.fadeInClass),t.onShow(e)},Ua=(e,t)=>{const o=e.element;Ma(o,t.transitionClass),Ba(o,t.fadeInClass),Ma(o,t.fadeOutClass),t.onHide(e)},Wa=(e,t)=>e.y>=t.y,$a=(e,t)=>e.bottom<=t.bottom,Ga=(e,t,o)=>({location:"top",leftX:t,topY:o.bounds.y-e.y}),ja=(e,t,o)=>({location:"bottom",leftX:t,bottomY:e.bottom-o.bounds.bottom}),qa=e=>e.box.x-e.win.x,Xa=(e,t,o)=>o.getInitialPos().map((o=>{const n=((e,t)=>{const o=t.optScrollEnv.fold(y(e.bounds.y),(t=>t.scrollElmTop+(e.bounds.y-t.currentScrollTop)));return Gt(e.bounds.x,o)})(o,t);return{box:Yo(n.left,n.top,Kt(e),Ut(e)),location:o.location}})),Ya=(e,t,o,n,s)=>{const r=((e,t)=>{const o=t.optScrollEnv.fold(y(e.y),(t=>e.y+t.currentScrollTop-t.scrollElmTop));return Gt(e.x,o)})(t,o),a=Yo(r.left,r.top,t.width,t.height);n.setInitialPos({style:Nt(e),position:It(e,"position")||"static",bounds:a,location:s.location})},Ka=(e,t,o)=>o.getInitialPos().bind((n=>{var s;switch(o.clearInitialPos(),n.position){case"static":return A.some({morph:"static"});case"absolute":const o=Va(e).getOr(xt()),r=Ko(o),a=null!==(s=o.dom.scrollTop)&&void 0!==s?s:0;return A.some({morph:"absolute",positionCss:za("absolute",fe(n.style,"left").map((e=>t.x-r.x)),fe(n.style,"top").map((e=>t.y-r.y+a)),fe(n.style,"right").map((e=>r.right-t.right)),fe(n.style,"bottom").map((e=>r.bottom-t.bottom)))});default:return A.none()}})),Ja=e=>{switch(e.location){case"top":return A.some({morph:"fixed",positionCss:za("fixed",A.some(e.leftX),A.some(e.topY),A.none(),A.none())});case"bottom":return A.some({morph:"fixed",positionCss:za("fixed",A.some(e.leftX),A.none(),A.none(),A.some(e.bottomY))});default:return A.none()}},Qa=(e,t,o)=>{const n=e.element;return xe(Rt(n,"position"),"fixed")?((e,t,o)=>((e,t,o)=>Xa(e,t,o).filter((({box:e})=>((e,t,o)=>X(e,(e=>{switch(e){case"bottom":return $a(t,o.bounds);case"top":return Wa(t,o.bounds)}})))(o.getModes(),e,t))).bind((({box:t})=>Ka(e,t,o))))(e,t,o).orThunk((()=>t.optScrollEnv.bind((n=>Xa(e,t,o))).bind((({box:e,location:o})=>{const n=Zo(),s=qa({win:n,box:e}),r="top"===o?Ga(n,s,t):ja(n,s,t);return Ja(r)})))))(n,t,o):((e,t,o)=>{const n=Ko(e),s=Zo(),r=((e,t,o)=>{const n=t.win,s=t.box,r=qa(t);return se(e,(e=>{switch(e){case"bottom":return $a(s,o.bounds)?A.none():A.some(ja(n,r,o));case"top":return Wa(s,o.bounds)?A.none():A.some(Ga(n,r,o));default:return A.none()}})).getOr({location:"no-dock"})})(o.getModes(),{win:s,box:n},t);return"top"===r.location||"bottom"===r.location?(Ya(e,n,t,o,r),Ja(r)):A.none()})(n,t,o)},Za=(e,t,o)=>{o.setDocked(!1),V(["left","right","top","bottom","position"],(t=>Lt(e.element,t))),t.onUndocked(e)},ei=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),La(e.element,n),(s?t.onDocked:t.onUndocked)(e)},ti=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n.bounds);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(Fa(e.element,[t.fadeOutClass]),t.onHide(e)):(a?Pa:Ua)(e,t))}))}))},oi=(e,t,o,n,s)=>{ti(e,t,o,n,!0),ei(e,t,o,s.positionCss)},ni=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);ti(e,t,o,n),Qa(e,n,o).each((s=>{((e,t,o,n,s)=>{switch(s.morph){case"static":return Za(e,t,o);case"absolute":return ei(e,t,o,s.positionCss);case"fixed":oi(e,t,o,n,s)}})(e,t,o,n,s)}))})(e,t,o)},si=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1);const s=t.lazyViewport(e);((e,t,o)=>{const n=e.element;return Xa(n,t,o).bind((({box:e})=>Ka(n,e,o)))})(e,s,o).each((n=>{switch(n.morph){case"static":Za(e,t,o);break;case"absolute":ei(e,t,o,n.positionCss)}})),o.setVisible(!0),t.contextual.each((t=>{Ra(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),ni(e,t,o)})(e,t,o)},ri=e=>(t,o,n)=>{const s=o.lazyViewport(t);((e,t,o,n)=>{const s=Ko(e),r=Zo(),a=n(r,qa({win:r,box:s}),t);return"bottom"===a.location||"top"===a.location?(((e,t,o,n,s)=>{n.getInitialPos().fold((()=>Ya(e,t,o,n,s)),(()=>b))})(e,s,t,o,a),Ja(a)):A.none()})(t.element,s,n,e).each((e=>{oi(t,o,n,s,e)}))},ai=ri(Ga),ii=ri(ja);var li,ci=Object.freeze({__proto__:null,refresh:ni,reset:si,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n),forceDockToTop:ai,forceDockToBottom:ii}),di=Object.freeze({__proto__:null,events:(e,t)=>Kr([aa(gr(),((o,n)=>{e.contextual.each((e=>{Ia(o.element,e.transitionClass)&&(Ra(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),Zr(Dr(),((o,n)=>{ni(o,e,t)})),Zr(Vr(),((o,n)=>{ni(o,e,t)})),Zr(Br(),((o,n)=>{si(o,e,t)}))])});const ui=e=>e.dom.innerHTML,mi=(e,t)=>{const o=tt(e).dom,n=ze(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,lt(ze(o))})(t,o);Lo(n,s),Vo(e),zo(e,n)},gi=(e,t)=>ze(e.dom.cloneNode(t)),pi=e=>(e=>{if(gt(e))return"#shadow-root";{const t=(e=>gi(e,!1))(e);return(e=>{const t=Re("div"),o=ze(e.dom.cloneNode(!0));return zo(t,o),ui(t)})(t)}})(e);var hi;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(hi||(hi={}));const fi=en({}),bi=["alloy/data/Fields","alloy/debugging/Debugging"],vi=(e,t,o)=>((e,t,o)=>{switch(fe(fi.get(),e).orThunk((()=>{const t=re(fi.get());return se(t,(t=>e.indexOf(t)>-1?A.some(fi.get()[t]):A.none()))})).getOr(hi.NORMAL)){case hi.NORMAL:return o(xi());case hi.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();F(["mousemove","mouseover","mouseout",Er()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:L(o,(e=>F(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+pi(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case hi.STOP:return!0}})(e,t,o),xi=y({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),yi=y([ps("menu"),ps("selectedMenu")]),wi=y([ps("item"),ps("selectedItem")]);y(Wn(wi().concat(yi())));const Si=y(Wn(wi())),Ci=ys("initSize",[ps("numColumns"),ps("numRows")]),ki=()=>ys("markers",[ps("backgroundMenu")].concat(yi()).concat(wi())),Oi=e=>ys("markers",L(e,ps)),_i=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");$(t,(e=>e.indexOf("alloy")>0&&!R(bi,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),us(t,t,o,ns((e=>bn.value(((...t)=>e.apply(void 0,t))))))),Ti=e=>_i(0,e,Dn(b)),Ei=e=>_i(0,e,Dn(A.none)),Ai=e=>_i(0,e,{tag:"required",process:{}}),Mi=e=>_i(0,e,{tag:"required",process:{}}),Di=(e,t)=>ms(e,y(t)),Bi=e=>ms(e,w),Ii=y(Ci);var Fi=[Ms("contextual",[bs("fadeInClass"),bs("fadeOutClass"),bs("transitionClass"),xs("lazyContext"),Ti("onShow"),Ti("onShown"),Ti("onHide"),Ti("onHidden")]),zs("lazyViewport",(()=>({bounds:Zo(),optScrollEnv:A.none()}))),Ls("modes",["top","bottom"],Jn),Ti("onDocked"),Ti("onUndocked")];const Ri=wa({fields:Fi,name:"docking",active:di,apis:ci,state:Object.freeze({__proto__:null,init:e=>{const t=en(!1),o=en(!0),n=nn(),s=en(e.modes);return va({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),Ni=Kr([(e=>({key:e,value:Xs({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>Ze(t,e.element)&&!Ze(t,o))(e,n,s)||(console.warn(vr()+" did not get interpreted by the desired target. \nOriginator: "+pi(n)+"\nTarget: "+pi(s)+"\nCheck the "+vr()+" event handlers"),!1)}})}))(vr())]);var zi=Object.freeze({__proto__:null,events:Ni});const Li=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},Vi=(e,t,o)=>Math.min(Math.max(e,t),o);let Hi=0;const Pi=e=>{const t=(new Date).getTime(),o=Math.floor(window.crypto.getRandomValues(new Uint32Array(1))[0]/4294967295*1e9);return Hi++,e+"_"+o+Hi+String(t)},Ui=y("alloy-id-"),Wi=y("data-alloy-id"),$i=Ui(),Gi=Wi(),ji=(e,t)=>{Object.defineProperty(e.dom,Gi,{value:t,writable:!0})},qi=e=>{const t=Ge(e)?e.dom[Gi]:null;return A.from(t)},Xi=e=>Pi(e),Yi=w,Ki=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+pi(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:y("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:T}},Ji=Ki(),Qi=Pi("alloy-premade"),Zi=e=>(Object.defineProperty(e.element.dom,Qi,{value:e.uid,writable:!0}),Ps(Qi,e)),el=e=>fe(e,Qi),tl=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:ua(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),ol=(e,t)=>{const o={};return ie(e,((e,n)=>{ie(e,((e,s)=>{const r=fe(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},nl=e=>e.cHandler,sl=(e,t)=>({name:e,handler:t}),rl=(e,t)=>{const o={};return V(e,(e=>{o[e.name()]=e.handlers(t)})),o},al=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const s=Z(o,((o,s)=>{const r=o[t],a=s[t],i=n.indexOf(r),l=n.indexOf(a);if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===l)throw new Error("The ordering for "+e+" does not have an entry for "+a+".\nOrder specified: "+JSON.stringify(n,null,2));return i<l?-1:l<i?1:0}));return bn.value(s)}catch(e){return bn.error([e])}})("Event: "+o,"name",e,n).map((e=>(e=>{const t=(e=>(...t)=>W(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=(e=>(...t)=>W(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{V(e,(e=>{e.run.apply(void 0,t)}))}}})(L(e,(e=>e.handler))))):((e,t)=>bn.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(L(t,(e=>e.name)),null,2)]))(o,e)},il=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return V(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,bn.error(j(n))):((e,t)=>0===e.length?bn.value(t):bn.value(En(t,An.apply(void 0,e))))(o.values,t);var n})(ge(e,((e,o)=>(1===e.length?bn.value(e[0].handler):al(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:E,abort:T,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?P(t[o],(t=>R(e,(e=>e.name===t)))).join(" > "):e[0].name;return Ps(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),ll="alloy.base.behaviour",cl=Wn([us("dom","dom",{tag:"required",process:{}},Wn([ps("tag"),Ds("styles",{}),Ds("classes",[]),Ds("attributes",{}),Cs("value"),Cs("innerHtml")])),ps("components"),ps("uid"),Ds("events",{}),Ds("apis",{}),us("eventOrder","eventOrder",(lc={[Sr()]:["disabling",ll,"toggling","typeaheadevents"],[vr()]:[ll,"focusing","keying"],[Er()]:[ll,"disabling","toggling","representing","tooltipping"],[cr()]:[ll,"representing","streaming","invalidating"],[Fr()]:[ll,"representing","item-events","toolbar-button-events","tooltipping"],[er()]:["focusing",ll,"item-type-events"],[Ks()]:["focusing",ll,"item-type-events"],[sr()]:["item-type-events","tooltipping"],[wr()]:["receiving","reflecting","tooltipping"]},Bn(y(lc))),Xn()),Cs("domModification")]),dl=e=>e.events,ul=e=>e.dom.value,ml=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},gl=(e,t,o)=>{o.fold((()=>zo(e,t)),(e=>{Ze(e,t)||(Fo(e,t),Ho(e))}))},pl=(e,t,o)=>{const n=L(t,o),s=lt(e);return V(s.slice(n.length),Ho),n},hl=(e,t,o,n)=>{const s=ct(e,t),r=n(o,s),a=((e,t,o)=>ct(e,t).map((e=>{if(o.exists((t=>!Ze(t,e)))){const t=o.map(Ue).getOr("span"),n=Re(t);return Fo(e,n),n}return e})))(e,t,s);return gl(e,r.element,a),r},fl=(e,t)=>{const o=re(e),n=re(t),s=K(n,o),r=(e=>{const o={},n={};return ue(e,((e,o)=>!be(t,o)||e!==t[o]),de(o),de(n)),{t:o,f:n}})(e).t;return{toRemove:s,toSet:r}},bl=(e,t)=>{const o=t.filter((t=>Ue(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>be(e.dom,Qi))(t))).bind((t=>((e,t)=>{try{const o=((e,t)=>{const{class:o,style:n,...s}=(e=>W(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=fl(e.attributes,s),i=Nt(t),{toSet:l,toRemove:c}=fl(e.styles,i),d=Na(t),u=K(d,e.classes),m=K(e.classes,d);return V(a,(e=>Tt(t,e))),Ct(t,r),Fa(t,m),Ra(t,u),V(c,(e=>Lt(t,e))),Dt(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{pl(e,t,((t,o)=>{const n=ct(e,o);return gl(e,t,n),t}))})(t,o)}),(e=>{mi(t,e)})),(()=>{const o=t,n=e.value.getOrUndefined();n!==ul(o)&&ml(o,null!=n?n:"")})(),t})(e,t);return A.some(o)}catch(e){return A.none()}})(e,t))).getOrThunk((()=>(e=>{const t=Re(e.tag);Ct(t,e.attributes),Fa(t,e.classes),Dt(t,e.styles),e.innerHtml.each((e=>mi(t,e)));const o=e.domChildren;return Lo(t,o),e.value.each((e=>{ml(t,e)})),t})(e)));return ji(o,e.uid),o},vl=e=>{const t=(e=>{const t=fe(e,"behaviours").getOr({});return q(re(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=L(t,(e=>Ms(e.name(),[ps("config"),Ds("state",ba)]))),n=rs("component.behaviours",Wn(o),e.behaviours).fold((t=>{throw new Error(ls(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),w);return{list:t,data:le(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return y(t)}))}})(e,t))(e,t)},xl=(e,t)=>{const o=()=>m,n=en(Ji),s=as((e=>rs("custom.definition",cl,e))(e)),r=vl(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:L(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>ga({})),ga))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};V(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=ol(s,((e,t)=>({name:e,modification:t}))),a=e=>U(e,((e,t)=>({...t.modification,...e})),{}),i=U(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return ga({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=bl(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":dl(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...rl(t,e)};return ol(n,sl)})(e,o,n);return il(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=en(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(Ki(o))},element:c,syncComponents:()=>{const e=lt(c),t=q(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},yl=e=>{const t=Ne(e);return wl({element:t})},wl=e=>{const t=is("external.component",Un([ps("element"),Cs("uid")]),e),o=en(Ki()),n=t.uid.getOrThunk((()=>Xi("external")));ji(t.element,n);const s={uid:n,getSystem:o.get,config:A.none,hasConfigured:T,connect:e=>{o.set(e)},disconnect:()=>{o.set(Ki((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:y("No state"),syncComponents:b,components:y([]),events:{}};return Zi(s)},Sl=Xi,Cl=(e,t)=>el(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=Yi(e),s=((e,t)=>{const o=fe(e,"components").getOr([]);return t.fold((()=>L(o,kl)),(e=>L(o,((t,o)=>Cl(t,ct(e,o))))))})(n,t),r={...n,events:{...zi,...o},components:s};return bn.value(xl(r,t))})((e=>be(e,"uid"))(e)?e:{uid:Sl(""),...e},t).getOrDie())),kl=e=>Cl(e,A.none()),Ol=Zi,_l=(e,t,o)=>an(e,t,o).isSome(),Tl="aria-controls",El=()=>{const e=Pi(Tl);return{id:e,link:t=>{St(t,Tl,e)},unlink:e=>{Tt(e,Tl)}}},Al=(e,t)=>_l(t,(t=>Ze(t,e.element)),T)||((e,t)=>(e=>an(e,(e=>{if(!Ge(e))return!1;const t=kt(e,"id");return void 0!==t&&t.indexOf(Tl)>-1})).bind((e=>{const t=kt(e,"id"),o=pt(e);return mn(o,`[${Tl}="${t}"]`)})))(t).exists((t=>Al(e,t))))(e,t),Ml=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),Dl=Hs([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Bl=Dl.southeast,Il=Dl.southwest,Fl=Dl.northeast,Rl=Dl.northwest,Nl=Dl.south,zl=Dl.north,Ll=Dl.east,Vl=Dl.west,Hl=(e,t)=>J(["left","right","top","bottom"],(o=>fe(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),Pl="layout",Ul=e=>e.x,Wl=(e,t)=>e.x+e.width/2-t.width/2,$l=(e,t)=>e.x+e.width-t.width,Gl=(e,t)=>e.y-t.height,jl=e=>e.y+e.height,ql=(e,t)=>e.y+e.height/2-t.height/2,Xl=(e,t,o)=>Ml(Ul(e),jl(e),o.southeast(),Bl(),"southeast",Hl(e,{left:1,top:3}),Pl),Yl=(e,t,o)=>Ml($l(e,t),jl(e),o.southwest(),Il(),"southwest",Hl(e,{right:0,top:3}),Pl),Kl=(e,t,o)=>Ml(Ul(e),Gl(e,t),o.northeast(),Fl(),"northeast",Hl(e,{left:1,bottom:2}),Pl),Jl=(e,t,o)=>Ml($l(e,t),Gl(e,t),o.northwest(),Rl(),"northwest",Hl(e,{right:0,bottom:2}),Pl),Ql=(e,t,o)=>Ml(Wl(e,t),Gl(e,t),o.north(),zl(),"north",Hl(e,{bottom:2}),Pl),Zl=(e,t,o)=>Ml(Wl(e,t),jl(e),o.south(),Nl(),"south",Hl(e,{top:3}),Pl),ec=(e,t,o)=>Ml((e=>e.x+e.width)(e),ql(e,t),o.east(),Ll(),"east",Hl(e,{left:0}),Pl),tc=(e,t,o)=>Ml(((e,t)=>e.x-t.width)(e,t),ql(e,t),o.west(),Vl(),"west",Hl(e,{right:1}),Pl),oc=()=>[Xl,Yl,Kl,Jl,Zl,Ql,ec,tc],nc=()=>[Yl,Xl,Jl,Kl,Zl,Ql,ec,tc],sc=()=>[Kl,Jl,Xl,Yl,Ql,Zl],rc=()=>[Jl,Kl,Yl,Xl,Ql,Zl],ac=()=>[Xl,Yl,Kl,Jl,Zl,Ql],ic=()=>[Yl,Xl,Jl,Kl,Zl,Ql];var lc,cc=Object.freeze({__proto__:null,events:e=>Kr([Zr(wr(),((t,o)=>{const n=e.channels,s=re(n),r=o,a=((e,t)=>t.universal?e:P(e,(e=>F(t.channels,e))))(s,r);V(a,(e=>{const o=n[e],s=o.schema,a=is("channel["+e+"] data\nReceiver: "+pi(t.element),s,r.data);o.onReceive(t,a)}))}))])}),dc=[hs("channels",ss(bn.value,Un([Ai("onReceive"),Ds("schema",Xn())])))];const uc=wa({fields:dc,name:"receiving",active:cc});var mc=Object.freeze({__proto__:null,exhibit:(e,t)=>ga({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const gc=(e,t=!1)=>e.dom.focus({preventScroll:t}),pc=e=>e.dom.blur(),hc=e=>{const t=pt(e).dom;return e.dom===t.activeElement},fc=(e=Go())=>A.from(e.dom.activeElement).map(ze),bc=e=>fc(pt(e)).filter((t=>e.dom.contains(t.dom))),vc=(e,t)=>{const o=pt(t),n=fc(o).bind((e=>{const o=t=>Ze(e,t);return o(t)?A.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=ze(e.childNodes[n]);if(t(s))return A.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return A.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{fc(o).filter((t=>Ze(t,e))).fold((()=>{gc(e)}),b)})),s},xc=Hs([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),yc=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=A.some(i),m=A.some(l),g=A.some(c),p=A.some(d),h=A.none();return t.direction.fold((()=>za(e,u,m,h,h)),(()=>za(e,h,m,g,h)),(()=>za(e,u,h,h,p)),(()=>za(e,h,h,g,p)),(()=>za(e,u,m,h,h)),(()=>za(e,u,h,h,p)),(()=>za(e,u,m,h,h)),(()=>za(e,h,m,g,h)))},wc=(e,t)=>e.fold((()=>{const e=t.rect;return za("absolute",A.some(e.x),A.some(e.y),A.none(),A.none())}),((e,o,n,s)=>yc("absolute",t,e,o,n,s)),((e,o,n,s)=>yc("fixed",t,e,o,n,s))),Sc=(e,t)=>{const o=C(Xo,t),n=e.fold(o,o,(()=>{const e=Po();return Xo(t).translate(-e.left,-e.top)})),s=Jt(t),r=Wt(t);return Yo(n.left,n.top,s,r)},Cc=(e,t)=>t.fold((()=>e.fold(Zo,Zo,Yo)),(t=>e.fold(y(t),y(t),(()=>{const o=kc(e,t.x,t.y);return Yo(o.left,o.top,t.width,t.height)})))),kc=(e,t,o)=>{const n=Gt(t,o);return e.fold(y(n),y(n),(()=>{const e=Po();return n.translate(-e.left,-e.top)}))};xc.none;const Oc=xc.relative,_c=xc.fixed,Tc="data-alloy-placement",Ec=e=>Ot(e,Tc),Ac=Hs([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),Mc=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?Vi(i,e.y,e.bottom):Vi(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return Yo(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=Yo(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=Vi(a,o,d),g=Vi(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return Yo(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=y(t.bottom-o.y),s=y(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=y(t.right-o.x),i=y(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),x={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?Ac.fit(x):Ac.nofit(x,m,g,f)},Dc=E,Bc=(e,t,o)=>((e,t,o,n)=>Bo(e,t,o,n,!1))(e,t,Dc,o),Ic=(e,t,o)=>((e,t,o,n)=>Bo(e,t,o,n,!0))(e,t,Dc,o),Fc=Do,Rc=["top","bottom","right","left"],Nc="data-alloy-transition-timer",zc=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>((e,t)=>X(t,(t=>Ia(e,t))))(e,t.classes))(e,n)){Mt(e,"position",o.position);const a=Sc(t,e),l=wc(t,{...s,rect:a}),c=J(Rc,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return pe(t,((t,n)=>!((e,t,o=S)=>we(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(Bt(e,c),i&&((e,t)=>{Fa(e,t.classes),Ot(e,Nc).each((t=>{clearTimeout(parseInt(t,10)),Tt(e,Nc)})),((e,t)=>{const o=on(),n=on();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return Ze(t.target,e)&&De(n)&&F(Rc,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===gr())&&(clearTimeout(s),Tt(e,Nc),Ra(e,t.classes))}},l=Bc(e,pr(),(t=>{a(t)&&(l.unbind(),o.set(Bc(e,gr(),i)),n.set(Bc(e,mr(),i)))})),c=(e=>{const t=t=>{const o=It(e,t).split(/\s*,\s*/);return P(o,Me)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return Ee(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return W(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);requestAnimationFrame((()=>{s=setTimeout(i,c+17),St(e,Nc,s)}))})(e,t)})(e,n),Vt(e))}else Ra(e,n.classes)},Lc=(e,t)=>{((e,t)=>{const o=Pt.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);Mt(e,"max-height",o+"px")})(e,Math.floor(t))},Vc=y(((e,t)=>{Lc(e,t),Dt(e,{"overflow-x":"hidden","overflow-y":"auto"})})),Hc=y(((e,t)=>{Lc(e,t)})),Pc=(e,t,o)=>void 0===e[t]?o:e[t],Uc=(e,t,o,n)=>{const s=((e,t,o,n)=>{Lt(t,"max-height"),Lt(t,"max-width");const s={width:Jt(r=t),height:Wt(r)};var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=Mc(m,a,i,r);return g.fold(y(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:Ac.nofit(l,c,d,u)))};return W(t,((e,t)=>{const o=C(l,t);return e.fold(y(e),o)}),Ac.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:Bl(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(w,w)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=wc(o.origin,t);o.transition.each((s=>{zc(e,o.origin,n,s,t,o.lastPlacement)})),La(e,n)})(t,s,n),((e,t)=>{((e,t)=>{St(e,Tc,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;Ra(e,o.off),Fa(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},Wc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],$c=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>fe(o,e).getOr([]),i=(e,t,o)=>{const n=K(Wc,o);return{offset:Gt(e,t),classesOn:q(o,a),classesOff:q(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},Gc=()=>$c(0,0,{}),jc=w,qc=(e,t)=>o=>"rtl"===Xc(o)?t:e,Xc=e=>"rtl"===It(e,"direction")?"rtl":"ltr";var Yc;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(Yc||(Yc={}));const Kc="data-alloy-vertical-dir",Jc=e=>_l(e,(e=>Ge(e)&&kt(e,"data-alloy-vertical-dir")===Yc.BottomToTop)),Qc=()=>Ms("layouts",[ps("onLtr"),ps("onRtl"),Cs("onBottomLtr"),Cs("onBottomRtl")]),Zc=(e,t,o,n,s,r,a)=>{const i=a.map(Jc).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return qc(d,u)(e)};var ed=[ps("hotspot"),Cs("bubble"),Ds("overrides",{}),Qc(),Di("placement",((e,t,o)=>{const n=t.hotspot,s=Sc(o,n.element),r=Zc(e.element,t,ac(),ic(),sc(),rc(),A.some(t.hotspot.element));return A.some(jc({anchorBox:s,bubble:t.bubble.getOr(Gc()),overrides:t.overrides,layouts:r}))}))],td=[ps("x"),ps("y"),Ds("height",0),Ds("width",0),Ds("bubble",Gc()),Ds("overrides",{}),Qc(),Di("placement",((e,t,o)=>{const n=kc(o,t.x,t.y),s=Yo(n.left,n.top,t.width,t.height),r=Zc(e.element,t,oc(),nc(),oc(),nc(),A.none());return A.some(jc({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r}))}))];const od=Hs([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),nd=e=>e.fold(w,((e,t,o)=>e.translate(-t,-o))),sd=e=>e.fold(w,w),rd=e=>W(e,((e,t)=>e.translate(t.left,t.top)),Gt(0,0)),ad=e=>{const t=L(e,sd);return rd(t)},id=od.screen,ld=od.absolute,cd=(e,t,o)=>{const n=tt(e.element),s=Po(n),r=((e,t,o)=>{const n=st(o.root).dom;return A.from(n.frameElement).map(ze).filter((t=>{const o=tt(t),n=tt(e.element);return Ze(o,n)})).map(qt)})(e,0,o).getOr(s);return ld(r,s.left,s.top)},dd=(e,t,o,n)=>{const s=id(Gt(e,t));return A.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},ud=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>ad(r),l=()=>ad(r),c=()=>(e=>{const t=L(e,nd);return rd(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?sc():ac(),m=o.showAbove?rc():ic(),g=Zc(s,o,u,m,u,m,A.none());var p,h,f,b;return jc({anchorBox:d,bubble:o.bubble.getOr(Gc()),overrides:o.overrides,layouts:g})}));var md=[ps("node"),ps("root"),Cs("bubble"),Qc(),Ds("overrides",{}),Ds("showAbove",!1),Di("placement",((e,t,o)=>{const n=cd(e,0,t);return t.node.filter(vt).bind((s=>{const r=s.dom.getBoundingClientRect(),a=dd(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return ud(a,n,t,o,i)}))}))];const gd=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),pd=Hs([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),hd=(pd.before,pd.on,pd.after,e=>e.fold(w,w,w)),fd=Hs([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),bd={domRange:fd.domRange,relative:fd.relative,exact:fd.exact,exactFromRange:e=>fd.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>ze(e.startContainer),relative:(e,t)=>hd(e),exact:(e,t,o,n)=>e}))(e);return st(t)},range:gd},vd=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},xd=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},yd=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),wd=Hs([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Sd=(e,t,o)=>t(ze(o.startContainer),o.startOffset,ze(o.endContainer),o.endOffset),Cd=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:y(e),rtl:A.none}),relative:(t,o)=>({ltr:Qt((()=>vd(e,t,o))),rtl:Qt((()=>A.some(vd(e,o,t))))}),exact:(t,o,n,s)=>({ltr:Qt((()=>xd(e,t,o,n,s))),rtl:Qt((()=>A.some(xd(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>wd.rtl(ze(e.endContainer),e.endOffset,ze(e.startContainer),e.startOffset))).getOrThunk((()=>Sd(0,wd.ltr,o))):Sd(0,wd.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});wd.ltr,wd.rtl;const kd=(e,t,o)=>P(((e,t)=>{const o=p(t)?t:T;let n=e.dom;const s=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=ze(e);if(s.push(t),!0===o(t))break;n=e}return s})(e,o),t),Od=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Je(o)?[]:L(o.querySelectorAll(e),ze)})(t,e),_d=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return A.some(gd(ze(t.startContainer),t.startOffset,ze(o.endContainer),o.endOffset))}return A.none()},Td=e=>{if(null===e.anchorNode||null===e.focusNode)return _d(e);{const t=ze(e.anchorNode),o=ze(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=tt(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=Ze(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?A.some(gd(t,e.anchorOffset,o,e.focusOffset)):_d(e)}},Ed=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?A.some(o).map(yd):A.none()})(Cd(e,t)),Ad=(e=>{const t=t=>e(t)?A.from(t.dom.nodeValue):A.none();return{get:o=>{if(!e(o))throw new Error("Can only get text value of a text node");return t(o).getOr("")},getOption:t,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(je),Md=(e,t)=>({element:e,offset:t}),Dd=(e,t)=>je(e)?Md(e,t):((e,t)=>{const o=lt(e);if(0===o.length)return Md(e,t);if(t<o.length)return Md(o[t],0);{const e=o[o.length-1],t=je(e)?(e=>Ad.get(e))(e).length:lt(e).length;return Md(e,t)}})(e,t),Bd=e=>void 0!==e.foffset,Id=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>A.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(Td))(e)))().map((e=>{if(Bd(e)){const t=Dd(e.start,e.soffset),o=Dd(e.finish,e.foffset);return bd.range(t.element,t.offset,o.element,o.offset)}return e}));var Fd=[Cs("getSelection"),ps("root"),Cs("bubble"),Qc(),Ds("overrides",{}),Ds("showAbove",!1),Di("placement",((e,t,o)=>{const n=st(t.root).dom,s=cd(e,0,t),r=Id(n,t).bind((e=>{if(Bd(e)){const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?A.some(t).map(yd):A.none()})(Cd(e,t)))(n,bd.exactFromRange(e)).orThunk((()=>{const t=Ne("\ufeff");Fo(e.start,t);const o=Ed(n,bd.exact(t,0,t,1));return Ho(t),o}));return t.bind((e=>dd(e.left,e.top,e.width,e.height)))}{const t=le(e,(e=>e.dom.getBoundingClientRect())),o={left:Math.min(t.firstCell.left,t.lastCell.left),right:Math.max(t.firstCell.right,t.lastCell.right),top:Math.min(t.firstCell.top,t.lastCell.top),bottom:Math.max(t.firstCell.bottom,t.lastCell.bottom)};return dd(o.left,o.top,o.right-o.left,o.bottom-o.top)}})),a=Id(n,t).bind((e=>Bd(e)?Ge(e.start)?A.some(e.start):at(e.start):A.some(e.firstCell))).getOr(e.element);return ud(r,s,t,o,a)}))];const Rd="link-layout",Nd=e=>e.x+e.width,zd=(e,t)=>e.x-t.width,Ld=(e,t)=>e.y-t.height+e.height,Vd=e=>e.y,Hd=(e,t,o)=>Ml(Nd(e),Vd(e),o.southeast(),Bl(),"southeast",Hl(e,{left:0,top:2}),Rd),Pd=(e,t,o)=>Ml(zd(e,t),Vd(e),o.southwest(),Il(),"southwest",Hl(e,{right:1,top:2}),Rd),Ud=(e,t,o)=>Ml(Nd(e),Ld(e,t),o.northeast(),Fl(),"northeast",Hl(e,{left:0,bottom:3}),Rd),Wd=(e,t,o)=>Ml(zd(e,t),Ld(e,t),o.northwest(),Rl(),"northwest",Hl(e,{right:1,bottom:3}),Rd),$d=()=>[Hd,Pd,Ud,Wd],Gd=()=>[Pd,Hd,Wd,Ud];var jd=[ps("item"),Qc(),Ds("overrides",{}),Di("placement",((e,t,o)=>{const n=Sc(o,t.item.element),s=Zc(e.element,t,$d(),Gd(),$d(),Gd(),A.none());return A.some(jc({anchorBox:n,bubble:Gc(),overrides:t.overrides,layouts:s}))}))],qd=cs("type",{selection:Fd,node:md,hotspot:ed,submenu:jd,makeshift:td});const Xd=[Ss("classes",Jn),Rs("mode","all",["all","layout","placement"])],Yd=[Ds("useFixed",T),Cs("getBounds")],Kd=[hs("anchor",qd),Ms("transition",Xd)],Jd=(e,t,o,n,s,r)=>{const a=is("placement.info",Wn(Kd),s),i=a.anchor,l=n.element,c=o.get(n.uid);vc((()=>{Mt(l,"position","fixed");const s=Rt(l,"visibility");Mt(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return _c(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=qt(e.element),o=e.element.dom.getBoundingClientRect();return Oc(t.left,t.top,o.width,o.height)})(e);i.placement(e,i,d).each((e=>{const s=r.orThunk((()=>t.getBounds.map(_))),i=((e,t,o,n,s,r)=>((e,t,o,n,s,r,a,i)=>{const l=Pc(a,"maxHeightFunction",Vc()),c=Pc(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:Cc(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return Uc(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(t.anchorBox,e),n.element,t.bubble,t.layouts,s,o,t.overrides,r))(d,e,s,n,c,a.transition);o.set(n.uid,i)})),s.fold((()=>{Lt(l,"visibility")}),(e=>{Mt(l,"visibility",e)})),Rt(l,"left").isNone()&&Rt(l,"top").isNone()&&Rt(l,"right").isNone()&&Rt(l,"bottom").isNone()&&xe(Rt(l,"position"),"fixed")&&Lt(l,"position")}),l)};var Qd=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{const r=A.none();Jd(e,t,o,n,s,r)},positionWithinBounds:Jd,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;V(["position","left","right","top","bottom"],(e=>Lt(s,e))),(e=>{Tt(e,Tc)})(s),o.clear(n.uid)}});const Zd=wa({fields:Yd,name:"positioning",active:mc,apis:Qd,state:Object.freeze({__proto__:null,init:()=>{let e={};return va({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>fe(e,t)})}})}),eu=e=>e.getSystem().isConnected(),tu=e=>{$r(e,Fr());const t=e.components();V(t,tu)},ou=e=>{const t=e.components();V(t,ou),$r(e,Ir())},nu=(e,t)=>{e.getSystem().addToWorld(t),vt(e.element)&&ou(t)},su=e=>{tu(e),e.getSystem().removeFromWorld(e)},ru=(e,t)=>{zo(e.element,t.element)},au=(e,t)=>{iu(e,t,zo)},iu=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),vt(e.element)&&ou(t),e.syncComponents()},lu=e=>{tu(e),Ho(e.element),e.getSystem().removeFromWorld(e)},cu=e=>{const t=rt(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));lu(e),t.each((e=>{e.syncComponents()}))},du=e=>{const t=e.components();V(t,lu),Vo(e.element),e.syncComponents()},uu=(e,t)=>{gu(e,t,zo)},mu=(e,t)=>{gu(e,t,Ro)},gu=(e,t,o)=>{o(e,t.element);const n=lt(t.element);V(n,(e=>{t.getByDom(e).each(ou)}))},pu=e=>{const t=lt(e.element);V(t,(t=>{e.getByDom(t).each(tu)})),Ho(e.element)},hu=(e,t,o,n)=>{o.get().each((t=>{du(e)}));const s=t.getAttachPoint(e);au(s,e);const r=e.getSystem().build(n);return au(e,r),o.set(r),r},fu=(e,t,o,n)=>{const s=hu(e,t,o,n);return t.onOpen(e,s),s},bu=(e,t,o)=>{o.get().each((n=>{du(e),cu(e),t.onClose(e,n),o.clear()}))},vu=(e,t,o)=>o.isOpen(),xu=(e,t,o)=>{const n=t.getAttachPoint(e);Mt(e.element,"position",Zd.getMode(n)),((e,t,o)=>{Rt(e.element,t).fold((()=>{Tt(e.element,o)}),(t=>{St(e.element,o,t)})),Mt(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},yu=(e,t,o)=>{(e=>R(["top","left","right","bottom"],(t=>Rt(e,t).isSome())))(e.element)||Lt(e.element,"position"),((e,t,o)=>{Ot(e.element,o).fold((()=>Lt(e.element,t)),(o=>Mt(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var wu=Object.freeze({__proto__:null,cloak:xu,decloak:yu,open:fu,openWhileCloaked:(e,t,o,n,s)=>{xu(e,t),fu(e,t,o,n),s(),yu(e,t)},close:bu,isOpen:vu,isPartOf:(e,t,o,n)=>vu(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>hu(e,t,o,n)))}),Su=Object.freeze({__proto__:null,events:(e,t)=>Kr([Zr(_r(),((o,n)=>{bu(o,e,t)}))])}),Cu=[Ti("onOpen"),Ti("onClose"),ps("isPartOf"),ps("getAttachPoint"),Ds("cloakVisibilityAttr","data-precloak-visibility")],ku=Object.freeze({__proto__:null,init:()=>{const e=nn(),t=y("not-implemented");return va({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const Ou=wa({fields:Cu,name:"sandboxing",active:Su,apis:wu,state:ku}),_u=y("dismiss.popups"),Tu=y("reposition.popups"),Eu=y("mouse.released"),Au=Un([Ds("isExtraPart",T),Ms("fireEventInstead",[Ds("event",Rr())])]),Mu=e=>{const t=is("Dismissal",Au,e);return{[_u()]:{schema:Un([ps("target")]),onReceive:(e,o)=>{Ou.isOpen(e)&&(Ou.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>Ou.close(e)),(t=>$r(e,t.event))))}}}},Du=Un([Ms("fireEventInstead",[Ds("event",Nr())]),xs("doReposition")]),Bu=e=>{const t=is("Reposition",Du,e);return{[Tu()]:{onReceive:e=>{Ou.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>$r(e,t.event)))}}}},Iu=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},Fu=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var Ru=Object.freeze({__proto__:null,onLoad:Iu,onUnload:Fu,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),Nu=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[ia(((o,n)=>{Iu(o,e,t)})),la(((o,n)=>{Fu(o,e,t)}))]:[pa(e,t,Iu)];return Kr(o)}});const zu=()=>{const e=en(null);return va({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},Lu=()=>{const e=en({}),t=en({});return va({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>fe(e.get(),o).orThunk((()=>fe(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};V(o,(e=>{r[e.value]=e,fe(e,"meta").each((t=>{fe(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var Vu=Object.freeze({__proto__:null,memory:zu,dataset:Lu,manual:()=>va({readState:b}),init:e=>e.store.manager.state(e)});const Hu=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var Pu=[Cs("initialValue"),ps("getFallbackEntry"),ps("getDataKey"),ps("setValue"),Di("manager",{setValue:Hu,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{Hu(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:Lu})],Uu=[ps("getValue"),Ds("setValue",b),Cs("initialValue"),Di("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:ba.init})],Wu=[Cs("initialValue"),Di("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:zu})],$u=[Bs("store",{mode:"memory"},cs("mode",{memory:Wu,manual:Uu,dataset:Pu})),Ti("onSetValue"),Ds("resetOnDom",!1)];const Gu=wa({fields:$u,name:"representing",active:Nu,apis:Ru,extra:{setValueFrom:(e,t)=>{const o=Gu.getValue(t);Gu.setValue(e,o)}},state:Vu}),ju=(e,t)=>Vs(e,{},L(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,us(o,o,{tag:"option",process:{}},Nn((e=>Cn("The field: "+o+" is forbidden. "+n))));var o,n})).concat([ms("dump",w)])),qu=e=>e.dump,Xu=(e,t)=>({...xa(t),...e.dump}),Yu=ju,Ku=Xu,Ju="placeholder",Qu=Hs([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Zu=e=>be(e,"uiType"),em=(e,t,o,n)=>((e,t,o,n)=>Zu(o)&&o.uiType===Ju?((e,t,o,n)=>e.exists((e=>e!==o.owner))?Qu.single(!0,y(o)):fe(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+re(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):Qu.single(!1,y(o)))(e,0,o,n).fold(((s,r)=>{const a=Zu(o)?r(t,o.config,o.validated):r(t),i=fe(a,"components").getOr([]),l=q(i,(o=>em(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(Zu(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(w)(e)}return n(t)})),tm=Qu.single,om=Qu.multiple,nm=y(Ju),sm=Hs([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),rm=Ds("factory",{sketch:w}),am=Ds("schema",[]),im=ps("name"),lm=us("pname","pname",Mn((e=>"<alloy."+Pi(e.name)+">")),Xn()),cm=ms("schema",(()=>[Cs("preprocess")])),dm=Ds("defaults",y({})),um=Ds("overrides",y({})),mm=Wn([rm,am,im,lm,dm,um]),gm=Wn([rm,am,im,dm,um]),pm=Wn([rm,am,im,lm,dm,um]),hm=Wn([rm,cm,im,ps("unit"),lm,dm,um]),fm=e=>e.fold(A.some,A.none,A.some,A.some),bm=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},vm=(e,t)=>o=>{const n=is("Converting part type",t,o);return e(n)},xm=vm(sm.required,mm),ym=vm(sm.external,gm),wm=vm(sm.optional,pm),Sm=vm(sm.group,hm),Cm=y("entirety");var km=Object.freeze({__proto__:null,required:xm,external:ym,optional:wm,group:Sm,asNamedPart:fm,name:bm,asCommon:e=>e.fold(w,w,w,w),original:Cm});const Om=(e,t,o,n)=>En(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),_m=(e,t)=>{const o={};return V(t,(t=>{fm(t).each((t=>{const n=Tm(e,t.pname);o[t.name]=o=>{const s=is("Part: "+t.name+" in "+e,Wn(t.schema),o);return{...n,config:o,validated:s}}}))})),o},Tm=(e,t)=>({uiType:nm(),owner:e,name:t}),Em=(e,t,o)=>({uiType:nm(),owner:e,name:t,config:o,validated:{}}),Am=e=>q(e,(e=>e.fold(A.none,A.some,A.none,A.none).map((e=>ys(e.name,e.schema.concat([Bi(Cm())])))).toArray())),Mm=e=>L(e,bm),Dm=(e,t,o)=>((e,t,o)=>{const n={},s={};return V(o,(e=>{e.fold((e=>{n[e.pname]=tm(!0,((t,o,n)=>e.factory.sketch(Om(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=y(e.factory.sketch(Om(t,e,o[Cm()]),o))}),(e=>{n[e.pname]=tm(!1,((t,o,n)=>e.factory.sketch(Om(t,e,o,n))))}),(e=>{n[e.pname]=om(!0,((t,o,n)=>{const s=t[e.name];return L(s,(o=>e.factory.sketch(En(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:y(n),externals:y(s)}})(0,t,o),Bm=(e,t,o)=>((e,t,o,n)=>{const s=le(n,((e,t)=>((e,t)=>{let o=!1;return{name:y(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>q(o,(o=>em(e,t,o,n))))(e,t,o,s);return ie(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(A.some(e),t,t.components,o),Im=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},Fm=(e,t,o)=>Im(e,t,o).getOrDie("Could not find part: "+o),Rm=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return V(o,(e=>{n[e]=y(r.getByUid(s[e]))})),n},Nm=(e,t)=>{const o=e.getSystem();return le(t.partUids,((e,t)=>y(o.getByUid(e))))},zm=e=>re(e.partUids),Lm=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return V(o,(e=>{n[e]=y(r.getByUid(s[e]).getOrDie())})),n},Vm=(e,t)=>{const o=Mm(t);return Us(L(o,(t=>({key:t,value:e+"-"+t}))))},Hm=e=>us("partUids","partUids",Bn((t=>Vm(t.uid,e))),Xn());var Pm=Object.freeze({__proto__:null,generate:_m,generateOne:Em,schemas:Am,names:Mm,substitutes:Dm,components:Bm,defaultUids:Vm,defaultUidsSchema:Hm,getAllParts:Nm,getAllPartNames:zm,getPart:Im,getPartOrDie:Fm,getParts:Rm,getPartsOrDie:Lm});const Um=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[ys("parts",e)]:[]).concat([ps("uid"),Ds("dom",{}),Ds("components",[]),Bi("originalSpec"),Ds("debug.sketcher",{})]).concat(t))(n,s);return is(e+" [SpecSchema]",Un(r.concat(t)),o)},Wm=(e,t,o,n,s)=>{const r=$m(s),a=Am(o),i=Hm(o),l=Um(e,t,r,a,[i]),c=Dm(0,l,o);return n(l,Bm(e,l,c.internals()),r,c.externals())},$m=e=>(e=>be(e,"uid"))(e)?e:{...e,uid:Xi("uid")},Gm=Un([ps("name"),ps("factory"),ps("configFields"),Ds("apis",{}),Ds("extraApis",{})]),jm=Un([ps("name"),ps("factory"),ps("configFields"),ps("partFields"),Ds("apis",{}),Ds("extraApis",{})]),qm=e=>{const t=is("Sketcher for "+e.name,Gm,e),o=le(t.apis,tl),n=le(t.extraApis,((e,t)=>ma(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=$m(n);return o(Um(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},Xm=e=>{const t=is("Sketcher for "+e.name,jm,e),o=_m(t.name,t.partFields),n=le(t.apis,tl),s=le(t.extraApis,((e,t)=>ma(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>Wm(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},Ym=e=>Ye("input")(e)&&"radio"!==kt(e,"type")||Ye("textarea")(e);var Km=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const Jm=[ps("find")],Qm=wa({fields:Jm,name:"composing",apis:Km}),Zm=["input","button","textarea","select"],eg=(e,t,o)=>{(t.disabled()?ag:ig)(e,t)},tg=(e,t)=>!0===t.useNative&&F(Zm,Ue(e.element)),og=e=>{St(e.element,"disabled","disabled")},ng=e=>{Tt(e.element,"disabled")},sg=e=>{St(e.element,"aria-disabled","true")},rg=e=>{St(e.element,"aria-disabled","false")},ag=(e,t,o)=>{t.disableClass.each((t=>{Ma(e.element,t)})),(tg(e,t)?og:sg)(e),t.onDisabled(e)},ig=(e,t,o)=>{t.disableClass.each((t=>{Ba(e.element,t)})),(tg(e,t)?ng:rg)(e),t.onEnabled(e)},lg=(e,t)=>tg(e,t)?(e=>_t(e.element,"disabled"))(e):(e=>"true"===kt(e.element,"aria-disabled"))(e);var cg=Object.freeze({__proto__:null,enable:ig,disable:ag,isDisabled:lg,onLoad:eg,set:(e,t,o,n)=>{(n?ag:ig)(e,t)}}),dg=Object.freeze({__proto__:null,exhibit:(e,t)=>ga({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>Kr([Jr(Sr(),((t,o)=>lg(t,e))),pa(e,t,eg)])}),ug=[zs("disabled",T),Ds("useNative",!0),Cs("disableClass"),Ti("onDisabled"),Ti("onEnabled")];const mg=wa({fields:ug,name:"disabling",active:dg,apis:cg}),gg=(e,t,o,n)=>{const s=Od(e.element,"."+t.highlightClass);V(s,(o=>{R(n,(e=>Ze(e.element,o)))||(Ba(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),$r(o,Wr())})))}))},pg=(e,t,o,n)=>{gg(e,t,0,[n]),hg(e,t,o,n)||(Ma(n.element,t.highlightClass),t.onHighlight(e,n),$r(n,Ur()))},hg=(e,t,o,n)=>Ia(n.element,t.highlightClass),fg=(e,t,o)=>mn(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),bg=(e,t,o)=>{const n=Od(e.element,"."+t.itemClass);return(n.length>0?A.some(n[n.length-1]):A.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},vg=(e,t,o,n)=>{const s=Od(e.element,"."+t.itemClass);return G(s,(e=>Ia(e,t.highlightClass))).bind((t=>{const o=Li(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},xg=(e,t,o)=>{const n=Od(e.element,"."+t.itemClass);return ye(L(n,(t=>e.getSystem().getByDom(t).toOptional())))};var yg=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>gg(e,t,0,[]),dehighlight:(e,t,o,n)=>{hg(e,t,o,n)&&(Ba(n.element,t.highlightClass),t.onDehighlight(e,n),$r(n,Wr()))},highlight:pg,highlightFirst:(e,t,o)=>{fg(e,t).each((n=>{pg(e,t,o,n)}))},highlightLast:(e,t,o)=>{bg(e,t).each((n=>{pg(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=Od(e.element,"."+t.itemClass);return A.from(s[n]).fold((()=>bn.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{pg(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=xg(e,t);$(s,n).each((n=>{pg(e,t,o,n)}))},isHighlighted:hg,getHighlighted:(e,t,o)=>mn(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:fg,getLast:bg,getPrevious:(e,t,o)=>vg(e,t,0,-1),getNext:(e,t,o)=>vg(e,t,0,1),getCandidates:xg}),wg=[ps("highlightClass"),ps("itemClass"),Ti("onHighlight"),Ti("onDehighlight")];const Sg=wa({fields:wg,name:"highlighting",apis:yg}),Cg=[8],kg=[9],Og=[13],_g=[27],Tg=[32],Eg=[37],Ag=[38],Mg=[39],Dg=[40],Bg=(e,t,o)=>{const n=Y(e.slice(0,t)),s=Y(e.slice(t+1));return $(n.concat(s),o)},Ig=(e,t,o)=>{const n=Y(e.slice(0,t));return $(n,o)},Fg=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return $(s.concat(n),o)},Rg=(e,t,o)=>{const n=e.slice(t+1);return $(n,o)},Ng=e=>t=>{const o=t.raw;return F(e,o.which)},zg=e=>t=>X(e,(e=>e(t))),Lg=e=>!0===e.raw.shiftKey,Vg=e=>!0===e.raw.ctrlKey,Hg=k(Lg),Pg=(e,t)=>({matches:e,classification:t}),Ug=(e,t,o)=>{t.exists((e=>o.exists((t=>Ze(t,e)))))||Gr(e,zr(),{prevFocus:t,newFocus:o})},Wg=()=>{const e=e=>bc(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);Ug(t,n,s)}}},$g=()=>{const e=e=>Sg.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Sg.highlight(t,e)}));const s=e(t);Ug(t,n,s)}}};var Gg;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(Gg||(Gg={}));const jg=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,$(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([Ds("focusManager",Wg()),Bs("focusInside","onFocus",ns((e=>F(["onFocus","onEnterOrSpace","onApi"],e)?bn.value(e):bn.error("Invalid value for focusInside")))),Di("handler",a),Di("state",t),Di("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==Gg.OnFocusMode?A.none():s(e).map((o=>Zr(vr(),((n,s)=>{o(n,e,t),s.stop()})))),i=[Zr(ir(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=Ng(Tg.concat(Og))(n.event);e.focusInside===Gg.OnEnterOrSpaceMode&&r&&js(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),Zr(lr(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return Kr(a.toArray().concat(i))}};return a},qg=e=>{const t=[Cs("onEscape"),Cs("onEnter"),Ds("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),Ds("firstTabstop",0),Ds("useTabstopAt",E),Cs("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>gn(t,e))).getOr(t);return Ut(o)>0},n=(e,t)=>t.focusManager.get(e).bind((e=>gn(e,t.selector))),s=(e,t,n)=>{((e,t)=>{const n=Od(e.element,t.selector),s=P(n,(e=>o(t,e)));return A.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},r=(e,t,s,r)=>{const a=P(Od(e.element,s.selector),(e=>o(s,e)));return n(e,s).bind((t=>G(a,C(Ze,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?A.some(!0):A.none()),(t=>(s.focusManager.set(e,t),A.some(!0)))))(e,a,t,s,r)))))},a=(e,t,o)=>{const n=o.cyclic?Bg:Ig;return r(e,0,o,n)},i=(e,t,o)=>{const n=o.cyclic?Fg:Rg;return r(e,0,o,n)},l=y([Pg(zg([Lg,Ng(kg)]),a),Pg(Ng(kg),i),Pg(zg([Hg,Ng(Og)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),c=y([Pg(Ng(_g),((e,t,o)=>o.onEscape.bind((o=>o(e,t))))),Pg(Ng(kg),((e,t,o)=>n(e,o).filter((e=>!o.useTabstopAt(e))).bind((n=>((e=>(e=>rt(e))(e).bind(dt).exists((t=>Ze(t,e))))(n)?a:i)(e,t,o)))))]);return jg(t,ba.init,l,c,(()=>A.some(s)))};var Xg=qg(ms("cyclic",T)),Yg=qg(ms("cyclic",E));const Kg=(e,t,o)=>Ym(o)&&Ng(Tg)(t.event)?A.none():((e,t,o)=>(qr(e,o,Sr()),A.some(!0)))(e,0,o),Jg=(e,t)=>A.some(!0),Qg=[Ds("execute",Kg),Ds("useSpace",!1),Ds("useEnter",!0),Ds("useControlEnter",!1),Ds("useDown",!1)],Zg=(e,t,o)=>o.execute(e,t,e.element);var ep=jg(Qg,ba.init,((e,t,o,n)=>{const s=o.useSpace&&!Ym(e.element)?Tg:[],r=o.useEnter?Og:[],a=o.useDown?Dg:[],i=s.concat(r).concat(a);return[Pg(Ng(i),Zg)].concat(o.useControlEnter?[Pg(zg([Vg,Ng(Og)]),Zg)]:[])}),((e,t,o,n)=>o.useSpace&&!Ym(e.element)?[Pg(Ng(Tg),Jg)]:[]),(()=>A.none()));const tp=()=>{const e=nn();return va({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var op=Object.freeze({__proto__:null,flatgrid:tp,init:e=>e.state(e)});const np=e=>(t,o,n,s)=>{const r=e(t.element);return ip(r,t,o,n,s)},sp=(e,t)=>{const o=qc(e,t);return np(o)},rp=(e,t)=>{const o=qc(t,e);return np(o)},ap=e=>(t,o,n,s)=>ip(e,t,o,n,s),ip=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),lp=ap,cp=ap,dp=ap,up=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),mp=(e,t,o)=>{const n=Od(e,o);return(e=>G(e,(e=>Ze(e,t))).map((t=>({index:t,candidates:e}))))(P(n,up))},gp=(e,t)=>G(e,(e=>Ze(t,e))),pp=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?A.some(e[n]):A.none()})),hp=(e,t,o,n,s)=>pp(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=Li(r,s,0,a-1);return A.some({row:t,column:i})})),fp=(e,t,o,n,s)=>pp(e,t,n,((t,r)=>{const a=Li(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=Vi(r,0,i-1);return A.some({row:a,column:l})})),bp=[ps("selector"),Ds("execute",Kg),Ei("onEscape"),Ds("captureTab",!1),Ii()],vp=(e,t,o)=>{mn(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},xp=e=>(t,o,n,s)=>mp(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),yp=(e,t,o)=>o.captureTab?A.some(!0):A.none(),wp=xp(((e,t,o,n)=>hp(e,t,o,n,-1))),Sp=xp(((e,t,o,n)=>hp(e,t,o,n,1))),Cp=xp(((e,t,o,n)=>fp(e,t,o,n,-1))),kp=xp(((e,t,o,n)=>fp(e,t,o,n,1))),Op=y([Pg(Ng(Eg),sp(wp,Sp)),Pg(Ng(Mg),rp(wp,Sp)),Pg(Ng(Ag),lp(Cp)),Pg(Ng(Dg),cp(kp)),Pg(zg([Lg,Ng(kg)]),yp),Pg(zg([Hg,Ng(kg)]),yp),Pg(Ng(Tg.concat(Og)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>gn(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),_p=y([Pg(Ng(_g),((e,t,o)=>o.onEscape(e,t))),Pg(Ng(Tg),Jg)]);var Tp=jg(bp,tp,Op,_p,(()=>A.some(vp)));const Ep=(e,t,o,n,s)=>{const r=(e,t,o)=>s(e,t,n,0,o.length-1,o[t],(t=>{return n=o[t],"button"===Ue(n)&&"disabled"===kt(n,"disabled")?r(e,t,o):A.from(o[t]);var n}));return mp(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return r(t,t,o)}))},Ap=(e,t,o,n)=>Ep(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Vi(t+o,n,s);return i===e?A.from(r):a(i)})),Mp=(e,t,o,n)=>Ep(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Li(t,o,n,s);return i===e?A.none():a(i)})),Dp=[ps("selector"),Ds("getInitial",A.none),Ds("execute",Kg),Ei("onEscape"),Ds("executeOnMove",!1),Ds("allowVertical",!0),Ds("allowHorizontal",!0),Ds("cycles",!0)],Bp=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>gn(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),Ip=(e,t,o)=>{t.getInitial(e).orThunk((()=>mn(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},Fp=(e,t,o)=>(o.cycles?Mp:Ap)(e,o.selector,t,-1),Rp=(e,t,o)=>(o.cycles?Mp:Ap)(e,o.selector,t,1),Np=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?Bp(t,o,n):A.some(!0))),zp=y([Pg(Ng(Tg),Jg),Pg(Ng(_g),((e,t,o)=>o.onEscape(e,t)))]);var Lp=jg(Dp,ba.init,((e,t,o,n)=>{const s=[...o.allowHorizontal?Eg:[]].concat(o.allowVertical?Ag:[]),r=[...o.allowHorizontal?Mg:[]].concat(o.allowVertical?Dg:[]);return[Pg(Ng(s),Np(sp(Fp,Rp))),Pg(Ng(r),Np(rp(Fp,Rp))),Pg(Ng(Og),Bp),Pg(Ng(Tg),Bp)]}),zp,(()=>A.some(Ip)));const Vp=(e,t,o)=>A.from(e[t]).bind((e=>A.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),Hp=(e,t,o,n)=>{const s=e[t].length,r=Li(o,n,0,s-1);return Vp(e,t,r)},Pp=(e,t,o,n)=>{const s=Li(o,n,0,e.length-1),r=e[s].length,a=Vi(t,0,r-1);return Vp(e,s,a)},Up=(e,t,o,n)=>{const s=e[t].length,r=Vi(o+n,0,s-1);return Vp(e,t,r)},Wp=(e,t,o,n)=>{const s=Vi(o+n,0,e.length-1),r=e[s].length,a=Vi(t,0,r-1);return Vp(e,s,a)},$p=[ys("selectors",[ps("row"),ps("cell")]),Ds("cycles",!0),Ds("previousSelector",A.none),Ds("execute",Kg)],Gp=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return mn(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},jp=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return gn(n,s.selectors.row).bind((e=>{const t=Od(e,s.selectors.cell);return gp(t,n).bind((t=>{const n=Od(o,s.selectors.row);return gp(n,e).bind((e=>{const o=((e,t)=>L(e,(e=>Od(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},qp=jp(((e,t,o)=>Hp(e,t,o,-1)),((e,t,o)=>Up(e,t,o,-1))),Xp=jp(((e,t,o)=>Hp(e,t,o,1)),((e,t,o)=>Up(e,t,o,1))),Yp=jp(((e,t,o)=>Pp(e,o,t,-1)),((e,t,o)=>Wp(e,o,t,-1))),Kp=jp(((e,t,o)=>Pp(e,o,t,1)),((e,t,o)=>Wp(e,o,t,1))),Jp=y([Pg(Ng(Eg),sp(qp,Xp)),Pg(Ng(Mg),rp(qp,Xp)),Pg(Ng(Ag),lp(Yp)),Pg(Ng(Dg),cp(Kp)),Pg(Ng(Tg.concat(Og)),((e,t,o)=>bc(e.element).bind((n=>o.execute(e,t,n)))))]),Qp=y([Pg(Ng(Tg),Jg)]);var Zp=jg($p,ba.init,Jp,Qp,(()=>A.some(Gp)));const eh=[ps("selector"),Ds("execute",Kg),Ds("moveOnTab",!1)],th=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),oh=(e,t,o)=>{mn(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},nh=(e,t,o)=>Mp(e,o.selector,t,-1),sh=(e,t,o)=>Mp(e,o.selector,t,1),rh=y([Pg(Ng(Ag),dp(nh)),Pg(Ng(Dg),dp(sh)),Pg(zg([Lg,Ng(kg)]),((e,t,o,n)=>o.moveOnTab?dp(nh)(e,t,o,n):A.none())),Pg(zg([Hg,Ng(kg)]),((e,t,o,n)=>o.moveOnTab?dp(sh)(e,t,o,n):A.none())),Pg(Ng(Og),th),Pg(Ng(Tg),th)]),ah=y([Pg(Ng(Tg),Jg)]);var ih=jg(eh,ba.init,rh,ah,(()=>A.some(oh)));const lh=[Ei("onSpace"),Ei("onEnter"),Ei("onShiftEnter"),Ei("onLeft"),Ei("onRight"),Ei("onTab"),Ei("onShiftTab"),Ei("onUp"),Ei("onDown"),Ei("onEscape"),Ds("stopSpaceKeyup",!1),Cs("focusIn")];var ch=jg(lh,ba.init,((e,t,o)=>[Pg(Ng(Tg),o.onSpace),Pg(zg([Hg,Ng(Og)]),o.onEnter),Pg(zg([Lg,Ng(Og)]),o.onShiftEnter),Pg(zg([Lg,Ng(kg)]),o.onShiftTab),Pg(zg([Hg,Ng(kg)]),o.onTab),Pg(Ng(Ag),o.onUp),Pg(Ng(Dg),o.onDown),Pg(Ng(Eg),o.onLeft),Pg(Ng(Mg),o.onRight),Pg(Ng(Tg),o.onSpace)]),((e,t,o)=>[...o.stopSpaceKeyup?[Pg(Ng(Tg),Jg)]:[],Pg(Ng(_g),o.onEscape)]),(e=>e.focusIn));const dh=Xg.schema(),uh=Yg.schema(),mh=Lp.schema(),gh=Tp.schema(),ph=Zp.schema(),hh=ep.schema(),fh=ih.schema(),bh=ch.schema(),vh=Ca({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:dh,cyclic:uh,flow:mh,flatgrid:gh,matrix:ph,execution:hh,menu:fh,special:bh}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ve(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:op}),xh=(e,t)=>{vc((()=>{((e,t,o)=>{const n=e.components();(e=>{V(e.components(),(e=>Ho(e.element))),Vo(e.element),e.syncComponents()})(e);const s=o(t),r=K(n,s);V(r,(t=>{tu(t),e.getSystem().removeFromWorld(t)})),V(s,(t=>{eu(t)?ru(e,t):(e.getSystem().addToWorld(t),ru(e,t),vt(e.element)&&ou(t))})),e.syncComponents()})(e,t,(()=>L(t,e.getSystem().build)))}),e.element)},yh=(e,t)=>{vc((()=>{((o,n)=>{const s=o.components(),r=q(n,(e=>el(e).toArray()));V(s,(e=>{F(r,e)||su(e)}));const a=((e,t,o)=>pl(e,t,((t,n)=>hl(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),i=K(s,a);V(i,(e=>{eu(e)&&su(e)})),V(a,(e=>{eu(e)||nu(o,e)})),o.syncComponents()})(e,t)}),e.element)},wh=(e,t,o,n)=>{su(t);const s=hl(e.element,o,n,e.getSystem().buildOrPatch);nu(e,s),e.syncComponents()},Sh=(e,t,o)=>{const n=e.getSystem().build(o);iu(e,n,t)},Ch=(e,t,o,n)=>{cu(t),Sh(e,((e,t)=>((e,t,o)=>{ct(e,o).fold((()=>{zo(e,t)}),(e=>{Fo(e,t)}))})(e,t,o)),n)},kh=(e,t)=>e.components(),Oh=(e,t,o,n,s)=>{const r=kh(e);return A.from(r[n]).map((o=>(s.fold((()=>cu(o)),(s=>{(t.reuseDom?wh:Ch)(e,o,n,s)})),o)))};var _h=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Sh(e,zo,n)},prepend:(e,t,o,n)=>{Sh(e,No,n)},remove:(e,t,o,n)=>{const s=kh(e),r=$(s,(e=>Ze(n.element,e.element)));r.each(cu)},replaceAt:Oh,replaceBy:(e,t,o,n,s)=>{const r=kh(e);return G(r,n).bind((o=>Oh(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?yh:xh)(e,n),contents:kh});const Th=wa({fields:[Ns("reuseDom",!0)],name:"replacing",apis:_h}),Eh=(e,t)=>{const o=((e,t)=>{const o=Kr(t);return wa({fields:[ps("enabled")],name:e,active:{events:y(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:y({}),initialConfig:{},state:ba}}},Ah=(e,t)=>{t.ignore||(gc(e.element),t.onFocus(e))};var Mh=Object.freeze({__proto__:null,focus:Ah,blur:(e,t)=>{t.ignore||pc(e.element)},isFocused:e=>hc(e.element)}),Dh=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return ga(o)},events:e=>Kr([Zr(vr(),((t,o)=>{Ah(t,e),o.stop()}))].concat(e.stopMousedown?[Zr(er(),((e,t)=>{t.event.prevent()}))]:[]))}),Bh=[Ti("onFocus"),Ds("stopMousedown",!1),Ds("ignore",!1)];const Ih=wa({fields:Bh,name:"focusing",active:Dh,apis:Mh}),Fh=(e,t,o,n)=>{const s=o.get();o.set(n),((e,t,o)=>{t.toggleClass.each((t=>{o.get()?Ma(e.element,t):Ba(e.element,t)}))})(e,t,o),((e,t,o)=>{const n=t.aria;n.update(e,n,o.get())})(e,t,o),s!==n&&t.onToggled(e,n)},Rh=(e,t,o)=>{Fh(e,t,o,!o.get())},Nh=(e,t,o)=>{Fh(e,t,o,t.selected)};var zh=Object.freeze({__proto__:null,onLoad:Nh,toggle:Rh,isOn:(e,t,o)=>o.get(),on:(e,t,o)=>{Fh(e,t,o,!0)},off:(e,t,o)=>{Fh(e,t,o,!1)},set:Fh}),Lh=Object.freeze({__proto__:null,exhibit:()=>ga({}),events:(e,t)=>{const o=(n=e,s=t,r=Rh,da((e=>{r(e,n,s)})));var n,s,r;const a=pa(e,t,Nh);return Kr(j([e.toggleOnExecute?[o]:[],[a]]))}});const Vh=(e,t,o)=>{St(e.element,"aria-expanded",o)};var Hh=[Ds("selected",!1),Cs("toggleClass"),Ds("toggleOnExecute",!0),Ti("onToggled"),Bs("aria",{mode:"none"},cs("mode",{pressed:[Ds("syncWithExpanded",!1),Di("update",((e,t,o)=>{St(e.element,"aria-pressed",o),t.syncWithExpanded&&Vh(e,0,o)}))],checked:[Di("update",((e,t,o)=>{St(e.element,"aria-checked",o)}))],expanded:[Di("update",Vh)],selected:[Di("update",((e,t,o)=>{St(e.element,"aria-selected",o)}))],none:[Di("update",b)]}))];const Ph=wa({fields:Hh,name:"toggling",active:Lh,apis:zh,state:(Uh=!1,{init:()=>{const e=en(Uh);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(Uh),readState:()=>e.get()}}})});var Uh;const Wh=()=>{const e=(e,t)=>{t.stop(),jr(e)};return[Zr(ur(),e),Zr(kr(),e),sa(Ks()),sa(er())]},$h=e=>Kr(j([e.map((e=>da(((t,o)=>{e(t),o.stop()})))).toArray(),Wh()])),Gh="alloy.item-hover",jh="alloy.item-focus",qh="alloy.item-toggled",Xh=e=>{(bc(e.element).isNone()||Ih.isFocused(e))&&(Ih.isFocused(e)||Ih.focus(e),Gr(e,Gh,{item:e}))},Yh=e=>{Gr(e,jh,{item:e})},Kh=y(Gh),Jh=y(jh),Qh=y(qh),Zh=e=>e.role.fold((()=>e.toggling.map((e=>e.exclusive?"menuitemradio":"menuitemcheckbox")).getOr("menuitem")),w),ef=[ps("data"),ps("components"),ps("dom"),Ds("hasSubmenu",!1),Cs("toggling"),Cs("role"),Yu("itemBehaviours",[Ph,Ih,vh,Gu]),Ds("ignoreFocus",!1),Ds("domModification",{}),Di("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:Zh(e),...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:Ku(e.itemBehaviours,[e.toggling.fold(Ph.revoke,(t=>Ph.config(((e,t)=>({aria:{mode:t?"selected":"checked"},...me(e,((e,t)=>"exclusive"!==t)),onToggled:(t,o)=>{p(e.onToggled)&&e.onToggled(t,o),((e,t)=>{Gr(e,qh,{item:e,state:t})})(t,o)}}))(t,e.role.exists((e=>"option"===e)))))),Ih.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{Yh(e)}}),vh.config({mode:"execution"}),Gu.config({store:{mode:"memory",initialValue:e.data}}),Eh("item-type-events",[...Wh(),Zr(sr(),Xh),Zr(Cr(),Ih.focus)])]),components:e.components,eventOrder:e.eventOrder}))),Ds("eventOrder",{})],tf=[ps("dom"),ps("components"),Di("builder",(e=>({dom:e.dom,components:e.components,events:Kr([ra(Cr())])})))],of=y("item-widget"),nf=y([xm({name:"widget",overrides:e=>({behaviours:xa([Gu.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),sf=[ps("uid"),ps("data"),ps("components"),ps("dom"),Ds("autofocus",!1),Ds("ignoreFocus",!1),Yu("widgetBehaviours",[Gu,Ih,vh]),Ds("domModification",{}),Hm(nf()),Di("builder",(e=>{const t=Dm(of(),e,nf()),o=Bm(of(),e,t.internals()),n=t=>Im(t,e,"widget").map((e=>(vh.focusIn(e),e))),s=(t,o)=>Ym(o.event.target)?A.none():e.autofocus?(o.setSource(t.element),A.none()):A.none();return{dom:e.dom,components:o,domModification:e.domModification,events:Kr([da(((e,t)=>{n(e).each((e=>{t.stop()}))})),Zr(sr(),Xh),Zr(Cr(),((t,o)=>{e.autofocus?n(t):Ih.focus(t)}))]),behaviours:Ku(e.widgetBehaviours,[Gu.config({store:{mode:"memory",initialValue:e.data}}),Ih.config({ignore:e.ignoreFocus,onFocus:e=>{Yh(e)}}),vh.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:ka(),onLeft:s,onRight:s,onEscape:(t,o)=>Ih.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),A.none()):A.none():(Ih.focus(t),A.some(!0))})])}}))],rf=cs("type",{widget:sf,item:ef,separator:tf}),af=y([Sm({factory:{sketch:e=>{const t=is("menu.spec item",rf,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>be(t,"uid")?t:{...t,uid:Xi("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),lf=y([_s("role"),ps("value"),ps("items"),ps("dom"),ps("components"),Ds("eventOrder",{}),ju("menuBehaviours",[Sg,Gu,Qm,vh]),Bs("movement",{mode:"menu",moveOnTab:!0},cs("mode",{grid:[Ii(),Di("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[Di("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},previousSelector:t.previousSelector,focusManager:e.focusManager}))),ps("rowSelector"),Ds("previousSelector",A.none)],menu:[Ds("moveOnTab",!0),Di("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),hs("markers",Si()),Ds("fakeFocus",!1),Ds("focusManager",Wg()),Ti("onHighlight"),Ti("onDehighlight"),Ds("showMenuRole",!0)]),cf=y("alloy.menu-focus"),df=Xm({name:"Menu",configFields:lf(),partFields:af(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Xu(e.menuBehaviours,[Sg.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight,onDehighlight:e.onDehighlight}),Gu.config({store:{mode:"memory",initialValue:e.value}}),Qm.config({find:A.some}),vh.config(e.movement.config(e,e.movement))]),events:Kr([Zr(Jh(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Sg.highlight(e,o),t.stop(),Gr(e,cf(),{menu:e,item:o})}))})),Zr(Kh(),((e,t)=>{const o=t.event.item;Sg.highlight(e,o)})),Zr(Qh(),((e,t)=>{const{item:o,state:n}=t.event;n&&"menuitemradio"===kt(o.element,"role")&&((e,t)=>{const o=Od(e.element,'[role="menuitemradio"][aria-checked="true"]');V(o,(o=>{Ze(o,t.element)||e.getSystem().getByDom(o).each((e=>{Ph.off(e)}))}))})(e,o)}))]),components:t,eventOrder:e.eventOrder,...e.showMenuRole?{domModification:{attributes:{role:e.role.getOr("menu")}}}:{}})}),uf=(e,t,o,n)=>fe(o,n).bind((n=>fe(e,n).bind((n=>{const s=uf(e,t,o,n);return A.some([n].concat(s))})))).getOr([]),mf=e=>"prepared"===e.type?A.some(e.menu):A.none(),gf=()=>{const e=en({}),t=en({}),o=en({}),n=nn(),s=en({}),r=e=>a(e).bind(mf),a=e=>fe(t.get(),e),i=t=>fe(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};ie(e,((e,t)=>{V(e,(e=>{o[e]=t}))}));const n=t,s=ce(t,((e,t)=>({k:e,v:t}))),r=le(s,((e,t)=>[t].concat(uf(o,n,s,t))));return le(o,(e=>fe(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>fe(e.get(),t).map((e=>{const n=fe(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>fe(o.get(),e),collapse:e=>fe(o.get(),e).bind((e=>e.length>1?A.some(e.slice(1)):A.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return K(re(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=P(i(t).toArray(),(e=>r(e).isSome()));return fe(o.get(),t).bind((t=>{const o=Y(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return A.none();t.push(n.getOrDie())}return A.some(t)})(q(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>pe(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>xe(n.get(),t)?[]:[A.none()]),(e=>[A.some(e)])))))}))}}},pf=mf,hf=Pi("tiered-menu-item-highlight"),ff=Pi("tiered-menu-item-dehighlight");var bf;!function(e){e[e.HighlightMenuAndItem=0]="HighlightMenuAndItem",e[e.HighlightJustMenu=1]="HighlightJustMenu",e[e.HighlightNone=2]="HighlightNone"}(bf||(bf={}));const vf=y("collapse-item"),xf=qm({name:"TieredMenu",configFields:[Mi("onExecute"),Mi("onEscape"),Ai("onOpenMenu"),Ai("onOpenSubmenu"),Ti("onRepositionMenu"),Ti("onCollapseMenu"),Ds("highlightOnOpen",bf.HighlightMenuAndItem),ys("data",[ps("primary"),ps("menus"),ps("expansions")]),Ds("fakeFocus",!1),Ti("onHighlightItem"),Ti("onDehighlightItem"),Ti("onHover"),ki(),ps("dom"),Ds("navigateOnHover",!0),Ds("stayInDom",!1),ju("tmenuBehaviours",[vh,Sg,Qm,Th]),Ds("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=nn(),n=gf(),s=e=>Gu.getValue(e).value,r=t=>le(e.data.menus,((e,t)=>q(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=Sg.highlight,i=(t,o)=>{a(t,o),Sg.getHighlighted(o).orThunk((()=>Sg.getFirst(o))).each((n=>{e.fakeFocus?Sg.highlight(o,n):qr(t,n.element,Cr())}))},l=(e,t)=>ye(L(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?A.some(e.menu):A.none()))))),c=(t,o,n)=>{const s=l(o,o.otherMenus(n));V(s,(o=>{Ra(o.element,[e.markers.backgroundMenu]),e.stayInDom||Th.remove(t,o)}))},d=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=Od(t.element,`.${e.markers.item}`),a=P(r,(e=>"true"===kt(e,"aria-haspopup")));return V(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);ie(r,((e,t)=>{const o=F(n,t);St(e.element,"aria-expanded",o)}))},u=(t,o,n)=>A.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return A.none();{const r=s.menu,a=l(o,n.slice(1));return V(a,(t=>{Ma(t.element,e.markers.backgroundMenu)})),vt(r.element)||Th.append(t,Ol(r)),Ra(r.element,[e.markers.backgroundMenu]),i(t,r),c(t,o,n),A.some(r)}}))));let m;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(m||(m={}));const g=(t,o,r=m.HighlightSubmenu)=>{if(o.hasConfigured(mg)&&mg.isDisabled(o))return A.some(o);{const a=s(o);return n.expand(a).bind((s=>(d(t,s),A.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return vt(l.element)||Th.append(t,Ol(l)),e.onOpenSubmenu(t,o,l,Y(s)),r===m.HighlightSubmenu?(Sg.highlightFirst(l),u(t,n,s)):(Sg.dehighlightAll(l),A.some(o))})))))))}},p=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(d(t,s),u(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},h=t=>(o,n)=>gn(n.getSource(),`.${e.markers.item}`).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(E))))),f=Kr([Zr(cf(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Sg.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>c(e,n,t)))}))})),da(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&p(t,o),g(t,o,m.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),ia(((t,o)=>{(t=>{const o=((t,o,n)=>le(n,((n,s)=>{const r=()=>df.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:(e,t)=>{Gr(e,hf,{menuComp:e,itemComp:t})},onDehighlight:(e,t)=>{Gr(e,ff,{menuComp:e,itemComp:t})},focusManager:e.fakeFocus?$g():Wg()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{Th.append(t,Ol(o)),e.onOpenMenu(t,o),e.highlightOnOpen===bf.HighlightMenuAndItem?i(t,o):e.highlightOnOpen===bf.HighlightJustMenu&&a(t,o)}))})),Zr(hf,((t,o)=>{e.onHighlightItem(t,o.event.menuComp,o.event.itemComp)})),Zr(ff,((t,o)=>{e.onDehighlightItem(t,o.event.menuComp,o.event.itemComp)})),...e.navigateOnHover?[Zr(Kh(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(d(e,t),u(e,n,t))))})(t,r),g(t,r,m.HighlightParent),e.onHover(t,r)}))]:[]]),v=e=>Sg.getHighlighted(e).bind(Sg.getHighlighted),x={collapseMenu:e=>{v(e).each((t=>{p(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{i(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>v(t).bind((e=>{const t=s(e),o=he(n.getMenus()),r=ye(L(o,pf));return n.getTriggeringPath(t,(e=>((e,t,o)=>se(t,(e=>{if(!e.getSystem().isConnected())return A.none();const t=Sg.getCandidates(e);return $(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>A.from(e.components()[0]).filter((e=>"menu"===kt(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Xu(e.tmenuBehaviours,[vh.config({mode:"special",onRight:h(((e,t)=>Ym(t.element)?A.none():g(e,t,m.HighlightSubmenu))),onLeft:h(((e,t)=>Ym(t.element)?A.none():p(e,t))),onEscape:h(((t,o)=>p(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{qr(e,t.element,Cr())}))}}),Sg.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),Qm.config({find:e=>Sg.getHighlighted(e)}),Th.config({})]),eventOrder:e.eventOrder,apis:x,events:f}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:Ps(e,t),expansions:{}}),collapseItem:e=>({value:Pi(vf()),meta:{text:e}})}}),yf=qm({name:"InlineView",configFields:[ps("lazySink"),Ti("onShow"),Ti("onHide"),Es("onEscape"),ju("inlineBehaviours",[Ou,Gu,uc]),Ms("fireDismissalEventInstead",[Ds("event",Rr())]),Ms("fireRepositionEventInstead",[Ds("event",Nr())]),Ds("getRelated",A.none),Ds("isExtraPart",T),Ds("eventOrder",A.none)],factory:(e,t)=>{const o=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();Ou.openWhileCloaked(t,o,(()=>Zd.positionWithinBounds(r,t,n,s()))),Gu.setValue(t,A.some({mode:"position",config:n,getBounds:s}))},n=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>ac(),onRtl:()=>ic()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return xf.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightOnOpen:n.menu.highlightOnOpen,fakeFocus:n.menu.fakeFocus,onEscape:()=>(Ou.close(t),e.onEscape.map((e=>e(t))),A.some(!0)),onExecute:()=>A.some(!0),onOpenMenu:(e,t)=>{Zd.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();Zd.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();Zd.positionWithinBounds(a,t,o,s()),V(n,(e=>{const t=i(e.triggeringPath);Zd.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);Ou.open(t,r),Gu.setValue(t,A.some({mode:"menu",menu:r}))},s=t=>{Ou.isOpen(t)&&Gu.getValue(t).each((o=>{switch(o.mode){case"menu":Ou.getState(t).each(xf.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();Zd.positionWithinBounds(n,t,o.config,o.getBounds())}}))},r={setContent:(e,t)=>{Ou.setContent(e,t)},showAt:(e,t,n)=>{const s=A.none;o(e,t,n,s)},showWithinBounds:o,showMenuAt:(e,t,o)=>{n(e,t,o,A.none)},showMenuWithinBounds:n,hide:e=>{Ou.isOpen(e)&&(Gu.setValue(e,A.none()),Ou.close(e))},getContent:e=>Ou.getState(e),reposition:s,isOpen:Ou.isOpen};return{uid:e.uid,dom:e.dom,behaviours:Xu(e.inlineBehaviours,[Ou.config({isPartOf:(t,o,n)=>Al(o,n)||((t,o)=>e.getRelated(t).exists((e=>Al(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),Gu.config({store:{mode:"memory",initialValue:A.none()}}),uc.config({channels:{...Mu({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...Bu({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:s})}})]),eventOrder:e.eventOrder,apis:r}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}});var wf,Sf,Cf=tinymce.util.Tools.resolve("tinymce.util.Delay"),kf=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Of=tinymce.util.Tools.resolve("tinymce.EditorManager"),_f=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(wf||(wf={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(Sf||(Sf={}));const Tf=e=>t=>t.options.get(e),Ef=e=>t=>A.from(e(t)),Af=e=>{const t=_f.deviceType.isPhone(),o=_f.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:kf.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("font_size_input_default_unit",{processor:"string",default:"pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),N(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:Sf.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("ui_mode",{processor:"string",default:"combined"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("promotion",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!_f.deviceType.isTouch()}),n("sidebar_show",{processor:"string"}),n("help_accessibility",{processor:"boolean",default:e.hasPlugin("help")}),n("default_font_stack",{processor:"string[]",default:[]})},Mf=Tf("readonly"),Df=Tf("disabled"),Bf=Tf("height"),If=Tf("width"),Ff=Ef(Tf("min_width")),Rf=Ef(Tf("min_height")),Nf=Ef(Tf("max_width")),zf=Ef(Tf("max_height")),Lf=Ef(Tf("style_formats")),Vf=Tf("style_formats_merge"),Hf=Tf("style_formats_autohide"),Pf=Tf("content_langs"),Uf=Tf("removed_menuitems"),Wf=Tf("toolbar_mode"),$f=Tf("toolbar_groups"),Gf=Tf("toolbar_location"),jf=Tf("fixed_toolbar_container"),qf=Tf("fixed_toolbar_container_target"),Xf=Tf("toolbar_persist"),Yf=Tf("toolbar_sticky_offset"),Kf=Tf("menubar"),Jf=Tf("toolbar"),Qf=Tf("file_picker_callback"),Zf=Tf("file_picker_validator_handler"),eb=Tf("font_size_input_default_unit"),tb=Tf("file_picker_types"),ob=Tf("typeahead_urls"),nb=Tf("anchor_top"),sb=Tf("anchor_bottom"),rb=Tf("draggable_modal"),ab=Tf("statusbar"),ib=Tf("elementpath"),lb=Tf("branding"),cb=Tf("resize"),db=Tf("paste_as_text"),ub=Tf("sidebar_show"),mb=Tf("promotion"),gb=Tf("help_accessibility"),pb=Tf("default_font_stack"),hb=Tf("skin"),fb=e=>!1===e.options.get("skin"),bb=e=>!1!==e.options.get("menubar"),vb=e=>{const t=e.options.get("skin_url");if(fb(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return Of.baseURL+"/skins/ui/"+t}},xb=e=>e.options.get("line_height_formats").split(" "),yb=e=>{const t=Jf(e),o=r(t),n=l(t)&&t.length>0;return!Sb(e)&&(n||o||!0===t)},wb=e=>{const t=N(9,(t=>e.options.get("toolbar"+(t+1)))),o=P(t,r);return Ce(o.length>0,o)},Sb=e=>wb(e).fold((()=>{const t=Jf(e);return f(t,r)&&t.length>0}),E),Cb=e=>Gf(e)===Sf.bottom,kb=e=>{var t;if(!e.inline)return A.none();const o=null!==(t=jf(e))&&void 0!==t?t:"";if(o.length>0)return mn(xt(),o);const n=qf(e);return g(n)?A.some(ze(n)):A.none()},Ob=e=>e.inline&&kb(e).isSome(),_b=e=>kb(e).getOrThunk((()=>ht(pt(ze(e.getElement()))))),Tb=e=>e.inline&&!bb(e)&&!yb(e)&&!Sb(e),Eb=e=>(e.options.get("toolbar_sticky")||e.inline)&&!Ob(e)&&!Tb(e),Ab=e=>!Ob(e)&&"split"===e.options.get("ui_mode"),Mb=e=>{const t=e.options.get("menu");return le(t,(e=>({...e,items:e.items})))};var Db=Object.freeze({__proto__:null,get ToolbarMode(){return wf},get ToolbarLocation(){return Sf},register:Af,getSkinUrl:vb,getSkinUrlOption:e=>A.from(e.options.get("skin_url")),isReadOnly:Mf,isDisabled:Df,getSkin:hb,isSkinDisabled:fb,getHeightOption:Bf,getWidthOption:If,getMinWidthOption:Ff,getMinHeightOption:Rf,getMaxWidthOption:Nf,getMaxHeightOption:zf,getUserStyleFormats:Lf,shouldMergeStyleFormats:Vf,shouldAutoHideStyleFormats:Hf,getLineHeightFormats:xb,getContentLanguages:Pf,getRemovedMenuItems:Uf,isMenubarEnabled:bb,isMultipleToolbars:Sb,isToolbarEnabled:yb,isToolbarPersist:Xf,getMultipleToolbarsOption:wb,getUiContainer:_b,useFixedContainer:Ob,isSplitUiMode:Ab,getToolbarMode:Wf,isDraggableModal:rb,isDistractionFree:Tb,isStickyToolbar:Eb,getStickyToolbarOffset:Yf,getToolbarLocation:Gf,isToolbarLocationBottom:Cb,getToolbarGroups:$f,getMenus:Mb,getMenubar:Kf,getToolbar:Jf,getFilePickerCallback:Qf,getFilePickerTypes:tb,useTypeaheadUrls:ob,getAnchorTop:nb,getAnchorBottom:sb,getFilePickerValidatorHandler:Zf,getFontSizeInputDefaultUnit:eb,useStatusBar:ab,useElementPath:ib,promotionEnabled:mb,useBranding:lb,getResize:cb,getPasteAsText:db,getSidebarShow:ub,useHelpAccessibility:gb,getDefaultFontStack:pb});const Bb=["visible","hidden","clip"],Ib=e=>Ae(e).length>0&&!F(Bb,e),Fb=e=>{if($e(e)){const t=It(e,"overflow-x"),o=It(e,"overflow-y");return Ib(t)||Ib(o)}return!1},Rb=e=>e.plugins.fullscreen&&e.plugins.fullscreen.isFullscreen(),Nb=(e,t)=>Ab(e)?((e,t)=>{const o=kd(t,Fb),n=0===o.length?ft(t).map(bt).map((e=>kd(e,Fb))).getOr([]):o;return te(n).map((t=>({element:t,others:n.slice(1),isFullscreen:()=>Rb(e)})))})(e,t):A.none(),zb=e=>{const t=[...L(e.others,Ko),Zo()];return e.isFullscreen()?Zo():((e,t)=>W(t,((e,t)=>Qo(e,t)),e))(Ko(e.element),t)},Lb=qm({name:"Button",factory:e=>{const t=$h(e.action),o=e.dom.tag,n=t=>fe(e.dom,"attributes").bind((e=>fe(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:Ku(e.buttonBehaviours,[Ih.config({}),vh.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:e.role.getOr(n("role").getOr("button"))}},eventOrder:e.eventOrder}},configFields:[Ds("uid",void 0),ps("dom"),Ds("components",[]),Yu("buttonBehaviours",[Ih,vh]),Cs("action"),Cs("role"),Ds("eventOrder",{})]}),Vb=e=>{const t=Fe(e),o=lt(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return W(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:ui(t)};return{tag:Ue(t),classes:s,attributes:n,...r}},Hb=e=>{const t=(e=>void 0!==e.uid)(e)&&ve(e,"uid")?e.uid:Xi("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}};var Pb=Object.freeze({__proto__:null,exhibit:(e,t)=>ga({attributes:Us([{key:t.tabAttr,value:"true"}])})}),Ub=[Ds("tabAttr","data-alloy-tabstop")];const Wb=wa({fields:Ub,name:"tabstopping",active:Pb}),$b=Pi("tooltip.exclusive"),Gb=Pi("tooltip.show"),jb=Pi("tooltip.hide"),qb=Pi("tooltip.immediateHide"),Xb=Pi("tooltip.immediateShow"),Yb=(e,t,o)=>{e.getSystem().broadcastOn([$b],{})};var Kb=Object.freeze({__proto__:null,hideAllExclusive:Yb,immediateOpenClose:(e,t,o,n)=>$r(e,n?Xb:qb),isEnabled:(e,t,o)=>o.isEnabled(),setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&Th.set(e,n)}))},setEnabled:(e,t,o,n)=>o.setEnabled(n)}),Jb=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{n.getSystem().isConnected()&&(cu(n),e.onHide(o,n),t.clearTooltip())})),t.clearTimer()},n=o=>{if(!t.isShowing()&&t.isEnabled()){Yb(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:Kr("normal"===e.mode?[Zr(sr(),(e=>{$r(o,Gb)})),Zr(or(),(e=>{$r(o,jb)}))]:[]),behaviours:xa([Th.config({})])});t.setTooltip(s),au(n,s),e.onShow(o,s),Zd.position(n,s,{anchor:e.anchor(o)})}},s=o=>{t.getTooltip().each((t=>{const n=e.lazySink(o).getOrDie();Zd.position(n,t,{anchor:e.anchor(o)})}))};return Kr(j([[ca((t=>{e.onSetup(t)})),Zr(Gb,(o=>{t.resetTimer((()=>{n(o)}),e.delayForShow())})),Zr(jb,(n=>{t.resetTimer((()=>{o(n)}),e.delayForHide())})),Zr(Xb,(e=>{t.resetTimer((()=>{n(e)}),0)})),Zr(qb,(e=>{t.resetTimer((()=>{o(e)}),0)})),Zr(wr(),((e,t)=>{const n=t;n.universal||F(n.channels,$b)&&o(e)})),la((e=>{o(e)}))],(()=>{switch(e.mode){case"normal":return[Zr(rr(),(e=>{$r(e,Xb)})),Zr(xr(),(e=>{$r(e,qb)})),Zr(sr(),(e=>{$r(e,Gb)})),Zr(or(),(e=>{$r(e,jb)}))];case"follow-highlight":return[Zr(Ur(),((e,t)=>{$r(e,Gb)})),Zr(Wr(),(e=>{$r(e,jb)}))];case"children-normal":return[Zr(rr(),((o,n)=>{bc(o.element).each((r=>{Ke(n.event.target,"[data-mce-tooltip]")&&t.getTooltip().fold((()=>{$r(o,Xb)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),Zr(xr(),(e=>{bc(e.element).fold((()=>{$r(e,qb)}),b)})),Zr(sr(),(o=>{mn(o.element,"[data-mce-tooltip]:hover").each((n=>{t.getTooltip().fold((()=>{$r(o,Gb)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),Zr(or(),(e=>{mn(e.element,"[data-mce-tooltip]:hover").fold((()=>{$r(e,jb)}),b)}))];default:return[Zr(rr(),((o,n)=>{bc(o.element).each((r=>{Ke(n.event.target,"[data-mce-tooltip]")&&t.getTooltip().fold((()=>{$r(o,Xb)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),Zr(xr(),(e=>{bc(e.element).fold((()=>{$r(e,qb)}),b)}))]}})()]))}}),Qb=[ps("lazySink"),ps("tooltipDom"),Ds("exclusive",!0),Ds("tooltipComponents",[]),zs("delayForShow",y(300)),zs("delayForHide",y(100)),zs("onSetup",b),Rs("mode","normal",["normal","follow-highlight","children-keyboard-focus","children-normal"]),Ds("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:y([Zl,Ql,Xl,Kl,Yl,Jl]),onRtl:y([Zl,Ql,Xl,Kl,Yl,Jl])},bubble:$c(0,-2,{})}))),Ti("onHide"),Ti("onShow")],Zb=Object.freeze({__proto__:null,init:()=>{const e=en(!0),t=nn(),o=nn(),n=()=>{t.on(clearTimeout)},s=y("not-implemented");return va({getTooltip:o.get,isShowing:o.isSet,setTooltip:o.set,clearTooltip:o.clear,clearTimer:n,resetTimer:(e,o)=>{n(),t.set(setTimeout(e,o))},readState:s,isEnabled:()=>e.get(),setEnabled:t=>e.set(t)})}});const ev=wa({fields:Qb,name:"tooltipping",active:Jb,state:Zb,apis:Kb}),{entries:tv,setPrototypeOf:ov,isFrozen:nv,getPrototypeOf:sv,getOwnPropertyDescriptor:rv}=Object;
/*! @license DOMPurify 3.2.4 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.4/LICENSE */let{freeze:av,seal:iv,create:lv}=Object,{apply:cv,construct:dv}="undefined"!=typeof Reflect&&Reflect;// eslint-disable-line import/no-mutable-exports
av||(av=function(e){return e}),iv||(iv=function(e){return e}),cv||(cv=function(e,t,o){return e.apply(t,o)}),dv||(dv=function(e,t){return new e(...t)});const uv=_v(Array.prototype.forEach),mv=_v(Array.prototype.lastIndexOf),gv=_v(Array.prototype.pop),pv=_v(Array.prototype.push),hv=_v(Array.prototype.splice),fv=_v(String.prototype.toLowerCase),bv=_v(String.prototype.toString),vv=_v(String.prototype.match),xv=_v(String.prototype.replace),yv=_v(String.prototype.indexOf),wv=_v(String.prototype.trim),Sv=_v(Object.prototype.hasOwnProperty),Cv=_v(RegExp.prototype.test),kv=(Ov=TypeError,function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return dv(Ov,t)});
/**
     * Creates a new function that constructs an instance of the given constructor function with the provided arguments.
     *
     * @param func - The constructor function to be wrapped and called.
     * @returns A new function that constructs an instance of the given constructor function with the provided arguments.
     */
var Ov;
/**
     * Add properties to a lookup table
     *
     * @param set - The set to which elements will be added.
     * @param array - The array containing elements to be added to the set.
     * @param transformCaseFunc - An optional function to transform the case of each element before adding to the set.
     * @returns The modified set with added elements.
     */
/**
     * Creates a new function that calls the given function with a specified thisArg and arguments.
     *
     * @param func - The function to be wrapped and called.
     * @returns A new function that calls the given function with a specified thisArg and arguments.
     */
function _v(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),s=1;s<o;s++)n[s-1]=arguments[s];return cv(e,t,n)}}function Tv(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:fv;ov&&
// Make 'in' and truthy checks like Boolean(set.constructor)
// independent of any properties defined on Object.prototype.
// Prevent prototype setters from intercepting set as a this value.
ov(e,null);let n=t.length;for(;n--;){let s=t[n];if("string"==typeof s){const e=o(s);e!==s&&(
// Config presets (e.g. tags.js, attrs.js) are immutable.
nv(t)||(t[n]=e),s=e)}e[s]=!0}return e}
/**
     * Clean up an array to harden against CSPP
     *
     * @param array - The array to be cleaned.
     * @returns The cleaned version of the array
     */function Ev(e){for(let t=0;t<e.length;t++)Sv(e,t)||(e[t]=null);return e}
/**
     * Shallow clone an object
     *
     * @param object - The object to be cloned.
     * @returns A new object that copies the original.
     */function Av(e){const t=lv(null);for(const[o,n]of tv(e))Sv(e,o)&&(Array.isArray(n)?t[o]=Ev(n):n&&"object"==typeof n&&n.constructor===Object?t[o]=Av(n):t[o]=n);return t}
/**
     * This method automatically checks if the prop is function or getter and behaves accordingly.
     *
     * @param object - The object to look up the getter function in its prototype chain.
     * @param prop - The property name for which to find the getter function.
     * @returns The getter function found in the prototype chain or a fallback function.
     */function Mv(e,t){for(;null!==e;){const o=rv(e,t);if(o){if(o.get)return _v(o.get);if("function"==typeof o.value)return _v(o.value)}e=sv(e)}return function(){return null}}const Dv=av(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Bv=av(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Iv=av(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Fv=av(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Rv=av(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Nv=av(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),zv=av(["#text"]),Lv=av(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Vv=av(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Hv=av(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Pv=av(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Uv=iv(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Wv=iv(/<%[\w\W]*|[\w\W]*%>/gm),$v=iv(/\$\{[\w\W]*/gm),Gv=iv(/^data-[\-\w.\u00B7-\uFFFF]+$/),jv=iv(/^aria-[\-\w]+$/),qv=iv(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Xv=iv(/^(?:\w+script|data):/i),Yv=iv(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Kv=iv(/^html$/i),Jv=iv(/^[a-z][.\w]*(-[.\w]+)+$/i);var Qv=Object.freeze({__proto__:null,ARIA_ATTR:jv,ATTR_WHITESPACE:Yv,CUSTOM_ELEMENT:Jv,DATA_ATTR:Gv,DOCTYPE_NAME:Kv,ERB_EXPR:Wv,IS_ALLOWED_URI:qv,IS_SCRIPT_OR_DATA:Xv,MUSTACHE_EXPR:Uv,TMPLIT_EXPR:$v});
/* eslint-disable @typescript-eslint/indent */
// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType
const Zv=function(){return"undefined"==typeof window?null:window};var ex=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Zv();const o=t=>e(t);if(o.version="3.2.4",o.removed=[],!t||!t.document||9!==t.document.nodeType||!t.Element)
// Not running in a browser, provide a factory function
// so that you can pass your own Window
return o.isSupported=!1,o;let{document:n}=t;const s=n,r=s.currentScript,{DocumentFragment:a,HTMLTemplateElement:i,Node:l,Element:c,NodeFilter:d,NamedNodeMap:u=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:m,DOMParser:g,trustedTypes:p}=t,h=c.prototype,f=Mv(h,"cloneNode"),b=Mv(h,"remove"),v=Mv(h,"nextSibling"),x=Mv(h,"childNodes"),y=Mv(h,"parentNode");
// As per issue #47, the web-components registry is inherited by a
// new document created via createHTMLDocument. As per the spec
// (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)
// a new empty registry is used when creating a template contents owner
// document, so we use that as our parent document to ensure nothing
// is inherited.
if("function"==typeof i){const e=n.createElement("template");e.content&&e.content.ownerDocument&&(n=e.content.ownerDocument)}let w,S="";const{implementation:C,createNodeIterator:k,createDocumentFragment:O,getElementsByTagName:_}=n,{importNode:T}=s;let E={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};
/**
       * Expose whether this browser supports running the full DOMPurify.
       */o.isSupported="function"==typeof tv&&"function"==typeof y&&C&&void 0!==C.createHTMLDocument;const{MUSTACHE_EXPR:A,ERB_EXPR:M,TMPLIT_EXPR:D,DATA_ATTR:B,ARIA_ATTR:I,IS_SCRIPT_OR_DATA:F,ATTR_WHITESPACE:R,CUSTOM_ELEMENT:N}=Qv;let{IS_ALLOWED_URI:z}=Qv,L=null;
/**
       * We consider the elements and attributes below to be safe. Ideally
       * don't add any new ones but feel free to remove unwanted ones.
       */
/* allowed element names */const V=Tv({},[...Dv,...Bv,...Iv,...Rv,...zv]);
/* Allowed attribute names */let H=null;const P=Tv({},[...Lv,...Vv,...Hv,...Pv]);
/*
       * Configure how DOMPurify should handle custom elements and their attributes as well as customized built-in elements.
       * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)
       * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)
       * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.
       */let U=Object.seal(lv(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),W=null,$=null,G=!0,j=!0,q=!1,X=!0,Y=!1,K=!0,J=!1,Q=!1,Z=!1,ee=!1,te=!1,oe=!1,ne=!0,se=!1,re=!0,ae=!1,ie={},le=null;
/* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */const ce=Tv({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);
/* Tags that are safe for data: URIs */let de=null;const ue=Tv({},["audio","video","img","source","image","track"]);
/* Attributes safe for values like "javascript:" */let me=null;const ge=Tv({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),pe="http://www.w3.org/1998/Math/MathML",he="http://www.w3.org/2000/svg",fe="http://www.w3.org/1999/xhtml";
/* Document namespace */
let be=fe,ve=!1,xe=null;const ye=Tv({},[pe,he,fe],bv);let we=Tv({},["mi","mo","mn","ms","mtext"]),Se=Tv({},["annotation-xml"]);
// Certain elements are allowed in both SVG and HTML
// namespace. We need to specify them explicitly
// so that they don't get erroneously deleted from
// HTML namespace.
const Ce=Tv({},["title","style","font","a","script"]);
/* Parsing of strict XHTML documents */let ke=null;const Oe=["application/xhtml+xml","text/html"];let _e=null,Te=null;
/* Keep a reference to config to pass to hooks */
/* Ideally, do not touch anything below this line */
/* ______________________________________________ */
const Ee=n.createElement("form"),Ae=function(e){return e instanceof RegExp||e instanceof Function},Me=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!Te||Te!==e){if(
/* Shield configuration object from tampering */
e&&"object"==typeof e||(e={})
/* Shield configuration object from prototype pollution */,e=Av(e),ke=
// eslint-disable-next-line unicorn/prefer-includes
-1===Oe.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,
// HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.
_e="application/xhtml+xml"===ke?bv:fv,
/* Set configuration parameters */
L=Sv(e,"ALLOWED_TAGS")?Tv({},e.ALLOWED_TAGS,_e):V,H=Sv(e,"ALLOWED_ATTR")?Tv({},e.ALLOWED_ATTR,_e):P,xe=Sv(e,"ALLOWED_NAMESPACES")?Tv({},e.ALLOWED_NAMESPACES,bv):ye,me=Sv(e,"ADD_URI_SAFE_ATTR")?Tv(Av(ge),e.ADD_URI_SAFE_ATTR,_e):ge,de=Sv(e,"ADD_DATA_URI_TAGS")?Tv(Av(ue),e.ADD_DATA_URI_TAGS,_e):ue,le=Sv(e,"FORBID_CONTENTS")?Tv({},e.FORBID_CONTENTS,_e):ce,W=Sv(e,"FORBID_TAGS")?Tv({},e.FORBID_TAGS,_e):{},$=Sv(e,"FORBID_ATTR")?Tv({},e.FORBID_ATTR,_e):{},ie=!!Sv(e,"USE_PROFILES")&&e.USE_PROFILES,G=!1!==e.ALLOW_ARIA_ATTR,// Default true
j=!1!==e.ALLOW_DATA_ATTR,// Default true
q=e.ALLOW_UNKNOWN_PROTOCOLS||!1,// Default false
X=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,// Default true
Y=e.SAFE_FOR_TEMPLATES||!1,// Default false
K=!1!==e.SAFE_FOR_XML,// Default true
J=e.WHOLE_DOCUMENT||!1,// Default false
ee=e.RETURN_DOM||!1,// Default false
te=e.RETURN_DOM_FRAGMENT||!1,// Default false
oe=e.RETURN_TRUSTED_TYPE||!1,// Default false
Z=e.FORCE_BODY||!1,// Default false
ne=!1!==e.SANITIZE_DOM,// Default true
se=e.SANITIZE_NAMED_PROPS||!1,// Default false
re=!1!==e.KEEP_CONTENT,// Default true
ae=e.IN_PLACE||!1,// Default false
z=e.ALLOWED_URI_REGEXP||qv,be=e.NAMESPACE||fe,we=e.MATHML_TEXT_INTEGRATION_POINTS||we,Se=e.HTML_INTEGRATION_POINTS||Se,U=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&Ae(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(U.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&Ae(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(U.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(U.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Y&&(j=!1),te&&(ee=!0)
/* Parse profile info */,ie&&(L=Tv({},zv),H=[],!0===ie.html&&(Tv(L,Dv),Tv(H,Lv)),!0===ie.svg&&(Tv(L,Bv),Tv(H,Vv),Tv(H,Pv)),!0===ie.svgFilters&&(Tv(L,Iv),Tv(H,Vv),Tv(H,Pv)),!0===ie.mathMl&&(Tv(L,Rv),Tv(H,Hv),Tv(H,Pv)))
/* Merge configuration parameters */,e.ADD_TAGS&&(L===V&&(L=Av(L)),Tv(L,e.ADD_TAGS,_e)),e.ADD_ATTR&&(H===P&&(H=Av(H)),Tv(H,e.ADD_ATTR,_e)),e.ADD_URI_SAFE_ATTR&&Tv(me,e.ADD_URI_SAFE_ATTR,_e),e.FORBID_CONTENTS&&(le===ce&&(le=Av(le)),Tv(le,e.FORBID_CONTENTS,_e))
/* Add #text in case KEEP_CONTENT is set to true */,re&&(L["#text"]=!0)
/* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */,J&&Tv(L,["html","head","body"])
/* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */,L.table&&(Tv(L,["tbody"]),delete W.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw kv('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw kv('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');
// Overwrite existing TrustedTypes policy.
w=e.TRUSTED_TYPES_POLICY,
// Sign local variables required by `sanitize`.
S=w.createHTML("")}else
// Uninitialized policy, attempt to initialize the internal dompurify policy.
void 0===w&&(w=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;
// Allow the callers to control the unique policy name
// by adding a data-tt-policy-suffix to the script element with the DOMPurify.
// Policy creation with duplicate names throws in Trusted Types.
let o=null;const n="data-tt-policy-suffix";t&&t.hasAttribute(n)&&(o=t.getAttribute(n));const s="dompurify"+(o?"#"+o:"");try{return e.createPolicy(s,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){
// Policy creation failed (most likely another DOMPurify script has
// already run). Skip creating the policy, as this will only cause errors
// if TT are enforced.
return console.warn("TrustedTypes policy "+s+" could not be created."),null}}(p,r)),
// If creating the internal policy succeeded sign internal variables.
null!==w&&"string"==typeof S&&(S=w.createHTML(""));
// Prevent further manipulation of configuration.
// Not available in IE8, Safari 5, etc.
av&&av(e),Te=e}},De=Tv({},[...Bv,...Iv,...Fv]),Be=Tv({},[...Rv,...Nv]),Ie=function(e){pv(o.removed,{element:e});try{
// eslint-disable-next-line unicorn/prefer-dom-node-remove
y(e).removeChild(e)}catch(t){b(e)}},Fe=function(e,t){try{pv(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){pv(o.removed,{attribute:null,from:t})}
// We void attribute values for unremovable "is" attributes
if(t.removeAttribute(e),"is"===e)if(ee||te)try{Ie(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Re=function(e){
/* Create a HTML document */
let t=null,o=null;if(Z)e="<remove></remove>"+e;else{
/* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */
const t=vv(e,/^[\r\n\t ]+/);o=t&&t[0]}"application/xhtml+xml"===ke&&be===fe&&(
// Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)
e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const s=w?w.createHTML(e):e;
/*
         * Use the DOMParser API by default, fallback later if needs be
         * DOMParser not work for svg when has multiple root element.
         */if(be===fe)try{t=(new g).parseFromString(s,ke)}catch(e){}
/* Use createHTMLDocument in case DOMParser is not available */if(!t||!t.documentElement){t=C.createDocument(be,"template",null);try{t.documentElement.innerHTML=ve?S:s}catch(e){
// Syntax error if dirtyPayload is invalid xml
}}const r=t.body||t.documentElement;
/* Work on whole document or just its body */
return e&&o&&r.insertBefore(n.createTextNode(o),r.childNodes[0]||null),be===fe?_.call(t,J?"html":"body")[0]:J?t.documentElement:r},Ne=function(e){return k.call(e.ownerDocument||e,e,
// eslint-disable-next-line no-bitwise
d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT|d.SHOW_PROCESSING_INSTRUCTION|d.SHOW_CDATA_SECTION,null)},ze=function(e){return e instanceof m&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof u)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Le=function(e){return"function"==typeof l&&e instanceof l};function Ve(e,t,n){uv(e,(e=>{e.call(o,t,n,Te)}))}
/**
       * _sanitizeElements
       *
       * @protect nodeName
       * @protect textContent
       * @protect removeChild
       * @param currentNode to check for permission to exist
       * @return true if node was killed, false if left alive
       */const He=function(e){let t=null;
/* Execute a hook if present */
/* Check if element is clobbered or can clobber */
if(Ve(E.beforeSanitizeElements,e,null),ze(e))return Ie(e),!0;
/* Now let's check the element's type and name */const n=_e(e.nodeName);
/* Execute a hook if present */
/* Detect mXSS attempts abusing namespace confusion */
if(Ve(E.uponSanitizeElement,e,{tagName:n,allowedTags:L}),e.hasChildNodes()&&!Le(e.firstElementChild)&&Cv(/<[/\w]/g,e.innerHTML)&&Cv(/<[/\w]/g,e.textContent))return Ie(e),!0;
/* Remove any occurrence of processing instructions */if(7===e.nodeType)return Ie(e),!0;
/* Remove any kind of possibly harmful comments */if(K&&8===e.nodeType&&Cv(/<[/\w]/g,e.data))return Ie(e),!0;
/* Remove element if anything forbids its presence */if(!L[n]||W[n]){
/* Check if we have a custom element to handle */
if(!W[n]&&Ue(n)){if(U.tagNameCheck instanceof RegExp&&Cv(U.tagNameCheck,n))return!1;if(U.tagNameCheck instanceof Function&&U.tagNameCheck(n))return!1}
/* Keep content except for bad-listed elements */if(re&&!le[n]){const t=y(e)||e.parentNode,o=x(e)||e.childNodes;if(o&&t)for(let n=o.length-1;n>=0;--n){const s=f(o[n],!0);s.__removalCount=(e.__removalCount||0)+1,t.insertBefore(s,v(e))}}return Ie(e),!0}
/* Check whether element has a valid namespace */return e instanceof c&&!function(e){let t=y(e);
// In JSDOM, if we're inside shadow DOM, then parentNode
// can be null. We just simulate parent in this case.
t&&t.tagName||(t={namespaceURI:be,tagName:"template"});const o=fv(e.tagName),n=fv(t.tagName);return!!xe[e.namespaceURI]&&(e.namespaceURI===he?
// The only way to switch from HTML namespace to SVG
// is via <svg>. If it happens via any other tag, then
// it should be killed.
t.namespaceURI===fe?"svg"===o:
// The only way to switch from MathML to SVG is via`
// svg if parent is either <annotation-xml> or MathML
// text integration points.
t.namespaceURI===pe?"svg"===o&&("annotation-xml"===n||we[n]):Boolean(De[o]):e.namespaceURI===pe?
// The only way to switch from HTML namespace to MathML
// is via <math>. If it happens via any other tag, then
// it should be killed.
t.namespaceURI===fe?"math"===o:
// The only way to switch from SVG to MathML is via
// <math> and HTML integration points
t.namespaceURI===he?"math"===o&&Se[n]:Boolean(Be[o]):e.namespaceURI===fe?
// The only way to switch from SVG to HTML is via
// HTML integration points, and from MathML to HTML
// is via MathML text integration points
!(t.namespaceURI===he&&!Se[n])&&!(t.namespaceURI===pe&&!we[n])&&!Be[o]&&(Ce[o]||!De[o]):!("application/xhtml+xml"!==ke||!xe[e.namespaceURI]))}(e)?(Ie(e),!0):
/* Make sure that older browsers don't get fallback-tag mXSS */
"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!Cv(/<\/no(script|embed|frames)/i,e.innerHTML)?(
/* Sanitize element content to be template-safe */
Y&&3===e.nodeType&&(
/* Get the element's text content */
t=e.textContent,uv([A,M,D],(e=>{t=xv(t,e," ")})),e.textContent!==t&&(pv(o.removed,{element:e.cloneNode()}),e.textContent=t))
/* Execute a hook if present */,Ve(E.afterSanitizeElements,e,null),!1):(Ie(e),!0)},Pe=function(e,t,o){
/* Make sure attribute cannot clobber */
if(ne&&("id"===t||"name"===t)&&(o in n||o in Ee))return!1;
/* Allow valid data-* attributes: At least one character after "-"
            (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)
            XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)
            We don't need to check the value; it's always URI safe. */if(j&&!$[t]&&Cv(B,t));else if(G&&Cv(I,t));else if(!H[t]||$[t]){if(
// First condition does a very basic check if a) it's basically a valid custom element tagname AND
// b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck
// and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck
!(Ue(e)&&(U.tagNameCheck instanceof RegExp&&Cv(U.tagNameCheck,e)||U.tagNameCheck instanceof Function&&U.tagNameCheck(e))&&(U.attributeNameCheck instanceof RegExp&&Cv(U.attributeNameCheck,t)||U.attributeNameCheck instanceof Function&&U.attributeNameCheck(t))||
// Alternative, second condition checks if it's an `is`-attribute, AND
// the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck
"is"===t&&U.allowCustomizedBuiltInElements&&(U.tagNameCheck instanceof RegExp&&Cv(U.tagNameCheck,o)||U.tagNameCheck instanceof Function&&U.tagNameCheck(o))))return!1;
/* Check value is safe. First, is attr inert? If so, is safe */}else if(me[t]);else if(Cv(z,xv(o,R,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==yv(o,"data:")||!de[e])if(q&&!Cv(F,xv(o,R,"")));else if(o)return!1;return!0},Ue=function(e){return"annotation-xml"!==e&&vv(e,N)},We=function(e){
/* Execute a hook if present */
Ve(E.beforeSanitizeAttributes,e,null);const{attributes:t}=e;
/* Check if we have attributes; if not we might have a text node */if(!t||ze(e))return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:H,forceKeepAttr:void 0};let s=t.length;
/* Go backwards over all attributes; safely remove bad ones */for(;s--;){const r=t[s],{name:a,namespaceURI:i,value:l}=r,c=_e(a);let d="value"===a?l:wv(l);const u=d;
/* Execute a hook if present */
/* Work around a security issue with comments inside attributes */
if(n.attrName=c,n.attrValue=d,n.keepAttr=!0,n.forceKeepAttr=void 0,// Allows developers to see this is a property they can set
Ve(E.uponSanitizeAttribute,e,n),d=n.attrValue,
/* Full DOM Clobbering protection via namespace isolation,
           * Prefix id and name attributes with `user-content-`
           */
!se||"id"!==c&&"name"!==c||(
// Remove the attribute with this value
Fe(a,e),
// Prefix the value and later re-create the attribute with the sanitized value
d="user-content-"+d),K&&Cv(/((--!?|])>)|<\/(style|title)/i,d)){Fe(a,e);continue}
/* Did the hooks approve of the attribute? */if(n.forceKeepAttr)continue;
/* Remove attribute */
/* Did the hooks approve of the attribute? */if(!n.keepAttr){Fe(a,e);continue}
/* Work around a security issue in jQuery 3.0 */if(!X&&Cv(/\/>/i,d)){Fe(a,e);continue}
/* Sanitize attribute content to be template-safe */Y&&uv([A,M,D],(e=>{d=xv(d,e," ")}))
/* Is `value` valid for this attribute? */;const m=_e(e.nodeName);if(Pe(m,c,d)){
/* Handle attributes that require Trusted Types */
if(w&&"object"==typeof p&&"function"==typeof p.getAttributeType)if(i);else switch(p.getAttributeType(m,c)){case"TrustedHTML":d=w.createHTML(d);break;case"TrustedScriptURL":d=w.createScriptURL(d)}
/* Handle invalid data-* attribute set by try-catching it */if(d!==u)try{i?e.setAttributeNS(i,a,d):
/* Fallback to setAttribute() for browser-unrecognized namespaces e.g. "x-schema". */
e.setAttribute(a,d),ze(e)?Ie(e):gv(o.removed)}catch(e){}}else Fe(a,e)}
/* Execute a hook if present */Ve(E.afterSanitizeAttributes,e,null)},$e=function e(t){let o=null;const n=Ne(t);
/* Execute a hook if present */for(Ve(E.beforeSanitizeShadowDOM,t,null);o=n.nextNode();)
/* Execute a hook if present */
Ve(E.uponSanitizeShadowNode,o,null),
/* Sanitize tags and elements */
He(o),
/* Check attributes next */
We(o),
/* Deep shadow DOM detected */
o.content instanceof a&&e(o.content);
/* Execute a hook if present */Ve(E.afterSanitizeShadowDOM,t,null)};
/**
       * _isValidAttribute
       *
       * @param lcTag Lowercase tag name of containing element.
       * @param lcName Lowercase attribute name.
       * @param value Attribute value.
       * @return Returns true if `value` is valid, otherwise false.
       */
// eslint-disable-next-line complexity
// eslint-disable-next-line complexity
return o.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,r=null,i=null,c=null;
/* Stringify, in case dirty is an object */
if(
/* Make sure we have a string to sanitize.
          DO NOT return early, as this will return the wrong type if
          the user has requested a DOM object rather than a string */
ve=!e,ve&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Le(e)){if("function"!=typeof e.toString)throw kv("toString is not a function");if("string"!=typeof(e=e.toString()))throw kv("dirty is not a string, aborting")}
/* Return dirty HTML if DOMPurify cannot run */if(!o.isSupported)return e;
/* Assign config vars */if(Q||Me(t)
/* Clean up removed elements */,o.removed=[],
/* Check if dirty is correctly typed for IN_PLACE */
"string"==typeof e&&(ae=!1),ae){
/* Do some early pre-sanitization to avoid unsafe root nodes */
if(e.nodeName){const t=_e(e.nodeName);if(!L[t]||W[t])throw kv("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof l)
/* If dirty is a DOM element, append to an empty document to avoid
             elements being stripped by the parser */
n=Re("\x3c!----\x3e"),r=n.ownerDocument.importNode(e,!0),1===r.nodeType&&"BODY"===r.nodeName||"HTML"===r.nodeName?
/* Node is already a body, use as is */
n=r:
// eslint-disable-next-line unicorn/prefer-dom-node-append
n.appendChild(r);else{
/* Exit directly if we have nothing to do */
if(!ee&&!Y&&!J&&
// eslint-disable-next-line unicorn/prefer-includes
-1===e.indexOf("<"))return w&&oe?w.createHTML(e):e;
/* Initialize the document to work on */
/* Check we have a DOM node from the data */
if(n=Re(e),!n)return ee?null:oe?S:""}
/* Remove first element node (ours) if FORCE_BODY is set */n&&Z&&Ie(n.firstChild)
/* Get node iterator */;const d=Ne(ae?e:n);
/* Now start iterating over the created document */for(;i=d.nextNode();)
/* Sanitize tags and elements */
He(i),
/* Check attributes next */
We(i),
/* Shadow DOM detected, sanitize it */
i.content instanceof a&&$e(i.content);
/* If we sanitized `dirty` in-place, return it. */if(ae)return e;
/* Return sanitized string or DOM */if(ee){if(te)for(c=O.call(n.ownerDocument);n.firstChild;)
// eslint-disable-next-line unicorn/prefer-dom-node-append
c.appendChild(n.firstChild);else c=n;return(H.shadowroot||H.shadowrootmode)&&(
/*
              AdoptNode() is not used because internal state is not reset
              (e.g. the past names map of a HTMLFormElement), this is safe
              in theory but we would rather not risk another attack vector.
              The state that is cloned by importNode() is explicitly defined
              by the specs.
            */
c=T.call(s,c,!0)),c}let u=J?n.outerHTML:n.innerHTML;
/* Serialize doctype if allowed */return J&&L["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&Cv(Kv,n.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+u)
/* Sanitize final string template-safe */,Y&&uv([A,M,D],(e=>{u=xv(u,e," ")})),w&&oe?w.createHTML(u):u},o.setConfig=function(){Me(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Q=!0},o.clearConfig=function(){Te=null,Q=!1},o.isValidAttribute=function(e,t,o){
/* Initialize shared config vars if necessary. */
Te||Me({});const n=_e(e),s=_e(t);return Pe(n,s,o)},o.addHook=function(e,t){"function"==typeof t&&pv(E[e],t)},o.removeHook=function(e,t){if(void 0!==t){const o=mv(E[e],t);return-1===o?void 0:hv(E[e],o,1)[0]}return gv(E[e])},o.removeHooks=function(e){E[e]=[]},o.removeAllHooks=function(){E={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},o}();const tx=e=>ex().sanitize(e);var ox=tinymce.util.Tools.resolve("tinymce.util.I18n");const nx={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},sx="temporary-placeholder",rx=e=>()=>fe(e,sx).getOr("!not found!"),ax=(e,t)=>{const o=e.toLowerCase();if(ox.isRtl()){const e=((e,t)=>Ee(e,t)?e:((e,t)=>e+t)(e,t))(o,"-rtl");return be(t,e)?e:o}return o},ix=(e,t)=>fe(t,ax(e,t)),lx=(e,t)=>{const o=t();return ix(e,o).getOrThunk(rx(o))},cx=()=>Eh("add-focusable",[ia((e=>{un(e.element,"svg").each((e=>St(e,"focusable","false")))}))]),dx=(e,t,o,n)=>{var s,r;const a=(e=>!!ox.isRtl()&&be(nx,e))(t)?["tox-icon--flip"]:[],i=fe(o,ax(t,o)).or(n).getOrThunk(rx(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(a),innerHtml:i},behaviours:xa([...null!==(r=e.behaviours)&&void 0!==r?r:[],cx()])}},ux=(e,t,o,n=A.none())=>dx(t,e,o(),n),mx={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},gx=qm({name:"Notification",factory:e=>{const t=Pi("notification-text"),o=Hb({dom:Vb(`<p id=${t}>${tx(e.backstageProvider.translate(e.text))}</p>`),behaviours:xa([Th.config({})])}),n=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),s=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),r=Hb({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[n(0)]},s(0)],behaviours:xa([Th.config({})])}),a={updateProgress:(e,t)=>{e.getSystem().isConnected()&&r.getOpt(e).each((e=>{Th.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[n(t)]},s(t)])}))},updateText:(e,t)=>{if(e.getSystem().isConnected()){const n=o.get(e);Th.set(n,[yl(t)])}}},i=j([e.icon.toArray(),[e.level],A.from(mx[e.level]).toArray()]),l=Hb(Lb.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":e.backstageProvider.translate("Close")}},components:[ux("close",{tag:"span",classes:["tox-icon"]},e.iconProvider)],buttonBehaviours:xa([Wb.config({}),ev.config({...e.backstageProvider.tooltips.getConfig({tooltipText:e.backstageProvider.translate("Close")})})]),action:t=>{e.onAction(t)}})),c=((e,t,o)=>{const n=o(),s=$(e,(e=>be(n,ax(e,n))));return dx({tag:"div",classes:["tox-notification__icon"]},s.getOr(sx),n,A.none())})(i,0,e.iconProvider),d=[c,{dom:{tag:"div",classes:["tox-notification__body"]},components:[o.asSpec()],behaviours:xa([Th.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert","aria-labelledby":t},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},behaviours:xa([Wb.config({}),Ih.config({}),vh.config({mode:"special",onEscape:t=>(e.onAction(t),A.some(!0))})]),components:d.concat(e.progress?[r.asSpec()]:[]).concat([l.asSpec()]),apis:a}},configFields:[Rs("level","info",["success","error","warning","warn","info"]),ps("progress"),Cs("icon"),ps("onAction"),ps("text"),ps("iconProvider"),ps("backstageProvider")],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var px=(e,t,o,n)=>{const s=t.backstage.shared,r=()=>ze(""===e.queryCommandValue("ToggleView")?e.getContentAreaContainer():e.getContainer()),a=()=>{const e=Ko(r());return A.some(e)},i=e=>{a().each((t=>{V(e,(e=>{Lt(e.element,"width"),Kt(e.element)>t.width&&Mt(e.element,"width",t.width+"px")}))}))};return{open:(t,l,c)=>{const d=()=>{n.on((t=>{l();const o=c();(e=>{Th.remove(e,u),m()})(t),((t,o)=>{0===lt(t.element).length?((t,o)=>{yf.hide(t),n.clear(),o&&e.focus()})(t,o):((e,t)=>{t&&vh.focusIn(e)})(t,o)})(t,o)}))},u=kl(gx.sketch({text:t.text,level:F(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:t.icon,onAction:d,iconProvider:s.providers.icons,backstageProvider:s.providers}));if(n.isSet()){const e=Ol(u);n.on((t=>{Th.append(t,e),yf.reposition(t),u.hasConfigured(Ri)&&Ri.refresh(t),i(t.components())}))}else{const t=kl(yf.sketch({dom:{tag:"div",classes:["tox-notifications-container"],attributes:{"aria-label":"Notifications",role:"region"}},lazySink:s.getSink,fireDismissalEventInstead:{},...s.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}},inlineBehaviours:xa([vh.config({mode:"cyclic",selector:".tox-notification, .tox-notification a, .tox-notification button"}),Th.config({}),...Eb(e)&&s.header.isPositionedAtTop()?[]:[Ri.config({contextual:{lazyContext:()=>A.some(Ko(r())),fadeInClass:"tox-notification-container-dock-fadein",fadeOutClass:"tox-notification-container-dock-fadeout",transitionClass:"tox-notification-container-dock-transition"},modes:["top"],lazyViewport:t=>Nb(e,t.element).map((e=>({bounds:zb(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:qt(e.element).top})}))).getOrThunk((()=>({bounds:Zo(),optScrollEnv:A.none()})))})]])})),i=Ol(u),l={maxHeightFunction:Hc()},c={...s.anchors.banner(),overrides:l};n.set(t),o.add(t),yf.showWithinBounds(t,i,{anchor:c},a)}h(t.timeout)&&t.timeout>0&&Cf.setEditorTimeout(e,(()=>{d()}),t.timeout);const m=()=>{n.on((e=>{yf.reposition(e),e.hasConfigured(Ri)&&Ri.refresh(e),i(e.components())}))};return{close:d,reposition:m,text:e=>{gx.updateText(u,e)},settings:t,getEl:()=>u.element.dom,progressBar:{value:e=>{gx.updateProgress(u,e)}}}},close:e=>{e.close()},getArgs:e=>e.settings}};var hx;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(hx||(hx={}));var fx=hx;const bx="tox-menu-nav__js",vx="tox-collection__item",xx="tox-swatch",yx={normal:bx,color:xx},wx="tox-collection__item--enabled",Sx="tox-collection__item-icon",Cx="tox-collection__item-label",kx="tox-collection__item-caret",Ox="tox-collection__item--active",_x="tox-collection__item-container",Tx="tox-collection__item-container--row",Ex=e=>fe(yx,e).getOr(bx),Ax=e=>"color"===e?"tox-swatches":"tox-menu",Mx=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:Ax(e),tieredMenu:"tox-tiered-menu"}),Dx=e=>{const t=Mx(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:Ex(e)}},Bx=(e,t,o)=>{const n=Mx(o);return{tag:"div",classes:j([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},Ix=[df.parts.items({})],Fx=(e,t,o)=>{const n=Mx(o);return{dom:{tag:"div",classes:j([[n.tieredMenu]])},markers:Dx(o)}},Rx=y([Fs("type","text"),Cs("data"),Ds("inputAttributes",{}),Ds("inputStyles",{}),Ds("tag","input"),Ds("inputClasses",[]),Ti("onSetValue"),zs("fromInputValue",w),zs("toInputValue",w),Ds("styles",{}),Ds("eventOrder",{}),ju("inputBehaviours",[Gu,Ih]),Ds("selectOnFocus",!0)]),Nx=e=>xa([Ih.config({onFocus:e.selectOnFocus?t=>{const o=t.element,n=ul(o);"range"!==e.type&&o.dom.setSelectionRange(0,n.length)}:b})]),zx=e=>({...Nx(e),...Xu(e.inputBehaviours,[Gu.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:t=>e.fromInputValue(ul(t.element)),setValue:(t,o)=>{ul(t.element)!==o&&ml(t.element,e.toInputValue(o))}},onSetValue:e.onSetValue})])}),Lx=e=>({tag:e.tag,attributes:{type:e.type,...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),Vx=qm({name:"Input",configFields:Rx(),factory:(e,t)=>({uid:e.uid,dom:Lx(e),components:[],behaviours:zx(e),eventOrder:e.eventOrder})}),Hx=Pi("refetch-trigger-event"),Px=Pi("redirect-menu-item-interaction"),Ux="tox-menu__searcher",Wx=e=>mn(e.element,`.${Ux}`).bind((t=>e.getSystem().getByDom(t).toOptional())),$x=Wx,Gx=e=>({fetchPattern:Gu.getValue(e),selectionStart:e.element.dom.selectionStart,selectionEnd:e.element.dom.selectionEnd}),jx=e=>{const t=(e,t)=>(t.cut(),A.none()),o=(e,t)=>{const o={interactionEvent:t.event,eventType:t.event.raw.type};return Gr(e,Px,o),A.some(!0)},n="searcher-events";return{dom:{tag:"div",classes:[vx]},components:[Vx.sketch({inputClasses:[Ux,"tox-textfield"],inputAttributes:{...e.placeholder.map((t=>({placeholder:e.i18n(t)}))).getOr({}),type:"search","aria-autocomplete":"list"},inputBehaviours:xa([Eh(n,[Zr(cr(),(e=>{$r(e,Hx)})),Zr(ir(),((e,t)=>{"Escape"===t.event.raw.key&&t.stop()}))]),vh.config({mode:"special",onLeft:t,onRight:t,onSpace:t,onEnter:o,onEscape:o,onUp:o,onDown:o})]),eventOrder:{keydown:[n,vh.name()]}})]}},qx="tox-collection--results__js",Xx=e=>{var t;return e.dom?{...e,dom:{...e.dom,attributes:{...null!==(t=e.dom.attributes)&&void 0!==t?t:{},id:Pi("aria-item-search-result-id"),"aria-selected":"false"}}}:e},Yx=(e,t)=>o=>{const n=z(o,t);return L(n,(t=>({dom:e,components:t})))},Kx=(e,t)=>{const o=[];let n=[];return V(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(be(e.dom,"innerHtml")||e.components&&e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),L(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},Jx=(e,t,o)=>df.parts.items({preprocess:n=>{const s=L(n,o);return"auto"!==e&&e>1?Yx({tag:"div",classes:["tox-collection__group"]},e)(s):Kx(s,((e,o)=>"separator"===t[o].type))}}),Qx=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Jx(e,t,w)]}),Zx=e=>R(e,(e=>"icon"in e&&void 0!==e.icon)),ey=e=>(console.error(ls(e)),console.log(e),A.none()),ty=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[df.parts.items({preprocess:e=>Kx(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},oy=(e,t,o,n,s)=>{if("color"===s.menuType){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[df.parts.items({preprocess:"auto"!==e?Yx({tag:"div",classes:["tox-swatches__row"]},e):w})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("imageselector"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-image-selector-menu"]},components:[{dom:{tag:"div",classes:["tox-image-selector"]},components:[df.parts.items({preprocess:"auto"!==e?Yx({tag:"div",classes:["tox-image-selector__row"]},e):w})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType&&"auto"===n){const t=Qx(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType||"searchable"===s.menuType){const t="searchable"!==s.menuType?Qx(n,o):"search-with-field"===s.searchMode.searchMode?((e,t,o)=>{const n=Pi("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[jx({i18n:ox.translate,placeholder:o.placeholder}),{dom:{tag:"div",classes:[...1===e?["tox-collection--list"]:["tox-collection--grid"],qx],attributes:{id:n}},components:[Jx(e,t,Xx)]}]}})(n,o,s.searchMode):((e,t)=>{const o=Pi("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection",qx].concat(1===e?["tox-collection--list"]:["tox-collection--grid"]),attributes:{id:o}},components:[Jx(e,t,Xx)]}})(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[df.parts.items({preprocess:Yx({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:Bx(t,n,s.menuType),components:Ix,items:o}},ny=bs("type"),sy=bs("name"),ry=bs("label"),ay=bs("text"),iy=bs("title"),ly=bs("icon"),cy=bs("url"),dy=bs("value"),uy=xs("fetch"),my=xs("getSubmenuItems"),gy=xs("onAction"),py=xs("onItemAction"),hy=zs("onSetup",(()=>b)),fy=_s("name"),by=_s("text"),vy=_s("role"),xy=_s("icon"),yy=_s("tooltip"),wy=_s("label"),Sy=_s("shortcut"),Cy=Es("select"),ky=Ns("active",!1),Oy=Ns("borderless",!1),_y=Ns("enabled",!0),Ty=Ns("primary",!1),Ey=e=>Ds("columns",e),Ay=Ds("meta",{}),My=zs("onAction",b),Dy=e=>Fs("type",e),By=e=>us("name","name",Mn((()=>Pi(`${e}-name`))),Jn),Iy=Wn([ny,by]),Fy=Wn([Dy("autocompleteitem"),ky,_y,Ay,dy,by,xy]),Ry=[_y,yy,xy,by,hy,Fs("context","mode:design")],Ny=Wn([ny,gy,Sy].concat(Ry)),zy=e=>rs("toolbarbutton",Ny,e),Ly=[ky].concat(Ry),Vy=Wn(Ly.concat([ny,gy,Sy])),Hy=e=>rs("ToggleButton",Vy,e),Py=[zs("predicate",T),Rs("scope","node",["node","editor"]),Rs("position","selection",["node","selection","line"])],Uy=Ry.concat([Dy("contextformbutton"),Fs("align","end"),Ty,gy,ms("original",w)]),Wy=Ly.concat([Dy("contextformbutton"),Fs("align","end"),Ty,gy,ms("original",w)]),$y=Ry.concat([Dy("contextformbutton")]),Gy=Ly.concat([Dy("contextformtogglebutton")]),jy=[wy,Ss("commands",cs("type",{contextformbutton:Uy,contextformtogglebutton:Wy})),ks("launch",cs("type",{contextformbutton:$y,contextformtogglebutton:Gy})),zs("onInput",b),zs("onSetup",b)],qy=[...Py,...jy,vs("type",["contextform"]),zs("initValue",y("")),_s("placeholder")],Xy=[...Py,...jy,vs("type",["contextsliderform"]),zs("initValue",y(0)),zs("min",y(0)),zs("max",y(100))],Yy=[...Py,...jy,vs("type",["contextsizeinputform"]),zs("initValue",y({width:"",height:""}))],Ky=cs("type",{contextform:qy,contextsliderform:Xy,contextsizeinputform:Yy}),Jy=Ry.concat([Dy("contexttoolbarbutton")]),Qy=Wn([Dy("contexttoolbar"),Ms("launch",Jy),hs("items",Gn([Jn,qn([_s("name"),_s("label"),Ss("items",Jn)])]))].concat(Py)),Zy=e=>({name:e.name.getOrUndefined(),label:e.label.getOrUndefined(),items:e.items}),ew=[ny,bs("src"),_s("alt"),Ls("classes",[],Jn)],tw=Wn(ew),ow=[ny,ay,fy,Ls("classes",["tox-collection__item-label"],Jn)],nw=Wn(ow),sw=Hn((()=>os("type",{cardimage:tw,cardtext:nw,cardcontainer:rw}))),rw=Wn([ny,Fs("direction","horizontal"),Fs("align","left"),Fs("valign","middle"),Ss("items",sw)]),aw=[_y,by,vy,Sy,us("value","value",Mn((()=>Pi("menuitem-value"))),Xn()),Ay,Fs("context","mode:design")];const iw=Wn([ny,wy,Ss("items",sw),hy,My].concat(aw)),lw=Wn([ny,ky,xy,wy].concat(aw)),cw=[ny,bs("fancytype"),My],dw=[Ds("initData",{})].concat(cw),uw=[Es("select"),Vs("initData",{},[Ns("allowCustomColors",!0),Fs("storageKey","default"),As("colors",Xn())])].concat(cw),mw=[Es("select"),ys("initData",[fs("columns"),Ls("items",[],Xn())])].concat(cw),gw=cs("fancytype",{inserttable:dw,colorswatch:uw,imageselect:mw}),pw=Wn([ny,ky,cy,wy,yy].concat(aw)),hw=Wn([ny,ky,ly,ry,yy,dy].concat(aw)),fw=Wn([ny,hy,My,xy].concat(aw)),bw=Wn([ny,my,hy,xy].concat(aw)),vw=Wn([ny,xy,ky,hy,gy].concat(aw)),xw=(e,t,o)=>{const n=Od(e.element,"."+o);if(n.length>0){const e=G(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return A.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return A.none()},yw=e=>((e,t)=>xa([Eh(e,t)]))(Pi("unnamed-events"),e),ww=e=>mg.config({disabled:e,disableClass:"tox-collection__item--state-disabled"}),Sw=e=>mg.config({disabled:e}),Cw=e=>mg.config({disabled:e,disableClass:"tox-tbtn--disabled"}),kw=e=>mg.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),Ow=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},_w=(e,t)=>ia((o=>{p(e.onBeforeSetup)&&e.onBeforeSetup(o),Ow(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),Tw=(e,t)=>la((o=>Ow(e,o)(t.get()))),Ew=(e,t,o)=>la((n=>(o.set(Gu.getValue(n)),Ow(e,n)(t.get())))),Aw="silver.uistate",Mw="setDisabled",Dw="init",Bw=["switchmode",Dw],Iw=(e,t)=>{const o=e.mainUi.outerContainer,n=[e.mainUi.mothership,...e.uiMotherships];t===Mw&&V(n,(e=>{e.broadcastOn([_u()],{target:o.element})})),V(n,(e=>{e.broadcastOn([Aw],t)}))},Fw=(e,t)=>{e.on("init SwitchMode",(e=>{Iw(t,e.type)})),e.on("DisabledStateChange",(o=>{if(!o.isDefaultPrevented()){const n=o.state?Mw:Dw;Iw(t,n),o.state||e.nodeChanged()}})),e.on("NodeChange",(o=>{const n=e.ui.isEnabled()?o.type:Mw;Iw(t,n)})),Mf(e)&&e.mode.set("readonly")},Rw=e=>uc.config({channels:{[Aw]:{onReceive:(t,o)=>{if(o===Mw||"setEnabled"===o)return void mg.set(t,o===Mw);const{contextType:n,shouldDisable:s}=e();("mode"!==n||F(Bw,o))&&mg.set(t,s)}}}}),Nw=(e,t)=>da(((o,n)=>{Ow(e,o)(e.onAction),e.triggersSubmenu||t!==fx.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&$r(o,_r()),n.stop())})),zw={[Sr()]:["disabling","alloy.base.behaviour","toggling","item-events"]},Lw=ye,Vw=(e,t,o,n)=>{const s=en(b);return{type:"item",dom:t.dom,components:Lw(t.optComponents),data:e.data,eventOrder:zw,hasSubmenu:e.triggersSubmenu,itemBehaviours:xa([Eh("item-events",[Nw(e,o),_w(e,s),Tw(e,s)]),ww((()=>!e.enabled||n.checkUiComponentContext(e.context).shouldDisable)),Rw((()=>n.checkUiComponentContext(e.context))),Th.config({})].concat(e.itemBehaviours))}},Hw=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),Pw=e=>new Promise(((t,o)=>{const n=()=>{r(),t(e)},s=[Bc(e,"load",n),Bc(e,"error",(()=>{r(),o("Unable to load data from image: "+e.dom.src)}))],r=()=>V(s,(e=>e.unbind()));e.dom.complete&&n()})),Uw=(e,t)=>{var o,n;const s=Re("div");return Ma(s,"tox-image-selector-loading-spinner"),{dom:{tag:e.tag,attributes:null!==(o=e.attributes)&&void 0!==o?o:{},classes:e.classes},components:[{dom:{tag:"div",classes:["tox-image-selector-image-wrapper"]},components:[{dom:{tag:"img",attributes:{src:t},classes:["tox-image-selector-image-img"]}}]},...e.checkMark.toArray()],behaviours:xa([...null!==(n=e.behaviours)&&void 0!==n?n:[],Eh("render-image-events",[ia((e=>{var t;t=e.element,Ma(t,"tox-image-selector-loading-spinner-wrapper"),zo(t,s),mn(e.element,"img").each((t=>{Pw(t).catch((e=>{console.error(e)})).finally((()=>{(e=>{Ba(e,"tox-image-selector-loading-spinner-wrapper"),Ho(s)})(e.element)}))}))}))])])}},Ww=e=>{const t=_f.os.isMacOS()||_f.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=L(n,(e=>{const t=e.toLowerCase().trim();return be(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},$w=(e,t,o=[Sx])=>ux(e,{tag:"div",classes:o},t),Gw=e=>({dom:{tag:"div",classes:[Cx]},components:[yl(ox.translate(e))]}),jw=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),qw=(e,t)=>({dom:{tag:"div",classes:[Cx]},components:[{dom:{tag:e.tag,styles:e.styles},components:[yl(ox.translate(t))]}]}),Xw=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[yl(Ww(e))]}),Yw=e=>$w("checkmark",e,["tox-collection__item-checkmark"]),Kw=(e,t)=>{const o=e.map((e=>({attributes:{id:Pi("menu-item"),"aria-label":ox.translate(e)}}))).getOr({});return{tag:"div",classes:[bx,vx].concat(t),...o}},Jw=e=>({dom:{tag:"label"},components:[yl(e)]}),Qw=(e,t,o,n=A.none())=>"color"===e.presets?((e,t,o)=>{const n=e.value,s=e.iconContent.map((e=>((e,t,o)=>{const n=t();return ix(e,n).or(o).getOrThunk(rx(n))})(e,t.icons,o))),r=e.ariaLabel.map((e=>({"aria-label":t.translate(e),"data-mce-name":e}))).getOr({});return{dom:(()=>{const e=xx,t=s.getOr(""),o={tag:"div",attributes:r,classes:[e]};return"custom"===n?{...o,tag:"button",classes:[...o.classes,"tox-swatches__picker-btn"],innerHtml:t}:"remove"===n?{...o,classes:[...o.classes,"tox-swatch--remove"],innerHtml:t}:g(n)?{...o,attributes:{...o.attributes,"data-mce-color":n},styles:{"background-color":n},innerHtml:t}:o})(),optComponents:[]}})(e,t,n):"img"===e.presets?(e=>{var t,o;return{dom:Kw(e.ariaLabel,["tox-collection__item-image-selector"]),optComponents:[A.some((t=e.iconContent.getOrDie(),o={tag:"div",classes:["tox-collection__item-image"],checkMark:e.checkMark},Uw(o,t))),e.labelContent.map(Jw)]}})(e):((e,t,o,n)=>{const s={tag:"div",classes:[Sx]},r=o?e.iconContent.map((e=>ux(e,s,t.icons,n))).orThunk((()=>A.some({dom:s}))):A.none(),a=e.checkMark,i=A.from(e.meta).fold((()=>Gw),(e=>be(e,"style")?C(qw,e.style):Gw)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>A.some(jw(e,[Cx]))));return{dom:Kw(e.ariaLabel,[]),optComponents:[r,l,e.shortcutContent.map(Xw),a,e.caret,e.labelContent.map(Jw)]}})(e,t,o,n),Zw=(e,t,o)=>fe(e,"tooltipWorker").map((e=>[ev.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:Hc}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{ev.setComponents(t,[wl({element:ze(e)})])}))}})])).getOrThunk((()=>o.map((e=>[ev.config({...t.providers.tooltips.getConfig({tooltipText:e}),mode:"follow-highlight"})])).getOr([]))),eS=(e,t)=>{const o=(e=>kf.DOM.encode(e))(ox.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},tS=(e,t)=>L(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":Tx,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[_x,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,tS(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>F(t.cardText.highlightOn,e))),n=o?A.from(t.cardText.matchText).getOr(""):"";return jw(eS(e.text,n),e.classes)}})),oS=(e,t,o,n,s,r,a,i=!0)=>{const l=Qw({presets:o,textContent:t?e.text:A.none(),htmlContent:A.none(),labelContent:e.label,ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:A.none(),checkMark:t?A.some(Yw(a.icons)):A.none(),caret:A.none(),value:e.value},a,i),c=e.text.filter(y(!t)).map((e=>ev.config(a.tooltips.getConfig({tooltipText:a.translate(e)}))));return En(Vw({context:e.context,data:Hw(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Ph.set(e,t)},isActive:()=>Ph.isOn(e),isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[...c.toArray()]},l,r,a),{toggling:{toggleClass:wx,toggleOnExecute:!1,selected:e.active,exclusive:!0}})},nS=_m(of(),nf()),sS=e=>({value:lS(e)}),rS=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,aS=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,iS=e=>rS.test(e)||aS.test(e),lS=e=>Oe(e,"#").toUpperCase(),cS=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},dS=e=>{const t=cS(e.red)+cS(e.green)+cS(e.blue);return sS(t)},uS=Math.min,mS=Math.max,gS=Math.round,pS=/^\s*rgb\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*\)\s*$/i,hS=/^\s*rgba\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*((?:\d?\.\d+|\d+)%?)\s*\)\s*$/i,fS=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),bS=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},vS=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=mS(0,uS(r,1)),a=mS(0,uS(a,1)),0===r)return t=o=n=gS(255*a),fS(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=gS(255*(t+d)),o=gS(255*(o+d)),n=gS(255*(n+d)),fS(t,o,n,1)},xS=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(rS,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=aS.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return fS(o,n,s,1)},yS=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return fS(s,r,a,i)},wS=e=>{const t=pS.exec(e);if(null!==t)return A.some(yS(t[1],t[2],t[3],"1"));const o=hS.exec(e);return null!==o?A.some(yS(o[1],o[2],o[3],o[4])):A.none()},SS=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,CS=fS(255,0,0,1),kS=(e,t)=>{e.dispatch("ResizeContent",t)},OS=(e,t)=>{e.dispatch("TextColorChange",t)},_S=(e,t)=>e.dispatch("ResolveName",{name:t.nodeName.toLowerCase(),target:t}),TS=e=>{e.dispatch("ContextToolbarClose")},ES=(e,t)=>()=>{e(),t()},AS=e=>DS(e,"NodeChange",(t=>{t.setEnabled(e.selection.isEditable())})),MS=(e,t)=>o=>{const n=AS(e)(o),s=((e,t)=>o=>{const n=on(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}})(e,t)(o);return()=>{n(),s()}},DS=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},BS=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},IS=(e,t)=>()=>e.execCommand(t);var FS=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const RS={},NS=e=>fe(RS,e).getOrThunk((()=>{const t=`tinymce-custom-colors-${e}`,o=FS.getItem(t);if(m(o)){const e=FS.getItem("tinymce-custom-colors");FS.setItem(t,g(e)?e:"[]")}const n=((e,t=10)=>{const o=FS.getItem(e),n=r(o)?JSON.parse(o):[],s=t-(a=n).length<0?a.slice(0,t):a;var a;const i=e=>{s.splice(e,1)};return{add:o=>{((e,t)=>{const o=I(e,t);return-1===o?A.none():A.some(o)})(s,o).each(i),s.unshift(o),s.length>t&&s.pop(),FS.setItem(e,JSON.stringify(s))},state:()=>s.slice(0)}})(t,10);return RS[e]=n,n})),zS=(e,t)=>{NS(e).add(t)},LS=(e,t,o)=>({hue:e,saturation:t,value:o}),VS=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,LS(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,LS(Math.round(t),Math.round(100*o),Math.round(100*n)))},HS=e=>dS(vS(e)),PS="forecolor",US="hilitecolor",WS=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:e[o],icon:"checkmark",type:"choiceitem"});return t},$S=e=>t=>t.options.get(e),GS="#000000",jS=(e,t)=>t===PS&&e.options.isSet("color_map_foreground")?$S("color_map_foreground")(e):t===US&&e.options.isSet("color_map_background")?$S("color_map_background")(e):e.options.isSet("color_map_raw")?$S("color_map_raw")(e):$S("color_map")(e),qS=(e,t="default")=>Math.max(5,Math.ceil(Math.sqrt(jS(e,t).length))),XS=(e,t)=>{const o=$S("color_cols")(e),n=qS(e,t);return o===qS(e)?n:o},YS=(e,t="default")=>Math.round(t===PS?$S("color_cols_foreground")(e):t===US?$S("color_cols_background")(e):$S("color_cols")(e)),KS=$S("custom_colors"),JS=$S("color_default_foreground"),QS=$S("color_default_background"),ZS=(e,t)=>{const o=ze(e.selection.getStart()),n="hilitecolor"===t?Gs(o,(e=>{if(Ge(e)){const t=It(e,"background-color");return Ce(wS(t).exists((e=>0!==e.alpha)),t)}return A.none()})).getOr("rgba(0, 0, 0, 0)"):It(o,"color");return wS(n).map((e=>"#"+dS(e).value))},eC=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},tC=(e,t,o,n)=>{"custom"===o?dC(e)((o=>{o.each((o=>{zS(t,o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),ZS(e,t).getOr(GS)):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},oC=(e,t,o)=>e.concat((e=>L(NS(e).state(),(e=>({type:"choiceitem",text:e,icon:"checkmark",value:e}))))(t).concat(eC(o))),nC=(e,t,o)=>n=>{n(oC(e,t,o))},sC=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},rC=(e,t)=>{e.setTooltip(t)},aC=(e,t)=>o=>{const n=ZS(e,t);return xe(n,o.toUpperCase())},iC=(e,t,o)=>{if(De(o))return"forecolor"===t?"Text color":"Background color";const n="forecolor"===t?"Text color {0}":"Background color {0}",s=oC(jS(e,t),t,!1),r=$(s,(e=>e.value===o)).getOr({text:""}).text;return e.translate([n,e.translate(r)])},lC=(e,t,o,n)=>{e.ui.registry.addSplitButton(t,{tooltip:iC(e,o,n.get()),presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:aC(e,o),columns:YS(e,o),fetch:nC(jS(e,o),o,KS(e)),onAction:t=>{tC(e,o,n.get(),b)},onItemAction:(s,r)=>{tC(e,o,r,(o=>{n.set(o),OS(e,{name:t,color:o})}))},onSetup:s=>{sC(s,t,n.get());const r=n=>{n.name===t&&(sC(s,n.name,n.color),rC(s,iC(e,o,n.color)))};return e.on("TextColorChange",r),ES(AS(e)(s),(()=>{e.off("TextColorChange",r)}))}})},cC=(e,t,o,n,s)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",onSetup:n=>(rC(n,iC(e,o,s.get())),sC(n,t,s.get()),AS(e)(n)),getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",select:aC(e,o),initData:{storageKey:o},onAction:n=>{tC(e,o,n.value,(o=>{s.set(o),OS(e,{name:t,color:o})}))}}]})},dC=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(A.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(A.none())}})},uC=(e,t,o,n,s,r,a,i)=>{const l=Zx(t),c=mC(t,o,n,"color"!==s?"normal":"color",r,a,i);return oy(e,l,c,n,{menuType:s})},mC=(e,t,o,n,s,r,a)=>ye(L(e,(i=>{return"choiceitem"===i.type?(l=i,rs("choicemenuitem",lw,l)).fold(ey,(i=>A.some(oS(i,1===o,n,t,r(i.value),s,a,Zx(e))))):"imageitem"===i.type?(e=>rs("imagemenuitem",pw,e))(i).fold(ey,(e=>A.some(((e,t,o,n,s)=>{const r=Qw({presets:"img",textContent:A.none(),htmlContent:A.none(),ariaLabel:e.tooltip,iconContent:A.some(e.url),labelContent:e.label,shortcutContent:A.none(),checkMark:A.some(Yw(s.icons)),caret:A.none(),value:e.value},s,!0),a=e.tooltip.map((e=>ev.config(s.tooltips.getConfig({tooltipText:s.translate(e)}))));return En(Vw({context:e.context,data:Hw(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Ph.set(e,t)},isActive:()=>Ph.isOn(e),isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t)}),onAction:o=>{t(e.value),o.setActive(!0)},onSetup:e=>(e.setActive(o),b),triggersSubmenu:!1,itemBehaviours:[...a.toArray()]},r,n,s),{toggling:{toggleClass:wx,toggleOnExecute:!1,selected:e.active,exclusive:!0}})})(e,t,r(e.value),s,a)))):"resetimage"===i.type?(e=>rs("resetimageitem",hw,e))(i).fold(ey,(i=>A.some(oS({...i,type:"choiceitem",text:i.tooltip,icon:A.some(i.icon),label:A.some(i.label)},1===o,n,t,r(i.value),s,a,Zx(e))))):A.none();var l}))),gC=(e,t)=>{const o=Dx(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+{color:"tox-swatches__row",imageselector:"tox-image-selector__row",listpreview:"tox-collection__group",normal:"tox-collection__group"}[t],previousSelector:e=>"color"===t?mn(e.element,"[aria-checked=true]"):A.none()}},pC=Pi("cell-over"),hC=Pi("cell-execute"),fC=(e,t,o)=>{const n=o=>Gr(o,hC,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return kl({dom:{tag:"div",attributes:{role:"button","aria-label":o}},behaviours:xa([Eh("insert-table-picker-cell",[Zr(sr(),Ih.focus),Zr(Sr(),n),Zr(ur(),s),Zr(kr(),s)]),Ph.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),Ih.config({onFocus:o=>Gr(o,pC,{row:e,col:t})})])})},bC=e=>q(e,(e=>L(e,Ol))),vC=(e,t)=>yl(`${t}x${e}`),xC={inserttable:(e,t)=>{const o=(e=>(t,o)=>e.shared.providers.translate(["{0} columns, {1} rows",o,t]))(t),n=(e=>{const t=[];for(let o=0;o<10;o++){const n=[];for(let t=0;t<10;t++){const s=e(o+1,t+1);n.push(fC(o,t,s))}t.push(n)}return t})(o),s=vC(0,0),r=Hb({dom:{tag:"span",classes:["tox-insert-table-picker__label"]},components:[s],behaviours:xa([Th.config({})])});return{type:"widget",data:{value:Pi("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[nS.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:bC(n).concat(r.asSpec()),behaviours:xa([Eh("insert-table-picker",[ia((e=>{Th.set(r.get(e),[s])})),na(pC,((e,t,o)=>{const{row:s,col:a}=o.event;((e,t,o)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)Ph.set(e[n][s],n<=t&&s<=o)})(n,s,a),Th.set(r.get(e),[vC(s+1,a+1)])})),na(hC,((t,o,n)=>{const{row:s,col:r}=n.event;$r(t,_r()),e.onAction({numRows:s+1,numColumns:r+1})}))]),vh.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>oC(t.colorinput.getColors(e.initData.storageKey),e.initData.storageKey,o)),(e=>e.concat(eC(o))))})(e,t),n=t.colorinput.getColorCols(e.initData.storageKey),s="color",r={...uC(Pi("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,fx.CLOSE_ON_EXECUTE,e.select.getOr(T),t.shared.providers),markers:Dx(s),movement:gC(n,s),showMenuRole:!1};return{type:"widget",data:{value:Pi("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[nS.widget(df.sketch(r))]}},imageselect:(e,t)=>{const o="imageselector",n=e.initData.columns,s={...uC(Pi("menu-value"),e.initData.items,(t=>{e.onAction({value:t})}),n,o,fx.CLOSE_ON_EXECUTE,e.select.getOr(T),t.shared.providers),markers:Dx(o),movement:gC(n,o),showMenuRole:!1};return{type:"widget",data:{value:Pi("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem","tox-collection--toolbar"]},autofocus:!0,components:[nS.widget(df.sketch(s))]}}},yC=e=>({type:"separator",dom:{tag:"div",classes:[vx,"tox-collection__group-heading"]},components:e.text.map(yl).toArray()});var wC=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n),getExistingCoupled:(e,t,o,n)=>o.getExisting(e,t,n)}),SC=[hs("others",ss(bn.value,Xn()))],CC=Object.freeze({__proto__:null,init:()=>{const e={},t=(t,o)=>{if(0===re(t.others).length)throw new Error("Cannot find any known coupled components");return fe(e,o)},o=y({});return va({readState:o,getExisting:(e,o,n)=>t(o,n).orThunk((()=>(fe(o.others,n).getOrDie("No information found for coupled component: "+n),A.none()))),getOrCreate:(o,n,s)=>t(n,s).getOrThunk((()=>{const t=fe(n.others,s).getOrDie("No information found for coupled component: "+s)(o),r=o.getSystem().build(t);return e[s]=r,r}))})}});const kC=wa({fields:SC,name:"coupling",apis:wC,state:CC}),OC=e=>{let t=A.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=A.some(e),V(o,r),o=[])})),{get:n,map:e=>OC((t=>{n((o=>{t(e(o))}))})),isReady:s}},_C={nu:OC,pure:e=>OC((t=>{t(e)}))},TC=e=>{setTimeout((()=>{throw e}),0)},EC=e=>{const t=t=>{e().then(t,TC)};return{map:t=>EC((()=>e().then(t))),bind:t=>EC((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>EC((()=>e().then((()=>t.toPromise())))),toLazy:()=>_C.nu(t),toCached:()=>{let t=null;return EC((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},AC=e=>EC((()=>new Promise(e))),MC=e=>EC((()=>Promise.resolve(e))),DC=y("sink"),BC=y(wm({name:DC(),overrides:y({dom:{tag:"div"},behaviours:xa([Zd.config({useFixed:E})]),events:Kr([sa(ir()),sa(er()),sa(ur())])})})),IC=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},FC=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=zC(n,e);return i.map((t=>t.bind((t=>{const i=t.menus[t.primary];return A.from(i).each((t=>{e.listRole.each((e=>{t.role=e}))})),A.from(xf.sketch({...r.menu(),uid:Xi(""),data:t,highlightOnOpen:a,onOpenMenu:(e,t)=>{const n=l().getOrDie();Zd.position(n,t,{anchor:o}),Ou.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();Zd.position(n,o,{anchor:{type:"submenu",item:t}}),Ou.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();Zd.position(s,t,{anchor:o}),V(n,(e=>{Zd.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(Ih.focus(n),Ou.close(s),A.some(!0))}))}))))})(e,t,IC(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{Ou.isOpen(n)&&Ou.close(n)}),(e=>{Ou.cloak(n),Ou.open(n,e),r(n)})),n)))},RC=(e,t,o,n,s,r,a)=>(Ou.close(n),MC(n)),NC=(e,t,o,n,s,r)=>{const a=kC.getCoupled(o,"sandbox");return(Ou.isOpen(a)?RC:FC)(e,t,o,a,n,s,r)},zC=(e,t)=>e.getSystem().getByUid(t.uid+"-"+DC()).map((e=>()=>bn.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>bn.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),LC=e=>{Ou.getState(e).each((e=>{xf.repositionMenus(e)}))},VC=(e,t,o)=>{const n=El(),s=zC(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id}},behaviours:Ku(e.sandboxBehaviours,[Gu.config({store:{mode:"memory",initialValue:t}}),Ou.config({onOpen:(s,r)=>{const a=IC(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=Qm.getCurrent(t).getOr(t),s=Kt(e.element);o?Mt(n.element,"min-width",s+"px"):((e,t)=>{Yt.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,r)=>{n.unlink(t.element),s().getOr(r).element.dom.dispatchEvent(new window.FocusEvent("focusout")),void 0!==o&&void 0!==o.onClose&&o.onClose(e,r)},isPartOf:(e,o,n)=>Al(o,n)||Al(t,n),getAttachPoint:()=>s().getOrDie()}),Qm.config({find:e=>Ou.getState(e).bind((e=>Qm.getCurrent(e)))}),uc.config({channels:{...Mu({isExtraPart:T}),...Bu({doReposition:LC})}})])}},HC=e=>{const t=kC.getCoupled(e,"sandbox");LC(t)},PC=()=>[Ds("sandboxClasses",[]),Yu("sandboxBehaviours",[Qm,uc,Ou,Gu])],UC=y([ps("dom"),ps("fetch"),Ti("onOpen"),Ei("onExecute"),Ds("getHotspot",A.some),Ds("getAnchorOverrides",y({})),Qc(),ju("dropdownBehaviours",[Ph,kC,vh,Ih]),ps("toggleClass"),Ds("eventOrder",{}),Cs("lazySink"),Ds("matchWidth",!1),Ds("useMinWidth",!1),Cs("role"),Cs("listRole")].concat(PC())),WC=y([ym({schema:[ki(),Ds("fakeFocus",!1)],name:"menu",defaults:e=>({onExecute:e.onExecute})}),BC()]),$C=Xm({name:"Dropdown",configFields:UC(),partFields:WC(),factory:(e,t,o,n)=>{const s=e=>{Ou.getState(e).each((e=>{xf.highlightPrimary(e)}))},r=(t,o,s)=>NC(e,w,t,n,o,s),a={expand:e=>{Ph.isOn(e)||r(e,b,bf.HighlightNone).get(b)},open:e=>{Ph.isOn(e)||r(e,b,bf.HighlightMenuAndItem).get(b)},refetch:t=>kC.getExistingCoupled(t,"sandbox").fold((()=>r(t,b,bf.HighlightMenuAndItem).map(b)),(o=>FC(e,w,t,o,n,b,bf.HighlightMenuAndItem).map(b))),isOpen:Ph.isOn,close:e=>{Ph.isOn(e)&&r(e,b,bf.HighlightMenuAndItem).get(b)},repositionMenus:e=>{Ph.isOn(e)&&HC(e)}},i=(e,t)=>(jr(e),A.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:Xu(e.dropdownBehaviours,[Ph.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),kC.config({others:{sandbox:t=>VC(e,t,{onOpen:()=>Ph.on(t),onClose:()=>Ph.off(t)})}}),vh.config({mode:"special",onSpace:i,onEnter:i,onDown:(e,t)=>{if($C.isOpen(e)){const t=kC.getCoupled(e,"sandbox");s(t)}else $C.open(e);return A.some(!0)},onEscape:(e,t)=>$C.isOpen(e)?($C.close(e),A.some(!0)):A.none()}),Ih.config({})]),events:$h(A.some((e=>{r(e,s,bf.HighlightMenuAndItem).get(b)}))),eventOrder:{...e.eventOrder,[Sr()]:["disabling","toggling","alloy.base.behaviour"]},apis:a,domModification:{attributes:{"aria-haspopup":e.listRole.getOr("true"),...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:fe(e.dom,"attributes").bind((e=>fe(e,"type"))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),refetch:(e,t)=>e.refetch(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),GC=(e,t,o)=>{$x(e).each((e=>{var n;((e,t)=>{Ot(t.element,"id").each((t=>St(e.element,"aria-activedescendant",t)))})(e,o),(Ia((n=t).element,qx)?A.some(n.element):mn(n.element,"."+qx)).each((t=>{Ot(t,"id").each((t=>St(e.element,"aria-controls",t)))}))})),St(o.element,"aria-selected","true")},jC=(e,t,o)=>{St(o.element,"aria-selected","false")},qC=e=>kC.getExistingCoupled(e,"sandbox").bind(Wx).map(Gx).map((e=>e.fetchPattern)).getOr("");var XC;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(XC||(XC={}));const YC=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:A.none(),icon:e.text.isSome()?A.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,rs("menuitem",fw,i)).fold(ey,(e=>A.some(((e,t,o,n=!0)=>{const s=Qw({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),labelContent:A.none(),ariaLabel:e.text,caret:A.none(),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return Vw({context:e.context,data:Hw(e),getApi:e=>({isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>rs("nestedmenuitem",bw,e))(e).fold(ey,(e=>A.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,$w("chevron-down",a,[kx])):(e=>$w("chevron-right",e,[kx]))(o.icons);var a;const i=Qw({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,labelContent:A.none(),caret:A.some(r),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return Vw({context:e.context,data:Hw(e),getApi:e=>({isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t),setIconFill:(t,o)=>{mn(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{St(e,"fill",o)}))},setTooltip:t=>{const n=o.translate(t);St(e.element,"aria-label",n)}}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>rs("togglemenuitem",vw,e))(e).fold(ey,(e=>A.some(((e,t,o,n=!0)=>{const s=Qw({iconContent:e.icon,textContent:e.text,htmlContent:A.none(),labelContent:A.none(),ariaLabel:e.text,checkMark:A.some(Yw(o.icons)),caret:A.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return En(Vw({context:e.context,data:Hw(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Ph.set(e,t)},isActive:()=>Ph.isOn(e),isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:wx,toggleOnExecute:!1,selected:e.active},role:e.role.getOrUndefined()})})(a(e),t,r,n))));case"separator":return(e=>rs("separatormenuitem",Iy,e))(e).fold(ey,(e=>A.some(yC(e))));case"fancymenuitem":return(e=>rs("fancymenuitem",gw,e))(e).fold(ey,(e=>((e,t)=>fe(xC,e.fancytype).map((o=>o(e,t))))(e,o)));default:return console.error("Unknown item in general menu",e),A.none()}var i},KC=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||Zx(e);return ye(L(e,(e=>{switch(e.type){case"separator":return(n=e,rs("Autocompleter.Separator",Iy,n)).fold(ey,(e=>A.some(yC(e))));case"cardmenuitem":return(e=>rs("cardmenuitem",iw,e))(e).fold(ey,(e=>A.some(((e,t,o,n)=>{const s={dom:Kw(e.label,[]),optComponents:[A.some({dom:{tag:"div",classes:[_x,Tx]},components:tS(e.items,n)})]};return Vw({context:"mode:design",data:Hw({text:A.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>{mg.set(e,!t),V(Od(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(mg)&&mg.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:A.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:Zw(e.meta,r,A.none()),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>rs("Autocompleter.Item",Fy,e))(e).fold(ey,(e=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Qw({presets:n,textContent:A.none(),htmlContent:o?e.text.map((e=>eS(e,t))):A.none(),ariaLabel:e.text,labelContent:A.none(),iconContent:e.icon,shortcutContent:A.none(),checkMark:A.none(),caret:A.none(),value:e.value},a.providers,i,e.icon),c=e.text.filter((e=>!o&&""!==e));return Vw({context:"mode:design",data:Hw(e),enabled:e.enabled,getApi:y({}),onAction:t=>s(e.value,e.meta),onSetup:y(b),triggersSubmenu:!1,itemBehaviours:Zw(e,a,c)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},JC=(e,t,o,n,s,r)=>{const a=Zx(t),i=ye(L(t,(e=>{const t=e=>YC(e,o,n,(e=>s?!be(e,"text"):a)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)}))),l=(e=>"no-search"===e.searchMode?{menuType:"normal"}:{menuType:"searchable",searchMode:e})(r);return(s?ty:oy)(e,a,i,1,l)},QC=e=>xf.singleData(e.value,e),ZC=e=>gd(ze(e.startContainer),e.startOffset,ze(e.endContainer),e.endOffset),ek=(e,t)=>{const o=Pi("autocompleter"),n=en(!1),s=en(!1),r=nn(),a=kl(yf.sketch({dom:{tag:"div",classes:["tox-autocompleter"],attributes:{id:o}},components:[],fireDismissalEventInstead:{},inlineBehaviours:xa([Eh("dismissAutocompleter",[Zr(Rr(),(()=>u())),Zr(Ur(),((t,o)=>{Ot(o.event.target,"id").each((t=>St(ze(e.getBody()),"aria-activedescendant",t)))}))])]),lazySink:t.getSink})),i=()=>yf.isOpen(a),l=s.get,c=()=>{if(i()){yf.hide(a),e.dom.remove(o,!1);const t=ze(e.getBody());Ot(t,"aria-owns").filter((e=>e===o)).each((()=>{Tt(t,"aria-owns"),Tt(t,"aria-activedescendant")}))}},d=()=>yf.getContent(a).bind((e=>ee(e.components(),0))),u=()=>e.execCommand("mceAutocompleterClose"),m=s=>{const i=(o=>{const s=se(o,(e=>A.from(e.columns))).getOr(1);return q(o,(o=>{const a=o.items;return KC(a,o.matchText,((t,s)=>{const a={hide:()=>u(),reload:t=>{c(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};e.execCommand("mceAutocompleterRefreshActiveRange"),r.get().each((e=>{n.set(!0),o.onAction(a,e,t,s),n.set(!1)}))}),s,fx.BUBBLE_TO_SANDBOX,t,o.highlightOn)}))})(s);i.length>0?(((t,o)=>{const n=se(t,(e=>A.from(e.columns))).getOr(1);yf.showMenuAt(a,{anchor:{type:"selection",getSelection:()=>r.get().map(ZC),root:ze(e.getBody())}},((e,t,o,n)=>{const s=gC(t,n),r=Dx(n);return{data:QC({...e,movement:s,menuBehaviours:yw("auto"!==t?[]:[ia(((e,t)=>{xw(e,4,r.item).each((({numColumns:t,numRows:o})=>{vh.setGridSize(e,o,t)}))}))])}),menu:{markers:Dx(n),fakeFocus:o===XC.ContentFocus}}})(oy("autocompleter-value",!0,o,n,{menuType:"normal"}),n,XC.ContentFocus,"normal")),d().each(Sg.highlightFirst)})(s,i),St(ze(e.getBody()),"aria-owns",o),e.inline||g()):c()},g=()=>{e.dom.get(o)&&e.dom.remove(o,!1);const t=e.getDoc().documentElement,n=e.selection.getNode(),s=(e=>gi(e,!0))(a.element);Dt(s,{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px",top:`${n.offsetTop}px`,left:`${n.offsetLeft}px`}),e.dom.add(t,s.dom),mn(s,'[role="menu"]').each((e=>{Lt(e,"position"),Lt(e,"max-height")}))};e.on("AutocompleterStart",(({lookupData:e})=>{s.set(!0),n.set(!1),m(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>m(e))),e.on("AutocompleterUpdateActiveRange",(({range:e})=>r.set(e))),e.on("AutocompleterEnd",(()=>{c(),s.set(!1),n.set(!1),r.clear()}));((e,t)=>{const o=(e,t)=>{Gr(e,ir(),{raw:t})},n=()=>e.getMenu().bind(Sg.getHighlighted);t.on("keydown",(t=>{const s=t.which;e.isActive()&&(e.isMenuOpen()?13===s?(n().each(jr),t.preventDefault()):40===s?(n().fold((()=>{e.getMenu().each(Sg.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==s&&38!==s&&39!==s||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==s&&38!==s&&40!==s||e.cancelIfNecessary())})),t.on("NodeChange",(()=>{!e.isActive()||e.isProcessingAction()||t.queryCommandState("mceAutoCompleterInRange")||e.cancelIfNecessary()}))})({cancelIfNecessary:u,isMenuOpen:i,isActive:l,isProcessingAction:n.get,getMenu:d},e)},tk=(e,t,o)=>gn(e,t,o).isSome(),ok=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},nk=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?A.none():A.some(t.touches[0])},sk=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=nn(),o=en(!1),n=ok((t=>{e.triggerEvent(Or(),t),o.set(!0)}),400),s=Us([{key:Ks(),value:e=>(nk(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),A.none())},{key:Js(),value:e=>(n.cancel(),nk(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),A.none())},{key:Qs(),value:s=>(n.cancel(),t.get().filter((e=>Ze(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(kr(),s))))}]);return{fireIfReady:(e,t)=>fe(s,t).bind((t=>t(e)))}})(o),s=L(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>Bc(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=nn(),a=Bc(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(yr(),e)}),0))})),i=Bc(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===Cg[0]&&!F(["input","textarea"],Ue(e.target))&&!tk(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=Bc(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=nn(),d=Bc(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(xr(),e)}),0))}));return{unbind:()=>{V(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},rk=(e,t)=>{const o=fe(e,"target").getOr(t);return en(o)},ak=Hs([{stopped:[]},{resume:["element"]},{complete:[]}]),ik=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=en(!1),n=en(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),ak.complete())),(e=>{const o=e.descHandler;return nl(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),ak.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),ak.complete()):rt(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),ak.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),ak.resume(n))))}))},lk=(e,t,o,n,s,r)=>ik(e,t,o,n,s,r).fold(E,(n=>lk(e,t,o,n,s,r)),T),ck=(e,t,o,n,s)=>{const r=rk(o,n);return lk(e,t,o,n,r,s)},dk=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{ie(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:C.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{ie(e,((e,o)=>{be(e,t)&&delete e[t]}))},filterByType:t=>fe(e,t).map((e=>ge(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>fe(e,o).bind((e=>Gs(n,(t=>((e,t)=>qi(t).bind((t=>fe(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{qi(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return qi(t).getOrThunk((()=>((e,t)=>{const o=Pi($i+"uid-");return ji(t,o),o})(0,e.element)))})(n);ve(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+pi(s.element)+"\nCannot use it for: "+pi(e.element)+"\nThe conflicting element is"+(vt(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>fe(t,e)}},uk=qm({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:qu(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[Ds("components",[]),ju("containerBehaviours",[]),Ds("events",{}),Ds("domModification",{}),Ds("eventOrder",{})]}),mk=e=>{const t=t=>rt(e.element).fold(E,(e=>Ze(t,e))),o=dk(),n=(e,n)=>o.find(t,e,n),s=sk(e.element,{triggerEvent:(e,t)=>vi(e,t.target,(o=>((e,t,o,n)=>ck(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:y("real"),triggerEvent:(e,t,o)=>{vi(e,t,(s=>ck(n,e,o,t,s)))},triggerFocus:(e,t)=>{qi(e).fold((()=>{gc(e)}),(o=>{vi(vr(),e,(o=>(((e,t,o,n,s)=>{const r=rk(o,n);ik(e,t,o,n,r,s)})(n,vr(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:kl,buildOrPatch:Cl,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:E},a=e=>{e.connect(r),je(e.element)||(o.register(e),V(e.components(),a),r.triggerEvent(Er(),e.element,{target:e.element}))},i=e=>{je(e.element)||(V(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{au(e,t)},c=e=>{cu(e)},d=e=>{const t=o.filter(wr());V(t,(t=>{const o=t.descHandler;nl(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t)=>{const o=(e=>{const t=en(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:T,event:e,setSource:O("Cannot set source of a broadcasted event"),getSource:O("Cannot get source of a broadcasted event")}})(t);return V(e,(e=>{const t=e.descHandler;nl(t)(o)})),o.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>bn.error(new Error('Could not find component with uid: "'+e+'" in system.'))),bn.value),h=e=>{const t=qi(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),Ho(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},gk=y([Ds("prefix","form-field"),ju("fieldBehaviours",[Qm,Gu])]),pk=y([wm({schema:[ps("dom")],name:"label"}),wm({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[ps("text")],name:"aria-descriptor"}),xm({factory:{sketch:e=>{const t=((e,t)=>{const o={};return ie(e,((e,n)=>{F(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[ps("factory")],name:"field"})]),hk=Xm({name:"FormField",configFields:gk(),partFields:pk(),factory:(e,t,o,n)=>{const s=Xu(e.fieldBehaviours,[Qm.config({find:t=>Im(t,e,"field")}),Gu.config({store:{mode:"manual",getValue:e=>Qm.getCurrent(e).bind(Gu.getValue),setValue:(e,t)=>{Qm.getCurrent(e).each((e=>{Gu.setValue(e,t)}))}}})]),r=Kr([ia(((t,o)=>{const n=Rm(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=Pi(e.prefix);n.label().each((e=>{St(e.element,"for",o),St(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=Pi(e.prefix);St(o.element,"id",n),St(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>Im(t,e,"field"),getLabel:t=>Im(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}});var fk=tinymce.util.Tools.resolve("tinymce.html.Entities");const bk=(e,t,o,n)=>{const s=vk(e,t,o,n);return hk.sketch(s)},vk=(e,t,o,n)=>({dom:xk(o),components:e.toArray().concat([t]),fieldBehaviours:xa(n)}),xk=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),yk=(e,t)=>hk.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[yl(t.translate(e))]}),wk=Pi("form-component-change"),Sk=Pi("form-component-input"),Ck=Pi("form-close"),kk=Pi("form-cancel"),Ok=Pi("form-action"),_k=Pi("form-submit"),Tk=Pi("form-block"),Ek=Pi("form-unblock"),Ak=Pi("form-tabchange"),Mk=Pi("form-resize"),Dk=(e,t,o)=>{const n=e.label.map((e=>yk(e,t))),s=t.icons(),r=e=>(t,o)=>{gn(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,kt(n,"data-collection-item-value"))}))},a=r(((o,n,s,r)=>{n.stop(),t.checkUiComponentContext("mode:design").shouldDisable||t.isDisabled()||Gr(o,Ok,{name:e.name,value:r})})),i=[Zr(sr(),r(((e,t,o)=>{gc(o,!0)}))),Zr(ur(),a),Zr(kr(),a),Zr(rr(),r(((e,t,o)=>{mn(e.element,"."+Ox).each((e=>{Ba(e,Ox)})),Ma(o,Ox)}))),Zr(ar(),r((e=>{mn(e.element,"."+Ox).each((e=>{Ba(e,Ox),pc(e)}))}))),da(r(((t,o,n,s)=>{Gr(t,Ok,{name:e.name,value:s})})))],l=(e,t)=>L(Od(e.element,".tox-collection__item"),t),c=hk.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:w},behaviours:xa([mg.config({disabled:()=>t.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{l(e,(e=>{Ma(e,"tox-collection__item--state-disabled"),St(e,"aria-disabled",!0)}))},onEnabled:e=>{l(e,(e=>{Ba(e,"tox-collection__item--state-disabled"),Tt(e,"aria-disabled")}))}}),Rw((()=>t.checkUiComponentContext(e.context))),Th.config({}),ev.config({...t.tooltips.getConfig({tooltipText:"",onShow:e=>{mn(e.element,"."+Ox+"[data-mce-tooltip]").each((o=>{Ot(o,"data-mce-tooltip").each((o=>{ev.setComponents(e,t.tooltips.getComponents({tooltipText:o}))}))}))}}),mode:"children-keyboard-focus",anchor:e=>({type:"node",node:mn(e.element,"."+Ox).orThunk((()=>Qe(".tox-collection__item"))),root:e.element,layouts:{onLtr:y([Zl,Ql,Xl,Kl,Yl,Jl]),onRtl:y([Zl,Ql,Xl,Kl,Yl,Jl])},bubble:$c(0,-2,{})})}),Gu.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const r=t.checkUiComponentContext("mode:design").shouldDisable||t.isDisabled()?" tox-collection__item--state-disabled":"",a=L(n,(t=>{const o=ox.translate(t.text),n=1===e.columns?`<div class="tox-collection__item-label">${o}</div>`:"",a=`<div class="tox-collection__item-icon">${(e=>{var t;return null!==(t=s[e])&&void 0!==t?t:e})(t.icon)}</div>`,i={_:" "," - ":" ","-":" "},l=o.replace(/\_| \- |\-/g,(e=>i[e]));return`<div data-mce-tooltip="${l}" class="tox-collection__item${r}" tabindex="-1" data-collection-item-value="${fk.encodeAllRaw(t.value)}" aria-label="${l}">${a}${n}</div>`})),i="auto"!==e.columns&&e.columns>1?z(a,e.columns):[a],l=L(i,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));mi(o.element,l.join(""))})(o,n),"auto"===e.columns&&xw(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{vh.setGridSize(o,e,t)})),$r(o,Mk)}}),Wb.config({}),vh.config((d=e.columns,1===d?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===d?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${vx}`}})),Eh("collection-events",i)]),eventOrder:{[Sr()]:["disabling","alloy.base.behaviour","collection-events"],[rr()]:["collection-events","tooltipping"]}});var d;return bk(n,c,["tox-form__group--collection"],[])},Bk=["input","textarea"],Ik=e=>{const t=Ue(e);return F(Bk,t)},Fk=(e,t)=>{const o=t.getRoot(e).getOr(e.element);Ba(o,t.invalidClass),t.notify.each((t=>{Ik(e.element)&&St(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{mi(e,t.validHtml)})),t.onValid(e)}))},Rk=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);Ma(s,t.invalidClass),t.notify.each((t=>{Ik(e.element)&&St(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{mi(e,n)})),t.onInvalid(e,n)}))},Nk=(e,t,o)=>t.validator.fold((()=>MC(bn.value(!0))),(t=>t.validate(e))),zk=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),Nk(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(Rk(e,t,0,o),bn.error(o))),(o=>(Fk(e,t),bn.value(o)))):bn.error("No longer in system"))));var Lk=Object.freeze({__proto__:null,markValid:Fk,markInvalid:Rk,query:Nk,run:zk,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return Ia(o,t.invalidClass)}}),Vk=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>Kr([Zr(t.onEvent,(t=>{zk(t,e).get(w)}))].concat(t.validateOnLoad?[ia((t=>{zk(t,e).get(b)}))]:[])))).getOr({})}),Hk=[ps("invalidClass"),Ds("getRoot",A.none),Ms("notify",[Ds("aria","alert"),Ds("getContainer",A.none),Ds("validHtml",""),Ti("onValid"),Ti("onInvalid"),Ti("onValidate")]),Ms("validator",[ps("validate"),Ds("onEvent","input"),Ds("validateOnLoad",!0)])];const Pk=wa({fields:Hk,name:"invalidating",active:Vk,apis:Lk,extra:{validation:e=>t=>{const o=Gu.getValue(t);return MC(e(o))}}}),Uk=wa({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>Kr([Jr(hr(),E)]),exhibit:()=>ga({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),Wk=Pi("color-input-change"),$k=Pi("color-swatch-change"),Gk=Pi("color-picker-cancel"),jk=wm({schema:[ps("dom")],name:"label"}),qk=e=>wm({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:Kr([ea(Ks(),((t,o,n)=>e(t,n)),[t]),ea(er(),((t,o,n)=>e(t,n)),[t]),ea(tr(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),Xk=qk("top-left"),Yk=qk("top"),Kk=qk("top-right"),Jk=qk("right"),Qk=qk("bottom-right"),Zk=qk("bottom"),eO=qk("bottom-left"),tO=qk("left"),oO=xm({name:"thumb",defaults:y({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:Kr([oa(Ks(),e,"spectrum"),oa(Js(),e,"spectrum"),oa(Qs(),e,"spectrum"),oa(er(),e,"spectrum"),oa(tr(),e,"spectrum"),oa(nr(),e,"spectrum")])})}),nO=e=>Lg(e.event);var sO=[jk,tO,Jk,Yk,Zk,Xk,Kk,eO,Qk,oO,xm({schema:[ms("mouseIsDown",(()=>en(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:xa([vh.config({mode:"special",onLeft:(o,n)=>t.onLeft(o,e,nO(n)),onRight:(o,n)=>t.onRight(o,e,nO(n)),onUp:(o,n)=>t.onUp(o,e,nO(n)),onDown:(o,n)=>t.onDown(o,e,nO(n))}),Wb.config({}),Ih.config({})]),events:Kr([Zr(Ks(),o),Zr(Js(),o),Zr(er(),o),Zr(tr(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}})];const rO=y("slider.change.value"),aO=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?A.some(e.touches[0]).map((e=>Gt(e.clientX,e.clientY))):A.none()}{const e=t;return void 0!==e.clientX?A.some(e).map((e=>Gt(e.clientX,e.clientY))):A.none()}},iO=e=>e.model.minX,lO=e=>e.model.minY,cO=e=>e.model.minX-1,dO=e=>e.model.minY-1,uO=e=>e.model.maxX,mO=e=>e.model.maxY,gO=e=>e.model.maxX+1,pO=e=>e.model.maxY+1,hO=(e,t,o)=>t(e)-o(e),fO=e=>hO(e,uO,iO),bO=e=>hO(e,mO,lO),vO=e=>fO(e)/2,xO=e=>bO(e)/2,yO=(e,t)=>t?e.stepSize*e.speedMultiplier:e.stepSize,wO=e=>e.snapToGrid,SO=e=>e.snapStart,CO=e=>e.rounded,kO=(e,t)=>void 0!==e[t+"-edge"],OO=e=>kO(e,"left"),_O=e=>kO(e,"right"),TO=e=>kO(e,"top"),EO=e=>kO(e,"bottom"),AO=e=>e.model.value.get(),MO=(e,t)=>({x:e,y:t}),DO=(e,t)=>{Gr(e,rO(),{value:t})},BO=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),IO=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),FO=(e,t,o)=>Math.max(t,Math.min(o,e)),RO=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=FO(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return FO(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},NO=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},zO="top",LO="right",VO="bottom",HO="left",PO=e=>e.element.dom.getBoundingClientRect(),UO=(e,t)=>e[t],WO=e=>{const t=PO(e);return UO(t,HO)},$O=e=>{const t=PO(e);return UO(t,LO)},GO=e=>{const t=PO(e);return UO(t,zO)},jO=e=>{const t=PO(e);return UO(t,VO)},qO=e=>{const t=PO(e);return UO(t,"width")},XO=e=>{const t=PO(e);return UO(t,"height")},YO=(e,t,o)=>(e+t)/2-o,KO=(e,t)=>{const o=PO(e),n=PO(t),s=UO(o,HO),r=UO(o,LO),a=UO(n,HO);return YO(s,r,a)},JO=(e,t)=>{const o=PO(e),n=PO(t),s=UO(o,zO),r=UO(o,VO),a=UO(n,zO);return YO(s,r,a)},QO=(e,t)=>{Gr(e,rO(),{value:t})},ZO=(e,t,o)=>{const n={min:iO(t),max:uO(t),range:fO(t),value:o,step:yO(t),snap:wO(t),snapStart:SO(t),rounded:CO(t),hasMinEdge:OO(t),hasMaxEdge:_O(t),minBound:WO(e),maxBound:$O(e),screenRange:qO(e)};return RO(n)},e_=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?IO:BO)(AO(o),iO(o),uO(o),yO(o,n));return QO(t,s),A.some(s)})(e,t,o,n).map(E),t_=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=qO(e),a=n.bind((t=>A.some(KO(t,e)))).getOr(0),i=s.bind((t=>A.some(KO(t,e)))).getOr(r),l={min:iO(t),max:uO(t),range:fO(t),value:o,hasMinEdge:OO(t),hasMaxEdge:_O(t),minBound:WO(e),minOffset:0,maxBound:$O(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return NO(l)})(t,r,o,n,s);return WO(t)-WO(e)+a},o_=e_(-1),n_=e_(1),s_=A.none,r_=A.none,a_={"top-left":A.none(),top:A.none(),"top-right":A.none(),right:A.some(((e,t)=>{DO(e,gO(t))})),"bottom-right":A.none(),bottom:A.none(),"bottom-left":A.none(),left:A.some(((e,t)=>{DO(e,cO(t))}))};var i_=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=ZO(e,t,o);return QO(e,n),n},setToMin:(e,t)=>{const o=iO(t);QO(e,o)},setToMax:(e,t)=>{const o=uO(t);QO(e,o)},findValueOfOffset:ZO,getValueFromEvent:e=>aO(e).map((e=>e.left)),findPositionOfValue:t_,setPositionFromValue:(e,t,o,n)=>{const s=AO(o),r=t_(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=Kt(t.element)/2;Mt(t.element,"left",r-a+"px")},onLeft:o_,onRight:n_,onUp:s_,onDown:r_,edgeActions:a_});const l_=(e,t)=>{Gr(e,rO(),{value:t})},c_=(e,t,o)=>{const n={min:lO(t),max:mO(t),range:bO(t),value:o,step:yO(t),snap:wO(t),snapStart:SO(t),rounded:CO(t),hasMinEdge:TO(t),hasMaxEdge:EO(t),minBound:GO(e),maxBound:jO(e),screenRange:XO(e)};return RO(n)},d_=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?IO:BO)(AO(o),lO(o),mO(o),yO(o,n));return l_(t,s),A.some(s)})(e,t,o,n).map(E),u_=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=XO(e),a=n.bind((t=>A.some(JO(t,e)))).getOr(0),i=s.bind((t=>A.some(JO(t,e)))).getOr(r),l={min:lO(t),max:mO(t),range:bO(t),value:o,hasMinEdge:TO(t),hasMaxEdge:EO(t),minBound:GO(e),minOffset:0,maxBound:jO(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return NO(l)})(t,r,o,n,s);return GO(t)-GO(e)+a},m_=A.none,g_=A.none,p_=d_(-1),h_=d_(1),f_={"top-left":A.none(),top:A.some(((e,t)=>{DO(e,dO(t))})),"top-right":A.none(),right:A.none(),"bottom-right":A.none(),bottom:A.some(((e,t)=>{DO(e,pO(t))})),"bottom-left":A.none(),left:A.none()};var b_=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=c_(e,t,o);return l_(e,n),n},setToMin:(e,t)=>{const o=lO(t);l_(e,o)},setToMax:(e,t)=>{const o=mO(t);l_(e,o)},findValueOfOffset:c_,getValueFromEvent:e=>aO(e).map((e=>e.top)),findPositionOfValue:u_,setPositionFromValue:(e,t,o,n)=>{const s=AO(o),r=u_(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=Ut(t.element)/2;Mt(t.element,"top",r-a+"px")},onLeft:m_,onRight:g_,onUp:p_,onDown:h_,edgeActions:f_});const v_=(e,t)=>{Gr(e,rO(),{value:t})},x_=(e,t)=>({x:e,y:t}),y_=(e,t)=>(o,n,s)=>((e,t,o,n,s)=>{const r=e>0?IO:BO,a=t?AO(n).x:r(AO(n).x,iO(n),uO(n),yO(n,s)),i=t?r(AO(n).y,lO(n),mO(n),yO(n,s)):AO(n).y;return v_(o,x_(a,i)),A.some(a)})(e,t,o,n,s).map(E),w_=y_(-1,!1),S_=y_(1,!1),C_=y_(-1,!0),k_=y_(1,!0),O_={"top-left":A.some(((e,t)=>{DO(e,MO(cO(t),dO(t)))})),top:A.some(((e,t)=>{DO(e,MO(vO(t),dO(t)))})),"top-right":A.some(((e,t)=>{DO(e,MO(gO(t),dO(t)))})),right:A.some(((e,t)=>{DO(e,MO(gO(t),xO(t)))})),"bottom-right":A.some(((e,t)=>{DO(e,MO(gO(t),pO(t)))})),bottom:A.some(((e,t)=>{DO(e,MO(vO(t),pO(t)))})),"bottom-left":A.some(((e,t)=>{DO(e,MO(cO(t),pO(t)))})),left:A.some(((e,t)=>{DO(e,MO(cO(t),xO(t)))}))};var __=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=ZO(e,t,o.left),s=c_(e,t,o.top),r=x_(n,s);return v_(e,r),r},setToMin:(e,t)=>{const o=iO(t),n=lO(t);v_(e,x_(o,n))},setToMax:(e,t)=>{const o=uO(t),n=mO(t);v_(e,x_(o,n))},getValueFromEvent:e=>aO(e),setPositionFromValue:(e,t,o,n)=>{const s=AO(o),r=t_(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=u_(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=Kt(t.element)/2,l=Ut(t.element)/2;Mt(t.element,"left",r-i+"px"),Mt(t.element,"top",a-l+"px")},onLeft:w_,onRight:S_,onUp:C_,onDown:k_,edgeActions:O_});const T_=Xm({name:"Slider",configFields:[Ds("stepSize",1),Ds("speedMultiplier",10),Ds("onChange",b),Ds("onChoose",b),Ds("onInit",b),Ds("onDragStart",b),Ds("onDragEnd",b),Ds("snapToGrid",!1),Ds("rounded",!0),Cs("snapStart"),hs("model",cs("mode",{x:[Ds("minX",0),Ds("maxX",100),ms("value",(e=>en(e.mode.minX))),ps("getInitialValue"),Di("manager",i_)],y:[Ds("minY",0),Ds("maxY",100),ms("value",(e=>en(e.mode.minY))),ps("getInitialValue"),Di("manager",b_)],xy:[Ds("minX",0),Ds("maxX",100),Ds("minY",0),Ds("maxY",100),ms("value",(e=>en({x:e.mode.minX,y:e.mode.minY}))),ps("getInitialValue"),Di("manager",__)]})),ju("sliderBehaviours",[vh,Gu]),ms("mouseIsDown",(()=>en(!1)))],partFields:sO,factory:(e,t,o,n)=>{const s=t=>Fm(t,e,"thumb"),r=t=>Fm(t,e,"spectrum"),a=t=>Im(t,e,"left-edge"),i=t=>Im(t,e,"right-edge"),l=t=>Im(t,e,"top-edge"),c=t=>Im(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&Im(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)},b=t=>{Im(t,e,"spectrum").map(vh.focusIn)};return{uid:e.uid,dom:e.dom,components:t,behaviours:Xu(e.sliderBehaviours,[vh.config({mode:"special",focusIn:b}),Gu.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),uc.config({channels:{[Eu()]:{onReceive:p}}})]),events:Kr([Zr(rO(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),A.some(!0)})(t,o.event.value)})),ia(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),Zr(Ks(),h),Zr(Qs(),f),Zr(er(),((e,t)=>{b(e),h(e,t)})),Zr(nr(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),E_=Pi("rgb-hex-update"),A_=Pi("slider-update"),M_=Pi("palette-update"),D_="form",B_=[ju("formBehaviours",[Gu])],I_=e=>"<alloy.field."+e+">",F_=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Xu(e.formBehaviours,[Gu.config({store:{mode:"manual",getValue:t=>{const o=Nm(t,e);return le(o,((e,t)=>e().bind((e=>{return o=Qm.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+pi(e.element)),o.fold((()=>bn.error(n)),bn.value);var o,n})).map(Gu.getValue)))},setValue:(t,o)=>{ie(o,((o,n)=>{Im(t,e,n).each((e=>{Qm.getCurrent(e).each((e=>{Gu.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>Im(t,e,o).bind(Qm.getCurrent)}}),R_={getField:tl(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),Em(D_,I_(t),o)),record:y(e)}})(),o=e(t),n=t.record(),s=L(n,(e=>xm({name:e,pname:I_(e)})));return Wm(D_,B_,s,F_,o)}},N_=Pi("valid-input"),z_=Pi("invalid-input"),L_=Pi("validating-input"),V_="colorcustom.rgb.",H_={isEnabled:E,setEnabled:b,immediatelyShow:b,immediatelyHide:b},P_=(e,t,o,n,s,r)=>{const a=(e,t)=>{const o=t.get();e!==o.isEnabled()&&(o.setEnabled(e),e?o.immediatelyShow():o.immediatelyHide())},i=(o,n,s)=>Pk.config({invalidClass:t("invalid"),notify:{onValidate:e=>{Gr(e,L_,{type:o})},onValid:e=>{a(!1,s),Gr(e,N_,{type:o,value:Gu.getValue(e)})},onInvalid:e=>{a(!0,s),Gr(e,z_,{type:o,value:Gu.getValue(e)})}},validator:{validate:t=>{const o=Gu.getValue(t),s=n(o)?bn.value(!0):bn.error(e("aria.input.invalid"));return MC(s)},validateOnLoad:!1}}),l=(o,n,a,l,c)=>{const d=en(H_),u=e(V_+"range"),m=hk.parts.label({dom:{tag:"label"},components:[yl(a)]}),g=hk.parts.field({data:c,factory:Vx,inputAttributes:{type:"text","aria-label":l,..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:xa([i(n,o,d),Wb.config({}),ev.config({...s({tooltipText:"",onSetup:e=>{d.set({isEnabled:()=>ev.isEnabled(e),setEnabled:t=>ev.setEnabled(e,t),immediatelyShow:()=>ev.immediateOpenClose(e,!0),immediatelyHide:()=>ev.immediateOpenClose(e,!1)}),ev.setEnabled(e,!1)},onShow:(o,s)=>{ev.setComponents(o,[{dom:{tag:"p",classes:[t("rgb-warning-note")]},components:[yl(e("hex"===n?"colorcustom.rgb.invalidHex":"colorcustom.rgb.invalid"))]}])}})})]),onSetValue:e=>{Pk.isInvalid(e)&&Pk.run(e).get(b)}}),p=Pi("aria-invalid"),h=Hb(r("invalid",A.some(p),"warning")),f=[m,g,Hb({dom:{tag:"div",classes:[t("invalid-icon")]},components:[h.asSpec()]}).asSpec()],v="hex"!==n?[hk.parts["aria-descriptor"]({text:u})]:[],x=f.concat(v);return{dom:{tag:"div",attributes:{role:"presentation"},classes:[t("rgb-container")]},components:x}},c=(e,t)=>{const o=t.red,n=t.green,s=t.blue;Gu.setValue(e,{red:o,green:n,blue:s})},d=Hb({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),u=(e,t)=>{d.getOpt(e).each((e=>{Mt(e.element,"background-color","#"+t.value)}))},m=qm({factory:()=>{const s={red:en(A.some(255)),green:en(A.some(255)),blue:en(A.some(255)),hex:en(A.some("ffffff"))},r=e=>s[e].get(),a=(e,t)=>{s[e].set(t)},i=e=>{const t=e.red,o=e.green,n=e.blue;a("red",A.some(t)),a("green",A.some(o)),a("blue",A.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?a(o.type,A.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=sS(t);a("hex",A.some(n.value));const s=xS(n);c(e,s),i(s),Gr(e,E_,{hex:n}),u(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);a(t,A.some(n)),r("red").bind((e=>r("green").bind((t=>r("blue").map((o=>fS(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=dS(t);return R_.getField(e,"hex").each((t=>{Ih.isFocused(t)||Gu.setValue(e,{hex:o.value})})),o})(e,t);Gr(e,E_,{hex:o}),u(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(V_+t+".label"),description:e(V_+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return En(R_.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",hk.sketch(l(bS,"red",h.label,h.description,255))),o.field("green",hk.sketch(l(bS,"green",f.label,f.description,255))),o.field("blue",hk.sketch(l(bS,"blue",b.label,b.description,255))),o.field("hex",hk.sketch(l(iS,"hex",v.label,v.description,"ffffff"))),d.asSpec()],formBehaviours:xa([Pk.config({invalidClass:t("form-invalid")}),Eh("rgb-form-events",[Zr(N_,g),Zr(z_,m),Zr(L_,m)])])}))),{apis:{updateHex:(e,t)=>{Gu.setValue(e,{hex:t.value}),((e,t)=>{const o=xS(t);c(e,o),i(o)})(e,t),u(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return m},U_=(e,t,o,n)=>{const s=qm({name:"ColourPicker",configFields:[ps("dom"),Ds("onValidHex",b),Ds("onInvalidHex",b)],factory:s=>{const r=P_(e,t,s.onValidHex,s.onInvalidHex,o,n),a=((e,t)=>{const o=T_.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=T_.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)};return qm({factory:r=>{const a=y({x:0,y:0}),i=xa([Qm.config({find:A.some}),Ih.config({})]);return T_.sketch({dom:{tag:"div",attributes:{role:"slider","aria-valuetext":e(["Saturation {0}%, Brightness {1}%",0,0])},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:a},rounded:!1,components:[o,n],onChange:(t,o,n)=>{h(n)||St(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",Math.floor(n.x),Math.floor(100-n.y)])),Gr(t,M_,{value:n})},onInit:(e,t,o,n)=>{s(o.element.dom,SS(CS))},sliderBehaviours:i})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=LS(t,100,100),r=vS(n);s(o,SS(r))})(t,o)},setThumb:(t,o,n)=>{((t,o)=>{const n=VS(xS(o));T_.setValue(t,{x:n.saturation,y:100-n.value}),St(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",n.saturation,n.value]))})(o,n)}},extraApis:{}})})(e,t),i={paletteRgba:en(CS),paletteHue:en(0)},l=Hb(((e,t)=>{const o=T_.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=T_.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return T_.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"slider","aria-valuemin":0,"aria-valuemax":360,"aria-valuenow":120}},rounded:!1,model:{mode:"y",getInitialValue:y(0)},components:[o,n],sliderBehaviours:xa([Ih.config({})]),onChange:(e,t,o)=>{St(e.element,"aria-valuenow",Math.floor(360-3.6*o)),Gr(e,A_,{value:o})}})})(0,t)),c=Hb(a.sketch({})),d=Hb(r.sketch({})),u=(e,t,o)=>{c.getOpt(e).each((e=>{a.setHue(e,o)}))},m=(e,t)=>{d.getOpt(e).each((e=>{r.updateHex(e,t)}))},g=(e,t,o)=>{l.getOpt(e).each((e=>{T_.setValue(e,(e=>100-e/360*100)(o))}))},p=(e,t)=>{c.getOpt(e).each((e=>{a.setThumb(e,t)}))},f=(e,t,o,n)=>{((e,t)=>{const o=xS(e);i.paletteRgba.set(o),i.paletteHue.set(t)})(t,o),V(n,(n=>{n(e,t,o)}))};return{uid:s.uid,dom:s.dom,components:[c.asSpec(),l.asSpec(),d.asSpec()],behaviours:xa([Eh("colour-picker-events",[Zr(E_,(()=>{const e=[u,g,p];return(t,o)=>{const n=o.event.hex,s=(e=>VS(xS(e)))(n);f(t,n,s.hue,e)}})()),Zr(M_,(()=>{const e=[m];return(t,o)=>{const n=o.event.value,s=i.paletteHue.get(),r=LS(s,n.x,100-n.y),a=HS(r);f(t,a,s,e)}})()),Zr(A_,(()=>{const e=[u,m];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=i.paletteRgba.get(),r=VS(s),a=LS(n,r.saturation,r.value),l=HS(a);f(t,l,n,e)}})())]),Qm.config({find:e=>d.getOpt(e)}),vh.config({mode:"acyclic"})])}}});return s},W_=()=>Qm.config({find:A.some}),$_=e=>Qm.config({find:t=>ct(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),G_=Wn([Ds("preprocess",w),Ds("postprocess",w)]),j_=(e,t)=>{const o=is("RepresentingConfigs.memento processors",G_,t);return Gu.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=Gu.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);Gu.setValue(r,s)}}})},q_=(e,t,o)=>Gu.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),X_=(e,t,o)=>q_(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),Y_=e=>Gu.config({store:{mode:"memory",initialValue:e}}),K_={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red channel","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green channel","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue channel","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.rgb.invalid":"Numbers only, 0 to 255","colorcustom.rgb.invalidHex":"Hexadecimal only, 000000 to FFFFFF","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var J_=tinymce.util.Tools.resolve("tinymce.Resource");const Q_=e=>be(e,"init");var Z_=tinymce.util.Tools.resolve("tinymce.util.Tools");const eT=Pi("browse.files.event"),tT=(e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{V(e,(e=>{e(t,o)}))},r=Hb({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:xa([Eh("input-file-events",[sa(ur()),sa(kr())])])}),a=e.label.map((e=>yk(e,t))),i=hk.parts.field({factory:Lb,dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[yl(t.translate("Browse for an image")),r.asSpec()],action:e=>{r.get(e).element.dom.click()},buttonBehaviours:xa([W_(),Y_(o.getOr([])),Wb.config({}),Sw((()=>t.checkUiComponentContext(e.context).shouldDisable)),Rw((()=>t.checkUiComponentContext(e.context)))])}),l={dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:xa([mg.config({disabled:()=>t.checkUiComponentContext(e.context).shouldDisable}),Rw((()=>t.checkUiComponentContext(e.context))),Ph.config({toggleClass:"dragenter",toggleOnExecute:!1}),Eh("dropzone-events",[Zr("dragenter",s([n,Ph.toggle])),Zr("dragleave",s([n,Ph.toggle])),Zr("dragover",n),Zr("drop",s([n,(e,t)=>{var o;if(!mg.isDisabled(e)){const n=t.event.raw;Gr(e,eT,{files:null===(o=n.dataTransfer)||void 0===o?void 0:o.files})}}])),Zr(dr(),((e,t)=>{const o=t.event.raw.target;Gr(e,eT,{files:o.files})}))])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[yl(t.translate("Drop an image here"))]},i]}]};return bk(a,l,["tox-form__group--stretched"],[Eh("handle-files",[Zr(eT,((o,n)=>{hk.getField(o).each((o=>{var s,r;s=o,(r=n.event.files)&&(Gu.setValue(s,((e,t)=>{const o=Z_.explode(t.getOption("images_file_types"));return P(ne(e),(e=>R(o,(t=>Ee(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(r,t)),Gr(s,wk,{name:e.name}))}))}))])])},oT=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},nT=Pi("alloy-fake-before-tabstop"),sT=Pi("alloy-fake-after-tabstop"),rT=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:xa([Ih.config({ignore:!0}),Wb.config({})])}),aT=(e,t)=>({dom:{tag:"div",classes:["tox-navobj",...e.getOr([])]},components:[rT([nT]),t,rT([sT])],behaviours:xa([$_(1)])}),iT=(e,t)=>{Gr(e,ir(),{raw:{which:9,shiftKey:t}})},lT=(e,t)=>{const o=t.element;Ia(o,nT)?iT(e,!0):Ia(o,sT)&&iT(e,!1)},cT=e=>tk(e,["."+nT,"."+sT].join(","),T),dT=Pi("update-dialog"),uT=Pi("update-title"),mT=Pi("update-body"),gT=Pi("update-footer"),pT=Pi("body-send-message"),hT=Pi("dialog-focus-shifted"),fT=Mo().browser,bT=fT.isSafari(),vT=fT.isFirefox(),xT=bT||vT,yT=fT.isChromium(),wT=({scrollTop:e,scrollHeight:t,clientHeight:o})=>Math.ceil(e)+o>=t,ST=(e,t)=>e.scrollTo(0,"bottom"===t?99999999:t),CT=(e,t,o)=>{const n=e.dom;A.from(n.contentDocument).fold(o,(e=>{let o=0;const s=((e,t)=>{const o=e.body;return A.from(!/^<!DOCTYPE (html|HTML)/.test(t)&&(!yT&&!bT||g(o)&&(0!==o.scrollTop||Math.abs(o.scrollHeight-o.clientHeight)>1))?o:e.documentElement)})(e,t).map((e=>(o=e.scrollTop,e))).forall(wT),r=()=>{const e=n.contentWindow;g(e)&&(s?ST(e,"bottom"):!s&&xT&&0!==o&&ST(e,o))};bT&&n.addEventListener("load",r,{once:!0}),e.open(),e.write(t),e.close(),bT||r()}))},kT=Ce(xT,bT?500:200).map((e=>((e,t)=>{let o=null,n=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null,n=null)},throttle:(...s)=>{n=s,c(o)&&(o=setTimeout((()=>{const t=n;o=null,n=null,e.apply(null,t)}),t))}}})(CT,e))),OT=Pi("toolbar.button.execute"),_T=Pi("common-button-display-events"),TT={[Sr()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events","tooltipping"],[Ir()]:["toolbar-button-events",_T],[Fr()]:["toolbar-button-events","dropdown-events","tooltipping"],[er()]:["focusing","alloy.base.behaviour",_T]},ET=e=>Mt(e.element,"width",It(e.element,"width")),AT=(e,t,o)=>ux(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),MT=(e,t)=>AT(e,t,[]),DT=(e,t)=>AT(e,t,[Th.config({})]),BT=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[yl(o.translate(e))],behaviours:xa([Th.config({})])}),IT=Pi("update-menu-text"),FT=Pi("update-menu-icon"),RT=Pi("update-tooltip-text"),NT=(e,t,o,n)=>{const s=en(b),r=en(e.tooltip),a=e.text.map((e=>Hb(BT(e,t,o.providers)))),i=e.icon.map((e=>Hb(DT(e,o.providers.icons)))),l=(e,t)=>{const o=Gu.getValue(e);return Ih.focus(o),Gr(o,"keydown",{raw:t.event.raw}),$C.close(o),A.some(!0)},c=e.role.fold((()=>({})),(e=>({role:e}))),d=A.from(e.listRole).map((e=>({listRole:e}))).getOr({}),u=e.ariaLabel.fold((()=>({})),(e=>({"aria-label":o.providers.translate(e)}))),m=ux("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons),p=Pi("common-button-display-events"),h="dropdown-events",f=Hb($C.sketch({...e.uid?{uid:e.uid}:{},...c,...d,dom:{tag:"button",classes:[t,`${t}--select`].concat(L(e.classes,(e=>`${t}--${e}`))),attributes:{...u,...g(n)?{"data-mce-name":n}:{}}},components:Lw([i.map((e=>e.asSpec())),a.map((e=>e.asSpec())),A.some(m)]),matchWidth:!0,useMinWidth:!0,onOpen:(t,o,n)=>{e.searchable&&(e=>{$x(e).each((e=>Ih.focus(e)))})(n)},dropdownBehaviours:xa([...e.dropdownBehaviours,Sw((()=>e.disabled||o.providers.checkUiComponentContext(e.context).shouldDisable)),Rw((()=>o.providers.checkUiComponentContext(e.context))),Uk.config({}),Th.config({}),...e.tooltip.map((t=>ev.config(o.providers.tooltips.getConfig({tooltipText:o.providers.translate(t),onShow:t=>{if(we(r.get(),e.tooltip,((e,t)=>t!==e)).getOr(!1)){const e=o.providers.translate(r.get().getOr(""));ev.setComponents(t,o.providers.tooltips.getComponents({tooltipText:e}))}}})))).toArray(),Eh(h,[_w(e,s),Tw(e,s)]),Eh(p,[ia(((t,o)=>{"listbox"!==e.listRole&&ET(t)}))]),Eh("update-dropdown-width-variable",[Zr(Br(),((e,t)=>$C.close(e)))]),Eh("menubutton-update-display-text",[Zr(IT,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{Th.set(e,[yl(o.providers.translate(t.event.text))])}))})),Zr(FT,((e,t)=>{i.bind((t=>t.getOpt(e))).each((e=>{Th.set(e,[DT(t.event.icon,o.providers.icons)])}))})),Zr(RT,((e,t)=>{const n=o.providers.translate(t.event.text);St(e.element,"aria-label",n),r.set(A.some(t.event.text))}))])]),eventOrder:En(TT,{[er()]:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"],[Ir()]:["toolbar-button-events",ev.name(),h,p]}),sandboxBehaviours:xa([vh.config({mode:"special",onLeft:l,onRight:l}),Eh("dropdown-sandbox-events",[Zr(Hx,((e,t)=>{(e=>{const t=Gu.getValue(e),o=Wx(e).map(Gx);$C.refetch(t).get((()=>{const e=kC.getCoupled(t,"sandbox");o.each((t=>Wx(e).each((e=>((e,t)=>{Gu.setValue(e,t.fetchPattern),e.element.dom.selectionStart=t.selectionStart,e.element.dom.selectionEnd=t.selectionEnd})(e,t)))))}))})(e),t.stop()})),Zr(Px,((e,t)=>{((e,t)=>{(e=>Ou.getState(e).bind(Sg.getHighlighted).bind(Sg.getHighlighted))(e).each((o=>{((e,t,o,n)=>{const s={...n,target:t};e.getSystem().triggerEvent(o,t,s)})(e,o.element,t.event.eventType,t.event.interactionEvent)}))})(e,t),t.stop()}))])]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:{...Fx(0,e.columns,e.presets),fakeFocus:e.searchable,..."listbox"===e.listRole?{}:{onHighlightItem:GC,onCollapseMenu:(e,t,o)=>{Sg.getHighlighted(o).each((t=>{GC(e,o,t)}))},onDehighlightItem:jC}}},getAnchorOverrides:()=>({maxHeightFunction:(e,t)=>{Vc()(e,t-10)}}),fetch:t=>AC(C(e.fetch,t))}));return f.asSpec()},zT=e=>"separator"===e.type,LT={type:"separator"},VT=(e,t)=>{const o=((e,t)=>{const o=W(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!zT(e[e.length-1])?e.concat([LT]):e:be(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&zT(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return U(o,((e,o)=>{if((e=>be(e,"getSubmenuItems"))(o)){const n=(e=>{const t=fe(e,"value").getOrThunk((()=>Pi("generated-menu-item")));return En({value:t},e)})(o),s=((e,t)=>{const o=e.getSubmenuItems(),n=VT(o,t);return{item:e,menus:En(n.menus,{[e.value]:n.items}),expansions:En(n.expansions,{[e.value]:e.value})}})(n,t);return{menus:En(e.menus,s.menus),items:[s.item,...e.items],expansions:En(e.expansions,s.expansions)}}return{...e,items:[o,...e.items]}}),{menus:{},expansions:{},items:[]})},HT=(e,t,o,n)=>{const s=Pi("primary-menu"),r=VT(e,o.shared.providers.menuItems());if(0===r.items.length)return A.none();const a=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-field",placeholder:e.placeholder}))))(n),i=JC(s,r.items,t,o,n.isHorizontalMenu,a),l=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-results"}))))(n),c=le(r.menus,((e,n)=>JC(n,e,t,o,!1,l))),d=En(c,Ps(s,i));return A.from(xf.tieredData(s,d,r.expansions))},PT=e=>!be(e,"items"),UT="data-value",WT=(e,t,o,n,s)=>L(o,(o=>PT(o)?{type:"togglemenuitem",...s?{}:{role:"option"},text:o.text,value:o.value,active:o.value===n,onAction:()=>{Gu.setValue(e,o.value),Gr(e,wk,{name:t}),Ih.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>WT(e,t,o.items,n,s)})),$T=(e,t)=>se(e,(e=>PT(e)?Ce(e.value===t,e):$T(e.items,t))),GT=qm({name:"HtmlSelect",configFields:[ps("options"),ju("selectBehaviours",[Ih,Gu]),Ds("selectClasses",[]),Ds("selectAttributes",{}),Cs("data")],factory:(e,t)=>{const o=L(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>Ps("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:Xu(e.selectBehaviours,[Ih.config({}),Gu.config({store:{mode:"manual",getValue:e=>ul(e.element),setValue:(t,o)=>{const n=te(e.options);$(e.options,(e=>e.value===o)).isSome()?ml(t.element,o):-1===t.element.dom.selectedIndex&&""===o&&n.each((e=>ml(t.element,e.value)))},...n}})])}}}),jT=y([Ds("field1Name","field1"),Ds("field2Name","field2"),Ai("onLockedChange"),Oi(["lockClass"]),Ds("locked",!1),Yu("coupledFieldBehaviours",[Qm,Gu]),zs("onInput",b)]),qT=(e,t)=>xm({factory:hk,name:e,overrides:e=>({fieldBehaviours:xa([Eh("coupled-input-behaviour",[Zr(cr(),(o=>{((e,t,o)=>Im(e,t,o).bind(Qm.getCurrent))(o,e,t).each((t=>{Im(o,e,"lock").each((n=>{Ph.isOn(n)&&e.onLockedChange(o,t,n),e.onInput(o)}))}))}))])])})}),XT=y([qT("field1","field2"),qT("field2","field1"),xm({factory:Lb,schema:[ps("dom")],name:"lock",overrides:e=>({buttonBehaviours:xa([Ph.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),YT=Xm({name:"FormCoupledInputs",configFields:jT(),partFields:XT(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Ku(e.coupledFieldBehaviours,[Qm.config({find:A.some}),Gu.config({store:{mode:"manual",getValue:t=>{const o=Lm(t,e,["field1","field2"]);return{[e.field1Name]:Gu.getValue(o.field1()),[e.field2Name]:Gu.getValue(o.field2())}},setValue:(t,o)=>{const n=Lm(t,e,["field1","field2"]);ve(o,e.field1Name)&&Gu.setValue(n.field1(),o[e.field1Name]),ve(o,e.field2Name)&&Gu.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>Im(t,e,"field1"),getField2:t=>Im(t,e,"field2"),getLock:t=>Im(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),KT=e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit},JT=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return bn.value({value:e,unit:o})}return bn.error(e)},QT=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>be(o,e);return e.unit===t?A.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?A.some(e.value):A.some(e.value/o[e.unit]*o[t]):A.none()},ZT=e=>A.none(),eE=(e,t)=>{const o=JT(e).toOptional(),n=JT(t).toOptional();return we(o,n,((e,t)=>QT(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>QT(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(ZT))).getOr(ZT)},tE=(e,t)=>{const o=e.label.map((e=>yk(e,t))),n=[mg.config({disabled:()=>e.disabled||t.checkUiComponentContext(e.context).shouldDisable}),Rw((()=>t.checkUiComponentContext(e.context))),vh.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>($r(e,_k),A.some(!0))}),Eh("textfield-change",[Zr(cr(),((t,o)=>{Gr(t,wk,{name:e.name})})),Zr(yr(),((t,o)=>{Gr(t,wk,{name:e.name})}))]),Wb.config({})],s=e.validation.map((e=>Pk.config({getRoot:e=>at(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=Gu.getValue(t),n=e.validator(o);return MC(!0===n?bn.value(o):bn.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r={...e.placeholder.fold(y({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(y({}),(e=>({inputmode:e}))),"data-mce-name":e.name},a=hk.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:r,inputClasses:[e.classname],inputBehaviours:xa(j([n,s])),selectOnFocus:!1,factory:Vx}),i=e.multiline?{dom:{tag:"div",classes:["tox-textarea-wrap"]},components:[a]}:a,l=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),c=[mg.config({disabled:()=>e.disabled||t.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{hk.getField(e).each(mg.disable)},onEnabled:e=>{hk.getField(e).each(mg.enable)}}),Rw((()=>t.checkUiComponentContext(e.context)))];return bk(o,i,l,c)},oE=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),nE=e=>e.dimension.property,sE=(e,t)=>e.dimension.getDimension(t),rE=(e,t)=>{const o=oE(e,t);Ra(o,[t.shrinkingClass,t.growingClass])},aE=(e,t)=>{Ba(e.element,t.openClass),Ma(e.element,t.closedClass),Mt(e.element,nE(t),"0px"),Vt(e.element)},iE=(e,t)=>{Ba(e.element,t.closedClass),Ma(e.element,t.openClass),Lt(e.element,nE(t))},lE=(e,t,o,n)=>{o.setCollapsed(),Mt(e.element,nE(t),sE(t,e.element)),rE(e,t),aE(e,t),t.onStartShrink(e),t.onShrunk(e)},cE=(e,t,o,n)=>{const s=n.getOrThunk((()=>sE(t,e.element)));o.setCollapsed(),Mt(e.element,nE(t),s),Vt(e.element);const r=oE(e,t);Ba(r,t.growingClass),Ma(r,t.shrinkingClass),aE(e,t),t.onStartShrink(e)},dE=(e,t,o)=>{const n=sE(t,e.element);("0px"===n?lE:cE)(e,t,o,A.some(n))},uE=(e,t,o)=>{const n=oE(e,t),s=Ia(n,t.shrinkingClass),r=sE(t,e.element);iE(e,t);const a=sE(t,e.element);(s?()=>{Mt(e.element,nE(t),r),Vt(e.element)}:()=>{aE(e,t)})(),Ba(n,t.shrinkingClass),Ma(n,t.growingClass),iE(e,t),Mt(e.element,nE(t),a),o.setExpanded(),t.onStartGrow(e)},mE=(e,t,o)=>{const n=oE(e,t);return!0===Ia(n,t.growingClass)},gE=(e,t,o)=>{const n=oE(e,t);return!0===Ia(n,t.shrinkingClass)};var pE=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){Lt(e.element,nE(t));const o=sE(t,e.element);Mt(e.element,nE(t),o)}},grow:(e,t,o)=>{o.isExpanded()||uE(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&dE(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&lE(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:mE,isShrinking:gE,isTransitioning:(e,t,o)=>mE(e,t)||gE(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?dE:uE)(e,t,o)},disableTransitions:rE,immediateGrow:(e,t,o)=>{o.isExpanded()||(iE(e,t),Mt(e.element,nE(t),sE(t,e.element)),rE(e,t),o.setExpanded(),t.onStartGrow(e),t.onGrown(e))}}),hE=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return ga(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:Ps(t.dimension.property,"0px")})},events:(e,t)=>Kr([aa(gr(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(rE(o,e),t.isExpanded()&&Lt(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),fE=[ps("closedClass"),ps("openClass"),ps("shrinkingClass"),ps("growingClass"),Cs("getAnimationRoot"),Ti("onShrunk"),Ti("onStartShrink"),Ti("onGrown"),Ti("onStartGrow"),Ds("expanded",!1),hs("dimension",cs("property",{width:[Di("property","width"),Di("getDimension",(e=>Kt(e)+"px"))],height:[Di("property","height"),Di("getDimension",(e=>Ut(e)+"px"))]}))];const bE=wa({fields:fE,name:"sliding",active:hE,apis:pE,state:Object.freeze({__proto__:null,init:e=>{const t=en(e.expanded);return va({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:C(t.set,!1),setExpanded:C(t.set,!0),readState:()=>"expanded: "+t.get()})}})}),vE=e=>({isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t),setActive:t=>{const o=e.element;t?(Ma(o,"tox-tbtn--enabled"),St(o,"aria-pressed",!0)):(Ba(o,"tox-tbtn--enabled"),Tt(o,"aria-pressed"))},isActive:()=>Ia(e.element,"tox-tbtn--enabled"),setTooltip:t=>{Gr(e,RT,{text:t})},setText:t=>{Gr(e,IT,{text:t})},setIcon:t=>Gr(e,FT,{icon:t})}),xE=(e,t,o,n,s=!0,r)=>{const a="bordered"===e.buttonType?["bordered"]:[];return NT({text:e.text,icon:e.icon,tooltip:e.tooltip,ariaLabel:e.tooltip,searchable:e.search.isSome(),role:n,fetch:(t,n)=>{const s={pattern:e.search.isSome()?qC(t):""};e.fetch((t=>{n(HT(t,fx.CLOSE_ON_EXECUTE,o,{isHorizontalMenu:!1,search:e.search}))}),s,vE(t))},onSetup:e.onSetup,getApi:e=>vE(e),columns:1,presets:"normal",classes:a,dropdownBehaviours:[...s?[Wb.config({})]:[]],context:e.context},t,o.shared,r)},yE=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{gc(t.element),Gr(t,Ok,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(L(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,context:e.context,onAction:n(e),onSetup:s(e)}})))}},wE=e=>({dom:{tag:"span",classes:["tox-tree__label"],attributes:{"aria-label":e}},components:[yl(e)]}),SE=(e,t,o)=>{e.customStateIcon.each((n=>t.push(OE(n,o.shared.providers.icons,e.customStateIconTooltip.fold((()=>[]),(e=>[ev.config(o.shared.providers.tooltips.getConfig({tooltipText:e}))])),["tox-icon-custom-state"]))))},CE=Pi("leaf-label-event-id"),kE=({leaf:e,onLeafAction:t,visible:o,treeId:n,selectedId:s,backstage:r})=>{const a=e.menu.map((e=>xE(e,"tox-mbtn",r,A.none(),o))),i=[wE(e.title)];return SE(e,i,r),a.each((e=>i.push(e))),Lb.sketch({dom:{tag:"div",classes:["tox-tree--leaf__label","tox-trbtn"].concat(o?["tox-tree--leaf__label--visible"]:[])},components:i,role:"treeitem",action:o=>{t(e.id),o.getSystem().broadcastOn([`update-active-item-${n}`],{value:e.id})},eventOrder:{[ir()]:[CE,"keying"]},buttonBehaviours:xa([...o?[Wb.config({})]:[],Ph.config({toggleClass:"tox-trbtn--enabled",toggleOnExecute:!1,aria:{mode:"selected"}}),uc.config({channels:{[`update-active-item-${n}`]:{onReceive:(t,o)=>{(o.value===e.id?Ph.on:Ph.off)(t)}}}}),Eh(CE,[ia(((t,o)=>{s.each((o=>{(o===e.id?Ph.on:Ph.off)(t)}))})),Zr(ir(),((e,t)=>{const o="ArrowLeft"===t.event.raw.code,n="ArrowRight"===t.event.raw.code;o?(cn(e.element,".tox-tree--directory").each((t=>{e.getSystem().getByDom(t).each((e=>{un(t,".tox-tree--directory__label").each((t=>{e.getSystem().getByDom(t).each(Ih.focus)}))}))})),t.stop()):n&&t.stop()}))])])})},OE=(e,t,o,n,s)=>ux(e,{tag:"span",classes:["tox-tree__icon-wrap","tox-icon"].concat(n||[]),behaviours:o,attributes:s},t),_E=Pi("directory-label-event-id"),TE=({directory:e,visible:t,noChildren:o,backstage:n})=>{const s=e.menu.map((e=>xE(e,"tox-mbtn",n,A.none()))),r=[{dom:{tag:"div",classes:["tox-chevron"]},components:[(a=n.shared.providers.icons,OE("chevron-right",a,[]))]},wE(e.title)];var a;SE(e,r,n),s.each((e=>{r.push(e)}));const i=t=>{cn(t.element,".tox-tree--directory").each((o=>{t.getSystem().getByDom(o).each((o=>{const n=!Ph.isOn(o);Ph.toggle(o),Gr(t,"expand-tree-node",{expanded:n,node:e.id})}))}))};return Lb.sketch({dom:{tag:"div",classes:["tox-tree--directory__label","tox-trbtn"].concat(t?["tox-tree--directory__label--visible"]:[])},components:r,action:i,eventOrder:{[ir()]:[_E,"keying"]},buttonBehaviours:xa([...t?[Wb.config({})]:[],Eh(_E,[Zr(ir(),((e,t)=>{const n="ArrowRight"===t.event.raw.code,s="ArrowLeft"===t.event.raw.code;n&&o&&t.stop(),(n||s)&&cn(e.element,".tox-tree--directory").each((o=>{e.getSystem().getByDom(o).each((o=>{!Ph.isOn(o)&&n||Ph.isOn(o)&&s?(i(e),t.stop()):s&&!Ph.isOn(o)&&(cn(o.element,".tox-tree--directory").each((e=>{un(e,".tox-tree--directory__label").each((e=>{o.getSystem().getByDom(e).each(Ih.focus)}))})),t.stop())}))}))}))])])})},EE=({children:e,onLeafAction:t,visible:o,treeId:n,expandedIds:s,selectedId:r,backstage:a})=>({dom:{tag:"div",classes:["tox-tree--directory__children"]},components:e.map((e=>"leaf"===e.type?kE({leaf:e,selectedId:r,onLeafAction:t,visible:o,treeId:n,backstage:a}):ME({directory:e,expandedIds:s,selectedId:r,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:a}))),behaviours:xa([bE.config({dimension:{property:"height"},closedClass:"tox-tree--directory__children--closed",openClass:"tox-tree--directory__children--open",growingClass:"tox-tree--directory__children--growing",shrinkingClass:"tox-tree--directory__children--shrinking",expanded:o}),Th.config({})])}),AE=Pi("directory-event-id"),ME=({directory:e,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:s,expandedIds:r,selectedId:a})=>{const{children:i}=e,l=en(r),c=r.includes(e.id);return{dom:{tag:"div",classes:["tox-tree--directory"],attributes:{role:"treeitem"}},components:[TE({directory:e,visible:o,noChildren:0===e.children.length,backstage:s}),EE({children:i,expandedIds:r,selectedId:a,onLeafAction:t,visible:c,treeId:n,backstage:s})],behaviours:xa([Eh(AE,[ia(((e,t)=>{Ph.set(e,c)})),Zr("expand-tree-node",((e,t)=>{const{expanded:o,node:n}=t.event;l.set(o?[...l.get(),n]:l.get().filter((e=>e!==n)))}))]),Ph.config({...e.children.length>0?{aria:{mode:"expanded"}}:{},toggleClass:"tox-tree--directory--expanded",onToggled:(e,o)=>{const r=e.components()[1],c=(d=o,i.map((e=>"leaf"===e.type?kE({leaf:e,selectedId:a,onLeafAction:t,visible:d,treeId:n,backstage:s}):ME({directory:e,expandedIds:l.get(),selectedId:a,onLeafAction:t,labelTabstopping:d,treeId:n,backstage:s}))));var d;o?bE.grow(r):bE.shrink(r),Th.set(r,c)}})])}},DE=Pi("tree-event-id");var BE=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return Kr([Zr(e.event,o),la((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[Zr(e,(()=>t.cancel()))])).getOr([])))}});const IE=e=>{const t=en(null);return va({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var FE=Object.freeze({__proto__:null,throttle:IE,init:e=>e.stream.streams.state(e)}),RE=[hs("stream",cs("mode",{throttle:[ps("delay"),Ds("stopEvent",!0),Di("streams",{setup:(e,t)=>{const o=e.stream,n=oT(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:IE})]})),Ds("event","input"),Cs("cancelEvent"),Ai("onStream")];const NE=wa({fields:RE,name:"streaming",active:BE,state:FE}),zE=(e,t,o)=>{const n=Gu.getValue(o);Gu.setValue(t,n),VE(t)},LE=(e,t)=>{const o=e.element,n=ul(o),s=o.dom;"number"!==kt(o,"type")&&t(s,n)},VE=e=>{LE(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},HE=y("alloy.typeahead.itemexecute"),PE=y([Cs("lazySink"),ps("fetch"),Ds("minChars",5),Ds("responseTime",1e3),Ti("onOpen"),Ds("getHotspot",A.some),Ds("getAnchorOverrides",y({})),Ds("layouts",A.none()),Ds("eventOrder",{}),Vs("model",{},[Ds("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),Ds("selectsOver",!0),Ds("populateFromBrowse",!0)]),Ti("onSetValue"),Ei("onExecute"),Ti("onItemExecute"),Ds("inputClasses",[]),Ds("inputAttributes",{}),Ds("inputStyles",{}),Ds("matchWidth",!0),Ds("useMinWidth",!1),Ds("dismissOnBlur",!0),Oi(["openClass"]),Cs("initialData"),Cs("listRole"),ju("typeaheadBehaviours",[Ih,Gu,NE,vh,Ph,kC]),ms("lazyTypeaheadComp",(()=>en(A.none))),ms("previewing",(()=>en(!0)))].concat(Rx()).concat(PC())),UE=y([ym({schema:[ki()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlightItem:(t,o,n)=>{e.previewing.get()?e.lazyTypeaheadComp.get().each((t=>{((e,t,o)=>{if(e.selectsOver){const n=Gu.getValue(t),s=e.getDisplayText(n),r=Gu.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?A.some((()=>{zE(0,t,o),((e,t)=>{LE(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):A.none()}return A.none()})(e.model,t,n).fold((()=>{e.model.selectsOver?(Sg.dehighlight(o,n),e.previewing.set(!0)):e.previewing.set(!1)}),(t=>{t(),e.previewing.set(!1)}))})):e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&zE(e.model,t,n),Ot(n.element,"id").each((e=>St(t.element,"aria-activedescendant",e)))}))},onExecute:(t,o)=>e.lazyTypeaheadComp.get().map((e=>(Gr(e,HE(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&zE(e.model,t,o)}))}})})]),WE=Xm({name:"Typeahead",configFields:PE(),partFields:UE(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=kC.getCoupled(t,"sandbox");if(Ou.isOpen(r))Qm.getCurrent(r).each((e=>{Sg.getHighlighted(e).fold((()=>{s(e)}),(()=>{Yr(r,e.element,"keydown",o)}))}));else{const o=e=>{Qm.getCurrent(e).each(s)};FC(e,a(t),t,r,n,o,bf.HighlightMenuAndItem).get(b)}},r=Nx(e),a=e=>t=>t.map((t=>{const o=he(t.menus),n=q(o,(e=>P(e.items,(e=>"item"===e.type))));return Gu.getState(e).update(L(n,(e=>e.data))),t})),i=e=>Qm.getCurrent(e),l="typeaheadevents",c=[Ih.config({}),Gu.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>ul(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{ml(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>Ps("initialValue",e))).getOr({})}}),NE.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=kC.getCoupled(t,"sandbox");if(Ih.isFocused(t)&&ul(t.element).length>=e.minChars){const o=i(s).bind((e=>Sg.getHighlighted(e).map(Gu.getValue)));e.previewing.set(!0);const r=t=>{i(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Sg.highlightFirst(t)}),(e=>{Sg.highlightBy(t,(t=>Gu.getValue(t).value===e.value)),Sg.getHighlighted(t).orThunk((()=>(Sg.highlightFirst(t),A.none())))}))}))};FC(e,a(t),t,s,n,r,bf.HighlightJustMenu).get(b)}},cancelEvent:Tr()}),vh.config({mode:"special",onDown:(e,t)=>(s(e,t,Sg.highlightFirst),A.some(!0)),onEscape:e=>{const t=kC.getCoupled(e,"sandbox");return Ou.isOpen(t)?(Ou.close(t),A.some(!0)):A.none()},onUp:(e,t)=>(s(e,t,Sg.highlightLast),A.some(!0)),onEnter:t=>{const o=kC.getCoupled(t,"sandbox"),n=Ou.isOpen(o);if(n&&!e.previewing.get())return i(o).bind((e=>Sg.getHighlighted(e))).map((e=>(Gr(t,HE(),{item:e}),!0)));{const s=Gu.getValue(t);return $r(t,Tr()),e.onExecute(o,t,s),n&&Ou.close(o),A.some(!0)}}}),Ph.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),kC.config({others:{sandbox:t=>VC(e,t,{onOpen:()=>Ph.on(t),onClose:()=>{e.lazyTypeaheadComp.get().each((e=>Tt(e.element,"aria-activedescendant"))),Ph.off(t)}})}}),Eh(l,[ia((t=>{e.lazyTypeaheadComp.set(A.some(t))})),la((t=>{e.lazyTypeaheadComp.set(A.none())})),da((t=>{const o=b;NC(e,a(t),t,n,o,bf.HighlightMenuAndItem).get(b)})),Zr(HE(),((t,o)=>{const n=kC.getCoupled(t,"sandbox");zE(e.model,t,o.event.item),$r(t,Tr()),e.onItemExecute(t,n,o.event.item,Gu.getValue(t)),Ou.close(n),VE(t)}))].concat(e.dismissOnBlur?[Zr(xr(),(e=>{const t=kC.getCoupled(e,"sandbox");bc(t.element).isNone()&&Ou.close(t)}))]:[]))],d={[Fr()]:[Gu.name(),NE.name(),l],...e.eventOrder};return{uid:e.uid,dom:Lx(En(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...Xu(e.typeaheadBehaviours,c)},eventOrder:d}}}),$E=e=>({...e,toCached:()=>$E(e.toCached()),bindFuture:t=>$E(e.bind((e=>e.fold((e=>MC(bn.error(e))),(e=>t(e)))))),bindResult:t=>$E(e.map((e=>e.bind(t)))),mapResult:t=>$E(e.map((e=>e.map(t)))),mapError:t=>$E(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>$E(AC((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n(bn.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),GE=e=>$E(AC(e)),jE=(e,t,o=[],n,s,r,a)=>{const i=t.fold((()=>({})),(e=>({action:e}))),l={buttonBehaviours:xa([ww((()=>!e.enabled||a.checkUiComponentContext(e.context).shouldDisable)),Rw((()=>a.checkUiComponentContext(e.context))),Wb.config({}),...r.map((e=>ev.config(a.tooltips.getConfig({tooltipText:a.translate(e)})))).toArray(),Eh("button press",[Qr("click")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...i},c=En(l,{dom:n});return En(c,{components:s})},qE=(e,t,o,n=[],s)=>{const r={tag:"button",classes:["tox-tbtn"],attributes:{...e.tooltip.map((e=>({"aria-label":o.translate(e)}))).getOr({}),"data-mce-name":s}},a=e.icon.map((e=>MT(e,o.icons))),i=Lw([a]);return jE(e,t,n,r,i,e.tooltip,o)},XE=e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}},YE=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>MT(e,o.icons))),i=[a.getOrThunk((()=>yl(r)))],l=e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary"),c={tag:"button",classes:[...XE(l),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s],attributes:{"aria-label":r,"data-mce-name":e.text}},d=e.icon.map(y(r));return jE(e,t,n,c,i,d,o)},KE=(e,t,o,n=[],s=[])=>{const r=YE(e,A.some(t),o,n,s);return Lb.sketch(r)},JE=(e,t)=>o=>{"custom"===t?Gr(o,Ok,{name:e,value:{}}):"submit"===t?$r(o,_k):"cancel"===t?$r(o,kk):console.error("Unknown button type: ",t)},QE=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,buttonType:"default",type:"menubutton",search:A.none(),onSetup:t=>(t.setEnabled(e.enabled),b),fetch:yE(n.items,t,o)},r=Hb(xE(s,"tox-tbtn",o,A.none(),!0,e.text.or(e.tooltip).getOrUndefined()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=JE(e.name,t),s={...e,context:"cancel"===t?"any":e.context,borderless:!1};return KE(s,n,o.shared.providers,[])}if(((e,t)=>"togglebutton"===t)(0,t))return((e,t,o)=>{var n,s;const r=e.icon.map((e=>DT(e,t.icons))).map(Hb),a=e.buttonType.getOr(e.primary?"primary":"secondary"),i={...e,name:null!==(n=e.name)&&void 0!==n?n:"",primary:"primary"===a,tooltip:e.tooltip,enabled:null!==(s=e.enabled)&&void 0!==s&&s,borderless:!1},l=i.tooltip.or(e.text).map((e=>({"aria-label":t.translate(e)}))).getOr({}),c=XE(null!=a?a:"secondary"),d=e.icon.isSome()&&e.text.isSome(),u={tag:"button",classes:[...c.concat(e.icon.isSome()?["tox-button--icon"]:[]),...e.active?["tox-button--enabled"]:[],...d?["tox-button--icon-and-text"]:[]],attributes:{...l,...g(o)?{"data-mce-name":o}:{}}},m=t.translate(e.text.getOr("")),p=yl(m),h=[...Lw([r.map((e=>e.asSpec()))]),...e.text.isSome()?[p]:[]],f=jE(i,A.some((o=>{Gr(o,Ok,{name:e.name,value:{setIcon:e=>{r.map((n=>n.getOpt(o).each((o=>{Th.set(o,[DT(e,t.icons)])}))))}}})})),[],u,h,e.tooltip,t);return Lb.sketch(f)})(e,o.shared.providers,e.text.or(e.tooltip).getOrUndefined());throw console.error("Unknown footer button type: ",t),new Error("Unknown footer button type")},ZE={type:"separator"},eA=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),tA=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),oA=(e,t)=>(e=>L(e,eA))(((e,t)=>P(t,(t=>t.type===e)))(e,t)),nA=e=>oA("header",e.targets),sA=e=>oA("anchor",e.targets),rA=e=>A.from(e.anchorTop).map((e=>tA("<top>",e))).toArray(),aA=e=>A.from(e.anchorBottom).map((e=>tA("<bottom>",e))).toArray(),iA=(e,t)=>{const o=e.toLowerCase();return P(t,(e=>{var t;const n=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text,s=null!==(t=e.value)&&void 0!==t?t:"";return _e(n.toLowerCase(),o)||_e(s.toLowerCase(),o)}))},lA=Pi("aria-invalid"),cA=(e,t)=>{e.dom.checked=t},dA=e=>e.dom.checked,uA=e=>(t,o,n,s,r)=>fe(o,"name").fold((()=>e(o,s,A.none(),r)),(a=>t.field(a,e(o,s,fe(n,a),r)))),mA={bar:uA(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:L(e.items,t.interpreter)}))(e,t.shared))),collection:uA(((e,t,o)=>Dk(e,t.shared.providers,o))),alertbanner:uA(((e,t)=>((e,t)=>{const o=lx(e.icon,t.icons);return uk.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:e.url?void 0:o},components:e.url?[Lb.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:o,attributes:{title:t.translate(e.iconTooltip)}},action:t=>Gr(t,Ok,{name:"alert-banner",value:e.url}),buttonBehaviours:xa([cx()])})]:void 0},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]})})(e,t.shared.providers))),input:uA(((e,t,o)=>((e,t,o)=>tE({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:A.none(),maximized:e.maximized,data:o,context:e.context},t))(e,t.shared.providers,o))),textarea:uA(((e,t,o)=>((e,t,o)=>tE({name:e.name,multiline:!0,label:e.label,inputMode:A.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:A.none(),maximized:e.maximized,data:o,context:e.context},t))(e,t.shared.providers,o))),label:uA(((e,t,o,n)=>((e,t,o)=>{const n="tox-label",s="center"===e.align?[`${n}--center`]:[],r="end"===e.align?[`${n}--end`]:[],a=Hb({dom:{tag:"label",classes:[n,...s,...r]},components:[yl(t.providers.translate(e.label))]}),i=L(e.items,t.interpreter);return{dom:{tag:"div",classes:["tox-form__group"]},components:[a.asSpec(),...i],behaviours:xa([W_(),Th.config({}),(l=A.none(),X_(l,ui,mi)),vh.config({mode:"acyclic"}),Eh("label",[ia((t=>{e.for.each((e=>{o(e).each((e=>{a.getOpt(t).each((t=>{var o;const n=null!==(o=kt(e.element,"id"))&&void 0!==o?o:Pi("form-field");St(e.element,"id",n),St(t.element,"for",n)}))}))}))}))])])};var l})(e,t.shared,n))),iframe:(VM=(e,t,o)=>((e,t,o)=>{const n="tox-dialog__iframe",s=e.transparent?[]:[`${n}--opaque`],r=e.border?["tox-navobj-bordered"]:[],a={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...e.sandboxed?{sandbox:"allow-scripts allow-same-origin"}:{}},i=((e,t)=>{const o=en(e.getOr(""));return{getValue:e=>o.get(),setValue:(e,n)=>{if(o.get()!==n){const o=e.element,s=()=>St(o,"srcdoc",n);t?kT.fold(y(CT),(e=>e.throttle))(o,n,s):s()}o.set(n)}}})(o,e.streamContent),l=e.label.map((e=>yk(e,t))),c=hk.parts.field({factory:{sketch:e=>aT(A.from(r),{uid:e.uid,dom:{tag:"iframe",attributes:a,classes:[n,...s]},behaviours:xa([Wb.config({}),Ih.config({}),q_(o,i.getValue,i.setValue),uc.config({channels:{[hT]:{onReceive:(e,t)=>{t.newFocus.each((t=>{at(e.element).each((o=>{(Ze(e.element,t)?Ma:Ba)(o,"tox-navobj-bordered-focus")}))}))}}}})])})}});return bk(l,c,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n,s)=>{const r=En(t,{source:"dynamic"});return uA(VM)(e,r,o,n,s)}),button:uA(((e,t)=>((e,t)=>{const o=JE(e.name,"custom");return n=A.none(),s=hk.parts.field({factory:Lb,...YE(e,A.some(o),t,[Y_(""),W_()])}),bk(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:uA(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),A.some(!0)),s=hk.parts.field({factory:{sketch:w},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:xa([W_(),mg.config({disabled:()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{at(e.element).each((e=>Ma(e,"tox-checkbox--disabled")))},onEnabled:e=>{at(e.element).each((e=>Ba(e,"tox-checkbox--disabled")))}}),Wb.config({}),Ih.config({}),X_(o,dA,cA),vh.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),Eh("checkbox-events",[Zr(dr(),((t,o)=>{Gr(t,wk,{name:e.name})}))])])}),r=hk.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[yl(t.translate(e.label))],behaviours:xa([Uk.config({})])}),a=e=>ux("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=Hb({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return hk.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:xa([mg.config({disabled:()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable}),Rw((()=>t.checkUiComponentContext(e.context)))])})})(e,t.shared.providers,o))),colorinput:uA(((e,t,o)=>((e,t,o,n)=>{const s=hk.parts.field({factory:Vx,inputClasses:["tox-textfield"],data:n,onSetValue:e=>Pk.run(e).get(b),inputBehaviours:xa([mg.config({disabled:()=>t.providers.isDisabled()||t.providers.checkUiComponentContext(e.context).shouldDisable}),Rw((()=>t.providers.checkUiComponentContext(e.context))),Wb.config({}),Pk.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>at(e.element),notify:{onValid:e=>{const t=Gu.getValue(e);Gr(e,Wk,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=Gu.getValue(e);if(0===t.length)return MC(bn.value(!0));{const e=Re("span");Mt(e,"background-color",t);const o=Rt(e,"background-color").fold((()=>bn.error("blah")),(e=>bn.value(t)));return MC(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>yk(e,t.providers))),a=(e,t)=>{Gr(e,$k,{value:t})},i=Hb(((e,t)=>$C.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:xa([Sw((()=>t.providers.isDisabled()||t.providers.checkUiComponentContext(e.context).shouldDisable)),Rw((()=>t.providers.checkUiComponentContext(e.context))),Uk.config({}),Wb.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>AC((t=>e.fetch(t))).map((n=>A.from(QC(En(uC(Pi("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,fx.CLOSE_ON_EXECUTE,T,t.providers),{movement:gC(e.columns,e.presets)}))))),parts:{menu:Fx(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[Yl,Xl,Zl],onLtr:()=>[Xl,Yl,Zl]},components:[],fetch:nC(o.getColors(e.storageKey),e.storageKey,o.hasCustomColors()),columns:o.getColorCols(e.storageKey),presets:"color",onItemAction:(t,n)=>{i.getOpt(t).each((t=>{"custom"===n?o.colorPicker((o=>{o.fold((()=>$r(t,Gk)),(o=>{a(t,o),zS(e.storageKey,o)}))}),"#ffffff"):a(t,"remove"===n?"":n)}))},context:e.context},t));return hk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:xa([Eh("form-field-events",[Zr(Wk,((t,o)=>{i.getOpt(t).each((e=>{Mt(e.element,"background-color",o.event.color)})),Gr(t,wk,{name:e.name})})),Zr($k,((e,t)=>{hk.getField(e).each((o=>{Gu.setValue(o,t.event.value),Qm.getCurrent(e).each(Ih.focus)}))})),Zr(Gk,((e,t)=>{hk.getField(e).each((t=>{Qm.getCurrent(e).each(Ih.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:uA(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=U_((e=>t=>r(t)?e.translate(K_[t]):e.translate(t))(t),n,t.tooltips.getConfig,((e,o,n=e,s=e)=>ux(n,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:t.translate(s),"aria-live":"polite",...o.fold((()=>({})),(e=>({id:e})))}},t.icons))),a=Hb(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{Gr(e,Ok,{name:"hex-valid",value:!0})},onInvalidHex:e=>{Gr(e,Ok,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[a.asSpec()],behaviours:xa([q_(o,(e=>{const t=a.get(e);return Qm.getCurrent(t).bind((e=>Gu.getValue(e).hex)).map((e=>"#"+Oe(e,"#"))).getOr("")}),((e,t)=>{const o=A.from(/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t)).bind((e=>ee(e,1))),n=a.get(e);Qm.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{Gu.setValue(e,{hex:o.getOr("")}),R_.getField(e,"hex").each((e=>{$r(e,cr())}))}))})),W_()])}})(0,t.shared.providers,o))),dropzone:uA(((e,t,o)=>tT(e,t.shared.providers,o))),grid:uA(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:L(e.items,t.interpreter)}))(e,t.shared))),listbox:uA(((e,t,o)=>((e,t,o)=>{const n=R(e.items,(e=>!PT(e))),s=t.shared.providers,r=o.bind((t=>$T(e.items,t))).orThunk((()=>te(e.items).filter(PT))),a=e.label.map((e=>yk(e,s))),i=hk.parts.field({dom:{},factory:{sketch:o=>NT({context:e.context,uid:o.uid,text:r.map((e=>e.text)),icon:A.none(),tooltip:A.none(),role:Ce(!n,"combobox"),...n?{}:{listRole:"listbox"},ariaLabel:e.label,fetch:(o,s)=>{const r=WT(o,e.name,e.items,Gu.getValue(o),n);s(HT(r,fx.CLOSE_ON_EXECUTE,t,{isHorizontalMenu:!1,search:A.none()}))},onSetup:y(b),getApi:y({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[Wb.config({}),q_(r.map((e=>e.value)),(e=>kt(e.element,UT)),((t,o)=>{$T(e.items,o).each((e=>{St(t.element,UT,e.value),Gr(t,IT,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),l={dom:{tag:"div",classes:["tox-listboxfield"]},components:[i]};return hk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:j([a.toArray(),[l]]),fieldBehaviours:xa([mg.config({disabled:()=>!e.enabled||s.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{hk.getField(e).each(mg.disable)},onEnabled:e=>{hk.getField(e).each(mg.enable)}})])})})(e,t,o))),selectbox:uA(((e,t,o)=>((e,t,o)=>{const n=L(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>yk(e,t))),r=hk.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:GT,selectBehaviours:xa([mg.config({disabled:()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable}),Wb.config({}),Eh("selectbox-change",[Zr(dr(),((t,o)=>{Gr(t,wk,{name:e.name})}))])])}),a=e.size>1?A.none():A.some(ux("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:j([[r],a.toArray()])};return hk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:j([s.toArray(),[i]]),fieldBehaviours:xa([mg.config({disabled:()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{hk.getField(e).each(mg.disable)},onEnabled:e=>{hk.getField(e).each(mg.enable)}}),Rw((()=>t.checkUiComponentContext(e.context)))])})})(e,t.shared.providers,o))),sizeinput:uA(((e,t)=>((e,t)=>{let o=ZT;const n=Pi("ratio-event"),s=e=>ux(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable,a=Rw((()=>t.checkUiComponentContext(e.context))),i=e.label.getOr("Constrain proportions"),l=t.translate(i),c=YT.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":l,"data-mce-name":i}},components:[s("lock"),s("unlock")],buttonBehaviours:xa([mg.config({disabled:r}),a,Wb.config({}),ev.config(t.tooltips.getConfig({tooltipText:l}))])}),d=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),u=t=>hk.parts.field({factory:Vx,inputClasses:["tox-textfield"],inputBehaviours:xa([mg.config({disabled:r}),a,Wb.config({}),Eh("size-input-events",[Zr(rr(),((e,o)=>{Gr(e,n,{isField1:t})})),Zr(dr(),((t,o)=>{Gr(t,wk,{name:e.name})}))])]),selectOnFocus:!1}),m=e=>({dom:{tag:"label",classes:["tox-label"]},components:[yl(t.translate(e))]}),g=YT.parts.field1(d([hk.parts.label(m("Width")),u(!0)])),p=YT.parts.field2(d([hk.parts.label(m("Height")),u(!1)]));return YT.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[g,p,d([m("\xa0"),c])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{JT(Gu.getValue(e)).each((e=>{o(e).each((e=>{Gu.setValue(t,KT(e))}))}))},coupledFieldBehaviours:xa([mg.config({disabled:r,onDisabled:e=>{YT.getField1(e).bind(hk.getField).each(mg.disable),YT.getField2(e).bind(hk.getField).each(mg.disable),YT.getLock(e).each(mg.disable)},onEnabled:e=>{YT.getField1(e).bind(hk.getField).each(mg.enable),YT.getField2(e).bind(hk.getField).each(mg.enable),YT.getLock(e).each(mg.enable)}}),Rw((()=>t.checkUiComponentContext("mode:design"))),Eh("size-input-events2",[Zr(n,((e,t)=>{const n=t.event.isField1,s=n?YT.getField1(e):YT.getField2(e),r=n?YT.getField2(e):YT.getField1(e),a=s.map(Gu.getValue).getOr(""),i=r.map(Gu.getValue).getOr("");o=eE(a,i)}))])])})})(e,t.shared.providers))),slider:uA(((e,t,o)=>((e,t,o)=>{const n=T_.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[yl(t.translate(e.label))]}),s=T_.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=T_.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return T_.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:y(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:xa([W_(),Ih.config({})]),onChoose:(t,o,n)=>{Gr(t,wk,{name:e.name,value:n})},onChange:(t,o,n)=>{Gr(t,wk,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:uA(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=Gu.getValue(t);o.addToHistory(n.value,e.filetype)},a={...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":lA,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{var n,s;const r=Gu.getValue(t),a=null!==(s=null===(n=null==r?void 0:r.meta)||void 0===n?void 0:n.text)&&void 0!==s?s:r.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=iA(a,(e=>L(e,(e=>tA(e,e))))(o.getHistory(e)));return"file"===e?(s=[n,iA(a,nA(t)),iA(a,j([rA(t),sA(t),aA(t)]))],W(s,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(ZE,t)),[])):n;var s}))})(e.filetype,n,o),r=HT(s,fx.BUBBLE_TO_SANDBOX,t,{isHorizontalMenu:!1,search:A.none()});return MC(r)},getHotspot:e=>g.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(Pk)&&Pk.run(e).get(b)},typeaheadBehaviours:xa([...o.getValidationHandler().map((t=>Pk.config({getRoot:e=>at(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{c.getOpt(e).each((e=>{St(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=Gu.getValue(o);return GE((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=bn.error(e.message);o(t)}else{const t=bn.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),mg.config({disabled:()=>!e.enabled||s.checkUiComponentContext(e.context).shouldDisable}),Wb.config({}),Eh("urlinput-events",[Zr(cr(),(t=>{const o=ul(t.element),n=o.trim();n!==o&&ml(t.element,n),"file"===e.filetype&&Gr(t,wk,{name:e.name})})),Zr(dr(),(t=>{Gr(t,wk,{name:e.name}),r(t)})),Zr(yr(),(t=>{Gr(t,wk,{name:e.name}),r(t)}))])]),eventOrder:{[cr()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:Fx(0,0,"normal")},onExecute:(e,t,o)=>{Gr(t,_k,{})},onItemExecute:(t,o,n,s)=>{r(t),Gr(t,wk,{name:e.name})}},i=hk.parts.field({...a,factory:WE}),l=e.label.map((e=>yk(e,s))),c=Hb(((e,t,o=e,n=e)=>ux(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",A.some(lA),"warning")),d=Hb({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[c.asSpec()]}),u=o.getUrlPicker(e.filetype),m=Pi("browser.url.event"),g=Hb({dom:{tag:"div",classes:["tox-control-wrap"]},components:[i,d.asSpec()],behaviours:xa([mg.config({disabled:()=>!e.enabled||s.checkUiComponentContext(e.context).shouldDisable})])}),p=Hb(KE({context:e.context,name:e.name,icon:A.some("browse"),text:e.picker_text.or(e.label).getOr(""),enabled:e.enabled,primary:!1,buttonType:A.none(),borderless:!0},(e=>$r(e,m)),s,[],["tox-browse-url"]));return hk.sketch({dom:xk([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:j([[g.asSpec()],u.map((()=>p.asSpec())).toArray()])}]),fieldBehaviours:xa([mg.config({disabled:()=>!e.enabled||s.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{hk.getField(e).each(mg.disable),p.getOpt(e).each(mg.disable)},onEnabled:e=>{hk.getField(e).each(mg.enable),p.getOpt(e).each(mg.enable)}}),Rw((()=>s.checkUiComponentContext(e.context))),Eh("url-input-events",[Zr(m,(t=>{Qm.getCurrent(t).each((o=>{const n=Gu.getValue(o),s={fieldname:e.name,...n};u.each((n=>{n(s).get((n=>{Gu.setValue(o,n),Gr(t,wk,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:uA((e=>{const t=nn(),o=Hb({dom:{tag:e.tag}}),n=nn(),s=!Q_(e)&&e.onFocus.isSome()?[Ih.config({onFocus:t=>{e.onFocus.each((e=>{e(t.element.dom)}))}}),Wb.config({})]:[];return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:xa([Eh("custom-editor-events",[ia((s=>{o.getOpt(s).each((o=>{(Q_(e)?e.init(o.element.dom):J_.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),q_(A.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),W_()].concat(s)),components:[o.asSpec()]}})),htmlpanel:uA(((e,t)=>((e,t)=>{const o=["tox-form__group",...e.stretched?["tox-form__group--stretched"]:[]],n=Eh("htmlpanel",[ia((t=>{e.onInit(t.element.dom)}))]);return"presentation"===e.presets?uk.sketch({dom:{tag:"div",classes:o,innerHtml:e.html},containerBehaviours:xa([ev.config({...t.tooltips.getConfig({tooltipText:"",onShow:e=>{mn(e.element,"[data-mce-tooltip]:hover").orThunk((()=>bc(e.element))).each((o=>{Ot(o,"data-mce-tooltip").each((o=>{ev.setComponents(e,t.tooltips.getComponents({tooltipText:o}))}))}))}}),mode:"children-normal",anchor:e=>({type:"node",node:mn(e.element,"[data-mce-tooltip]:hover").orThunk((()=>bc(e.element).filter((e=>Ot(e,"data-mce-tooltip").isSome())))),root:e.element,layouts:{onLtr:y([Zl,Ql,Xl,Kl,Yl,Jl]),onRtl:y([Zl,Ql,Xl,Kl,Yl,Jl])},bubble:$c(0,-2,{})})}),n])}):uk.sketch({dom:{tag:"div",classes:o,innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:xa([Wb.config({}),Ih.config({}),n])})})(e,t.shared.providers))),imagepreview:uA(((e,t,o)=>((e,t)=>{const o=en(t.getOr({url:""})),n=Hb({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=Hb({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:A.from(e.zoom),cachedWidth:A.from(e.cachedWidth),cachedHeight:A.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:xa([W_(),q_(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const{cachedWidth:t,cachedHeight:o,zoom:n}=r;if(!u(t)&&!u(o)){if(u(n)){const n=((e,t,o)=>{const n=Kt(e),s=Ut(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const a=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})(Kt(e.element),Ut(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{Dt(e.element,a)}))}};n.getOpt(e).each((o=>{const n=o.element;t.url!==kt(n,"src")&&(St(n,"src",t.url),Ba(e.element,"tox-imagepreview__loaded")),a(),Pw(n).then((t=>{e.getSystem().isConnected()&&(Ma(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:uA(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:L(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:L(n,(e=>({dom:{tag:"tr"},components:L(e,o)})))})],behaviours:xa([Wb.config({}),Ih.config({})])};var n,s})(e,t.shared.providers))),tree:uA(((e,t)=>((e,t)=>{const o=e.onLeafAction.getOr(b),n=e.onToggleExpand.getOr(b),s=e.defaultExpandedIds,r=en(s),a=en(e.defaultSelectedId),i=Pi("tree-id"),l=(n,s)=>e.items.map((e=>"leaf"===e.type?kE({leaf:e,selectedId:n,onLeafAction:o,visible:!0,treeId:i,backstage:t}):ME({directory:e,selectedId:n,onLeafAction:o,expandedIds:s,labelTabstopping:!0,treeId:i,backstage:t})));return{dom:{tag:"div",classes:["tox-tree"],attributes:{role:"tree"}},components:l(a.get(),r.get()),behaviours:xa([vh.config({mode:"flow",selector:".tox-tree--leaf__label--visible, .tox-tree--directory__label--visible",cycles:!1}),Eh(DE,[Zr("expand-tree-node",((e,t)=>{const{expanded:o,node:s}=t.event;r.set(o?[...r.get(),s]:r.get().filter((e=>e!==s))),n(r.get(),{expanded:o,node:s})}))]),uc.config({channels:{[`update-active-item-${i}`]:{onReceive:(e,t)=>{a.set(A.some(t.value)),Th.set(e,l(A.some(t.value),r.get()))}}}}),Th.config({})])}})(e,t))),panel:uA(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:L(e.items,t.shared.interpreter)}))(e,t)))},gA={field:(e,t)=>t,record:y([])},pA=(e,t,o,n,s)=>{const r=En(n,{shared:{interpreter:t=>hA(e,t,o,r,s)}});return hA(e,t,o,r,s)},hA=(e,t,o,n,s)=>fe(mA,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(r=>r(e,t,o,n,s))),fA=(e,t,o,n)=>hA(gA,e,t,o,n),bA="layout-inset",vA=e=>e.x,xA=(e,t)=>e.x+e.width/2-t.width/2,yA=(e,t)=>e.x+e.width-t.width,wA=e=>e.y,SA=(e,t)=>e.y+e.height-t.height,CA=(e,t)=>e.y+e.height/2-t.height/2,kA=(e,t,o)=>Ml(yA(e,t),SA(e,t),o.insetSouthwest(),Rl(),"southwest",Hl(e,{right:0,bottom:3}),bA),OA=(e,t,o)=>Ml(vA(e),SA(e,t),o.insetSoutheast(),Fl(),"southeast",Hl(e,{left:1,bottom:3}),bA),_A=(e,t,o)=>Ml(yA(e,t),wA(e),o.insetNorthwest(),Il(),"northwest",Hl(e,{right:0,top:2}),bA),TA=(e,t,o)=>Ml(vA(e),wA(e),o.insetNortheast(),Bl(),"northeast",Hl(e,{left:1,top:2}),bA),EA=(e,t,o)=>Ml(xA(e,t),wA(e),o.insetNorth(),Nl(),"north",Hl(e,{top:2}),bA),AA=(e,t,o)=>Ml(xA(e,t),SA(e,t),o.insetSouth(),zl(),"south",Hl(e,{bottom:3}),bA),MA=(e,t,o)=>Ml(yA(e,t),CA(e,t),o.insetEast(),Vl(),"east",Hl(e,{right:0}),bA),DA=(e,t,o)=>Ml(vA(e),CA(e,t),o.insetWest(),Ll(),"west",Hl(e,{left:1}),bA),BA=e=>{switch(e){case"north":return EA;case"northeast":return TA;case"northwest":return _A;case"south":return AA;case"southeast":return OA;case"southwest":return kA;case"east":return MA;case"west":return DA}},IA=(e,t,o,n,s)=>Ec(n).map(BA).getOr(EA)(e,t,o,n,s),FA=e=>{switch(e){case"north":return AA;case"northeast":return OA;case"northwest":return kA;case"south":return EA;case"southeast":return TA;case"southwest":return _A;case"east":return DA;case"west":return MA}},RA=(e,t,o,n,s)=>Ec(n).map(FA).getOr(EA)(e,t,o,n,s),NA={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},zA=(e,t,o)=>{const n={maxHeightFunction:Hc()};return()=>o()?{type:"node",root:ht(pt(e())),node:A.from(e()),bubble:$c(12,12,NA),layouts:{onRtl:()=>[TA],onLtr:()=>[_A]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:$c(-12,12,NA),layouts:{onRtl:()=>[Xl,Yl,Zl],onLtr:()=>[Yl,Xl,Zl]},overrides:n}},LA=(e,t,o,n)=>{const s={maxHeightFunction:Hc()};return()=>n()?{type:"node",root:ht(pt(t())),node:A.from(t()),bubble:$c(12,12,NA),layouts:{onRtl:()=>[EA],onLtr:()=>[EA]},overrides:s}:e?{type:"node",root:ht(pt(t())),node:A.from(t()),bubble:$c(0,-Wt(t()),NA),layouts:{onRtl:()=>[Ql],onLtr:()=>[Ql]},overrides:s}:{type:"hotspot",hotspot:o(),bubble:$c(0,0,NA),layouts:{onRtl:()=>[Ql],onLtr:()=>[Ql]},overrides:s}},VA=(e,t,o)=>()=>o()?{type:"node",root:ht(pt(e())),node:A.from(e()),layouts:{onRtl:()=>[EA],onLtr:()=>[EA]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[Zl],onLtr:()=>[Zl]}},HA=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng(),o=e.model.table.getSelectedCells();if(o.length>1){const e=o[0],t=o[o.length-1],n={firstCell:ze(e),lastCell:ze(t)};return A.some(n)}return A.some(bd.range(ze(t.startContainer),t.startOffset,ze(t.endContainer),t.endOffset))}}),PA=e=>t=>({type:"node",root:e(),node:t}),UA=(e,t,o,n)=>{const s=Ob(e),r=()=>ze(e.getBody()),a=()=>ze(e.getContentAreaContainer()),i=()=>s||!n();return{inlineDialog:zA(a,t,i),inlineBottomDialog:LA(e.inline,a,o,i),banner:VA(a,t,i),cursor:HA(e,r),node:PA(r)}},WA=e=>(t,o)=>{dC(e)(t,o)},$A=e=>()=>KS(e),GA=e=>t=>jS(e,t),jA=e=>t=>YS(e,t),qA=e=>()=>rb(e),XA=e=>ve(e,"items"),YA=e=>ve(e,"format"),KA=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],JA=e=>W(e,((e,t)=>{if(be(t,"items")){const o=JA(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(be(t,"inline")||(e=>be(e,"block"))(t)||(e=>be(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),QA=e=>Lf(e).map((t=>{const o=((e,t)=>{const o=JA(t),n=t=>{V(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return Vf(e)?KA.concat(o):o})).getOr(KA),ZA=(e,t,o)=>({...e,type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)}),eM=(e,t,o,n)=>{const s=t=>L(t,(t=>XA(t)?(e=>{const t=s(e.items);return{...e,type:"submenu",getStyleItems:y(t)}})(t):YA(t)?(e=>ZA(e,o,n))(t):(e=>{const t=re(e);return 1===t.length&&F(t,"title")})(t)?{...t,type:"separator"}:(t=>{const s=r(t.name)?t.name:Pi(t.title),a=`custom-${s}`,i={...t,type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)};return e.formatter.register(s,i),i})(t)));return s(t)},tM=e=>{let t=0;const o=e=>[{dom:{tag:"div",classes:["tox-tooltip__body"]},components:[yl(e.tooltipText)]}];return{getConfig:n=>({delayForShow:()=>t>0?60:300,delayForHide:y(300),exclusive:!0,lazySink:e,tooltipDom:{tag:"div",classes:["tox-tooltip","tox-tooltip--up"]},tooltipComponents:o(n),onShow:(e,o)=>{t++,n.onShow&&n.onShow(e,o)},onHide:(e,o)=>{t--,n.onHide&&n.onHide(e,o)},onSetup:n.onSetup}),getComponents:o}},oM=Z_.trim,nM=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},sM=nM("true"),rM=nM("false"),aM=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),iM=e=>e.innerText||e.textContent,lM=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&dM(e),cM=e=>e&&/^(H[1-6])$/.test(e.nodeName),dM=e=>(e=>{let t=e;for(;t=t.parentNode;){const e=t.contentEditable;if(e&&"inherit"!==e)return sM(t)}return!1})(e)&&!rM(e),uM=e=>cM(e)&&dM(e),mM=e=>{var t;const o=(e=>e.id?e.id:Pi("h"))(e);return aM("header",null!==(t=iM(e))&&void 0!==t?t:"","#"+o,(e=>cM(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=o}))},gM=e=>{const t=e.id||e.name,o=iM(e);return aM("anchor",o||"#"+t,"#"+t,0,b)},pM=e=>oM(e.title).length>0,hM=e=>{const t=(e=>{const t=L(Od(ze(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return P((e=>L(P(e,uM),mM))(t).concat((e=>L(P(e,lM),gM))(t)),pM)},fM="tinymce-url-history",bM=e=>r(e)&&/^https?/.test(e),vM=e=>a(e)&&pe(e,(e=>{return!(l(t=e)&&t.length<=5&&X(t,bM));var t})).isNone(),xM=()=>{const e=FS.getItem(fM);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+fM+" was not valid JSON",e),{};throw e}return vM(t)?t:(console.log("Local storage "+fM+" was not valid format",t),{})},yM=e=>{const t=xM();return fe(t,e).getOr([])},wM=(e,t)=>{if(!bM(e))return;const o=xM(),n=fe(o,t).getOr([]),s=P(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!vM(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));FS.setItem(fM,JSON.stringify(e))})(o)},SM=e=>!!e,CM=e=>le(Z_.makeMap(e,/[, ]/),SM),kM=e=>A.from(Qf(e)),OM=e=>A.from(e).filter(r).getOrUndefined(),_M=e=>({getHistory:yM,addToHistory:wM,getLinkInformation:()=>(e=>ob(e)?A.some({targets:hM(e.getBody()),anchorTop:OM(nb(e)),anchorBottom:OM(sb(e))}):A.none())(e),getValidationHandler:()=>(e=>A.from(Zf(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=A.from(tb(e)).filter(SM).map(CM);return kM(e).fold(T,(e=>t.fold(E,(e=>re(e).length>0&&e))))})(e);return d(o)?o?kM(e):A.none():o[t]?kM(e):A.none()})(e,t).map((o=>n=>AC((s=>{const i={filetype:t,fieldname:n.fieldname,...A.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),TM=Pm,EM=km,AM=y([Ds("shell",!1),ps("makeItem"),Ds("setupItem",b),Yu("listBehaviours",[Th])]),MM=wm({name:"items",overrides:()=>({behaviours:xa([Th.config({})])})}),DM=y([MM]),BM=Xm({name:y("CustomList")(),configFields:AM(),partFields:DM(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Th.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Xu(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?A.some(n):Im(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=Th.contents(n),r=o.length,a=r-s.length,i=a>0?N(a,(()=>e.makeItem())):[],l=s.slice(r);V(l,(e=>Th.remove(n,e))),V(i,(e=>Th.append(n,e)));const c=Th.contents(n);V(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),IM=y([ps("dom"),Ds("shell",!0),ju("toolbarBehaviours",[Th])]),FM=y([wm({name:"groups",overrides:()=>({behaviours:xa([Th.config({})])})})]),RM=Xm({name:"Toolbar",configFields:IM(),partFields:FM(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Th.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Xu(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?A.some(n):Im(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{Th.set(e,o)}))},refresh:b},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),NM=b,zM=T,LM=y([]);var VM,HM=Object.freeze({__proto__:null,setup:NM,isDocked:zM,getBehaviours:LM});const PM=y(Pi("toolbar-height-change")),UM={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},WM="tox-tinymce--toolbar-sticky-on",$M="tox-tinymce--toolbar-sticky-off",GM=(e,t)=>F(Ri.getModes(e),t),jM=e=>{const t=e.element;at(t).each((o=>{const n="padding-"+Ri.getModes(e)[0];if(Ri.isDocked(e)){const e=Kt(o);Mt(t,"width",e+"px"),Mt(o,n,(e=>Wt(e)+(parseInt(It(e,"margin-top"),10)||0)+(parseInt(It(e,"margin-bottom"),10)||0))(t)+"px")}else Lt(t,"width"),Lt(o,n)}))},qM=(e,t)=>{t?(Ba(e,UM.fadeOutClass),Fa(e,[UM.transitionClass,UM.fadeInClass])):(Ba(e,UM.fadeInClass),Fa(e,[UM.fadeOutClass,UM.transitionClass]))},XM=(e,t)=>{const o=ze(e.getContainer());t?(Ma(o,WM),Ba(o,$M)):(Ma(o,$M),Ba(o,WM))},YM=(e,t)=>{const o=nn(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||jM(t),XM(e,Ri.isDocked(t)),t.getSystem().broadcastOn([Tu()],{}),n().each((e=>e.getSystem().broadcastOn([Tu()],{})))},a=e.inline?[]:[uc.config({channels:{[PM()]:{onReceive:jM}}})];return[Ih.config({}),Ri.config({contextual:{lazyContext:t=>{const o=Wt(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer();return A.from(n).map((n=>{const s=Ko(ze(n));return Nb(e,t.element).fold((()=>{const e=s.height-o,n=s.y+(GM(t,"top")?0:o);return Yo(s.x,n,s.width,e)}),(e=>{const n=Qo(s,zb(e)),r=GM(t,"top")?n.y:n.y+o;return Yo(n.x,r,n.width,n.height-o)}))}))},onShow:()=>{s((e=>qM(e,!0)))},onShown:e=>{s((e=>Ra(e,[UM.transitionClass,UM.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=tt(t);fc(o).filter((e=>!Ze(t,e))).filter((t=>Ze(t,ze(o.dom.body))||et(e,t))).each((()=>gc(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>bc(e).orThunk((()=>t().toOptional().bind((e=>bc(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>qM(e,!1)))},onHidden:()=>{s((e=>Ra(e,[UM.transitionClass])))},...UM},lazyViewport:t=>Nb(e,t.element).fold((()=>{const o=Zo(),n=Yf(e),s=o.y+(GM(t,"top")&&!Rb(e)?n:0),r=o.height-(GM(t,"bottom")?n:0);return{bounds:Yo(o.x,s,o.width,r),optScrollEnv:A.none()}}),(e=>({bounds:zb(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:qt(e.element).top})}))),modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var KM=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each(Ri.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(jM)})),e.on("SkinLoaded",(()=>{o().each((e=>{Ri.isDocked(e)?Ri.reset(e):Ri.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each(Ri.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{Ri.refresh(t);const o=t.element;up(o)&&((e,t)=>{const o=tt(t),n=st(t).dom.innerHeight,s=Po(o),r=ze(e.elm),a=Jo(r),i=Ut(r),l=a.y,c=l+i,d=qt(t),u=Ut(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)Uo(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;Uo(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{XM(e,!1)}))},isDocked:e=>e().map(Ri.isDocked).getOr(!1),getBehaviours:YM});const JM=Wn([ny,hs("items",Gn([qn([sy,Ss("items",Jn)]),Jn]))].concat(Ry)),QM=[Fs("buttonType","default"),_s("text"),_s("tooltip"),_s("icon"),Bs("search",!1,Gn([Qn,Wn([_s("placeholder")])],(e=>d(e)?e?A.some({placeholder:A.none()}):A.none():A.some(e)))),xs("fetch"),zs("onSetup",(()=>b)),Fs("context","mode:design")],ZM=Wn([ny,...QM]),eD=e=>rs("menubutton",ZM,e),tD=Wn([ny,yy,xy,by,Cy,uy,hy,Rs("presets","normal",["normal","color","listpreview"]),Ey(1),gy,py,Fs("context","mode:design")]);var oD=qm({factory:(e,t)=>{const o={focus:vh.focusIn,setMenus:(e,o)=>{const n=L(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())},context:"any"},n=eD(o).mapError((e=>ls(e))).getOrDie();return xE(n,"tox-mbtn",t.backstage,A.some("menuitem"))}));Th.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:xa([Th.config({}),Eh("menubar-events",[ia((t=>{e.onSetup(t)})),Zr(sr(),((e,t)=>{mn(e.element,".tox-mbtn--active").each((o=>{gn(t.event.target,".tox-mbtn").each((t=>{Ze(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{$C.expand(e),$C.close(o),Ih.focus(e)}))}))}))}))})),Zr(zr(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{$C.isOpen(o)&&($C.expand(e),$C.close(o))}))}))}))]),vh.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),A.some(!0))}),Wb.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[ps("dom"),ps("uid"),ps("onEscape"),ps("backstage"),Ds("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const nD="container",sD=[ju("slotBehaviours",[])],rD=e=>"<alloy.field."+e+">",aD=(e,t)=>{const o=t=>zm(e),n=(t,o)=>(n,s)=>Im(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==kt(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;Mt(o,"display","none"),St(o,"aria-hidden","true"),Gr(e,Lr(),{name:t,visible:!1})}})),i=(e=>(t,o)=>{V(o,(o=>e(t,o)))})(a),l=n(((e,t)=>{if(!s(e)){const o=e.element;Lt(o,"display"),Tt(o,"aria-hidden"),Gr(e,Lr(),{name:t,visible:!0})}})),c={getSlotNames:o,getSlot:(t,o)=>Im(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:l};return{uid:e.uid,dom:e.dom,components:t,behaviours:qu(e.slotBehaviours),apis:c}},iD={...le({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>tl(e))),sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),Em(nD,rD(t),o)),record:y(e)}})(),o=e(t),n=t.record(),s=L(n,(e=>xm({name:e,pname:rD(e)})));return Wm(nD,sD,s,aD,o)}},lD=Wn([xy,yy,zs("onShow",b),zs("onHide",b),hy]),cD=e=>({element:()=>e.element.dom}),dD=(e,t)=>{const o=L(re(t),(e=>{const o=t[e],n=as((e=>rs("sidebar",lD,e))(o));return{name:e,getApi:cD,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return L(o,(t=>{const n=en(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:yw([_w(t,n),Tw(t,n),Zr(Lr(),((e,t)=>{const n=t.event,s=$(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},uD=e=>iD.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:dD(t,e),slotBehaviours:yw([ia((e=>iD.hideAllSlots(e)))])}))),mD=(e,t)=>{St(e,"role",t)},gD=e=>Qm.getCurrent(e).bind((e=>bE.isGrowing(e)||bE.hasGrown(e)?Qm.getCurrent(e).bind((e=>$(iD.getSlotNames(e),(t=>iD.isShowing(e,t))))):A.none())),pD=Pi("FixSizeEvent"),hD=Pi("AutoSizeEvent");var fD=Object.freeze({__proto__:null,block:(e,t,o,n)=>{St(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=xa([vh.config({mode:"special",onTab:()=>A.some(!0),onShiftTab:()=>A.some(!0)}),Ih.config({})]),a=n(s,r),i=s.getSystem().build(a);Th.append(s,Ol(i)),i.hasConfigured(vh)&&t.focus&&vh.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>Th.remove(s,i)))},unblock:(e,t,o)=>{Tt(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()},isBlocked:(e,t,o)=>o.isBlocked()}),bD=[zs("getRoot",A.none),Ns("focus",!0),Ti("onBlock"),Ti("onUnblock")];const vD=wa({fields:bD,name:"blocking",apis:fD,state:Object.freeze({__proto__:null,init:()=>{const e=tn((e=>e.destroy()));return va({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),xD=e=>Qm.getCurrent(e).each((e=>gc(e.element,!0))),yD=(e,t,o)=>{const n=en(!1),s=nn(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?te(s.composedPath()):A.from(s.target)).map(ze).filter(Ge).exists((e=>Ia(e,"mce-pastebin"))))&&(o.preventDefault(),xD(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n=`data-mce-${o}`;A.from(e.iframeElement).map(ze).each((e=>{t?(Ot(e,o).each((t=>St(e,n,t))),St(e,o,-1)):(Tt(e,o),Ot(e,n).each((t=>{St(e,o,t),Tt(e,n)})))}))})(e,o),o)vD.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:Vb('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),Lt(s,"display"),Tt(s,"aria-hidden"),e.hasFocus()&&xD(t);else{const o=Qm.getCurrent(t).exists((e=>hc(e.element)));vD.unblock(t),Mt(s,"display","none"),St(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),((e,t)=>{e.dispatch("AfterProgressState",{state:t})})(e,s))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=Cf.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},wD=(e,t,o)=>({within:e,extra:t,withinWidth:o}),SD=(e,t,o)=>{const n=W(e,((e,t)=>((e,t)=>{const n=o(e);return A.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(y(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=P(n,(e=>e.finish<=t)),r=U(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},CD=e=>L(e,(e=>e.element)),kD=(e,t)=>{const o=L(t,(e=>Ol(e)));RM.setGroups(e,o)},OD=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=Fm(e,t,"primary"),r=kC.getCoupled(e,"overflowGroup");Mt(s.element,"visibility","hidden");const a=n.concat([r]),i=se(a,(e=>bc(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),kD(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=SD(t,e,o);return 0===n.extra.length?A.some(n):A.none()})(e,t,o).getOrThunk((()=>SD(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=CD(e.concat(t));return wD(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=CD(e).concat([o]);return wD(s,CD(t),n)})(r,a,n,i):((e,t,o)=>wD(CD(e),[],o))(r,0,i)})(Kt(s.element),t.builtGroups.get(),(e=>Math.ceil(e.element.dom.getBoundingClientRect().width)),r);0===l.extra.length?(Th.remove(s,r),o([])):(kD(s,l.within),o(l.extra)),Lt(s.element,"visibility"),Vt(s.element),i.each(Ih.focus)},_D=y([ju("splitToolbarBehaviours",[kC]),ms("builtGroups",(()=>en([])))]),TD=y([Oi(["overflowToggledClass"]),Es("getOverflowBounds"),ps("lazySink"),ms("overflowGroups",(()=>en([]))),Ti("onOpened"),Ti("onClosed")].concat(_D())),ED=y([xm({factory:RM,schema:IM(),name:"primary"}),ym({schema:IM(),name:"overflow"}),ym({name:"overflow-button"}),ym({name:"overflow-group"})]),AD=y(((e,t)=>{((e,t)=>{const o=Yt.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);Mt(e,"max-width",o+"px")})(e,Math.floor(t))})),MD=y([Oi(["toggledClass"]),ps("lazySink"),xs("fetch"),Es("getBounds"),Ms("fireDismissalEventInstead",[Ds("event",Rr())]),Qc(),Ti("onToggled")]),DD=y([ym({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:xa([Ph.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1,onToggled:e.onToggled})])})}),ym({factory:RM,schema:IM(),name:"toolbar",overrides:e=>({toolbarBehaviours:xa([vh.config({mode:"cyclic",onEscape:t=>(Im(t,e,"button").each(Ih.focus),A.none())})])})})]),BD=nn(),ID=(e,t)=>{const o=kC.getCoupled(e,"toolbarSandbox");Ou.isOpen(o)?Ou.close(o):Ou.open(o,t.toolbar())},FD=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();Zd.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:AD()}}},s)},RD=(e,t,o,n,s)=>{RM.setGroups(t,s),FD(e,t,o,n),Ph.on(e)},ND=Xm({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...Lb.sketch({...n.button(),action:e=>{ID(e,n)},buttonBehaviours:Ku({dump:n.button().buttonBehaviours},[kC.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=El();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:xa([vh.config({mode:"special",onEscape:e=>(Ou.close(e),A.some(!0))}),Ou.config({onOpen:(s,r)=>{const a=BD.get().getOr(!1);o.fetch().get((s=>{RD(e,r,o,t.layouts,s),n.link(e.element),a||vh.focusIn(r)}))},onClose:()=>{Ph.off(e),BD.get().getOr(!1)||Ih.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>Al(o,n)||Al(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),uc.config({channels:{...Mu({isExtraPart:T,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...Bu({doReposition:()=>{Ou.getState(kC.getCoupled(e,"toolbarSandbox")).each((n=>{FD(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{Ou.getState(kC.getCoupled(t,"toolbarSandbox")).each((s=>{RD(t,s,e,o.layouts,n)}))},reposition:t=>{Ou.getState(kC.getCoupled(t,"toolbarSandbox")).each((n=>{FD(t,n,e,o.layouts)}))},toggle:e=>{ID(e,n)},toggleWithoutFocusing:e=>{((e,t)=>{BD.set(!0),ID(e,t),BD.clear()})(e,n)},getToolbar:e=>Ou.getState(kC.getCoupled(e,"toolbarSandbox")),isOpen:e=>Ou.isOpen(kC.getCoupled(e,"toolbarSandbox"))}}),configFields:MD(),partFields:DD(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggleWithoutFocusing(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),zD=y([ps("items"),Oi(["itemSelector"]),ju("tgroupBehaviours",[vh])]),LD=y([Sm({name:"items",unit:"item"})]),VD=Xm({name:"ToolbarGroup",configFields:zD(),partFields:LD(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Xu(e.tgroupBehaviours,[vh.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),HD=e=>L(e,(e=>Ol(e))),PD=(e,t,o)=>{OD(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{ND.setGroups(e,HD(n))}))}))},UD=Xm({name:"SplitFloatingToolbar",configFields:TD(),partFields:ED(),factory:(e,t,o,n)=>{const s=Hb(ND.sketch({fetch:()=>AC((t=>{t(HD(e.overflowGroups.get()))})),layouts:{onLtr:()=>[Yl,Xl],onRtl:()=>[Xl,Yl],onBottomLtr:()=>[Jl,Kl],onBottomRtl:()=>[Kl,Jl]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()},onToggled:(t,o)=>e[o?"onOpened":"onClosed"](t)}));return{uid:e.uid,dom:e.dom,components:t,behaviours:Xu(e.splitToolbarBehaviours,[kC.config({others:{overflowGroup:()=>VD.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(L(o,t.getSystem().build)),PD(t,s,e)},refresh:t=>PD(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{ND.toggle(e)}))},toggleWithoutFocusing:e=>{s.getOpt(e).each(ND.toggleWithoutFocusing)},isOpen:e=>s.getOpt(e).map(ND.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{ND.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(ND.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),WD=y([Oi(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),Ti("onOpened"),Ti("onClosed")].concat(_D())),$D=y([xm({factory:RM,schema:IM(),name:"primary"}),xm({factory:RM,schema:IM(),name:"overflow",overrides:e=>({toolbarBehaviours:xa([bE.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{Im(t,e,"overflow-button").each((e=>{Ph.off(e)})),e.onClosed(t)},onGrown:t=>{e.onOpened(t)},onStartGrow:t=>{Im(t,e,"overflow-button").each(Ph.on)}}),vh.config({mode:"acyclic",onEscape:t=>(Im(t,e,"overflow-button").each(Ih.focus),A.some(!0))})])})}),ym({name:"overflow-button",overrides:e=>({buttonBehaviours:xa([Ph.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])})}),ym({name:"overflow-group"})]),GD=(e,t,o)=>{Im(e,t,"overflow-button").each((n=>{Im(e,t,"overflow").each((s=>{if(jD(e,t),bE.hasShrunk(s)){const e=t.onOpened;t.onOpened=n=>{o||vh.focusIn(s),e(n),t.onOpened=e}}else{const e=t.onClosed;t.onClosed=s=>{o||Ih.focus(n),e(s),t.onClosed=e}}bE.toggleGrow(s)}))}))},jD=(e,t)=>{Im(e,t,"overflow").each((o=>{OD(e,t,(e=>{const t=L(e,(e=>Ol(e)));RM.setGroups(o,t)})),Im(e,t,"overflow-button").each((e=>{bE.hasGrown(o)&&Ph.on(e)})),bE.refresh(o)}))},qD=Xm({name:"SplitSlidingToolbar",configFields:WD(),partFields:$D(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:Xu(e.splitToolbarBehaviours,[kC.config({others:{overflowGroup:e=>VD.sketch({...n["overflow-group"](),items:[Lb.sketch({...n["overflow-button"](),action:t=>{$r(e,s)}})]})}}),Eh("toolbar-toggle-events",[Zr(s,(t=>{GD(t,e,!1)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=L(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),jD(t,e)},refresh:t=>jD(t,e),toggle:t=>{GD(t,e,!1)},toggleWithoutFocusing:t=>{GD(t,e,!0)},isOpen:t=>((e,t)=>Im(e,t,"overflow").map(bE.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),XD=e=>{const t=e.label.isNone()?e.title.fold((()=>({})),(e=>({attributes:{"aria-label":e}}))):e.label.fold((()=>({})),(e=>({attributes:{"aria-label":e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"].concat(e.label.isSome()?["tox-toolbar__group_with_label"]:[]),...t},components:[...e.label.map((e=>({dom:{tag:"span",classes:["tox-label","tox-label--context-toolbar"]},components:[yl(e)]}))).toArray(),VD.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-item:not([disabled]), .tox-number-input:not([disabled])"},tgroupBehaviours:xa([Wb.config({}),Ih.config({ignore:!0})])}},YD=e=>VD.sketch(XD(e)),KD=(e,t)=>{const o=ia((t=>{const o=L(e.initGroups,YD);RM.setGroups(t,o)}));return xa([kw((()=>e.providers.checkUiComponentContext("any").shouldDisable)),Rw((()=>e.providers.checkUiComponentContext("any"))),vh.config({mode:t,onEscape:e.onEscape,visibilitySelector:".tox-toolbar__overflow",selector:".tox-toolbar__group"}),Eh("toolbar-events",[o])])},JD=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":XD({title:A.none(),label:A.none(),items:[]}),"overflow-button":qE({context:"any",name:"more",icon:A.some("more-drawer"),enabled:!0,tooltip:A.some("Reveal or hide additional toolbar items"),primary:!1,buttonType:A.none(),borderless:!1},A.none(),e.providers,[],"overflow-button")},splitToolbarBehaviours:KD(e,t)}},QD=e=>{const t=JD(e),o=UD.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return UD.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=Jo(t),n=nt(t),s=Jo(n),r=Math.max(n.dom.scrollHeight,s.height);return Yo(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>e.onToggled(t,!0),onClosed:t=>e.onToggled(t,!1)})},ZD=e=>{const t=qD.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=qD.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=JD(e);return qD.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>{t.getSystem().broadcastOn([PM()],{type:"opened"}),e.onToggled(t,!0)},onClosed:t=>{t.getSystem().broadcastOn([PM()],{type:"closed"}),e.onToggled(t,!1)}})},eB=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return RM.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===wf.scrolling?["tox-toolbar--scrolling"]:[])},components:[RM.parts.groups({})],toolbarBehaviours:KD(e,t)})},tB=[by,xy,_s("tooltip"),Rs("buttonType","secondary",["primary","secondary"]),Ns("borderless",!1),xs("onAction"),Fs("context","mode:design")],oB={button:[...tB,ay,vs("type",["button"])],togglebutton:[...tB,Ns("active",!1),vs("type",["togglebutton"])]},nB=[vs("type",["group"]),Ls("buttons",[],cs("type",oB))],sB=cs("type",{...oB,group:nB}),rB=Wn([Ls("buttons",[],sB),xs("onShow"),xs("onHide")]),aB=(e,t)=>((e,t)=>{var o,n;const s="togglebutton"===e.type,r=e.icon.map((e=>DT(e,t.icons))).map(Hb),a={...e,name:s?e.text.getOr(e.icon.getOr("")):null!==(o=e.text)&&void 0!==o?o:e.icon.getOr(""),primary:"primary"===e.buttonType,buttonType:A.from(e.buttonType),tooltip:e.tooltip,icon:e.icon,enabled:!0,borderless:e.borderless},i=XE(null!==(n=e.buttonType)&&void 0!==n?n:"secondary"),l=s?e.text.map(t.translate):A.some(t.translate(e.text)),c=l.map(yl),d=a.tooltip.or(l).map((e=>({"aria-label":t.translate(e)}))).getOr({}),u=r.map((e=>e.asSpec())),m=Lw([u,c]),g=e.icon.isSome()&&c.isSome(),p={tag:"button",classes:i.concat(...e.icon.isSome()&&!g?["tox-button--icon"]:[]).concat(...g?["tox-button--icon-and-text"]:[]).concat(...e.borderless?["tox-button--naked"]:[]).concat(..."togglebutton"===e.type&&e.active?["tox-button--enabled"]:[]),attributes:d},h=jE(a,A.some((o=>{const n=e=>{r.map((n=>n.getOpt(o).each((o=>{Th.set(o,[DT(e,t.icons)])}))))};return s?e.onAction({setIcon:n,setActive:e=>{const t=o.element;e?(Ma(t,"tox-button--enabled"),St(t,"aria-pressed",!0)):(Ba(t,"tox-button--enabled"),Tt(t,"aria-pressed"))},isActive:()=>Ia(o.element,"tox-button--enabled"),focus:()=>gc(o.element)}):"button"===e.type?e.onAction({setIcon:n}):void 0})),[],p,m,e.tooltip,t);return Lb.sketch(h)})(e,t),iB=Mo().deviceType,lB=iB.isPhone(),cB=iB.isTablet();var dB=Xm({name:"silver.View",configFields:[ps("viewConfig")],partFields:[wm({factory:{sketch:e=>{let t=!1;const o=L(e.buttons,(o=>"group"===o.type?(t=!0,((e,t)=>({dom:{tag:"div",classes:["tox-view__toolbar__group"]},components:L(e.buttons,(e=>aB(e,t)))}))(o,e.providers)):aB(o,e.providers)));return{uid:e.uid,dom:{tag:"div",classes:[t?"tox-view__toolbar":"tox-view__header",...lB||cB?["tox-view--mobile","tox-view--scrolling"]:[]]},behaviours:xa([Ih.config({}),vh.config({mode:"flow",selector:"button, .tox-button",focusInside:Gg.OnEnterOrSpaceMode})]),components:t?o:[uk.sketch({dom:{tag:"div",classes:["tox-view__header-start"]},components:[]}),uk.sketch({dom:{tag:"div",classes:["tox-view__header-end"]},components:o})]}}},schema:[ps("buttons"),ps("providers")],name:"header"}),wm({factory:{sketch:e=>({uid:e.uid,behaviours:xa([Ih.config({}),Wb.config({})]),dom:{tag:"div",classes:["tox-view__pane"]}})},schema:[],name:"pane"})],factory:(e,t,o,n)=>{const s={getPane:t=>TM.getPart(t,e,"pane"),getOnShow:t=>e.viewConfig.onShow,getOnHide:t=>e.viewConfig.onHide};return{uid:e.uid,dom:e.dom,components:t,behaviours:xa([Ih.config({}),vh.config({mode:"cyclic",focusInside:Gg.OnEnterOrSpaceMode})]),apis:s}},apis:{getPane:(e,t)=>e.getPane(t),getOnShow:(e,t)=>e.getOnShow(t),getOnHide:(e,t)=>e.getOnHide(t)}});const uB=(e,t,o)=>ge(t,((t,n)=>{const s=as(rs("view",rB,t));return e.slot(n,dB.sketch({dom:{tag:"div",classes:["tox-view"]},viewConfig:s,components:[...s.buttons.length>0?[dB.parts.header({buttons:s.buttons,providers:o})]:[],dB.parts.pane({})]}))})),mB=(e,t)=>iD.sketch((o=>({dom:{tag:"div",classes:["tox-view-wrap__slot-container"]},components:uB(o,e,t),slotBehaviours:yw([ia((e=>iD.hideAllSlots(e)))])}))),gB=e=>$(iD.getSlotNames(e),(t=>iD.isShowing(e,t))),pB=(e,t,o)=>{iD.getSlot(e,t).each((e=>{dB.getPane(e).each((t=>{var n;o(e)((n=t.element.dom,{getContainer:y(n)}))}))}))};var hB=qm({factory:(e,t)=>{const o={setViews:(e,o)=>{Th.set(e,[mB(o,t.backstage.shared.providers)])},whichView:e=>Qm.getCurrent(e).bind(gB),toggleView:(e,t,o,n)=>Qm.getCurrent(e).exists((s=>{const r=gB(s),a=r.exists((e=>n===e)),i=iD.getSlot(s,n).isSome();return i&&(iD.hideAllSlots(s),a?((e=>{const t=e.element;Mt(t,"display","none"),St(t,"aria-hidden","true")})(e),t()):(o(),(e=>{const t=e.element;Lt(t,"display"),Tt(t,"aria-hidden")})(e),iD.showSlot(s,n),((e,t)=>{pB(e,t,dB.getOnShow)})(s,n)),r.each((e=>((e,t)=>pB(e,t,dB.getOnHide))(s,e)))),i}))};return{uid:e.uid,dom:{tag:"div",classes:["tox-view-wrap"],attributes:{"aria-hidden":"true"},styles:{display:"none"}},components:[],behaviours:xa([Th.config({}),Qm.config({find:e=>{const t=Th.contents(e);return te(t)}})]),apis:o}},name:"silver.ViewWrapper",configFields:[ps("backstage")],apis:{setViews:(e,t,o)=>e.setViews(t,o),toggleView:(e,t,o,n,s)=>e.toggleView(t,o,n,s),whichView:(e,t)=>e.whichView(t)}});const fB=EM.optional({factory:oD,name:"menubar",schema:[ps("backstage")]}),bB=EM.optional({factory:{sketch:e=>BM.sketch({uid:e.uid,dom:e.dom,listBehaviours:xa([vh.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>eB({type:e.type,uid:Pi("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),A.some(!0))}),setupItem:(e,t,o,n)=>{RM.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[ps("dom"),ps("onEscape")]}),vB=EM.optional({factory:{sketch:e=>{const t=(e=>e.type===wf.sliding?ZD:e.type===wf.floating?QD:eB)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),A.some(!0)),onToggled:(t,o)=>e.onToolbarToggled(o),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[ps("dom"),ps("onEscape"),ps("getSink")]}),xB=EM.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?YM:LM;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:xa(o(t,e.sharedBackstage))}}},name:"header",schema:[ps("dom")]}),yB=EM.optional({factory:{sketch:e=>{const t=e.promotionLink?[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/tinymce-upgrade-to-cloud/?utm_campaign=self_hosted_upgrade_promo&utm_source=tiny&utm_medium=referral",rel:"noopener",target:"_blank","aria-hidden":"true"},classes:["tox-promotion-link"],innerHtml:"\u{1f49d}Get all features"}}]:[];return{uid:e.uid,dom:e.dom,components:t}}},name:"promotion",schema:[ps("dom"),ps("promotionLink")]}),wB=EM.optional({name:"socket",schema:[ps("dom")]}),SB=EM.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"presentation"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:xa([Wb.config({}),Ih.config({}),bE.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{Qm.getCurrent(e).each(iD.hideAllSlots),$r(e,hD)},onGrown:e=>{$r(e,hD)},onStartGrow:e=>{Gr(e,pD,{width:Rt(e.element,"width").getOr("")})},onStartShrink:e=>{Gr(e,pD,{width:Kt(e.element)+"px"})}}),Th.config({}),Qm.config({find:e=>{const t=Th.contents(e);return te(t)}})])}],behaviours:xa([$_(0),Eh("sidebar-sliding-events",[Zr(pD,((e,t)=>{Mt(e.element,"width",t.event.width)})),Zr(hD,((e,t)=>{Lt(e.element,"width")}))])])})},name:"sidebar",schema:[ps("dom")]}),CB=EM.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:xa([Th.config({}),vD.config({focus:!1}),Qm.config({find:e=>te(e.components())})]),components:[]})},name:"throbber",schema:[ps("dom")]}),kB=EM.optional({factory:hB,name:"viewWrapper",schema:[ps("backstage")]}),OB=EM.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-editor-container"]},components:e.components})},name:"editorContainer",schema:[]});var _B=Xm({name:"OuterContainer",factory:(e,t,o)=>{let n=!1;const s=e=>{dn(e,".tox-statusbar").each((e=>{"none"===It(e,"display")&&"true"===kt(e,"aria-hidden")?(Lt(e,"display"),Tt(e,"aria-hidden")):(Mt(e,"display","none"),St(e,"aria-hidden","true"))}))},a={getSocket:t=>TM.getPart(t,e,"socket"),setSidebar:(t,o,n)=>{TM.getPart(t,e,"sidebar").each((e=>((e,t,o)=>{Qm.getCurrent(e).each((n=>{Th.set(n,[uD(t)]);const s=null==o?void 0:o.toLowerCase();r(s)&&be(t,s)&&Qm.getCurrent(n).each((t=>{iD.showSlot(t,s),bE.immediateGrow(n),Lt(n.element,"width"),mD(e.element,"region")}))}))})(e,o,n)))},toggleSidebar:(t,o)=>{TM.getPart(t,e,"sidebar").each((e=>((e,t)=>{Qm.getCurrent(e).each((o=>{Qm.getCurrent(o).each((n=>{bE.hasGrown(o)?iD.isShowing(n,t)?(bE.shrink(o),mD(e.element,"presentation")):(iD.hideAllSlots(n),iD.showSlot(n,t),mD(e.element,"region")):(iD.hideAllSlots(n),iD.showSlot(n,t),bE.grow(o),mD(e.element,"region"))}))}))})(e,o)))},whichSidebar:t=>TM.getPart(t,e,"sidebar").bind(gD).getOrNull(),getHeader:t=>TM.getPart(t,e,"header"),getToolbar:t=>TM.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{TM.getPart(t,e,"toolbar").each((e=>{const t=L(o,YD);e.getApis().setGroups(e,t)}))},setToolbars:(t,o)=>{TM.getPart(t,e,"multiple-toolbar").each((e=>{const t=L(o,(e=>L(e,YD)));BM.setItems(e,t)}))},refreshToolbar:t=>{TM.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{TM.getPart(t,e,"toolbar").each((e=>{Se(e.getApis().toggle,(t=>t(e)))}))},toggleToolbarDrawerWithoutFocusing:t=>{TM.getPart(t,e,"toolbar").each((e=>{Se(e.getApis().toggleWithoutFocusing,(t=>t(e)))}))},isToolbarDrawerToggled:t=>TM.getPart(t,e,"toolbar").bind((e=>A.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>TM.getPart(t,e,"throbber"),focusToolbar:t=>{TM.getPart(t,e,"toolbar").orThunk((()=>TM.getPart(t,e,"multiple-toolbar"))).each((e=>{vh.focusIn(e)}))},setMenubar:(t,o)=>{TM.getPart(t,e,"menubar").each((e=>{oD.setMenus(e,o)}))},focusMenubar:t=>{TM.getPart(t,e,"menubar").each((e=>{oD.focus(e)}))},setViews:(t,o)=>{TM.getPart(t,e,"viewWrapper").each((e=>{hB.setViews(e,o)}))},toggleView:(t,o)=>TM.getPart(t,e,"viewWrapper").exists((e=>hB.toggleView(e,(()=>a.showMainView(t)),(()=>a.hideMainView(t)),o))),whichView:t=>TM.getPart(t,e,"viewWrapper").bind(hB.whichView).getOrNull(),hideMainView:t=>{n=a.isToolbarDrawerToggled(t),n&&a.toggleToolbarDrawer(t),TM.getPart(t,e,"editorContainer").each((e=>{const t=e.element;s(t),Mt(t,"display","none"),St(t,"aria-hidden","true")}))},showMainView:t=>{n&&a.toggleToolbarDrawer(t),TM.getPart(t,e,"editorContainer").each((e=>{const t=e.element;s(t),Lt(t,"display"),Tt(t,"aria-hidden")}))}};return{uid:e.uid,dom:e.dom,components:t,apis:a,behaviours:e.behaviours}},configFields:[ps("dom"),ps("behaviours")],partFields:[xB,fB,vB,bB,wB,SB,yB,CB,kB,OB],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o,n)=>{e.setSidebar(t,o,n)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{e.setToolbar(t,o)},setToolbars:(e,t,o)=>{e.setToolbars(t,o)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},toggleToolbarDrawerWithoutFocusing:(e,t)=>{e.toggleToolbarDrawerWithoutFocusing(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)},setViews:(e,t,o)=>{e.setViews(t,o)},toggleView:(e,t,o)=>e.toggleView(t,o),whichView:(e,t)=>e.whichView(t)}});const TB={file:{title:"File",items:"newdocument restoredraft | preview | importword exportpdf exportword | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code revisionhistory | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed inserttemplate codesample inserttable accordion math | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents footnotes | mergetags | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"aidialog aishortcuts | spellchecker spellcheckerlanguage | autocorrect capitalization | a11ycheck code typography wordcount addtemplate"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},EB=e=>e.split(" "),AB=(e,t)=>{const o={...TB,...t.menus},n=re(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?EB("file edit view insert format tools table help"):EB(!1===t.menubar?"":t.menubar),a=P(s,(e=>{const o=be(TB,e);return n?o||fe(t.menus,e).exists((e=>be(e,"items"))):o})),i=L(a,(n=>{const s=o[n];return((e,t,o)=>{const n=Uf(o).split(/[ ,]/);return{text:e.title,getItems:()=>q(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||R(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:EB(s.items)},t,e)}));return P(i,(e=>e.getItems().length>0&&R(e.getItems(),(e=>r(e)||"separator"!==e.type))))},MB=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),DB=(e,t,o,n)=>(e.on("remove",(()=>n.unloadRawCss(t))),n.loadRawCss(t,o)),BB=e=>A.from(tinymce.Resource.get(e)).filter(r),IB=(e,t,o="")=>{const n=(e=>{const t=hb(e);return t?A.from(t):A.none()})(e).map((e=>((e,t)=>"ui/"+e+"/"+t)(e,`${t}.css`))),s=n.bind(BB);return we(n,s,((e,t)=>({_kind:"load-raw",key:e,css:t}))).getOrThunk((()=>{const n=e.editorManager.suffix;return{_kind:"load-stylesheet",url:o+`/${t}${n}.css`}}))},FB=(e,t)=>{const o=e.ui.styleSheetLoader,n=IB(e,"skin",t);switch(n._kind){case"load-raw":const{key:t,css:s}=n;return DB(e,t,s,o),Promise.resolve();case"load-stylesheet":const{url:r}=n;return MB(e,r,o);default:return Promise.resolve()}},RB=(e,t)=>{var o;if(o=ze(e.getElement()),!ft(o).isSome())return Promise.resolve();{const o=kf.DOM.styleSheetLoader,n=IB(e,"skin.shadowdom",t);switch(n._kind){case"load-raw":const{key:t,css:s}=n;return DB(e,t,s,o),Promise.resolve();case"load-stylesheet":const{url:r}=n;return MB(e,r,o);default:return Promise.resolve()}}},NB=(e,t)=>(async(e,t)=>{const o=vb(t);if(await((e,t,o)=>{const n=IB(e,t?"content.inline":"content",o);switch(n._kind){case"load-raw":const{key:s,css:r}=n;return t?DB(e,s,r,e.ui.styleSheetLoader):e.on("PostRender",(()=>{DB(e,s,r,e.dom.styleSheetLoader)})),Promise.resolve();case"load-stylesheet":const{url:a}=n;return o&&e.contentCSS.push(a),Promise.resolve();default:return Promise.resolve()}})(t,e,o),!fb(t)&&r(o))return Promise.all([FB(t,o),RB(t,o)]).then()})(e,t).then((e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}})(t),(e=>()=>((e,t)=>{e.dispatch("SkinLoadError",t)})(e,{message:"Skin could not be loaded"}))(t)),zB=C(NB,!1),LB=C(NB,!0),VB=y([ps("toggleClass"),ps("fetch"),Ai("onExecute"),Ds("getHotspot",A.some),Ds("getAnchorOverrides",y({})),Qc(),Ai("onItemExecute"),Cs("lazySink"),ps("dom"),Ti("onOpen"),ju("splitDropdownBehaviours",[kC,vh,Ih]),Ds("matchWidth",!1),Ds("useMinWidth",!1),Ds("eventOrder",{}),Cs("role"),Cs("listRole")].concat(PC())),HB=xm({factory:Lb,schema:[ps("dom")],name:"arrow",defaults:()=>({buttonBehaviours:xa([Ih.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(jr)},buttonBehaviours:xa([Ph.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),PB=xm({factory:Lb,schema:[ps("dom")],name:"button",defaults:()=>({buttonBehaviours:xa([Ih.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),UB=y([HB,PB,wm({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[ps("text")],name:"aria-descriptor"}),ym({schema:[ki()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),BC()]),WB=Xm({name:"SplitDropdown",configFields:VB(),partFields:UB(),factory:(e,t,o,n)=>{const s=e=>{Qm.getCurrent(e).each((e=>{Sg.highlightFirst(e),vh.focusIn(e)}))},r=t=>{NC(e,w,t,n,s,bf.HighlightMenuAndItem).get(b)},a=t=>{const o=Fm(t,e,"button");return jr(o),A.some(!0)},i={...Kr([ia(((t,o)=>{Im(t,e,"aria-descriptor").each((e=>{const o=Pi("aria");St(e.element,"id",o),St(t.element,"aria-describedby",o)}))}))]),...$h(A.some(r))},l={repositionMenus:e=>{Ph.isOn(e)&&HC(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[Sr()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:Xu(e.splitDropdownBehaviours,[kC.config({others:{sandbox:t=>{const o=Fm(t,e,"arrow");return VC(e,t,{onOpen:()=>{Ph.on(o),Ph.on(t)},onClose:()=>{Ph.off(o),Ph.off(t)}})}}}),vh.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(r(e),A.some(!0))}),Ih.config({}),Ph.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),$B=e=>({isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t),setText:t=>Gr(e,IT,{text:t}),setIcon:t=>Gr(e,FT,{icon:t})}),GB=e=>({setActive:t=>{Ph.set(e,t)},isActive:()=>Ph.isOn(e),isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t),setText:t=>Gr(e,IT,{text:t}),setIcon:t=>Gr(e,FT,{icon:t})}),jB=(e,t)=>e.map((e=>({"aria-label":t.translate(e)}))).getOr({}),qB=Pi("focus-button"),XB=(e,t,o,n,s,r,a)=>{const i=t.map((e=>Hb(BT(e,"tox-tbtn",s)))),l=e.map((e=>Hb(DT(e,s.icons))));return{dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:{...jB(o,s),...g(a)?{"data-mce-name":a}:{}}},components:Lw([l.map((e=>e.asSpec())),i.map((e=>e.asSpec()))]),eventOrder:{[er()]:["focusing","alloy.base.behaviour",_T],[Ir()]:[_T,"toolbar-group-button-events"],[Fr()]:[_T,"toolbar-group-button-events","tooltipping"]},buttonBehaviours:xa([kw((()=>s.checkUiComponentContext(r).shouldDisable)),Rw((()=>s.checkUiComponentContext(r))),Eh(_T,[ia(((e,t)=>ET(e))),Zr(IT,((e,t)=>{i.bind((t=>t.getOpt(e))).each((e=>{Th.set(e,[yl(s.translate(t.event.text))])}))})),Zr(FT,((e,t)=>{l.bind((t=>t.getOpt(e))).each((e=>{Th.set(e,[DT(t.event.icon,s.icons)])}))})),Zr(er(),((e,t)=>{t.event.prevent(),$r(e,qB)}))])].concat(n.getOr([])))}},YB=(e,t,o,n)=>{var s;const r=en(b),a=XB(e.icon,e.text,e.tooltip,A.none(),o,e.context,n);return Lb.sketch({dom:a.dom,components:a.components,eventOrder:TT,buttonBehaviours:{...xa([Eh("toolbar-button-events",[(i={onAction:e.onAction,getApi:t.getApi},da(((e,t)=>{Ow(i,e)((t=>{Gr(e,OT,{buttonApi:t}),i.onAction(t)}))}))),_w(t,r),Tw(t,r)]),...e.tooltip.map((t=>ev.config(o.tooltips.getConfig({tooltipText:o.translate(t)+e.shortcut.map((e=>` (${Ww(e)})`)).getOr("")})))).toArray(),kw((()=>!e.enabled||o.checkUiComponentContext(e.context).shouldDisable)),Rw((()=>o.checkUiComponentContext(e.context)))].concat(t.toolbarButtonBehaviours)),[_T]:null===(s=a.buttonBehaviours)||void 0===s?void 0:s[_T]}});var i},KB=(e,t,o,n)=>YB(e,{toolbarButtonBehaviours:o.length>0?[Eh("toolbarButtonWith",o)]:[],getApi:$B,onSetup:e.onSetup},t,n),JB=(e,t,o,n)=>YB(e,{toolbarButtonBehaviours:[Th.config({}),Ph.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[Eh("toolbarToggleButtonWith",o)]:[]),getApi:GB,onSetup:e.onSetup},t,n),QB=(e,t,o)=>n=>AC((e=>t.fetch(e))).map((s=>A.from(QC(En(uC(Pi("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,fx.CLOSE_ON_EXECUTE,t.select.getOr(T),o),{movement:gC(t.columns,t.presets),menuBehaviours:yw("auto"!==t.columns?[]:[ia(((e,o)=>{xw(e,4,Ex(t.presets)).each((({numRows:t,numColumns:o})=>{vh.setGridSize(e,t,o)}))}))])}))))),ZB=e=>{yf.getContent(e).each((e=>{mn(e.element,".tox-toolbar-slider__input,.tox-toolbar-textfield").fold((()=>vh.focusIn(e)),gc)}))},eI=Pi("forward-slide"),tI=Pi("backward-slide"),oI=Pi("change-slide-event"),nI="tox-pop--resizing",sI=(e,t,o)=>De(o)?e.translate(t):e.translate([t,e.translate(o)]),rI=(e,t)=>{const o=(o,s,r,a)=>{const i=e.shared.providers.translate(o.title);if("separator"===o.type)return A.some({type:"separator",text:i});if("submenu"===o.type){const e=q(o.getStyleItems(),(e=>n(e,s,a)));return 0===s&&e.length<=0?A.none():A.some({type:"nestedmenuitem",text:i,enabled:e.length>0,getSubmenuItems:()=>q(o.getStyleItems(),(e=>n(e,s,a)))})}return A.some({type:"togglemenuitem",text:i,icon:o.icon,active:o.isSelected(a),enabled:!r,onAction:t.onAction(o),...o.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},n=(e,n,s)=>{const r="formatter"===e.type&&t.isInvalid(e);return 0===n?r?[]:o(e,n,!1,s).toArray():o(e,n,r,s).toArray()},s=e=>{const o=t.getCurrentValue(),s=t.shouldHide?0:1;return q(e,(e=>n(e,s,o)))};return{validateItems:s,getFetch:(e,t)=>(o,n)=>{const r=t(),a=s(r);n(HT(a,fx.CLOSE_ON_EXECUTE,e,{isHorizontalMenu:!1,search:A.none()}))}}},aI=(e,t)=>{const o=t.dataset,n="basic"===o.type?()=>L(o.data,(e=>ZA(e,t.isSelectedFor,t.getPreviewFor))):o.getData;return{items:rI(e,t),getStyleItems:n}},iI=(e,t,o,n,s,r)=>{const{items:a,getStyleItems:i}=aI(t,o),l=en(o.tooltip);return NT({context:"mode:design",text:o.icon.isSome()?A.none():o.text,icon:o.icon,ariaLabel:A.some(o.tooltip),tooltip:A.none(),role:A.none(),fetch:a.getFetch(t,i),onSetup:t=>{const r=o=>t.setTooltip(sI(e,n(o.value),o.value));return e.on(s,r),ES(DS(e,"NodeChange",(t=>{const n=t.getComponent();o.updateText(n),mg.set(t.getComponent(),!e.selection.isEditable())}))(t),(()=>e.off(s,r)))},getApi:e=>({getComponent:y(e),setTooltip:o=>{const n=t.shared.providers.translate(o);St(e.element,"aria-label",n),l.set(o)}}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[ev.config({...t.shared.providers.tooltips.getConfig({tooltipText:t.shared.providers.translate(o.tooltip),onShow:e=>{if(o.tooltip!==l.get()){const o=t.shared.providers.translate(l.get());ev.setComponents(e,t.shared.providers.tooltips.getComponents({tooltipText:o}))}}})})]},"tox-tbtn",t.shared,r)};var lI;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(lI||(lI={}));const cI=(e,t,o)=>{const n=(s=((e,t)=>t===lI.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),L(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},dI=y("Alignment {0}"),uI="left",mI=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],gI=e=>{const t={type:"basic",data:mI};return{tooltip:sI(e,dI(),uI),text:A.none(),icon:A.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:e=>A.none,onAction:t=>()=>$(mI,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=$(mI,(t=>e.formatter.match(t.format))).fold(y(uI),(e=>e.title.toLowerCase()));Gr(t,FT,{icon:`align-${o}`}),((e,t)=>{e.dispatch("AlignTextUpdate",t)})(e,{value:o})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},pI=(e,t)=>{const o=t(),n=L(o,(e=>e.format));return A.from(e.formatter.closest(n)).bind((e=>$(o,(t=>t.format===e))))},hI=y("Block {0}"),fI="Paragraph",bI=e=>{const t=cI(e,"block_formats",lI.SemiColon);return{tooltip:sI(e,hI(),fI),text:A.some(fI),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:BS(e),updateText:o=>{const n=pI(e,(()=>t.data)).fold(y(fI),(e=>e.title));Gr(o,IT,{text:n}),((e,t)=>{e.dispatch("BlocksTextUpdate",t)})(e,{value:n})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},vI=y("Font {0}"),xI="System Font",yI=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],wI=e=>{const t=e.split(/\s*,\s*/);return L(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},SI=(e,t)=>t.length>0&&X(t,(t=>e.indexOf(t.toLowerCase())>-1)),CI=e=>{const t=()=>{const t=e=>e?wI(e)[0]:"",n=e.queryCommandValue("FontName"),s=o.data,r=n?n.toLowerCase():"",a=pb(e),i=$(s,(e=>{const o=e.format;return o.toLowerCase()===r||t(o).toLowerCase()===t(r).toLowerCase()})).orThunk((()=>Ce(((e,t)=>{if(0===e.indexOf("-apple-system")||t.length>0){const o=wI(e.toLowerCase());return SI(o,yI)||SI(o,t)}return!1})(r,a),{title:xI,format:r})));return{matchOpt:i,font:n}},o=cI(e,"font_family_formats",lI.SemiColon);return{tooltip:sI(e,vI(),xI),text:A.some(xI),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=t();return e},getPreviewFor:e=>()=>A.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:o=>{const{matchOpt:n,font:s}=t(),r=n.fold(y(s),(e=>e.title));Gr(o,IT,{text:r}),((e,t)=>{e.dispatch("FontFamilyTextUpdate",t)})(e,{value:r})},dataset:o,shouldHide:!1,isInvalid:T}},kI={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},OI=(()=>{const e="[0-9]+",t="[eE][+-]?"+e,o=e=>`(?:${e})?`,n=["Infinity",e+"\\."+o(e)+o(t),"\\."+e+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),_I=(e,t)=>A.from(OI.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>R(t,(t=>R(kI[t],(t=>e===t)))))(n,t)?A.some({value:o,unit:n}):A.none()})),TI={tab:y(9),escape:y(27),enter:y(13),backspace:y(8),delete:y(46),left:y(37),up:y(38),right:y(39),down:y(40),space:y(32),home:y(36),end:y(35),pageUp:y(33),pageDown:y(34)},EI=y("Font size {0}"),AI="12pt",MI={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},DI={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},BI=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":fe(DI,e).getOr(e),II=e=>fe(MI,e).getOr(""),FI=e=>{const t=()=>{let t=A.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=BI(s,e),r=II(n);t=$(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=y(A.none),n=cI(e,"font_size_formats",lI.Space);return{tooltip:sI(e,EI(),AI),text:A.some(AI),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:o=>{const{matchOpt:n,size:s}=t(),r=n.fold(y(s),(e=>e.title));Gr(o,IT,{text:r}),((e,t)=>{e.dispatch("FontSizeTextUpdate",t)})(e,{value:r})},dataset:n,shouldHide:!1,isInvalid:T}},RI=e=>De(e)?"Formats":"Format {0}",NI=(e,t)=>{const o="Formats";return{tooltip:sI(e,RI(""),""),text:A.some(o),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:BS(e),updateText:t=>{const n=e=>XA(e)?q(e.items,n):YA(e)?[{title:e.title,format:e.format}]:[],s=q(QA(e),n),r=pI(e,y(s)).fold(y({title:o,tooltipLabel:""}),(e=>({title:e.title,tooltipLabel:e.title})));Gr(t,IT,{text:r.title}),((e,t)=>{e.dispatch("StylesTextUpdate",t)})(e,{value:r.tooltipLabel})},shouldHide:Hf(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}},zI=[{name:"history",items:["undo","redo"]},{name:"ai",items:["aidialog","aishortcuts"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],LI=(e,t)=>(o,n,s,r)=>{const a=e(o).mapError((e=>ls(e))).getOrDie();return t(a,n,s,r)},VI={button:LI(zy,((e,t,o,n)=>((e,t,o)=>KB(e,t,[],o))(e,t.shared.providers,n))),togglebutton:LI(Hy,((e,t,o,n)=>((e,t,o)=>JB(e,t,[],o))(e,t.shared.providers,n))),menubutton:LI(eD,((e,t,o,n)=>xE(e,"tox-tbtn",t,A.none(),!1,n))),splitbutton:LI((e=>rs("SplitButton",tD,e)),((e,t,o,n)=>((e,t,o)=>{const n=en(e.tooltip.getOr("")),s=e=>({isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t),setIconFill:(t,o)=>{mn(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{St(e,"fill",o)}))},setActive:t=>{St(e.element,"aria-pressed",t),mn(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>Ph.set(e,t)))}))},isActive:()=>mn(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists(Ph.isOn))),setText:t=>mn(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Gr(e,IT,{text:t}))))),setIcon:t=>mn(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Gr(e,FT,{icon:t}))))),setTooltip:o=>{const s=t.providers.translate(o);St(e.element,"aria-label",s),n.set(o)}}),r=en(b),a={getApi:s,onSetup:e.onSetup};return WB.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...jB(e.tooltip,t.providers),...g(o)?{"data-mce-name":o}:{}}},onExecute:t=>{const o=s(t);o.isEnabled()&&e.onAction(o)},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:xa([Eh("split-dropdown-events",[ia(((e,t)=>ET(e))),Zr(qB,Ih.focus),_w(a,r),Tw(a,r)]),Cw((()=>t.providers.isDisabled()||t.providers.checkUiComponentContext(e.context).shouldDisable)),Rw((()=>t.providers.checkUiComponentContext(e.context))),Uk.config({}),...e.tooltip.map((e=>ev.config({...t.providers.tooltips.getConfig({tooltipText:t.providers.translate(e),onShow:o=>{if(n.get()!==e){const e=t.providers.translate(n.get());ev.setComponents(o,t.providers.tooltips.getComponents({tooltipText:e}))}}})}))).toArray()]),eventOrder:{[Ir()]:["alloy.base.behaviour","split-dropdown-events","tooltipping"],[Fr()]:["split-dropdown-events","tooltipping"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:QB(s,e,t.providers),parts:{menu:Fx(0,e.columns,e.presets)},components:[WB.parts.button(XB(e.icon,e.text,A.none(),A.some([Ph.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1}),kw(T),Rw(y({contextType:"any",shouldDisable:!1}))]),t.providers,e.context)),WB.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:lx("chevron-down",t.providers.icons)},buttonBehaviours:xa([Cw(T),Rw(y({contextType:"any",shouldDisable:!1}))])}),WB.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.shared,n))),grouptoolbarbutton:LI((e=>rs("GroupToolbarButton",JM,e)),((e,t,o,n)=>{const s=o.ui.registry.getAll().buttons,r={[Kc]:t.shared.header.isPositionedAtTop()?Yc.TopToBottom:Yc.BottomToTop};if(Wf(o)===wf.floating)return((e,t,o,n,s)=>{const r=t.shared,a=en(b),i={toolbarButtonBehaviours:[],getApi:$B,onSetup:e.onSetup},l=[Eh("toolbar-group-button-events",[_w(i,a),Tw(i,a)]),...e.tooltip.map((e=>ev.config(t.shared.providers.tooltips.getConfig({tooltipText:t.shared.providers.translate(e)})))).toArray()];return ND.sketch({lazySink:r.getSink,fetch:()=>AC((t=>{t(L(o(e.items),YD))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:XB(e.icon,e.text,e.tooltip,A.some(l),r.providers,e.context,s),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t,(e=>PI(o,{buttons:s,toolbar:e,allowToolbarGroups:!1},t,A.none())),r,n);throw new Error("Toolbar groups are only supported when using floating toolbar mode")}))},HI={styles:(e,t)=>{const o={type:"advanced",...t.styles};return iI(e,t,NI(e,o),RI,"StylesTextUpdate","styles")},fontsize:(e,t)=>iI(e,t,FI(e),EI,"FontSizeTextUpdate","fontsize"),fontsizeinput:(e,t)=>((e,t,o,n)=>{let s=A.none();const r=DS(e,"NodeChange SwitchMode DisabledStateChange",(t=>{const n=t.getComponent();s=A.some(n),o.updateInputValue(n),mg.set(n,!e.selection.isEditable()||Df(e))})),a=e=>({getComponent:y(e)}),i=en(b),l=Pi("custom-number-input-events"),c=(e,t,n)=>{const r=s.map((e=>Gu.getValue(e))).getOr(""),a=o.getNewValue(r,e),i=r.length-`${a}`.length,l=s.map((e=>e.element.dom.selectionStart-i)),c=s.map((e=>e.element.dom.selectionEnd-i));o.onAction(a,n),s.each((e=>{Gu.setValue(e,a),t&&(l.each((t=>e.element.dom.selectionStart=t)),c.each((t=>e.element.dom.selectionEnd=t)))}))},d=(e,t)=>c(((e,t)=>e-t),e,t),u=(e,t)=>c(((e,t)=>e+t),e,t),m=e=>at(e.element).fold(A.none,(e=>(gc(e),A.some(!0)))),p=e=>hc(e.element)?(dt(e.element).each((e=>gc(e))),A.some(!0)):A.none(),h=(o,n,s,r)=>{const i=en(b),l=t.shared.providers.translate(s),c=Pi("altExecuting"),d=DS(e,"NodeChange SwitchMode DisabledStateChange",(t=>{mg.set(t.getComponent(),!e.selection.isEditable()||Df(e))})),u=e=>{mg.isDisabled(e)||o(!0)};return Lb.sketch({dom:{tag:"button",attributes:{"aria-label":l,"data-mce-name":n},classes:r.concat(n)},components:[MT(n,t.shared.providers.icons)],buttonBehaviours:xa([mg.config({}),ev.config(t.shared.providers.tooltips.getConfig({tooltipText:l})),Eh(c,[_w({onSetup:d,getApi:a},i),Tw({getApi:a},i),Zr(ir(),((e,t)=>{t.event.raw.keyCode!==TI.space()&&t.event.raw.keyCode!==TI.enter()||mg.isDisabled(e)||o(!1)})),Zr(ur(),u),Zr(Qs(),u)])]),eventOrder:{[ir()]:[c,"keying"],[ur()]:[c,"alloy.base.behaviour"],[Qs()]:[c,"alloy.base.behaviour"],[Ir()]:["alloy.base.behaviour",c,"tooltipping"],[Fr()]:[c,"tooltipping"]}})},f=Hb(h((e=>d(!1,e)),"minus","Decrease font size",[])),v=Hb(h((e=>u(!1,e)),"plus","Increase font size",[])),x=Hb({dom:{tag:"div",classes:["tox-input-wrapper"]},components:[Vx.sketch({inputBehaviours:xa([mg.config({}),Eh(l,[_w({onSetup:r,getApi:a},i),Tw({getApi:a},i)]),Eh("input-update-display-text",[Zr(IT,((e,t)=>{Gu.setValue(e,t.event.text)})),Zr(ar(),(e=>{o.onAction(Gu.getValue(e))})),Zr(dr(),(e=>{o.onAction(Gu.getValue(e))}))]),vh.config({mode:"special",onEnter:e=>(c(w,!0,!0),A.some(!0)),onEscape:m,onUp:e=>(u(!0,!1),A.some(!0)),onDown:e=>(d(!0,!1),A.some(!0)),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})],behaviours:xa([Ih.config({}),vh.config({mode:"special",onEnter:p,onSpace:p,onEscape:m}),Eh("input-wrapper-events",[Zr(sr(),(e=>{V([f,v],(t=>{const o=ze(t.get(e).element.dom);hc(o)&&pc(o)}))}))])])});return{dom:{tag:"div",classes:["tox-number-input"],attributes:{...g(n)?{"data-mce-name":n}:{}}},components:[f.asSpec(),x.asSpec(),v.asSpec()],behaviours:xa([Ih.config({}),vh.config({mode:"flow",focusInside:Gg.OnEnterOrSpaceMode,cycles:!1,selector:"button, .tox-input-wrapper",onEscape:e=>hc(e.element)?A.none():(gc(e.element),A.some(!0))})])}})(e,t,(e=>{const t=()=>e.queryCommandValue("FontSize");return{updateInputValue:e=>Gr(e,IT,{text:t()}),onAction:(t,o)=>e.execCommand("FontSize",!1,t,{skip_focus:!o}),getNewValue:(o,n)=>{_I(o,["unsupportedLength","empty"]);const s=t(),r=_I(o,["unsupportedLength","empty"]).or(_I(s,["unsupportedLength","empty"])),a=r.map((e=>e.value)).getOr(16),i=eb(e),l=r.map((e=>e.unit)).filter((e=>""!==e)).getOr(i),c=n(a,(e=>{var t;return null!==(t={em:{step:.1},cm:{step:.1},in:{step:.1},pc:{step:.1},ch:{step:.1},rem:{step:.1}}[e])&&void 0!==t?t:{step:1}})(l).step),d=`${(e=>e>=0)(c)?c:a}${l}`;return d!==s&&((e,t)=>{e.dispatch("FontSizeInputTextUpdate",t)})(e,{value:d}),d}}})(e),"fontsizeinput"),fontfamily:(e,t)=>iI(e,t,CI(e),vI,"FontFamilyTextUpdate","fontfamily"),blocks:(e,t)=>iI(e,t,bI(e),hI,"BlocksTextUpdate","blocks"),align:(e,t)=>iI(e,t,gI(e),dI,"AlignTextUpdate","align"),navigateback:(e,t)=>{const o=as(zy({type:"button",icon:"chevron-left",tooltip:"Back",onAction:b}));return KB(o,t.shared.providers,[Zr(OT,(e=>{$r(e,tI)}))])}},PI=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=L(zI,(t=>{const o=P(t.items,(t=>be(e,t)||be(HI,t)));return{name:t.name,items:o}}));return P(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return L(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>(be(e,"name")||be(e,"label"))&&be(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=L(s,(s=>{const r=q(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>fe(t,o.toLowerCase()).orThunk((()=>r.bind((e=>se(e,(e=>fe(t,e+o.toLowerCase()))))))).fold((()=>fe(HI,o.toLowerCase()).map((t=>t(e,s)))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o,n)=>fe(VI,e.type).fold((()=>(console.error("skipping button defined by",e),A.none())),(s=>A.some(s(e,t,o,n)))))(t,s,e,o.toLowerCase()):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),A.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).toArray()));return{title:A.from(e.translate(s.name)),label:Ce(void 0!==s.label,e.translate(s.label)),items:r}}));return P(a,(e=>e.items.length>0))},UI=(e,t,o,n)=>{const s=t.mainUi.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return PI(e,s,n,A.none())}));_B.setToolbars(s,t)}else _B.setToolbar(s,PI(e,o,n,A.none()))},WI=Mo(),$I=WI.os.isiOS()&&WI.os.version.major<=12;var GI=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=t,i=en(0),l=r.outerContainer;zB(e);const d=ze(s.targetNode),u=ht(pt(d));mu(d,r.mothership),((e,t,o)=>{Ab(e)&&mu(o.mainUi.mothership.element,o.popupUi.mothership),uu(t,o.dialogUi.mothership)})(e,u,t),e.on("PostRender",(()=>{_B.setSidebar(l,o.sidebar,ub(e))})),e.on("SkinLoaded",(()=>{UI(e,t,o,n),i.set(e.getWin().innerWidth),_B.setMenubar(l,AB(e,o)),_B.setViews(l,o.views),((e,t)=>{const{uiMotherships:o}=t,n=e.dom;let s=e.getWin();const r=e.getDoc().documentElement,a=en(Gt(s.innerWidth,s.innerHeight)),i=en(Gt(r.offsetWidth,r.offsetHeight)),l=()=>{const t=a.get();t.left===s.innerWidth&&t.top===s.innerHeight||(a.set(Gt(s.innerWidth,s.innerHeight)),kS(e))},c=()=>{const t=e.getDoc().documentElement,o=i.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(i.set(Gt(t.offsetWidth,t.offsetHeight)),kS(e))},d=t=>{((e,t)=>{e.dispatch("ScrollContent",t)})(e,t)};n.bind(s,"resize",l),n.bind(s,"scroll",d);const u=Ic(ze(e.getBody()),"load",c);e.on("hide",(()=>{V(o,(e=>{Mt(e.element,"display","none")}))})),e.on("show",(()=>{V(o,(e=>{Lt(e.element,"display")}))})),e.on("NodeChange",c),e.on("remove",(()=>{u.unbind(),n.unbind(s,"resize",l),n.unbind(s,"scroll",d),s=null}))})(e,t)}));const m=_B.getSocket(l).getOrDie("Could not find expected socket element");if($I){Dt(m.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=(e=>{let t=null;return{cancel:()=>{c(t)||(clearTimeout(t),t=null)},throttle:(...o)=>{c(t)&&(t=setTimeout((()=>{t=null,e.apply(null,o)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=Bc(m.element,"scroll",t.throttle);e.on("remove",o.unbind)}Fw(e,t),e.addCommand("ToggleSidebar",((t,o)=>{_B.toggleSidebar(l,o),(e=>{e.dispatch("ToggleSidebar")})(e)})),e.addQueryValueHandler("ToggleSidebar",(()=>{var e;return null!==(e=_B.whichSidebar(l))&&void 0!==e?e:""})),e.addCommand("ToggleView",((t,o)=>{if(_B.toggleView(l,o)){const t=l.element;r.mothership.broadcastOn([_u()],{target:t}),V(a,(e=>{e.broadcastOn([_u()],{target:t})})),c(_B.whichView(l))&&(e.focus(),e.nodeChanged(),_B.refreshToolbar(l)),(e=>{e.dispatch("ToggleView")})(e)}})),e.addQueryValueHandler("ToggleView",(()=>{var e;return null!==(e=_B.whichView(l))&&void 0!==e?e:""}));const g=Wf(e);g!==wf.sliding&&g!==wf.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==i.get()&&(_B.refreshToolbar(t.mainUi.outerContainer),i.set(o))}));const p={setEnabled:e=>{Iw(t,e?"setEnabled":"setDisabled")},isEnabled:()=>!mg.isDisabled(l)};return{iframeContainer:m.element.dom,editorContainer:l.element.dom,api:p}}});const jI=e=>h(e)?e+"px":e,qI=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},XI=e=>{const t=If(e),o=Ff(e),n=Nf(e);return(s=t,/^[0-9\.]+(|px)$/i.test(""+s)?A.some(parseInt(""+s,10)):A.none()).map((e=>qI(e,o,n)));var s},{ToolbarLocation:YI,ToolbarMode:KI}=Db,JI=(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=o,i=kf.DOM,l=Ob(e),c=Eb(e),d=Nf(e).or(XI(e)),u=n.shared.header,m=u.isPositionedAtTop,g=Wf(e),p=g===KI.sliding||g===KI.floating,h=en(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(y(0),(e=>e.components().length>1?Ut(e.components()[1].element):0)):0,v=()=>{V(a,(e=>{e.broadcastOn([Tu()],{})}))},x=o=>{if(!f())return;l||s.on((e=>{const o=d.getOrThunk((()=>$o().width-Xt(t).left-10));Mt(e.element,"max-width",o+"px")}));const n=Po(),a=!(l||l||!(qt(r.outerContainer.element).left+Jt(r.outerContainer.element)>=window.innerWidth-40||Rt(r.outerContainer.element,"width").isSome())||(Mt(r.outerContainer.element,"position","absolute"),Mt(r.outerContainer.element,"left","0px"),Lt(r.outerContainer.element,"width"),0));if(p&&_B.refreshToolbar(r.outerContainer),!l){const o=Po(),i=Ce(n.left!==o.left,n);((o,n)=>{s.on((s=>{const a=_B.getToolbar(r.outerContainer),i=b(a),l=Ko(t),c=((e,t)=>Ab(e)?Va(t):A.none())(e,r.outerContainer.element),d=c.fold((()=>l.x),(e=>{const t=Ko(e);return Ze(e,xt())?l.x:l.x-t.x})),u=Ce(o,Math.ceil(r.outerContainer.element.dom.getBoundingClientRect().width)).filter((e=>e>150)).map((e=>{const t=n.getOr(Po()),o=window.innerWidth-(d-t.left),s=Math.max(Math.min(e,o),150);return o<e&&Mt(r.outerContainer.element,"width",s+"px"),{width:s+"px"}})).getOr({width:"max-content"}),g={position:"absolute",left:Math.round(d)+"px",top:c.fold((()=>m()?Math.max(l.y-Ut(s.element)+i,0):l.bottom),(e=>{var t;const o=Ko(e),n=null!==(t=e.dom.scrollTop)&&void 0!==t?t:0,r=Ze(e,xt())?Math.max(l.y-Ut(s.element)+i,0):l.y-o.y+n-Ut(s.element)+i;return m()?r:l.bottom}))+"px"};Dt(r.outerContainer.element,{...g,...u})}))})(a,i),i.each((e=>{Uo(e.left,o.top)}))}c&&s.on(o),v()},w=()=>!(l||!c||!f())&&s.get().exists((o=>{const n=u.getDockingMode(),a=(o=>{switch(Gf(e)){case YI.auto:const e=_B.getToolbar(r.outerContainer),n=b(e),s=Ut(o.element)-n,a=Ko(t);if(a.y>s)return"top";{const e=nt(t),o=Math.max(e.dom.scrollHeight,Ut(e));return a.bottom<o-s||Zo().bottom<a.bottom-s?"bottom":"top"}case YI.bottom:return"bottom";case YI.top:default:return"top"}})(o);return a!==n&&(i=a,s.on((e=>{Ri.setModes(e,[i]),u.setDockingMode(i);const t=m()?Yc.TopToBottom:Yc.BottomToTop;St(e.element,Kc,t)})),!0);var i}));return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),Mt(r.outerContainer.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),V(a,(e=>{Lt(e.element,"display")})),w(),Ab(e)?x((e=>Ri.isDocked(e)?Ri.reset(e):Ri.refresh(e))):x(Ri.refresh)},hide:()=>{h.set(!1),Mt(r.outerContainer.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus"),V(a,(e=>{Mt(e.element,"display","none")}))},update:x,updateMode:()=>{w()&&x(Ri.reset)},repositionPopups:v}},QI=(e,t)=>{const o=Ko(e);return{pos:t?o.y:o.bottom,bounds:o}};var ZI=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r}=t,a=nn(),i=ze(s.targetNode),l=JI(e,i,t,n,a),c=Xf(e);LB(e);const d=()=>{if(a.isSet())return void l.show();a.set(_B.getHeader(r.outerContainer).getOrDie());const s=_b(e);Ab(e)?(mu(i,r.mothership),mu(i,t.popupUi.mothership)):uu(s,r.mothership),uu(s,t.dialogUi.mothership);const d=()=>{UI(e,t,o,n),_B.setMenubar(r.outerContainer,AB(e,o)),l.show(),((e,t,o,n)=>{const s=en(QI(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=QI(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&kS(e,n),o.isVisible()&&(i!==r?o.update(Ri.reset):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update(Ri.reset))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))}));let a=0;const i=oT((()=>o.update(Ri.refresh)),33);e.on("ScrollWindow",(()=>{const e=Po().left;e!==a&&(a=e,i.throttle()),o.updateMode()})),Ab(e)&&e.on("ElementScroll",(e=>{o.update(Ri.refresh)}));const l=on();l.set(Ic(ze(e.getBody()),"load",(e=>r(e.raw)))),e.on("remove",(()=>{l.clear()}))})(e,i,l,c),e.nodeChanged()};c?e.once("SkinLoaded",d):d()};e.on("show",d),e.on("hide",l.hide),c||(e.on("focus",d),e.on("blur",l.hide)),e.on("init",(()=>{(e.hasFocus()||c)&&d()})),Fw(e,t);const u={show:d,hide:l.hide,setEnabled:e=>{Iw(t,e?"setEnabled":"setDisabled")},isEnabled:()=>!mg.isDisabled(r.outerContainer)};return{editorContainer:r.outerContainer.element.dom,api:u}}}),eF=tinymce.util.Tools.resolve("tinymce.util.VK");const tF="contexttoolbar-hide",oF=(e,t,o)=>({setInputEnabled:t=>{!t&&o&&gc(o),mg.set(e,!t)},isInputEnabled:()=>!mg.isDisabled(e),hide:()=>{$r(e,_r())},back:()=>{$r(e,tI)},getValue:()=>t.get().getOrThunk((()=>Gu.getValue(e))),setValue:o=>{e.getSystem().isConnected()?Gu.setValue(e,o):t.set(o)}}),nF=(e,t,o)=>Zr(OT,((n,s)=>{const r=e.get(n),a=oF(r,o,n.element);t.onAction(a,s.event.buttonApi)})),sF=(e,t,o,n)=>{const s=L(t,(t=>Hb(((e,t,o,n)=>(e=>"contextformtogglebutton"===e.type)(t)?((e,t,o,n)=>{const{primary:s,...r}=t.original,a=as(Hy({...r,type:"togglebutton",onAction:b}));return JB(a,o,[nF(e,t,n)])})(e,t,o,n):((e,t,o,n)=>{const{primary:s,...r}=t.original,a=as(zy({...r,type:"button",onAction:b}));return KB(a,o,[nF(e,t,n)])})(e,t,o,n))(e,t,o,n))));return{asSpecs:()=>L(s,(e=>e.asSpec())),findPrimary:e=>se(t,((t,o)=>t.primary?A.from(s[o]).bind((t=>t.getOpt(e))).filter(k(mg.isDisabled)):A.none()))}},rF=(e,t,o,n)=>{const{width:s,height:r}=e.initValue();let a=ZT;const i=Pi("ratio-event"),l=e=>oF(e,n),c=e=>ux(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),d=()=>!1,u=e.label.getOr("Constrain proportions"),m=t.translate(u),g=YT.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":m,"data-mce-name":u}},components:[c("lock"),c("unlock")],buttonBehaviours:xa([mg.config({disabled:d}),Wb.config({}),ev.config(t.tooltips.getConfig({tooltipText:m}))])}),p=e=>({dom:{tag:"div",classes:["tox-context-form__group"]},components:e}),h=e=>cn(e.element,"div.tox-focusable-wrapper").fold(A.none,(e=>(gc(e),A.some(!0)))),f=e=>hk.parts.field({factory:Vx,inputClasses:["tox-textfield","tox-toolbar-textfield","tox-textfield-size"],data:e?s:r,inputBehaviours:xa([mg.config({disabled:d}),Wb.config({}),Eh("size-input-toolbar-events",[Zr(rr(),((t,o)=>{Gr(t,i,{isField1:e})}))]),vh.config({mode:"special",onEnter:o,onEscape:h})]),selectOnFocus:!1}),v=e=>({dom:{tag:"label",classes:["tox-label"]},components:[yl(t.translate(e))]}),x=e=>({dom:{tag:"div",classes:["tox-focusable-wrapper","tox-toolbar-nav-item"]},components:[e],behaviours:xa([Wb.config({}),Ih.config({}),vh.config({mode:"special",onEnter:e=>mn(e.element,"input").fold(A.none,(e=>(gc(e),A.some(!0))))})])}),y=x(YT.parts.field1(p([hk.parts.label(v("Width:")),f(!0)]))),w=x(YT.parts.field2(p([hk.parts.label(v("Height:")),f(!1)]))),S=en(b),C=[_w({onBeforeSetup:e=>mn(e.element,"input").each(gc),onSetup:e.onSetup,getApi:l},S),Ew({getApi:l},S,n)];return YT.sketch({dom:{tag:"div",classes:["tox-context-form__group"]},components:[y,w,p([v("\xa0"),g])],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,o)=>{JT(Gu.getValue(e)).each((e=>{a(e).each((e=>{Gu.setValue(t,KT(e))}))}))},onInput:e=>$r(e,Sk),coupledFieldBehaviours:xa([Ih.config({}),vh.config({mode:"flow",focusInside:Gg.OnEnterOrSpaceMode,cycles:!1,selector:"button, .tox-focusable-wrapper"}),mg.config({disabled:d,onDisabled:e=>{YT.getField1(e).bind(hk.getField).each(mg.disable),YT.getField2(e).bind(hk.getField).each(mg.disable),YT.getLock(e).each(mg.disable)},onEnabled:e=>{YT.getField1(e).bind(hk.getField).each(mg.enable),YT.getField2(e).bind(hk.getField).each(mg.enable),YT.getLock(e).each(mg.enable)}}),Rw((()=>t.checkUiComponentContext("mode:design"))),Eh("size-input-toolbar-events2",[Zr(i,((e,t)=>{const o=t.event.isField1,n=o?YT.getField1(e):YT.getField2(e),s=o?YT.getField2(e):YT.getField1(e),r=n.map(Gu.getValue).getOr(""),i=s.map(Gu.getValue).getOr("");a=eE(r,i)})),Zr(Sk,(t=>e.onInput(l(t)))),...C])])})},aF=(e,t,o)=>hk.sketch({dom:{tag:"div",classes:["tox-context-form__group"]},components:[...e.toArray(),t],fieldBehaviours:xa([mg.config({disabled:()=>o.checkUiComponentContext("mode:design").shouldDisable,onDisabled:e=>{(e=>{bc(e.element).each((e=>{cn(e,'[tabindex="-1"]').each((e=>{gc(e)}))}))})(e),hk.getField(e).each(mg.disable)},onEnabled:e=>{hk.getField(e).each(mg.enable)}})])}),iF=(e,t,o,n)=>{const s=en(b),r=e=>oF(e,n),a=e.label.map((e=>hk.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[yl(t.translate(e))]}))),i=hk.parts.field({factory:Vx,type:"range",inputClasses:["tox-toolbar-slider__input","tox-toolbar-nav-item"],inputAttributes:{min:String(e.min()),max:String(e.max())},data:e.initValue().toString(),fromInputValue:t=>(e=>{const t=parseFloat(e);return isNaN(t)?A.none():A.some(t)})(t).getOr(e.min()),toInputValue:e=>String(e),inputBehaviours:xa([mg.config({disabled:()=>t.checkUiComponentContext("mode:design").shouldDisable}),Rw((()=>t.checkUiComponentContext("mode:design"))),vh.config({mode:"special",onEnter:o,onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())}),Eh("slider-events",[_w({onSetup:e.onSetup,getApi:r,onBeforeSetup:vh.focusIn},s),Ew({getApi:r},s,n),Zr(cr(),(t=>{e.onInput(r(t))}))])])});return aF(a,i,t)},lF=(e,t,o,n)=>{const s=en(b),r=e=>oF(e,n),a=e.label.map((e=>hk.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[yl(t.translate(e))]}))),i={...e.placeholder.map((e=>({placeholder:t.translate(e)}))).getOr({})},l=hk.parts.field({factory:Vx,inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-item"],inputAttributes:i,data:e.initValue(),selectOnFocus:!0,inputBehaviours:xa([mg.config({disabled:()=>t.checkUiComponentContext("mode:design").shouldDisable}),Rw((()=>t.checkUiComponentContext("mode:design"))),vh.config({mode:"special",onEnter:o,onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())}),Eh("input-events",[_w({onSetup:e.onSetup,getApi:e=>cn(e.element,".tox-toolbar").bind((e=>mn(e,"button:enabled"))).fold((()=>oF(e,n)),(t=>oF(e,n,t))),onBeforeSetup:vh.focusIn},s),Ew({getApi:r},s,n),Zr(cr(),(t=>{e.onInput(r(t))}))])])});return aF(a,l,t)},cF=(e,t,o)=>{const n=nn(),s=Hb(e(o,(e=>a.findPrimary(e).orThunk((()=>i.findPrimary(e))).map((e=>(jr(e),!0)))),n)),r=H(t.commands,(e=>"start"===e.align)),a=sF(s,r.pass,o,n),i=sF(s,r.fail,o,n);return P([{title:A.none(),label:A.none(),items:a.asSpecs()},{title:A.none(),label:A.none(),items:[s.asSpec()]},{title:A.none(),label:A.none(),items:i.asSpecs()}],(e=>e.items.length>0))},dF=(e,t)=>{switch(e.type){case"contextform":return cF(C(lF,e),e,t);case"contextsliderform":return cF(C(iF,e),e,t);case"contextsizeinputform":return cF(C(rF,e),e,t)}},uF=(e,t,o)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,mF=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=mt(ze(e.startContainer),e.startOffset).element;return(je(o)?rt(o):A.some(o)).filter(Ge).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=Po();return Yo(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=Jo(ze(e.getBody()));return Yo(o.x+t.left,o.y+t.top,t.width,t.height)}},gF=(e,t,o,n=0)=>{const s=$o(window),r=Ko(ze(e.getContentAreaContainer())),a=bb(e)||yb(e)||Sb(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return Yo(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=ze(e.getContainer()),i=mn(a,".tox-editor-header").getOr(a),l=Ko(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?Ko(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return Yo(i,c,l,d-c)}},pF={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},hF={maxHeightFunction:Hc(),maxWidthFunction:AD()},fF=e=>"node"===e,bF=(e,t,o,n,s)=>{const r=mF(e),a=n.lastElement().exists((e=>Ze(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=mt(ze(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&Ze(n.element,t)})(e,o)?a?IA:EA:a?((e,o)=>{const s=Rt(e,"position");Mt(e,"position",o);const a=uF(r,Ko(t),-20)&&!n.isReposition()?RA:IA;return s.each((t=>Mt(e,"position",t))),a})(t,n.getMode()):("fixed"===n.getMode()?s.y+Po().top:s.y)+(Ut(t)+12)<=r.y?EA:AA},vF=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({...bF(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>fF(n)?[s(e)]:[];return t?{onLtr:e=>[Zl,Xl,Yl,Kl,Jl,Ql].concat(r(e)),onRtl:e=>[Zl,Yl,Xl,Jl,Kl,Ql].concat(r(e))}:{onLtr:e=>[Ql,Zl,Kl,Xl,Jl,Yl].concat(r(e)),onRtl:e=>[Ql,Zl,Jl,Yl,Kl,Xl].concat(r(e))}},xF=(e,t)=>{const o=P(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=H(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},yF=(e,t)=>{const o={},n=[],s=[],r={},a={},i=re(e);return V(i,(i=>{const l=e[i];"contextform"===l.type||"contextsliderform"===l.type||"contextsizeinputform"===l.type?((e,i)=>{const l=as(rs("ContextForm",Ky,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,o)=>{var i;(i=o,rs("ContextToolbar",Qy,i)).each((i=>{i.launch.isSome()&&(r["toolbar:"+e]={...o.launch,type:"button",onAction:()=>{t(i)}}),"editor"===o.scope?s.push(i):n.push(i),a[e]=i}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},wF="tox-pop--transition",SF=(e,t,o,n)=>{const s=n.backstage,a=s.shared,i=Mo().deviceType.isTouch,l=nn(),c=nn(),d=nn(),u=(e=>{const t=en([]),o=yf.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),yf.getContent(e).each((e=>{Lt(e.element,"visibility")})),Ba(e.element,nI),Lt(e.element,"width")},onHide:()=>{t.set([]),e.onHide()},inlineBehaviours:xa([Eh("context-toolbar-events",[aa(gr(),((e,t)=>{"width"===t.event.raw.propertyName&&(Ba(e.element,nI),Lt(e.element,"width"))})),Zr(oI,((e,t)=>{const o=e.element;Lt(o,"width");const n=Kt(o),s=bc(e.element).isSome();Lt(o,"left"),Lt(o,"right"),Lt(o,"max-width"),yf.setContent(e,t.event.contents),Ma(o,nI);const r=Kt(o);Mt(o,"transition","none"),yf.reposition(e),Lt(o,"transition"),Mt(o,"width",n+"px"),t.event.focus.fold((()=>{s&&ZB(e)}),(t=>{fc(pt(e.element)).fold((()=>gc(t)),(e=>{Ze(e,t)||gc(t)}))})),setTimeout((()=>{Mt(e.element,"width",r+"px")}),0)})),Zr(eI,((e,o)=>{yf.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:fc(pt(e.element))}]))})),Gr(e,oI,{contents:o.event.forwardContents,focus:A.none()})})),Zr(tI,((o,n)=>{e.onBack(),oe(t.get()).each((e=>{t.set(t.get().slice(0,t.get().length-1)),Gr(o,oI,{contents:Ol(e.bar),focus:e.focus})}))}))]),vh.config({mode:"special",onEscape:o=>oe(t.get()).fold((()=>e.onEscape()),(e=>($r(o,tI),A.some(!0))))})]),lazySink:()=>bn.value(e.sink)});return{sketch:o,inSubtoolbar:()=>t.get().length>0}})({sink:o,onEscape:()=>(e.focus(),TS(e),A.some(!0)),onHide:()=>{TS(e)},onBack:()=>{(e=>{e.dispatch("ContextFormSlideBack")})(e)}}),m=kl(u.sketch),g=()=>{const t=d.get().getOr("node"),o=fF(t)?1:0;return gF(e,a,t,o)},p=()=>!(e.removed||i()&&s.isContextMenuOpen()),h=()=>{if(p()){const t=g(),o=xe(d.get(),"node")?((e,t)=>t.filter((e=>vt(e)&&$e(e))).map(Jo).getOrThunk((()=>mF(e))))(e,l.get()):mF(e);return t.height<=0||!uF(o,t,.01)}return!0},f=()=>{l.clear(),c.clear(),d.clear(),yf.hide(m)},v=()=>{if(yf.isOpen(m)){const e=m.element;Lt(e,"display"),h()?Mt(e,"display","none"):(c.set(0),yf.reposition(m))}},x=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:xa([vh.config({mode:"acyclic"}),Eh("pop-dialog-wrap-events",[ia((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>vh.focusIn(t)))})),la((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),y=e=>{const t=S([e]);Gr(m,eI,{forwardContents:x(t)})},w=Qt((()=>yF(t,y))),S=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...w().formNavigators},i=Wf(e)===wf.scrolling?wf.scrolling:wf.default,l=j(L(t,(t=>{return"contexttoolbar"===t.type?((t,o)=>PI(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n.backstage,A.some(["form:","toolbar:"])))(s,(o=t,{...o,launch:o.launch.getOrUndefined(),items:r(o.items)?o.items:L(o.items,Zy)})):((e,t)=>dF(e,t))(t,a.providers);var o})));return eB({type:i,uid:Pi("context-toolbar"),initGroups:l,onEscape:A.none,cyclicKeying:!0,providers:a.providers})},C=(t,n)=>{if(O.cancel(),!p())return;const s=S(t),r=t[0].position,u=((t,n)=>{const s="node"===t?a.anchors.node(n):a.anchors.cursor(),r=((e,t,o,n)=>"line"===t?{bubble:$c(12,0,pF),layouts:{onLtr:()=>[ec],onRtl:()=>[tc]},overrides:hF}:{bubble:$c(0,12,pF,1/12),layouts:vF(e,o,n,t),overrides:hF})(e,t,i(),{lastElement:l.get,isReposition:()=>xe(c.get(),0),getMode:()=>Zd.getMode(o)});return En(s,r)})(r,n);d.set(r),c.set(1);const f=m.element;Lt(f,"display"),(e=>xe(we(e,l.get(),Ze),!0))(n)||(Ba(f,wF),Zd.reset(o,m)),yf.showWithinBounds(m,x(s),{anchor:u,transition:{classes:[wF],mode:"placement"}},(()=>A.some(g()))),n.fold(l.clear,l.set),h()&&Mt(f,"display","none")};let k=!1;const O=oT((()=>{!e.hasFocus()||e.removed||k||(Ia(m.element,wF)?O.throttle():((e,t)=>{const o=ze(t.getBody()),n=e=>Ze(e,o),s=ze(t.selection.getNode());return(e=>!n(e)&&!et(o,e))(s)?A.none():((e,t,o)=>{const n=xF(e,t);if(n.contextForms.length>0)return A.some({elem:e,toolbars:[n.contextForms[0]]});{const t=xF(e,o);if(t.contextForms.length>0)return A.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>R(e,(e=>e.position===t)),o=t=>P(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=L(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return A.some({elem:e,toolbars:o})}return A.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?A.none():$s(t,(e=>{if(Ge(e)){const{contextToolbars:t,contextForms:n}=xF(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>$(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>P(e,(e=>e.position===t))))}})(t);return s.length>0?A.some({elem:e,toolbars:s}):A.none()}return A.none()}),e))(n,s,e)))})(w(),e).fold(f,(e=>{C(e.toolbars,A.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",f),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",v),e.on("click focus SetContent",O.throttle),e.on("keyup",(e=>{var t;((t=e.keyCode)!==eF.ENTER&&t!==eF.SPACEBAR||!u.inSubtoolbar())&&O.throttle()})),e.on(tF,f),e.on("contexttoolbar-show",(t=>{const o=w();fe(o.lookupTable,t.toolbarKey).each((o=>{C([o],Ce(t.target!==e,t.target)),ZB(m)}))})),e.on("focusout",(t=>{Cf.setEditorTimeout(e,(()=>{bc(o.element).isNone()&&bc(m.element).isNone()&&!e.hasFocus()&&f()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&f()})),e.on("DisabledStateChange",(e=>{e.state&&f()})),e.on("ExecCommand",(({command:e})=>{"toggleview"===e.toLowerCase()&&f()})),e.on("AfterProgressState",(t=>{t.state?f():e.hasFocus()&&O.throttle()})),e.on("dragstart",(()=>{k=!0})),e.on("dragend drop",(()=>{k=!1})),e.on("NodeChange",(e=>{u.inSubtoolbar()?(Mt(m.element,"transition","none"),v(),Lt(m.element,"transition")):bc(m.element).fold(O.throttle,b)}))}))},CF=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=nn();return L(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(xe(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},kF=e=>{CF(e,(e=>({name:"lineheight",text:"Line height",icon:"line-height",getOptions:xb,hash:e=>(e=>_I(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:w,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>A.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t),onToolbarSetup:AS(e),onMenuSetup:AS(e)}))(e)),(e=>A.from(Pf(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:y(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>{var n;return e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:null!==(n=t.customCode)&&void 0!==n?n:null}).unbind},getCurrent:e=>{const t=ze(e.selection.getNode());return Gs(t,(e=>A.some(e).filter(Ge).bind((e=>Ot(e,"lang").map((t=>({code:t,customCode:Ot(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=on();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),ES(o.clear,AS(e)(t))},onMenuSetup:AS(e)}))))(e).each((t=>CF(e,t)))},OF=e=>DS(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent")&&e.selection.isEditable())})),_F=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),ES((()=>e.off("PastePlainTextToggle",n)),AS(e)(o))},TF=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},EF=e=>{(e=>{(e=>{Z_.each([{name:"bold",text:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:MS(e,t.name),onAction:TF(e,t.name),shortcut:t.shortcut})}));for(let t=1;t<=6;t++){const o="h"+t,n=`Access+${t}`;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:MS(e,o),onAction:TF(e,o),shortcut:n})}})(e),(e=>{Z_.each([{name:"copy",text:"Copy",action:"Copy",icon:"copy",context:"any"},{name:"help",text:"Help",action:"mceHelp",icon:"help",shortcut:"Alt+0",context:"any"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A",context:"any"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"print",text:"Print",action:"mcePrint",icon:"print",shortcut:"Meta+P",context:"any"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:IS(e,t.action),shortcut:t.shortcut,context:t.context})})),Z_.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:AS(e),onAction:IS(e,t.action)})}))})(e),(e=>{Z_.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:IS(e,t.action),onSetup:MS(e,t.name)})}))})(e)})(e),(e=>{Z_.each([{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C",context:"any"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A",context:"any"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P",context:"any"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:IS(e,t.action),context:t.context})})),Z_.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onSetup:AS(e),onAction:IS(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onSetup:AS(e),onAction:TF(e,"code")})})(e)},AF=(e,t)=>DS(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),MF=e=>DS(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),DF=(e,t)=>{(e=>{V([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:IS(e,t.cmd),onSetup:MS(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onSetup:AS(e),onAction:IS(e,"JustifyNone")})})(e),EF(e),((e,t)=>{((e,t)=>{const o=aI(t,gI(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),onSetup:AS(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=aI(t,CI(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),onSetup:AS(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=aI(t,NI(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",onSetup:AS(e),getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=aI(t,bI(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",onSetup:AS(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=aI(t,FI(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",onSetup:AS(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:AF(e,"hasUndo"),onAction:IS(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:AF(e,"hasRedo"),onAction:IS(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:AF(e,"hasUndo"),onAction:IS(e,"undo"),shortcut:"Meta+Z"}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:AF(e,"hasRedo"),onAction:IS(e,"redo"),shortcut:"Meta+Y"})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},void 0,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=JS(e),o=QS(e),n=en(t),s=en(o);lC(e,"forecolor","forecolor",n),lC(e,"backcolor","hilitecolor",s),cC(e,"forecolor","forecolor","Text color",n),cC(e,"backcolor","hilitecolor","Background color",s)})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:IS(e,"mceToggleVisualAid"),context:"any"})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:MF(e),onAction:IS(e,"mceToggleVisualAid"),context:"any"})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:OF(e),onAction:IS(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onSetup:AS(e),onAction:IS(e,"indent")})})(e)})(e),kF(e),(e=>{const t=en(db(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:_F(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:_F(e,t)})})(e),(e=>{e.ui.registry.addContext("editable",(()=>e.selection.isEditable())),e.ui.registry.addContext("mode",(t=>e.mode.get()===t)),e.ui.registry.addContext("any",E),e.ui.registry.addContext("formatting",(t=>e.formatter.canApply(t))),e.ui.registry.addContext("insert",(t=>e.schema.isValidChild(e.selection.getNode().tagName,t)))})(e)},BF=e=>r(e)?e.split(/[ ,]/):e,IF=e=>t=>t.options.get(e),FF=IF("contextmenu_never_use_native"),RF=IF("contextmenu_avoid_overlap"),NF=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:P(o,(e=>be(t,e)))},zF=(e,t)=>({type:"makeshift",x:e,y:t}),LF=e=>"longpress"===e.type||0===e.type.indexOf("touch"),VF=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(LF(e)){const t=e.touches[0];return zF(t.pageX,t.pageY)}return zF(e.pageX,e.pageY)})(t):((e,t)=>{const o=kf.DOM.getPos(e);return((e,t,o)=>zF(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(LF(e)){const t=e.touches[0];return zF(t.clientX,t.clientY)}return zF(e.clientX,e.clientY)})(t)):HF(e),HF=e=>({type:"selection",root:ze(e.selection.getNode())}),PF=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:A.some(ze(e.selection.getNode())),root:ze(e.getBody())}))(e);case"point":return VF(e,t);case"selection":return HF(e)}},UF=(e,t,o,n,s,r)=>{const a=o(),i=PF(e,t,r);HT(a,fx.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!1,search:A.none()}).map((e=>{t.preventDefault(),yf.showMenuAt(s,{anchor:i},{menu:{markers:Dx("normal")},data:e})}))},WF={onLtr:()=>[Zl,Xl,Yl,Kl,Jl,Ql,EA,AA,TA,OA,_A,kA],onRtl:()=>[Zl,Yl,Xl,Jl,Kl,Ql,EA,AA,_A,kA,TA,OA]},$F={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},GF=(e,t,o,n,s,r)=>{const a=Mo(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=PF(e,t,o);return{bubble:$c(0,"point"===o?12:0,$F),layouts:WF,overrides:{maxWidthFunction:AD(),maxHeightFunction:Hc()},...n}})(e,t,r);HT(o,fx.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!0,search:A.none()}).map((o=>{t.preventDefault();const l=a?bf.HighlightMenuAndItem:bf.HighlightNone;yf.showMenuWithinBounds(s,{anchor:i},{menu:{markers:Dx("normal"),highlightOnOpen:l},data:o,type:"horizontal"},(()=>A.some(gF(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(tF)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{Cf.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return Ed(e.getWin(),bd.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},jF=e=>r(e)?"|"===e:"separator"===e.type,qF={type:"separator"},XF=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return qF;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:L(t,XF)}};default:const o=e;return{type:"menuitem",...t(o),onAction:v(o.onAction)}}},YF=(e,t)=>{if(0===t.length)return e;const o=oe(e).filter((e=>!jF(e))).fold((()=>[]),(e=>[qF]));return e.concat(o).concat(t).concat([qF])},KF=(e,t)=>!(e=>"longpress"===e.type||be(e,"touches"))(t)&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),JF=(e,t)=>KF(e,t)?e.selection.getStart(!0):t.target,QF=(e,t,o)=>{const n=Mo().deviceType.isTouch,s=kl(yf.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:xa([Eh("dismissContextMenu",[Zr(Rr(),((t,o)=>{Ou.close(t),e.focus()}))])])})),a=()=>yf.hide(s),i=t=>{if(FF(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!FF(e))(e,t)||(e=>0===NF(e).length)(e))return;const a=((e,t)=>{const o=RF(e),n=KF(e,t)?"selection":"point";if(Me(o)){const s=JF(e,t);return tk(ze(s),o)?"node":n}return n})(e,t);(n()?GF:UF)(e,t,(()=>{const o=JF(e,t),n=e.ui.registry.getAll(),s=NF(e);return((e,t,o)=>{const n=W(t,((t,n)=>fe(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n)&&Me(Ae(n)))return YF(t,n.split(" "));if(l(n)&&n.length>0){const e=L(n,XF);return YF(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&jF(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},ZF=Hs([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),eR=e=>t=>t.translate(-e.left,-e.top),tR=e=>t=>t.translate(e.left,e.top),oR=e=>(t,o)=>W(e,((e,t)=>t(e)),Gt(t,o)),nR=(e,t,o)=>e.fold(oR([tR(o),eR(t)]),oR([eR(t)]),oR([])),sR=(e,t,o)=>e.fold(oR([tR(o)]),oR([]),oR([tR(t)])),rR=(e,t,o)=>e.fold(oR([]),oR([eR(o)]),oR([tR(t),eR(o)])),aR=(e,t,o)=>{const n=e.fold(((e,t)=>({position:A.some("absolute"),left:A.some(e+"px"),top:A.some(t+"px")})),((e,t)=>({position:A.some("absolute"),left:A.some(e-o.left+"px"),top:A.some(t-o.top+"px")})),((e,t)=>({position:A.some("fixed"),left:A.some(e+"px"),top:A.some(t+"px")})));return{right:A.none(),bottom:A.none(),...n}},iR=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s(rR,lR),s(sR,cR),s(nR,dR))},lR=ZF.offset,cR=ZF.absolute,dR=ZF.fixed,uR=(e,t)=>{const o=kt(e,t);return u(o)?NaN:parseInt(o,10)},mR=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=uR(o,t.leftAttr),s=uR(o,t.topAttr);return isNaN(n)||isNaN(s)?A.none():A.some(Gt(n,s))})(e,t).fold((()=>o),(e=>dR(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?pR(e,t,a,s,r):hR(e,t,a,s,r),l=nR(a,s,r);return((e,t,o)=>{const n=e.element;St(n,t.leftAttr,o.left+"px"),St(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:dR(l.left,l.top),extra:A.none()})),(e=>({coord:e.output,extra:e.extra})))},gR=(e,t,o,n)=>se(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=sR(e,s,r),i=sR(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?A.some({output:iR(e.output,t,o,n),extra:e.extra}):A.none()})),pR=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return gR(r,o,n,s).orThunk((()=>{const e=W(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=sR(e,s,r),i=sR(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return Gt(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:A.some(a),snap:A.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:A.some(a),snap:A.some(t)}:e))}),{deltas:A.none(),snap:A.none()});return e.snap.map((e=>({output:iR(e.output,o,n,s),extra:e.extra})))}))},hR=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return gR(r,o,n,s)};var fR=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=tt(e.element),o=Po(t),r=Ha(s),a=((e,t,o)=>({coord:iR(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=aR(a.coord,0,r);Bt(s,i)}}});const bR="data-initial-z-index",vR=(e,t)=>{e.getSystem().addToGui(t),(e=>{rt(e.element).filter(Ge).each((t=>{Rt(t,"z-index").each((e=>{St(t,bR,e)})),Mt(t,"z-index",It(e.element,"z-index"))}))})(t)},xR=e=>{(e=>{rt(e.element).filter(Ge).each((e=>{Ot(e,bR).fold((()=>Lt(e,"z-index")),(t=>Mt(e,"z-index",t))),Tt(e,bR)}))})(e),e.getSystem().removeFromGui(e)},yR=(e,t,o)=>e.getSystem().build(uk.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var wR=Ms("snaps",[ps("getSnapPoints"),Ti("onSensor"),ps("leftAttr"),ps("topAttr"),Ds("lazyViewport",Zo),Ds("mustSnap",!1)]);const SR=[Ds("useFixed",T),ps("blockerClass"),Ds("getTarget",w),Ds("onDrag",b),Ds("repositionTarget",!0),Ds("onDrop",b),zs("getBounds",Zo),wR],CR=e=>{return(t=Rt(e,"left"),o=Rt(e,"top"),n=Rt(e,"position"),t.isSome()&&o.isSome()&&n.isSome()?A.some(((e,t,o)=>("fixed"===o?dR:lR)(parseInt(e,10),parseInt(t,10)))(t.getOrDie(),o.getOrDie(),n.getOrDie())):A.none()).getOrThunk((()=>{const t=qt(e);return cR(t.left,t.top)}));var t,o,n},kR=(e,t)=>({bounds:e.getBounds(),height:Wt(t.element),width:Jt(t.element)}),OR=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>kR(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=tt(e.element),a=Po(r),i=Ha(s),l=CR(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=sR(t,o,n),i=Vi(a.left,r.x,r.x+r.width-s.width),l=Vi(a.top,r.y,r.y+r.height-s.height),c=cR(i,l);return t.fold((()=>{const e=rR(c,o,n);return lR(e.left,e.top)}),y(c),(()=>{const e=nR(c,o,n);return dR(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>lR(e+a,t+i)),((e,t)=>cR(e+a,t+i)),((e,t)=>dR(e+a,t+i))));var t,a,i;const l=nR(e,n,s);return dR(l.left,l.top)}),(t=>{const a=mR(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=aR(c,0,i);Bt(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},_R=(e,t,o,n)=>{t.each(xR),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;Tt(o,t.leftAttr),Tt(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},TR=e=>(t,o)=>{const n=e=>{o.setStartData(kR(t,e))};return Kr([Zr(Dr(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var ER=Object.freeze({__proto__:null,getData:e=>A.from(Gt(e.x,e.y)),getDelta:(e,t)=>Gt(t.left-e.left,t.top-e.top)});const AR=(e,t,o)=>[Zr(er(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>_R(n,A.some(l),e,t),a=ok(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),OR(n,e,t,ER,o)}},l=yR(n,e.blockerClass,(e=>Kr([Zr(er(),e.forceDrop),Zr(nr(),e.drop),Zr(tr(),((t,o)=>{e.move(o.event)})),Zr(or(),e.delayDrop)]))(i));o(n),vR(n,l)}))],MR=[...SR,Di("dragger",{handlers:TR(AR)})];var DR=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return A.some(Gt(t.clientX,t.clientY))})(t):A.none()},getDelta:(e,t)=>Gt(t.left-e.left,t.top-e.top)});const BR=(e,t,o)=>{const n=nn(),s=o=>{_R(o,n.get(),e,t),n.clear()};return[Zr(Ks(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{OR(r,e,t,DR,o)}},c=yR(r,e.blockerClass,(e=>Kr([Zr(Ks(),e.forceDrop),Zr(Qs(),e.drop),Zr(Zs(),e.drop),Zr(Js(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),vR(r,c)})),Zr(Js(),((o,n)=>{n.stop(),OR(o,e,t,DR,n.event)})),Zr(Qs(),((e,t)=>{t.stop(),s(e)})),Zr(Zs(),s)]},IR=MR,FR=[...SR,Di("dragger",{handlers:TR(BR)})],RR=[...SR,Di("dragger",{handlers:TR(((e,t,o)=>[...AR(e,t,o),...BR(e,t,o)]))})];var NR=Object.freeze({__proto__:null,mouse:IR,touch:FR,mouseOrTouch:RR}),zR=Object.freeze({__proto__:null,init:()=>{let e=A.none(),t=A.none();const o=y({});return va({readState:o,reset:()=>{e=A.none(),t=A.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=A.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=A.some(e)}})}});const LR=Ca({branchKey:"mode",branches:NR,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:A.from(e.extra)})},state:zR,apis:fR}),VR=(e,t,o,n,s,r)=>e.fold((()=>LR.snap({sensor:cR(o-20,n-20),range:Gt(s,r),output:cR(A.some(o),A.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return LR.snap({sensor:cR(s,r),range:Gt(40,40),output:cR(A.some(o-a.width/2),A.some(n-a.height/2)),extra:{td:t}})})),HR=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>Ze(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),PR=e=>Hb(Lb.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:xa([LR.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),Uk.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),UR=(e,t)=>{const o=en([]),n=en([]),s=en(!1),r=nn(),a=nn(),i=e=>{const o=Jo(e);return VR(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=Jo(e);return VR(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=HR((()=>L(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=HR((()=>L(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=PR(c),m=PR(d),g=kl(u.asSpec()),p=kl(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);LR.snapTo(t,r),((t,o)=>{const n=o.dom.getBoundingClientRect();Lt(t.element,"display");const r=st(ze(e.getBody())).dom.innerHeight,a=n[s]<0,i=((e,t)=>e[s]>t)(n,r);(a||i)&&Mt(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");if(Mo().deviceType.isTouch()){const i=e=>L(e,ze);e.on("TableSelectionChange",(e=>{s.get()||(au(t,g),au(t,p),s.set(!0));const l=ze(e.start),c=ze(e.finish);r.set(l),a.set(c),A.from(e.otherCells).each((e=>{o.set(i(e.upOrLeftCells)),n.set(i(e.downOrRightCells)),f(l),b(c)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(cu(g),cu(p),s.set(!1)),r.clear(),a.clear()}))}},WR=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:xa([vh.config({mode:"flow",selector:"div[role=button]"}),mg.config({disabled:o.isDisabled}),Rw((()=>o.checkUiComponentContext("any"))),Wb.config({}),Th.config({}),Eh("elementPathEvents",[ia(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>vh.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=_S(e,r);if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?W(r,((t,n,r)=>{const a=((t,n,s)=>Lb.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s}},components:[yl(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:xa([ev.config({...o.tooltips.getConfig({tooltipText:o.translate(["Select the {0} element",n.nodeName.toLowerCase()]),onShow:(e,t)=>{((e,t)=>{const o=A.from(kt(e,"id")).getOrThunk((()=>{const e=Pi("aria");return St(t,"id",e),e}));St(e,"aria-describedby",o)})(e.element,t.element)},onHide:e=>{var t;t=e.element,Tt(t,"aria-describedby")}})}),Sw(o.isDisabled),Rw((()=>o.checkUiComponentContext("any")))])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[yl(` ${s} `)]},a])}),[]):[];Th.set(t,a)}))}))])]),components:[]}};var $R;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}($R||($R={}));const GR=(e,t,o)=>{const n=ze(e.getContainer()),s=((e,t,o,n,s)=>{const r={height:qI(n+t.top,Rf(e),zf(e))};return o===$R.Both&&(r.width=qI(s+t.left,Ff(e),Nf(e))),r})(e,t,o,Ut(n),Kt(n));ie(s,((e,t)=>{h(e)&&Mt(n,t,jI(e))})),(e=>{e.dispatch("ResizeEditor")})(e)},jR=(e,t,o,n)=>{const s=Gt(20*o,20*n);return GR(e,s,t),A.some(!0)},qR=(e,t)=>{const o=()=>{const o=[],n=gb(e),s=ib(e),r=lb(e)||e.hasPlugin("wordcount");return s&&o.push(WR(e,{},t)),n&&o.push((()=>{const e=Ww("Alt+0");return{dom:{tag:"div",classes:["tox-statusbar__help-text"]},components:[yl(ox.translate(["Press {0} for help",e]))]}})()),r&&o.push((()=>{const o=[];return e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>Th.set(e,[yl(t.translate(["{0} "+n,o[n]]))]);return Lb.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:xa([Sw(t.isDisabled),Rw((()=>t.checkUiComponentContext("any"))),Wb.config({}),Th.config({}),Gu.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Eh("wordcount-events",[da((e=>{const t=Gu.getValue(e),n="words"===t.mode?"characters":"words";Gu.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),ia((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=Gu.getValue(t);Gu.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[Sr()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),lb(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=poweredby&utm_source=tiny&utm_medium=referral&utm_content=v7",rel:"noopener",target:"_blank","aria-label":e.translate(["Build with {0}","TinyMCE"])},innerHtml:e.translate(["Build with {0}",'<svg height="16" viewBox="0 0 80 16" width="80" xmlns="http://www.w3.org/2000/svg"><g opacity=".8"><path d="m80 3.537v-2.202h-7.976v11.585h7.976v-2.25h-5.474v-2.621h4.812v-2.069h-4.812v-2.443zm-10.647 6.929c-.493.217-1.13.337-1.864.337s-1.276-.156-1.805-.47a3.732 3.732 0 0 1 -1.3-1.298c-.324-.554-.48-1.191-.48-1.877s.156-1.335.48-1.877a3.635 3.635 0 0 1 1.3-1.299 3.466 3.466 0 0 1 1.805-.481c.65 0 .914.06 1.263.18.36.12.698.277.986.47.289.192.578.384.842.6l.12.085v-2.586l-.023-.024c-.385-.35-.855-.614-1.384-.818-.53-.205-1.155-.313-1.877-.313-.721 0-1.6.144-2.333.445a5.773 5.773 0 0 0 -1.937 1.251 5.929 5.929 0 0 0 -1.324 1.9c-.324.735-.48 1.565-.48 2.455s.156 1.72.48 2.454c.325.734.758 1.383 1.324 1.913.553.53 1.215.938 1.937 1.25a6.286 6.286 0 0 0 2.333.434c.819 0 1.384-.108 1.961-.313.59-.216 1.083-.505 1.468-.866l.024-.024v-2.49l-.12.096c-.41.337-.878.626-1.396.866zm-14.869-4.15-4.8-5.04-.024-.025h-.902v11.67h2.502v-6.847l2.827 3.08.385.409.397-.41 2.791-3.067v6.845h2.502v-11.679h-.902l-4.788 5.052z"/><path clip-rule="evenodd" d="m15.543 5.137c0-3.032-2.466-5.113-4.957-5.137-.36 0-.745.024-1.094.096-.157.024-3.85.758-3.85.758-3.032.602-4.62 2.466-4.704 4.788-.024.89-.024 4.27-.024 4.27.036 3.165 2.406 5.138 5.017 5.126.337 0 1.119-.109 1.287-.145.144-.024.385-.084.746-.144.661-.12 1.684-.325 3.067-.602 2.37-.409 4.103-2.009 4.44-4.33.156-1.023.084-4.692.084-4.692zm-3.213 3.308-2.346.457v2.31l-5.859 1.143v-5.75l2.346-.458v3.441l3.513-.686v-3.44l-3.513.685v-2.297l5.859-1.143v5.75zm20.09-3.296-.083-1.023h-2.13v8.794h2.346v-4.884c0-1.107.95-1.985 2.057-1.997 1.095 0 1.901.89 1.901 1.997v4.884h2.346v-5.245c-.012-2.105-1.588-3.777-3.67-3.765a3.764 3.764 0 0 0 -2.778 1.25l.012-.011zm-6.014-4.102 2.346-.458v2.298l-2.346.457z" fill-rule="evenodd"/><path d="m28.752 4.126h-2.346v8.794h2.346z"/><path clip-rule="evenodd" d="m43.777 15.483 4.043-11.357h-2.418l-1.54 4.355-.445 1.324-.36-1.324-1.54-4.355h-2.418l3.151 8.794-1.083 3.08zm-21.028-5.51c0 .722.541 1.034.878 1.034s.638-.048.95-.144l.518 1.708c-.217.145-.879.518-2.13.518a2.565 2.565 0 0 1 -2.562-2.587c-.024-1.082-.024-2.49 0-4.21h-1.54v-2.142h1.54v-1.912l2.346-.458v2.37h2.201v2.142h-2.2v3.693-.012z" fill-rule="evenodd"/></g></svg>\n'.trim()])},behaviours:xa([Ih.config({})])}]}),{dom:{tag:"div",classes:["tox-statusbar__right-container"]},components:o}})()),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container",...(()=>{const e="tox-statusbar__text-container--flex-start",t="tox-statusbar__text-container--flex-end";if(n){const o="tox-statusbar__text-container-3-cols";return r||s?r&&!s?[o,t]:[o,e]:[o,"tox-statusbar__text-container--space-around"]}return[r&&!s?t:e]})()]},components:o}]:[]};return{dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const n=o(),s=((e,t)=>{const o=(e=>{const t=cb(e);return!1===t?$R.None:"both"===t?$R.Both:$R.Vertical})(e);if(o===$R.None)return A.none();const n=o===$R.Both?"Press the arrow keys to resize the editor.":"Press the Up and Down arrow keys to resize the editor.";return A.some(ux("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{"aria-label":t.translate(n),"data-mce-name":"resize-handle"},behaviours:[LR.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>GR(e,s,o),blockerClass:"tox-blocker"}),vh.config({mode:"special",onLeft:()=>jR(e,o,-1,0),onRight:()=>jR(e,o,1,0),onUp:()=>jR(e,o,0,-1),onDown:()=>jR(e,o,0,1)}),Wb.config({}),Ih.config({}),ev.config(t.tooltips.getConfig({tooltipText:t.translate("Resize")}))]},t.icons))})(e,t);return n.concat(s.toArray())})()}},XR=(e,t)=>t.get().getOrDie(`UI for ${e} has not been rendered`),YR=(e,t)=>{const o=e.inline,n=o?ZI:GI,s=Eb(e)?KM:HM,r=(()=>{const e=nn(),t=nn(),o=nn();return{dialogUi:e,popupUi:t,mainUi:o,getUiMotherships:()=>{const o=e.get().map((e=>e.mothership)),n=t.get().map((e=>e.mothership));return o.fold((()=>n.toArray()),(e=>n.fold((()=>[e]),(t=>Ze(e.element,t.element)?[e]:[e,t]))))},lazyGetInOuterOrDie:(e,t)=>()=>o.get().bind((e=>t(e.outerContainer))).getOrDie(`Could not find ${e} element in OuterContainer`)}})(),a=nn(),i=nn(),l=nn(),c=Mo().deviceType.isTouch()?["tox-platform-touch"]:[],d=Cb(e),u=Wf(e),m=Hb({dom:{tag:"div",classes:["tox-anchorbar"]}}),g=Hb({dom:{tag:"div",classes:["tox-bottom-anchorbar"]}}),p=()=>r.mainUi.get().map((e=>e.outerContainer)).bind(_B.getHeader),h=r.lazyGetInOuterOrDie("anchor bar",m.getOpt),f=r.lazyGetInOuterOrDie("bottom anchor bar",g.getOpt),b=r.lazyGetInOuterOrDie("toolbar",_B.getToolbar),v=r.lazyGetInOuterOrDie("throbber",_B.getThrobber),x=((e,t,o,n)=>{const s=en(!1),r=(e=>{const t=en(Cb(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),a={icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:ox.translate,isDisabled:()=>!t.ui.isEnabled(),getOption:t.options.get,tooltips:tM(e.dialog),checkUiComponentContext:e=>{if(Df(t))return{contextType:"disabled",shouldDisable:!0};const[o,n=""]=e.split(":"),s=t.ui.registry.getAll().contexts;return{contextType:o,shouldDisable:!fe(s,o).fold((()=>fe(s,"mode").map((e=>e("design"))).getOr(!1)),(e=>"!"===n.charAt(0)?!e(n.slice(1)):e(n)))}}},i=_M(t),l=(e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},n=en([]),s=en([]),r=en(!1);return e.on("PreInit",(s=>{const r=QA(e),a=eM(e,r,t,o);n.set(a)})),e.on("addStyleModifications",(n=>{const a=eM(e,n.items,t,o);s.set(a),r.set(n.replace)})),{getData:()=>{const e=r.get()?[]:n.get(),t=s.get();return e.concat(t)}}})(t),c=(e=>({colorPicker:WA(e),hasCustomColors:$A(e),getColors:GA(e),getColorCols:jA(e)}))(t),d=(e=>({isDraggableModal:qA(e)}))(t),u={shared:{providers:a,anchors:UA(t,o,n,r.isPositionedAtTop),header:r},urlinput:i,styles:l,colorinput:c,dialog:d,isContextMenuOpen:()=>s.get(),setContextMenuState:e=>s.set(e)},m=e=>A.none(),g={...u,shared:{...u.shared,interpreter:e=>fA(e,{},g,m),getSink:e.popup}},p={...u,shared:{...u.shared,interpreter:e=>fA(e,{},p,m),getSink:e.dialog}};return{popup:g,dialog:p}})({popup:()=>bn.fromOption(r.popupUi.get().map((e=>e.sink)),"(popup) UI has not been rendered"),dialog:()=>bn.fromOption(r.dialogUi.get().map((e=>e.sink)),"UI has not been rendered")},e,h,f),y=()=>{const t=(()=>{const t={attributes:{[Kc]:d?Yc.BottomToTop:Yc.TopToBottom}},o=_B.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:x.popup,onEscape:()=>{e.focus()}}),n=_B.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:x.popup.shared.getSink,providers:x.popup.shared.providers,onEscape:()=>{e.focus()},onToolbarToggled:t=>{((e,t)=>{e.dispatch("ToggleToolbarDrawer",{state:t})})(e,t)},type:u,lazyToolbar:b,lazyHeader:()=>p().getOrDie("Could not find header element"),...t}),s=_B.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:x.popup.shared.providers,onEscape:()=>{e.focus()},type:u}),r=Sb(e),a=yb(e),i=bb(e),l=mb(e),c=r||a||i,g=[(h=l,_B.parts.promotion({dom:{tag:"div",classes:["tox-promotion"]},promotionLink:h})),o];var h;return _B.parts.header({dom:{tag:"div",classes:["tox-editor-header"].concat(c?[]:["tox-editor-header--empty"]),...t},components:j([i?g:[],r?[s]:a?[n]:[],Ob(e)?[]:[m.asSpec()]]),sticky:Eb(e),editor:e,sharedBackstage:x.popup.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[_B.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),_B.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=_B.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:x.popup}),r=_B.parts.viewWrapper({backstage:x.popup}),i=ab(e)&&!o?A.some(qR(e,x.popup.shared.providers)):A.none(),l=j([d?[]:[t],o?[]:[n],d?[t]:[]]),h=_B.parts.editorContainer({components:j([l,o?[]:[g.asSpec()]])}),f=Tb(e),v={role:"application",...ox.isRtl()?{dir:"rtl"}:{},...f?{"aria-hidden":"true"}:{}},y=kl(_B.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(o?["tox-tinymce-inline"]:[]).concat(d?["tox-tinymce--toolbar-bottom"]:[]).concat(c),styles:{visibility:"hidden",...f?{opacity:"0",border:"0"}:{}},attributes:v},components:[h,...o?[]:[r,...i.toArray()],s],behaviours:xa([Rw((()=>x.popup.shared.providers.checkUiComponentContext("any"))),mg.config({disableClass:"tox-tinymce--disabled"}),vh.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),w=mk(y);return a.set(w),{mothership:w,outerContainer:y}},w=t=>{const o=jI((e=>(e=>{const t=((e,t)=>{if("number"==typeof t)return A.from(t);const o=/^([0-9.]+)(pt|em|px)$/.exec(t.trim());if(o){const t=o[2],n=Number.parseFloat(o[1]);if(Number.isNaN(n)||n<0)return A.none();if("em"===t)return A.from(n*Number.parseFloat(window.getComputedStyle(e.dom).fontSize));if("pt"===t)return A.from(.75*n);if("px"===t)return A.from(n)}return A.none()})(ze(e.targetElm),Bf(e)),o=Rf(e),n=zf(e);return t.map((e=>qI(e,o,n)))})(e).getOr(Bf(e)))(e)),n=jI((e=>XI(e).getOr(If(e)))(e));return e.inline||(zt("div","width",n)&&Mt(t.element,"width",n),zt("div","height",o)?Mt(t.element,"height",o):Mt(t.element,"height","400px")),o};return{popups:{backstage:x.popup,getMothership:()=>XR("popups",l)},dialogs:{backstage:x.dialog,getMothership:()=>XR("dialogs",i)},renderUI:()=>{const o=y(),a=(()=>{const t=_b(e),o=Ze(xt(),t)&&"grid"===It(t,"display"),n={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(c),attributes:{...ox.isRtl()?{dir:"rtl"}:{}}},behaviours:xa([Zd.config({useFixed:()=>s.isDocked(p)})])},r={dom:{styles:{width:document.body.clientWidth+"px"}},events:Kr([Zr(Br(),(e=>{Mt(e.element,"width",document.body.clientWidth+"px")}))])},a=kl(En(n,o?r:{})),l=mk(a);return i.set(l),{sink:a,mothership:l}})(),d=Ab(e)?(()=>{const e={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-silver-popup-sink","tox-tinymce-aux"].concat(c),attributes:{...ox.isRtl()?{dir:"rtl"}:{}}},behaviours:xa([Zd.config({useFixed:()=>s.isDocked(p),getBounds:()=>t.getPopupSinkBounds()})])},o=kl(e),n=mk(o);return l.set(n),{sink:o,mothership:n}})():(e=>(l.set(e.mothership),e))(a);r.dialogUi.set(a),r.popupUi.set(d),r.mainUi.set(o);return(t=>{const{mainUi:o,popupUi:r,uiMotherships:a}=t;le($f(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:i,menuItems:l,contextToolbars:c,sidebars:d,views:m}=e.ui.registry.getAll(),g=wb(e),h={menuItems:l,menus:Mb(e),menubar:Kf(e),toolbar:g.getOrThunk((()=>Jf(e))),allowToolbarGroups:u===wf.floating,buttons:i,sidebar:d,views:m};var f;f=o.outerContainer,e.addShortcut("alt+F9","focus menubar",(()=>{_B.focusMenubar(f)})),e.addShortcut("alt+F10","focus toolbar",(()=>{_B.focusToolbar(f)})),e.addCommand("ToggleToolbarDrawer",((e,t)=>{(null==t?void 0:t.skipFocus)?_B.toggleToolbarDrawerWithoutFocusing(f):_B.toggleToolbarDrawer(f)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>_B.isToolbarDrawerToggled(f))),((e,t,o)=>{const n=(e,n)=>{V([t,...o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{V([t,...o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(_u(),{target:e.target}),a=Go(),i=Bc(a,"touchstart",r),l=Bc(a,"touchmove",(e=>n(Ar(),e))),c=Bc(a,"touchend",(e=>n(Mr(),e))),d=Bc(a,"mousedown",r),u=Bc(a,"mouseup",(e=>{0===e.raw.button&&s(Eu(),{target:e.target})})),m=e=>s(_u(),{target:ze(e.target)}),g=e=>{0===e.button&&s(Eu(),{target:ze(e.target)})},p=()=>{V(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(Dr(),Fc(e)),f=e=>{s(Tu(),{}),n(Br(),Fc(e))},b=pt(ze(e.getElement())),v=Ic(b,"scroll",(o=>{requestAnimationFrame((()=>{if(null!=e.getContainer()){const s=Nb(e,t.element).map((e=>[e.element,...e.others])).getOr([]);R(s,(e=>Ze(e,o.target)))&&(e.dispatch("ElementScroll",{target:o.target.dom}),n(Vr(),o))}}))})),x=()=>s(Tu(),{}),y=t=>{t.state&&s(_u(),{target:ze(e.getContainer())})},w=e=>{s(_u(),{target:ze(e.relatedTarget.getContainer())})},S=t=>e.dispatch("focusin",t),C=t=>e.dispatch("focusout",t);e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",x),e.on("AfterProgressState",y),e.on("DismissPopups",w),V([t,...o],(e=>{e.element.dom.addEventListener("focusin",S),e.element.dom.addEventListener("focusout",C)}))})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",x),e.off("AfterProgressState",y),e.off("DismissPopups",w),V([t,...o],(e=>{e.element.dom.removeEventListener("focusin",S),e.element.dom.removeEventListener("focusout",C)})),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind(),v.unbind()})),e.on("detach",(()=>{V([t,...o],pu),V([t,...o],(e=>e.destroy()))}))})(e,o.mothership,a),s.setup(e,x.popup.shared,p),DF(e,x.popup),QF(e,x.popup.shared.getSink,x.popup),(e=>{const{sidebars:t}=e.ui.registry.getAll();V(re(t),(o=>{const n=t[o],s=()=>xe(A.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{t.setActive(s());const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}},context:"any"})}))})(e),yD(e,v,x.popup.shared),SF(e,c,r.sink,{backstage:x.popup}),UR(e,r.sink);const b={targetNode:e.getElement(),height:w(o.outerContainer)};return n.render(e,t,h,x.popup,b)})({popupUi:d,dialogUi:a,mainUi:o,uiMotherships:r.getUiMotherships()})}}},KR=y([ps("lazySink"),Cs("dragBlockClass"),zs("getBounds",Zo),Ds("useTabstopAt",E),Ds("firstTabstop",0),Ds("eventOrder",{}),ju("modalBehaviours",[vh]),Ei("onExecute"),Mi("onEscape")]),JR={sketch:w},QR=y([wm({name:"draghandle",overrides:(e,t)=>({behaviours:xa([LR.config({mode:"mouse",getTarget:e=>cn(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),xm({schema:[ps("dom")],name:"title"}),xm({factory:JR,schema:[ps("dom")],name:"close"}),xm({factory:JR,schema:[ps("dom")],name:"body"}),wm({factory:JR,schema:[ps("dom")],name:"footer"}),ym({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[Ds("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),Ds("components",[])],name:"blocker"})]),ZR=Xm({name:"ModalDialog",configFields:KR(),partFields:QR(),factory:(e,t,o,n)=>{const s=nn(),r=Pi("modal-events"),a={...e.eventOrder,[Ir()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])},i=Mo();return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([Ol(t)]),behaviours:xa([Ih.config({}),Eh("dialog-blocker-events",[aa(rr(),(()=>{vD.isBlocked(t)||vh.focusIn(t)}))])])});au(o,a),vh.focusIn(t)},hide:e=>{s.clear(),rt(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{cu(e)}))}))},getBody:t=>Fm(t,e,"body"),getFooter:t=>Im(t,e,"footer"),setIdle:e=>{vD.unblock(e)},setBusy:(e,t)=>{vD.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Xu(e.modalBehaviours,[Th.config({}),vh.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt,firstTabstop:e.firstTabstop}),vD.config({getRoot:s.get}),Eh(r,[ia((t=>{const o=Fm(t,e,"title").element,n=(e=>e.dom.textContent)(o);i.os.isMacOS()&&g(n)?St(t.element,"aria-label",n):((e,t)=>{const o=Ot(e,"id").fold((()=>{const e=Pi("dialog-label");return St(t,"id",e),e}),w);St(e,"aria-labelledby",o)})(t.element,o)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),eN=Wn([ny,sy].concat(aw)),tN=Qn,oN=[By("button"),xy,Rs("align","end",["start","end"]),Ty,_y,Ts("buttonType",["primary","secondary"]),Fs("context","mode:design")],nN=[...oN,ay],sN=[vs("type",["submit","cancel","custom"]),...nN],rN=[vs("type",["menu"]),by,yy,xy,Ss("items",eN),...oN],aN=[...oN,vs("type",["togglebutton"]),yy,xy,by,Ns("active",!1)],iN=cs("type",{submit:sN,cancel:sN,custom:sN,menu:rN,togglebutton:aN}),lN=[ny,ay,vs("level",["info","warn","error","success"]),ly,Ds("url","")],cN=Wn(lN),dN=[ny,ay,_y,By("button"),xy,Oy,Ts("buttonType",["primary","secondary","toolbar"]),Ty,Fs("context","mode:design")],uN=Wn(dN),mN=[ny,sy],gN=mN.concat([wy]),pN=mN.concat([ry,_y,Fs("context","mode:design")]),hN=Wn(pN),fN=Qn,bN=gN.concat([Ey("auto"),Fs("context","mode:design")]),vN=Wn(bN),xN=qn([dy,ay,ly]),yN=gN.concat([Fs("storageKey","default"),Fs("context","mode:design")]),wN=Wn(yN),SN=Jn,CN=Wn(gN),kN=Jn,ON=mN.concat([Fs("tag","textarea"),bs("scriptId"),bs("scriptUrl"),Es("onFocus"),Bs("settings",void 0,ts)]),_N=mN.concat([Fs("tag","textarea"),xs("init")]),TN=ns((e=>rs("customeditor.old",Un(_N),e).orThunk((()=>rs("customeditor.new",Un(ON),e))))),EN=Jn,AN=gN.concat([Fs("context","mode:design")]),MN=Wn(AN),DN=$n(zn),BN=e=>[ny,fs("columns"),e],IN=[ny,bs("html"),Rs("presets","presentation",["presentation","document"]),zs("onInit",b),Ns("stretched",!1)],FN=Wn(IN),RN=gN.concat([Ns("border",!1),Ns("sandboxed",!0),Ns("streamContent",!1),Ns("transparent",!0)]),NN=Wn(RN),zN=Jn,LN=Wn(mN.concat([_s("height")])),VN=Wn([bs("url"),Os("zoom"),Os("cachedWidth"),Os("cachedHeight")]),HN=gN.concat([_s("inputMode"),_s("placeholder"),Ns("maximized",!1),_y,Fs("context","mode:design")]),PN=Wn(HN),UN=Jn,WN=e=>[ny,ry,e,Rs("align","start",["start","center","end"]),_s("for")],$N=[ay,dy],GN=[ay,Ss("items",ds(0,(()=>jN)))],jN=Gn([Wn($N),Wn(GN)]),qN=gN.concat([Ss("items",jN),_y,Fs("context","mode:design")]),XN=Wn(qN),YN=Jn,KN=gN.concat([ws("items",[ay,dy]),Is("size",1),_y,Fs("context","mode:design")]),JN=Wn(KN),QN=Jn,ZN=gN.concat([Ns("constrain",!0),_y,Fs("context","mode:design")]),ez=Wn(ZN),tz=Wn([bs("width"),bs("height")]),oz=mN.concat([ry,Is("min",0),Is("max",0)]),nz=Wn(oz),sz=Kn,rz=[ny,Ss("header",Jn),Ss("cells",$n(Jn))],az=Wn(rz),iz=gN.concat([_s("placeholder"),Ns("maximized",!1),_y,Fs("context","mode:design")]),lz=Wn(iz),cz=Jn,dz=[vs("type",["directory","leaf"]),iy,bs("id"),ks("menu",ZM),_s("customStateIcon"),_s("customStateIconTooltip")],uz=Wn(dz),mz=dz.concat([Ss("children",ds(0,(()=>os("type",{directory:gz,leaf:uz}))))]),gz=Wn(mz),pz=os("type",{directory:gz,leaf:uz}),hz=[ny,Ss("items",pz),Es("onLeafAction"),Es("onToggleExpand"),Ls("defaultExpandedIds",[],Jn),_s("defaultSelectedId")],fz=Wn(hz),bz=gN.concat([Rs("filetype","file",["image","media","file"]),_y,_s("picker_text"),Fs("context","mode:design")]),vz=Wn(bz),xz=Wn([dy,Ay]),yz=e=>us("items","items",{tag:"required",process:{}},$n(ns((t=>rs(`Checking item of ${e}`,wz,t).fold((e=>bn.error(ls(e))),(e=>bn.value(e))))))),wz=Hn((()=>{return os("type",{alertbanner:cN,bar:Wn((e=yz("bar"),[ny,e])),button:uN,checkbox:hN,colorinput:wN,colorpicker:CN,dropzone:MN,grid:Wn(BN(yz("grid"))),iframe:NN,input:PN,listbox:XN,selectbox:JN,sizeinput:ez,slider:nz,textarea:lz,urlinput:vz,customeditor:TN,htmlpanel:FN,imagepreview:LN,collection:vN,label:Wn(WN(yz("label"))),table:az,tree:fz,panel:Cz});var e})),Sz=[ny,Ds("classes",[]),Ss("items",wz)],Cz=Wn(Sz),kz=[By("tab"),iy,Ss("items",wz)],Oz=[ny,ws("tabs",kz)],_z=Wn(Oz),Tz=nN,Ez=iN,Az=Wn([bs("title"),hs("body",os("type",{panel:Cz,tabpanel:_z})),Fs("size","normal"),Ls("buttons",[],Ez),Ds("initialData",{}),zs("onAction",b),zs("onChange",b),zs("onSubmit",b),zs("onClose",b),zs("onCancel",b),zs("onTabChange",b)]),Mz=Wn([vs("type",["cancel","custom"]),...Tz]),Dz=Wn([bs("title"),bs("url"),Os("height"),Os("width"),As("buttons",Mz),zs("onAction",b),zs("onCancel",b),zs("onClose",b),zs("onMessage",b)]),Bz=e=>a(e)?[e].concat(q(he(e),Bz)):l(e)?q(e,Bz):[],Iz=e=>r(e.type)&&r(e.name),Fz={checkbox:fN,colorinput:SN,colorpicker:kN,dropzone:DN,input:UN,iframe:zN,imagepreview:VN,selectbox:QN,sizeinput:tz,slider:sz,listbox:YN,size:tz,textarea:cz,urlinput:xz,customeditor:EN,collection:xN,togglemenuitem:tN},Rz=e=>{const t=(e=>P(Bz(e),Iz))(e),o=q(t,(e=>(e=>A.from(Fz[e.type]))(e).fold((()=>[]),(t=>[hs(e.name,t)]))));return Wn(o)},Nz=e=>{var t;return{internalDialog:as(rs("dialog",Az,e)),dataValidator:Rz(e),initialData:null!==(t=e.initialData)&&void 0!==t?t:{}}},zz={open:(e,t)=>{const o=Nz(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(as(rs("dialog",Dz,t))),redial:e=>Nz(e)};var Lz=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?yh:xh)(o,r)}))};return Kr([Zr(wr(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;F(s.channels,n)&&o(t,s.data)}})),ia(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),Vz=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),Hz=[ps("channel"),Cs("renderComponents"),Cs("updateState"),Cs("initialData"),Ns("reuseDom",!0)];const Pz=wa({fields:Hz,name:"reflecting",active:Lz,apis:Vz,state:Object.freeze({__proto__:null,init:()=>{const e=en(A.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(A.none())}}})}),Uz=e=>{const t=[],o={};return ie(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?bn.error(t):bn.value(o)},Wz=(e,t,o,n)=>{const s=Hb(R_.sketch((s=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:L(e.items,(e=>pA(s,e,t,o,n)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[s.asSpec()]}],behaviours:xa([vh.config({mode:"acyclic",useTabstopAt:k(cT)}),(r=s,Qm.config({find:r.getOpt})),j_(s,{postprocess:e=>Uz(e).fold((e=>(console.error(e),{})),w)}),Eh("dialog-body-panel",[Zr(rr(),((e,t)=>{e.getSystem().broadcastOn([hT],{newFocus:A.some(t.event.target)})}))])])};var r},$z=qm({name:"TabButton",configFields:[Ds("uid",void 0),ps("value"),us("dom","dom",Bn((()=>({attributes:{role:"tab",id:Pi("aria"),"aria-selected":"false"}}))),Xn()),Cs("action"),Ds("domModification",{}),ju("tabButtonBehaviours",[Ih,vh,Gu]),ps("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:$h(e.action),behaviours:Xu(e.tabButtonBehaviours,[Ih.config({}),vh.config({mode:"execution",useSpace:!0,useEnter:!0}),Gu.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),Gz=y([ps("tabs"),ps("dom"),Ds("clickToDismiss",!1),ju("tabbarBehaviours",[Sg,vh]),Oi(["tabClass","selectedClass"])]),jz=Sm({factory:$z,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Sg.dehighlight(e,t),Gr(e,Pr(),{tabbar:e,button:t})},o=(e,t)=>{Sg.highlight(e,t),Gr(e,Hr(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Sg.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),qz=y([jz]),Xz=Xm({name:"Tabbar",configFields:Gz(),partFields:qz(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Xu(e.tabbarBehaviours,[Sg.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{St(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{St(t.element,"aria-selected","false")}}),vh.config({mode:"flow",getInitial:e=>Sg.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),Yz=qm({name:"Tabview",configFields:[ju("tabviewBehaviours",[Th])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:Xu(e.tabviewBehaviours,[Th.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),Kz=y([Ds("selectFirst",!0),Ti("onChangeTab"),Ti("onDismissTab"),Ds("tabs",[]),ju("tabSectionBehaviours",[])]),Jz=xm({factory:Xz,schema:[ps("dom"),ys("markers",[ps("tabClass"),ps("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),Qz=xm({factory:Yz,name:"tabview"}),Zz=y([Jz,Qz]),eL=Xm({name:"TabSection",configFields:Kz(),partFields:Zz(),factory:(e,t,o,n)=>{const s=(t,o)=>{Im(t,e,"tabbar").each((e=>{o(e).each(jr)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:qu(e.tabSectionBehaviours),events:Kr(j([e.selectFirst?[ia(((e,t)=>{s(e,Sg.getFirst)}))]:[],[Zr(Hr(),((t,o)=>{(t=>{const o=Gu.getValue(t);Im(t,e,"tabview").each((n=>{$(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();Ot(t.element,"id").each((e=>{St(n.element,"aria-labelledby",e)})),Th.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),Zr(Pr(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>Im(t,e,"tabview").map((e=>Th.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Sg.getCandidates(e);return $(o,(e=>Gu.getValue(e)===t)).filter((t=>!Sg.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),tL=(e,t)=>{Mt(e,"height",t+"px"),Mt(e,"flex-basis",t+"px")},oL=(e,t,o)=>{cn(e,'[role="dialog"]').each((e=>{mn(e,'[role="tablist"]').each((n=>{o.get().map((o=>(Mt(t,"height","0"),Mt(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=nt(e).dom,s=cn(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===It(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=Ut(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+Kt(o)?Math.max(Ut(o),a):a,l=parseInt(It(e,"margin-top"),10)||0,c=parseInt(It(e,"margin-bottom"),10)||0;return r-(Ut(e)+l+c-i)})(e,t,n))))).each((e=>{tL(t,e)}))}))}))},nL=e=>mn(e,'[role="tabpanel"]'),sL="send-data-to-section",rL="send-data-to-view",aL=(e,t,o,n)=>{const s=en({}),r=e=>{const t=Gu.getValue(e),o=Uz(t).getOr({}),n=s.get(),r=En(n,o);s.set(r)},a=e=>{const t=s.get();Gu.setValue(e,t)},i=en(null),l=L(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[yl(o.shared.providers.translate(e.title))],view:()=>[R_.sketch((s=>({dom:{tag:"div",classes:["tox-form"]},components:L(e.items,(e=>pA(s,e,t,o,n))),formBehaviours:xa([vh.config({mode:"acyclic",useTabstopAt:k(cT)}),Eh("TabView.form.events",[ia(a),la(r)]),uc.config({channels:Us([{key:sL,value:{onReceive:r}},{key:rL,value:{onReceive:a}}])})])})))]}))),c=(e=>{const t=nn(),o=[ia((o=>{const n=o.element;nL(n).each((s=>{Mt(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>L(e,((n,s)=>{Th.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return Th.set(o,[]),r.height})))(e,s,o),r=(e=>te(Z(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),oL(n,s,t),Lt(s,"visibility"),((e,t)=>{te(e).each((e=>eL.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{oL(n,s,t)}))}))})),Zr(Br(),(e=>{const o=e.element;nL(o).each((e=>{oL(o,e,t)}))})),Zr(Mk,((e,o)=>{const n=e.element;nL(n).each((e=>{const o=fc(pt(e));Mt(e,"visibility","hidden");const s=Rt(e,"height").map((e=>parseInt(e,10)));Lt(e,"height"),Lt(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),oL(n,e,t)):s.each((t=>{tL(e,t)})),Lt(e,"visibility"),o.each(gc)}))}))];return{extraEvents:o,selectFirst:!1}})(l);return eL.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=Gu.getValue(t);Gr(e,Ak,{name:n,oldName:i.get()}),i.set(n)},tabs:l,components:[eL.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[Xz.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:xa([Wb.config({})])}),eL.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:c.selectFirst,tabSectionBehaviours:xa([Eh("tabpanel",c.extraEvents),vh.config({mode:"acyclic"}),Qm.config({find:e=>te(eL.getViewItems(e))}),q_(A.none(),(e=>(e.getSystem().broadcastOn([sL],{}),s.get())),((e,t)=>{s.set(t),e.getSystem().broadcastOn([rL],{})}))])})},iL=(e,t,o,n,s,r)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:xa([$_(0),Pz.config({channel:`${mT}-${t}`,updateState:(e,t)=>A.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[aL(t,e.initialData,n,r)]:[Wz(t,e.initialData,n,r)]},initialData:e})])}),lL=_f.deviceType.isTouch(),cL=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),dL=(e,t)=>ZR.parts.close(Lb.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:xa([Wb.config({})])})),uL=()=>ZR.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),mL=(e,t)=>ZR.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:Vb(`<p>${tx(t.translate(e))}</p>`)}]}]}),gL=e=>ZR.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),pL=(e,t)=>[uk.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),uk.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],hL=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return ZR.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),A.some(!0)),useTabstopAt:e=>!cT(e),firstTabstop:e.firstTabstop,dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:Vb(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:lL?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:xa([Ih.config({}),Eh("dialog-events",e.dialogEvents.concat([aa(rr(),((e,t)=>{vD.isBlocked(e)||vh.focusIn(e)})),Zr(zr(),((e,t)=>{e.getSystem().broadcastOn([hT],{newFocus:t.event.newFocus})}))])),Eh("scroll-lock",[ia((()=>{Ma(xt(),s)})),la((()=>{Ba(xt(),s)}))]),...e.extraBehaviours]),eventOrder:{[Sr()]:["dialog-events"],[Ir()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[Fr()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},fL=e=>Lb.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),"data-mce-name":"close"}},buttonBehaviours:xa([Wb.config({}),ev.config(e.tooltips.getConfig({tooltipText:e.translate("Close")}))]),components:[ux("close",{tag:"span",classes:["tox-icon"]},e.icons)],action:e=>{$r(e,kk)}}),bL=(e,t,o,n)=>({dom:{tag:"h1",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:xa([Pz.config({channel:`${uT}-${t}`,initialData:e,renderComponents:e=>[yl(n.translate(e.title))]})])}),vL=()=>({dom:Vb('<div class="tox-dialog__draghandle"></div>')}),xL=(e,t,o)=>((e,t,o)=>{const n=ZR.parts.title(bL(e,t,A.none(),o)),s=ZR.parts.draghandle(vL()),r=ZR.parts.close(fL(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return uk.sketch({dom:Vb('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),yL=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:`${n.getOr(0)}px`,position:"absolute"}},behaviours:t,components:[{dom:Vb('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),wL=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{const n=mn(e().element,".tox-dialog__header").map((e=>Ut(e)));ZR.setBusy(e(),((e,s)=>yL(o.message,s,t,n)))},onUnblock:()=>{ZR.setIdle(e())}}),SL="tox-dialog--fullscreen",CL="tox-dialog--width-lg",kL="tox-dialog--width-md",OL=e=>{switch(e){case"large":return A.some(CL);case"medium":return A.some(kL);default:return A.none()}},_L=(e,t)=>{const o=ze(t.element.dom);Ia(o,SL)||(Ra(o,[CL,kL]),OL(e).each((e=>Ma(o,e))))},TL=(e,t)=>{const o=ze(e.element.dom),n=Na(o),s=$(n,(e=>e===CL||e===kL)).or(OL(t));((e,t)=>{V(t,(t=>{((e,t)=>{const o=_a(e)?e.dom.classList.toggle(t):((e,t)=>F(Ta(e),t)?Aa(e,t):Ea(e,t))(e,t);Da(e)})(e,t)}))})(o,[SL,...s.toArray()])},EL=(e,t,o)=>kl(hL({...e,firstTabstop:1,lazySink:o.shared.getSink,extraBehaviours:[Y_({}),...e.extraBehaviours],onEscape:e=>{$r(e,kk)},dialogEvents:t,eventOrder:{[wr()]:[Pz.name(),uc.name()],[Ir()]:["scroll-lock",Pz.name(),"messages","dialog-events","alloy.base.behaviour"],[Fr()]:["alloy.base.behaviour","dialog-events","messages",Pz.name(),"scroll-lock"]}})),AL=(e,t={})=>L(e,(e=>"menu"===e.type?(e=>{const o=L(e.items,(e=>{const o=fe(t,e.name).getOr(en(!1));return{...e,storage:o}}));return{...e,items:o}})(e):e)),ML=e=>W(e,((e,t)=>"menu"===t.type?W(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),DL=(e,t)=>[na(rr(),lT),e(Ck,((e,o,n,s)=>{hc(s.element)&&fc(pt(s.element)).each(pc),t.onClose(),o.onClose()})),e(kk,((e,t,o,n)=>{t.onCancel(e),$r(n,Ck)})),Zr(Ek,((e,o)=>t.onUnblock())),Zr(Tk,((e,o)=>t.onBlock(o.event)))],BL=(e,t,o)=>{const n=(t,o)=>Zr(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{Pz.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...DL(n,t),n(_k,((e,t)=>t.onSubmit(e))),n(wk,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(Ok,((e,t,n,s)=>{const r=()=>s.getSystem().isConnected()?vh.focusIn(s):void 0,a=e=>_t(e,"disabled")||Ot(e,"aria-disabled").exists((e=>"true"===e)),i=pt(s.element),l=fc(i);t.onAction(e,{name:n.name,value:n.value}),fc(i).fold(r,(e=>{a(e)||l.exists((t=>et(e,t)&&a(t)))?r():o().toOptional().filter((t=>!et(t.element,e))).each(r)}))})),n(Ak,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),la((t=>{const o=e();Gu.setValue(t,o.getData())}))]},IL=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=H(o,(e=>"start"===e.align)),s=(e,t)=>uk.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:L(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},FL=(e,t,o)=>({dom:Vb('<div class="tox-dialog__footer"></div>'),components:[],behaviours:xa([Pz.config({channel:`${gT}-${t}`,initialData:e,updateState:(e,t)=>{const n=L(t.buttons,(e=>{const t=Hb(((e,t)=>QE(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return A.some({lookupByName:t=>((e,t,o)=>$(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:IL})])}),RL=(e,t,o)=>ZR.parts.footer(FL(e,t,o)),NL=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=Qm.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return R_.getField(o,t).orThunk((()=>{const o=e.getFooter().bind((e=>Pz.getState(e).get()));return o.bind((e=>e.lookupByName(t)))}))}return A.none()},zL=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...Gu.getValue(n),...le(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=En(r,t),i=((e,t)=>{const o=e.getRoot();return Pz.getState(o).get().map((e=>as(rs("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();Gu.setValue(l,i),ie(o,((e,t)=>{be(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{NL(e,t).each(o?mg.enable:mg.disable)},focus:t=>{NL(e,t).each(Ih.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{Gr(t,Tk,{message:e})}))},unblock:()=>{n((e=>{$r(e,Ek)}))},showTab:t=>{n((o=>{const n=e.getBody();Pz.getState(n).get().exists((e=>e.isTabPanel()))&&Qm.getCurrent(n).each((e=>{eL.showTab(e,t)}))}))},redial:r=>{n((n=>{const a=e.getId(),i=t(r),l=AL(i.internalDialog.buttons,o);n.getSystem().broadcastOn([`${dT}-${a}`],i),n.getSystem().broadcastOn([`${uT}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${mT}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${gT}-${a}`],{...i.internalDialog,buttons:l}),s.setData(i.initialData)}))},close:()=>{n((e=>{$r(e,Ck)}))},toggleFullscreen:e.toggleFullscreen};return s},LL=(e,t,o,n=!1,s)=>{const r=Pi("dialog"),a=Pi("dialog-label"),i=Pi("dialog-content"),l=e.internalDialog,c=en(l.size),d=OL(c.get()).toArray(),u=Hb(((e,t,o,n)=>uk.sketch({dom:Vb('<div class="tox-dialog__header"></div>'),components:[bL(e,t,A.some(o),n),vL(),fL(n)],containerBehaviours:xa([LR.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>gn(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"},onDrag:(e,t)=>{e.getSystem().broadcastOn([_u()],{target:t})}})])}))({title:l.title,draggable:!0},r,a,o.shared.providers)),m=Hb(((e,t,o,n,s,r)=>iL(e,t,A.some(o),n,s,r))({body:l.body,initialData:l.initialData},r,i,o,n,(e=>NL(x,e)))),g=AL(l.buttons),p=ML(g),h=Ce(0!==g.length,Hb(((e,t,o)=>FL(e,t,o))({buttons:g},r,o))),f=BL((()=>w),{onBlock:e=>{vD.block(v,((t,n)=>{const s=u.getOpt(v).map((e=>Ut(e.element)));return yL(e.message,n,o.shared.providers,s)}))},onUnblock:()=>{vD.unblock(v)},onClose:()=>t.closeWindow()},o.shared.getSink),b=Mo().os,v=kl({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline",...d],attributes:{role:"dialog",...b.isMacOS()?{"aria-label":l.title}:{"aria-labelledby":a}}},eventOrder:{[wr()]:[Pz.name(),uc.name()],[Sr()]:["execute-on-form"],[Ir()]:["reflecting","execute-on-form"]},behaviours:xa([vh.config({mode:"cyclic",onEscape:e=>($r(e,Ck),A.some(!0)),useTabstopAt:e=>!cT(e)&&("button"!==Ue(e)||"disabled"!==kt(e,"disabled")),firstTabstop:1}),Pz.config({channel:`${dT}-${r}`,updateState:(e,t)=>(c.set(t.internalDialog.size),_L(t.internalDialog.size,e),s(),A.some(t)),initialData:e}),Ih.config({}),Eh("execute-on-form",f.concat([aa(rr(),((e,t)=>{vh.focusIn(e)})),Zr(zr(),((e,t)=>{e.getSystem().broadcastOn([hT],{newFocus:t.event.newFocus})}))])),vD.config({getRoot:()=>A.some(v)}),Th.config({}),Y_({})]),components:[u.asSpec(),m.asSpec(),...h.map((e=>e.asSpec())).toArray()]}),x={getId:y(r),getRoot:y(v),getFooter:()=>h.map((e=>e.get(v))),getBody:()=>m.get(v),getFormWrapper:()=>{const e=m.get(v);return Qm.getCurrent(e).getOr(e)},toggleFullscreen:()=>{TL(v,c.get())}},w=zL(x,t.redial,p);return{dialog:v,instanceApi:w}};var VL=tinymce.util.Tools.resolve("tinymce.util.URI");const HL=["insertContent","setContent","execCommand","close","block","unblock"],PL=e=>a(e)&&-1!==HL.indexOf(e.mceAction),UL=(e,t,o,n)=>{const s=Pi("dialog"),i=xL(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[aT(A.none(),{dom:{tag:"iframe",attributes:{src:e.url}},behaviours:xa([Wb.config({}),Ih.config({})])})]}],behaviours:xa([vh.config({mode:"acyclic",useTabstopAt:k(cT)})])};return ZR.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?A.none():A.some(RL({buttons:e},s,n)))),u=((e,t)=>{const o=(e,t)=>Zr(e,((e,o)=>{n(e,((n,s)=>{t(y,n,o.event,e)}))})),n=(e,t)=>{Pz.getState(e).get().each((o=>{t(o,e)}))};return[...DL(o,t),o(Ok,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})(0,wL((()=>x),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},p=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],h=new VL(e.url,{base_uri:new VL(window.location.href)}),f=`${h.protocol}://${h.host}${h.port?":"+h.port:""}`,b=on(),v=[Pz.config({channel:`${dT}-${s}`,updateState:(e,t)=>A.some(t),initialData:e}),Eh("messages",[ia((()=>{const t=Bc(ze(window),"message",(t=>{if(h.isSameOrigin(new VL(t.raw.origin))){const n=t.raw.data;PL(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,y,n):(e=>!PL(e)&&a(e)&&be(e,"mceAction"))(n)&&e.onMessage(y,n)}}));b.set(t)})),la(b.clear)]),uc.config({channels:{[pT]:{onReceive:(e,t)=>{mn(e.element,"iframe").each((e=>{const o=e.dom.contentWindow;g(o)&&o.postMessage(t,f)}))}}}})],x=EL({id:s,header:i,body:l,footer:c,extraClasses:p,extraBehaviours:v,extraStyles:m},u,n),y=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{Gr(t,Tk,{message:e})}))},unblock:()=>{t((e=>{$r(e,Ek)}))},close:()=>{t((e=>{$r(e,Ck)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([pT],e)}))}}})(x);return{dialog:x,instanceApi:y}},WL=(e,t)=>as(rs("data",t,e)),$L=e=>tk(e,".tox-alert-dialog")||tk(e,".tox-confirm-dialog"),GL=(e,t,o)=>t&&o?[]:[Ri.config({contextual:{lazyContext:()=>A.some(Ko(ze(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"],lazyViewport:t=>Nb(e,t.element).map((e=>({bounds:zb(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:qt(e.element).top})}))).getOrThunk((()=>({bounds:Zo(),optScrollEnv:A.none()})))})],jL=e=>{const t=e.editor,o=Eb(t),n=(e=>{const t=e.shared;return{open:(o,n)=>{const s=()=>{ZR.hide(l),n()},r=Hb(QE({context:"any",name:"close-alert",text:"OK",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"cancel",e)),a=uL(),i=dL(s,t.providers),l=kl(hL({lazySink:()=>t.getSink(),header:cL(a,i),body:mL(o,t.providers),footer:A.some(gL(pL([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Zr(kk,s)],eventOrder:{}}));ZR.show(l);const c=r.get(l);Ih.focus(c)}}})(e.backstages.dialog),s=(e=>{const t=e.shared;return{open:(o,n)=>{const s=e=>{ZR.hide(c),n(e)},r=Hb(QE({context:"any",name:"yes",text:"Yes",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"submit",e)),a=QE({context:"any",name:"no",text:"No",primary:!1,buttonType:A.some("secondary"),align:"end",enabled:!0,icon:A.none()},"cancel",e),i=uL(),l=dL((()=>s(!1)),t.providers),c=kl(hL({lazySink:()=>t.getSink(),header:cL(i,l),body:mL(o,t.providers),footer:A.some(gL(pL([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Zr(kk,(()=>s(!1))),Zr(_k,(()=>s(!0)))],eventOrder:{}}));ZR.show(c);const d=r.get(c);Ih.focus(d)}}})(e.backstages.dialog),r=(t,o)=>zz.open(((t,n,s)=>{const r=n,a=((e,t,o)=>{const n=Pi("dialog"),s=e.internalDialog,r=xL(s.title,n,o),a=en(s.size),i=OL(a.get()).toArray(),l=((e,t,o,n)=>{const s=iL(e,t,A.none(),o,!1,n);return ZR.parts.body(s)})({body:s.body,initialData:s.initialData},n,o,(e=>NL(h,e))),c=AL(s.buttons),d=ML(c),u=Ce(0!==c.length,RL({buttons:c},n,o)),m=BL((()=>f),wL((()=>p),o.shared.providers,t),o.shared.getSink),g={id:n,header:r,body:l,footer:u,extraClasses:i,extraBehaviours:[Pz.config({channel:`${dT}-${n}`,updateState:(e,t)=>(a.set(t.internalDialog.size),_L(t.internalDialog.size,e),A.some(t)),initialData:e})],extraStyles:{}},p=EL(g,m,o),h={getId:y(n),getRoot:y(p),getBody:()=>ZR.getBody(p),getFooter:()=>ZR.getFooter(p),getFormWrapper:()=>{const e=ZR.getBody(p);return Qm.getCurrent(e).getOr(e)},toggleFullscreen:()=>{TL(p,a.get())}},f=zL(h,t.redial,d);return{dialog:p,instanceApi:f}})({dataValidator:s,initialData:r,internalDialog:t},{redial:zz.redial,closeWindow:()=>{ZR.hide(a.dialog),o(a.instanceApi)}},e.backstages.dialog);return ZR.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),t),a=(n,s,r,a)=>zz.open(((n,i,l)=>{const c=WL(i,l),d=nn(),u=e.backstages.popup.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{yf.reposition(e),o&&u||Ri.refresh(e)})),g=LL({dataValidator:l,initialData:c,internalDialog:n},{redial:zz.redial,closeWindow:()=>{d.on(yf.hide),t.off("ResizeEditor",m),d.clear(),r(g.instanceApi)}},e.backstages.popup,a.ariaAttrs,m),p=kl(yf.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:a.persistent?{event:"doNotDismissYet"}:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:xa([Eh("window-manager-inline-events",[Zr(Rr(),((e,t)=>{$r(g.dialog,kk)}))]),...GL(t,o,u)]),isExtraPart:(e,t)=>$L(t)}));return d.set(p),yf.showWithinBounds(p,Ol(g.dialog),{anchor:s},(()=>{const e=t.inline?xt():ze(t.getContainer()),o=Ko(e);return A.some(o)})),o&&u||(Ri.refresh(p),t.on("ResizeEditor",m)),g.instanceApi.setData(c),vh.focusIn(g.dialog),g.instanceApi}),n),i=(o,n,s,r)=>zz.open(((o,a,i)=>{const l=WL(a,i),c=nn(),d=e.backstages.popup.shared.header.isPositionedAtTop(),u=()=>c.on((e=>{yf.reposition(e),Ri.refresh(e)})),m=LL({dataValidator:i,initialData:l,internalDialog:o},{redial:zz.redial,closeWindow:()=>{c.on(yf.hide),t.off("ResizeEditor ScrollWindow ElementScroll",u),c.clear(),s(m.instanceApi)}},e.backstages.popup,r.ariaAttrs,u),g=kl(yf.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:r.persistent?{event:"doNotDismissYet"}:{},...d?{}:{fireRepositionEventInstead:{}},inlineBehaviours:xa([Eh("window-manager-inline-events",[Zr(Rr(),((e,t)=>{$r(m.dialog,kk)}))]),Ri.config({contextual:{lazyContext:()=>A.some(Ko(ze(t.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top","bottom"],lazyViewport:e=>Nb(t,e.element).map((e=>({bounds:zb(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:qt(e.element).top})}))).getOrThunk((()=>({bounds:Zo(),optScrollEnv:A.none()})))})]),isExtraPart:(e,t)=>$L(t)}));return c.set(g),yf.showWithinBounds(g,Ol(m.dialog),{anchor:n},(()=>e.backstages.popup.shared.getSink().toOptional().bind((e=>{const o=Nb(t,e.element).map((e=>zb(e))).getOr(Zo()),n=Ko(ze(t.getContentAreaContainer())),s=Qo(n,o);return A.some(Yo(s.x,s.y,s.width,s.height-15))})))),Ri.refresh(g),t.on("ResizeEditor ScrollWindow ElementScroll ResizeWindow",u),m.instanceApi.setData(l),vh.focusIn(m.dialog),m.instanceApi}),o);return{open:(t,o,n)=>{if(!u(o)){if("toolbar"===o.inline)return a(t,e.backstages.popup.shared.anchors.inlineDialog(),n,o);if("bottom"===o.inline)return i(t,e.backstages.popup.shared.anchors.inlineBottomDialog(),n,o);if("cursor"===o.inline)return a(t,e.backstages.popup.shared.anchors.cursor(),n,o)}return r(t,n)},openUrl:(o,n)=>((o,n)=>zz.openUrl((o=>{const s=UL(o,{closeWindow:()=>{ZR.hide(s.dialog),n(s.instanceApi)}},t,e.backstages.dialog);return ZR.show(s.dialog),s.instanceApi}),o))(o,n),alert:(e,t)=>{n.open(e,t)},close:e=>{e.close()},confirm:(e,t)=>{s.open(e,t)}}},qL=e=>{Af(e),(e=>{const t=e.options.register,o=e=>{return f(e,r)?{value:(t=e,WS(t.map(((e,t)=>t%2==0?"#"+(e=>{return(t=e,iS(t)?A.some({value:lS(t)}):A.none()).orThunk((()=>wS(e).map(dS))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return dS(fS(s,r,a,i))}));var t})(e).value:e)))),valid:!0}:{valid:!1,message:"Must be an array of strings."};var t},n=e=>h(e)&&e>0?{value:e,valid:!0}:{valid:!1,message:"Must be a positive number."};t("color_map",{processor:o,default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_map_raw",{processor:e=>f(e,r)?{value:WS(e),valid:!0}:{valid:!1,message:"Must be an array of strings."}}),t("color_map_background",{processor:o}),t("color_map_foreground",{processor:o}),t("color_cols",{processor:n,default:qS(e)}),t("color_cols_foreground",{processor:n,default:XS(e,PS)}),t("color_cols_background",{processor:n,default:XS(e,US)}),t("custom_colors",{processor:"boolean",default:!0}),t("color_default_foreground",{processor:"string",default:GS}),t("color_default_background",{processor:"string",default:GS})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:BF(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)};pn.add("silver",(e=>{qL(e);let t=()=>Zo();const{dialogs:o,popups:n,renderUI:s}=YR(e,{getPopupSinkBounds:()=>t()});ek(e,n.backstage.shared);const r=jL({editor:e,backstages:{popup:n.backstage,dialog:o.backstage}}),a=nn();return{renderUI:()=>{const o=s();return Nb(e,n.getMothership().element).each((e=>{t=()=>zb(e)})),o},getWindowManagerImpl:y(r),getNotificationManagerImpl:()=>px(e,{backstage:n.backstage},n.getMothership(),a),getPromotionElement:()=>mn(ze(e.getContainer()),".tox-promotion").map((e=>e.dom)).getOrNull()}}))}();
function handleKqmProductMapping(elem, selectedCategoryId, kqmProductIdToMap) {
    if ($(elem).is(':checked') === true) {
        createKqmProductMapping(elem, selectedCategoryId, kqmProductIdToMap);
    } else {
        removeKqmProductMapping(elem, selectedCategoryId, kqmProductIdToMap);
    }
}

function createKqmProductMapping(elem, selectedCategoryId, kqmProductIdToMap) {
    if ($(elem).is(':checked') === false) {
        return;
    }

    console.assert(selectedCategoryId !== null);
    console.assert(selectedCategoryId !== undefined);
    console.assert(typeof selectedCategoryId === "number");
    console.assert(kqmProductIdToMap !== null);
    console.assert(kqmProductIdToMap !== undefined);
    console.assert(typeof kqmProductIdToMap === "number");

    htmx.ajax('GET', '/Categories/CreateKqmProductMapping', {
        swap: "none",
        values: {
            nopCategoryId: selectedCategoryId,
            kqmProductIdToMap: kqmProductIdToMap,
        }
    });
}

function removeKqmProductMapping(elem, selectedCategoryId, kqmProductIdToMap) {
    if ($(elem).is(':checked') === true) {
        return;
    }

    console.assert(selectedCategoryId !== null);
    console.assert(selectedCategoryId !== undefined);
    console.assert(typeof selectedCategoryId === "number");
    console.assert(kqmProductIdToMap !== null);
    console.assert(kqmProductIdToMap !== undefined);
    console.assert(typeof kqmProductIdToMap === "number");

    htmx.ajax('GET', '/Categories/DeleteKqmProductMapping', {
        swap: "none",
        values: {
            nopCategoryId: selectedCategoryId,
            mappingIdToRemove: kqmProductIdToMap,
        }
    });
}

function createKqmQuoteMapping(evt, selectedCategoryId) {
    console.assert(evt !== null);
    console.assert(evt.item !== null);
    console.assert(evt.type === "add");
    console.assert(selectedCategoryId !== null);
    console.assert(selectedCategoryId !== undefined);
    console.assert(typeof selectedCategoryId === "number");

    const kqmQuoteToMap = $(evt.item);
    const kqmQuoteIdToMap = $(kqmQuoteToMap).attr("id");

    htmx.ajax('GET', '/Categories/CreateKqmQuoteMapping', {
        target: '#mainPageContent',
        swap: "innerHTML",
        values: {
            nopCategoryId: selectedCategoryId,
            kqmQuoteIdToMap: kqmQuoteIdToMap
        }
    });
}

function removeKqmQuoteMapping(evt, selectedCategoryId) {
    console.assert(evt !== null);
    console.assert(evt.item !== null);
    console.assert(evt.type === "remove");
    console.assert(selectedCategoryId !== null);
    console.assert(selectedCategoryId !== undefined);
    console.assert(typeof selectedCategoryId === "number");

    const categoryMappingToRemove = $(evt.item);
    const mappingIdToRemove = $(categoryMappingToRemove).attr("id");

    htmx.ajax('GET', '/Categories/DeleteKqmQuoteMapping', {
        target: '#mainPageContent',
        swap: "innerHTML",
        values: {
            nopCategoryId: selectedCategoryId,
            mappingIdToRemove: mappingIdToRemove
        }
    });
}

function initKqmQuoteMappingsForNopCategory(selectedCategoryId) {
    console.assert(selectedCategoryId !== null);
    console.assert(selectedCategoryId !== undefined);
    console.assert(typeof selectedCategoryId === "number");

    new Sortable(document.getElementById("mappedKqmQuotes"), {
        group: 'shared',
        animation: 150,
        swapThreshold: 1,
        draggable: ".draggable",
        onAdd: function (evt) {
            createKqmQuoteMapping(evt, selectedCategoryId);
        },
        onRemove: function (evt) {
            removeKqmQuoteMapping(evt, selectedCategoryId);
        }
    });
    new Sortable(document.getElementById("unmappedKqmQuotes"), {
        group: 'shared',
        animation: 150,
        swapThreshold: 1,
        draggable: ".draggable"
    });
}


function createPriceBookMapping(evt, selectedCategoryId) {
    console.assert(evt !== null);
    console.assert(evt.item !== null);
    console.assert(evt.type === "add");
    console.assert(selectedCategoryId !== null);
    console.assert(selectedCategoryId !== undefined);
    console.assert(typeof selectedCategoryId === "number");

    const priceBookToMap = $(evt.item);
    const priceBookIdToMap = $(priceBookToMap).attr("id");

    htmx.ajax('GET', '/Categories/CreatePriceBookMapping', {
        target: '#mainPageContent',
        swap: "innerHTML",
        values: {
            // datafeedCategoryId: selectedCategoryId,
            nopCategoryId: selectedCategoryId,
            priceBookIdToMap: priceBookIdToMap
        }
    });
}

function removePriceBookMapping(evt, selectedCategoryId) {
    console.assert(evt !== null);
    console.assert(evt.item !== null);
    console.assert(evt.type === "remove");
    console.assert(selectedCategoryId !== null);
    console.assert(selectedCategoryId !== undefined);
    console.assert(typeof selectedCategoryId === "number");

    const priceBookMappingToRemove = $(evt.item);
    const mappingIdToRemove = $(priceBookMappingToRemove).attr("id");

    htmx.ajax('GET', '/Categories/DeletePriceBookMapping', {
        target: '#mainPageContent',
        swap: "innerHTML",
        values: {
            nopCategoryId: selectedCategoryId,
            mappingIdToRemove: mappingIdToRemove
        }
    });
}

function initPriceBookMappingsForNopCategory(selectedCategoryId) {
    console.assert(selectedCategoryId !== null);
    console.assert(selectedCategoryId !== undefined);
    console.assert(typeof selectedCategoryId === "number");

    new Sortable(document.getElementById("mappedPriceBooks"), {
        group: 'shared',
        animation: 150,
        swapThreshold: 1,
        draggable: ".draggable",
        onAdd: function (evt) {
            createPriceBookMapping(evt, selectedCategoryId);
        },
        onRemove: function (evt) {
            removePriceBookMapping(evt, selectedCategoryId);
        }
    });
    new Sortable(document.getElementById("unmappedPriceBooks"), {
        group: 'shared',
        animation: 150,
        swapThreshold: 1,
        draggable: ".draggable"
    });
}
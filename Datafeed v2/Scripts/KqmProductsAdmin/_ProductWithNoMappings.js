function createNopCategoryMappingForProduct(evt, selectedProductId) {
    console.assert(evt !== null);
    console.assert(evt.item !== null);
    console.assert(evt.type === "add");
    console.assert(selectedProductId !== null);
    console.assert(selectedProductId !== undefined);
    console.assert(typeof selectedProductId === "number");

    const categoryId = $(evt.item).attr('id');

    htmx.ajax('GET', 'Categories/CreateKqmProductMapping', {
        target: '#mainPageContent',
        swap: "none",
        values: {
            kqmProductIdToMap: selectedProductId,
            nopCategoryId: categoryId
        }
    })
}

function removeNopCategoryMappingForProduct(evt, selectedProductId) {
    console.assert(evt !== null);
    console.assert(evt.item !== null);
    console.assert(evt.type === "remove");
    console.assert(selectedProductId !== null);
    console.assert(selectedProductId !== undefined);
    console.assert(typeof selectedProductId === "number");

    const categoryId = $(evt.item).attr('id');

    htmx.ajax('GET', 'Categories/DeleteKqmProductMapping', {
        target: '#mainPageContent',
        swap: "none",
        values: {
            mappingIdToRemove: selectedProductId,
            nopCategoryId: categoryId
        }
    })
}

function initNopCategoryMappings(selectedProductId) {
    console.assert(selectedProductId !== null);
    console.assert(selectedProductId !== undefined);
    console.assert(typeof selectedProductId === "number");

    new Sortable(document.getElementById('mappedNopCategories'), {
        group: 'shared',
        animation: 150,
        swapThreshold: 1,
        draggable: ".draggable",
        onAdd: function (evt) {
            createNopCategoryMappingForProduct(evt, selectedProductId);
        },
        onRemove: function (evt) {
            removeNopCategoryMappingForProduct(evt, selectedProductId);
        }
    });
    new Sortable(document.getElementById('unmappedNopCategories'), {
        group: 'shared',
        animation: 150,
        swapThreshold: 1,
        draggable: ".draggable"
    });
}
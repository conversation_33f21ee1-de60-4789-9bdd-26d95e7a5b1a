<?xml version="1.0" encoding="utf-8"?>
<packages>
    <package id="AIProductDescription" version="1.6.6" targetFramework="net48"/>
    <package id="Antlr" version="*******" targetFramework="net48"/>
    <package id="Azure.Core" version="1.44.1" targetFramework="net48"/>
    <package id="Azure.Identity" version="1.13.2" targetFramework="net48"/>
    <package id="FandoogleReflectorService" version="1.1.5" targetFramework="net48"/>
    <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.2" targetFramework="net48"/>
    <package id="Microsoft.Graph" version="5.75.0" targetFramework="net48"/>
    <package id="Microsoft.Graph.Core" version="3.2.4" targetFramework="net48"/>
    <package id="Microsoft.Identity.Client" version="4.70.0" targetFramework="net48"/>
    <package id="Microsoft.Identity.Client.Extensions.Msal" version="4.67.2" targetFramework="net48"/>
    <package id="Microsoft.IdentityModel.JsonWebTokens" version="8.6.1" targetFramework="net48"/>
    <package id="Microsoft.IdentityModel.Protocols" version="8.6.1" targetFramework="net48"/>
    <package id="Microsoft.IdentityModel.Protocols.OpenIdConnect" version="8.6.1" targetFramework="net48"/>
    <package id="Microsoft.IdentityModel.Tokens" version="8.6.1" targetFramework="net48"/>
    <package id="Microsoft.IdentityModel.Validators" version="8.6.1" targetFramework="net48"/>
    <package id="Microsoft.Kiota.Authentication.Azure" version="1.17.1" targetFramework="net48"/>
    <package id="System.ClientModel" version="1.1.0" targetFramework="net48"/>
    <package id="System.IO.FileSystem.AccessControl" version="5.0.0" targetFramework="net48"/>
    <package id="System.IdentityModel.Tokens.Jwt" version="8.6.1" targetFramework="net48"/>
    <package id="Microsoft.Bcl.Memory" version="9.0.0" targetFramework="net48"/>
    <package id="Microsoft.Bcl.TimeProvider" version="8.0.1" targetFramework="net48"/>
    <package id="Microsoft.Extensions.Caching.Abstractions" version="9.0.2" targetFramework="net48"/>
    <package id="Microsoft.Extensions.Caching.Memory" version="9.0.2" targetFramework="net48"/>
    <package id="Microsoft.Extensions.DependencyInjection" version="8.0.0" targetFramework="net48"/>
    <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="9.0.2" targetFramework="net48"/>
    <package id="Microsoft.Extensions.Logging" version="8.0.0" targetFramework="net48"/>
    <package id="Microsoft.Extensions.Logging.Abstractions" version="9.0.2" targetFramework="net48"/>
    <package id="Microsoft.Extensions.Options" version="9.0.2" targetFramework="net48"/>
    <package id="Microsoft.Extensions.Primitives" version="9.0.2" targetFramework="net48"/>
    <package id="Microsoft.IdentityModel.Logging" version="8.6.1" targetFramework="net48"/>
    <package id="Microsoft.IdentityModel.Abstractions" version="8.6.1" targetFramework="net48"/>
    <package id="Microsoft.Kiota.Abstractions" version="1.17.1" targetFramework="net48"/>
    <package id="Microsoft.Kiota.Http.HttpClientLibrary" version="1.17.1" targetFramework="net48"/>
    <package id="Microsoft.Kiota.Serialization.Form" version="1.17.1" targetFramework="net48"/>
    <package id="Microsoft.Kiota.Serialization.Json" version="1.17.1" targetFramework="net48"/>
    <package id="Microsoft.Kiota.Serialization.Multipart" version="1.17.1" targetFramework="net48"/>
    <package id="Microsoft.Kiota.Serialization.Text" version="1.17.1" targetFramework="net48"/>
    <package id="PuppeteerSharp" version="20.1.3" targetFramework="net48"/>
    <package id="SSH.NET" version="2024.2.0" targetFramework="net48"/>
    <package id="Std.UriTemplate" version="2.0.1" targetFramework="net48"/>
    <package id="Sylvan.Data.Excel" version="0.4.25" targetFramework="net48"/>
    <package id="System.Buffers" version="4.5.1" targetFramework="net48"/>
    <package id="System.Diagnostics.DiagnosticSource" version="9.0.2" targetFramework="net48"/>
    <package id="System.Formats.Asn1" version="8.0.1" targetFramework="net48"/>
    <package id="System.IO.Compression" version="4.3.0" targetFramework="net48"/>
    <package id="System.Linq.Async" version="6.0.1" targetFramework="net48"/>
    <package id="System.Memory" version="4.5.5" targetFramework="net48"/>
    <package id="System.Memory.Data" version="6.0.0" targetFramework="net48"/>
    <package id="System.Net.Http.WinHttpHandler" version="6.0.0" targetFramework="net48"/>
    <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48"/>
    <package id="BouncyCastle.Cryptography" version="2.4.0" targetFramework="net48"/>
    <package id="CsvHelper" version="33.0.1" targetFramework="net48"/>
    <package id="EPPlus" version="4.0.4" targetFramework="net48"/>
    <package id="KaseyaAPI" version="1.5.0" targetFramework="net48"/>
    <package id="LogErrorsToAutotask" version="1.3.0" targetFramework="net48"/>
    <package id="Microsoft.Bcl.HashCode" version="1.1.1" targetFramework="net48"/>
    <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net48"/>
    <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48"/>
    <package id="System.Security.AccessControl" version="5.0.0" targetFramework="net48"/>
    <package id="System.Security.Cryptography.ProtectedData" version="4.5.0" targetFramework="net48"/>
    <package id="System.Security.Principal.Windows" version="5.0.0" targetFramework="net48"/>
    <package id="System.Text.Encodings.Web" version="8.0.0" targetFramework="net48"/>
    <package id="System.Text.Json" version="8.0.5" targetFramework="net48"/>
    <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48"/>
    <package id="System.ValueTuple" version="4.5.0" targetFramework="net48"/>
    <package id="bootstrap" version="3.4.1" targetFramework="net48"/>
    <package id="CSharpFunctionalExtensions" version="2.42.0" targetFramework="net48"/>
    <package id="EntityFramework" version="6.2.0" targetFramework="net48"/>
    <package id="Hangfire" version="1.8.14" targetFramework="net48"/>
    <package id="Hangfire.Core" version="1.8.14" targetFramework="net48"/>
    <package id="Hangfire.SqlServer" version="1.8.14" targetFramework="net48"/>
    <package id="jQuery" version="3.3.1" targetFramework="net48"/>
    <package id="jQuery.Validation" version="1.17.0" targetFramework="net48"/>
    <package id="Microsoft.AspNet.Mvc" version="5.2.7" targetFramework="net48"/>
    <package id="Microsoft.AspNet.Razor" version="3.2.7" targetFramework="net48"/>
    <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net48"/>
    <package id="Microsoft.AspNet.WebPages" version="3.2.7" targetFramework="net48"/>
    <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.0" targetFramework="net48"/>
    <package id="Microsoft.jQuery.Unobtrusive.Validation" version="3.2.11" targetFramework="net48"/>
    <package id="Microsoft.Owin" version="4.2.2" targetFramework="net48"/>
    <package id="Microsoft.Owin.Host.SystemWeb" version="3.0.0" targetFramework="net48"/>
    <package id="Microsoft.Owin.Security" version="4.2.2" targetFramework="net48"/>
    <package id="Microsoft.Owin.Security.Cookies" version="4.2.2" targetFramework="net48"/>
    <package id="Microsoft.Owin.Security.OpenIdConnect" version="4.2.2" targetFramework="net48"/>
    <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net48"/>
    <package id="Modernizr" version="2.8.3" targetFramework="net48"/>
    <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48"/>
    <package id="Owin" version="1.0" targetFramework="net48"/>
    <package id="WebGrease" version="1.6.0" targetFramework="net48"/>
</packages>
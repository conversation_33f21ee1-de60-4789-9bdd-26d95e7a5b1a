# UpdateProductInformationFromKqm Hangfire Task

## Overview

The `UpdateProductInformationFromKqm` task is a Hangfire background job that processes non-compliant ProductSource
records by fetching comprehensive product information from the KQM admin backend and updating the ProductSource records
with the retrieved data.

## Purpose

This task addresses ProductSource records that are marked as 'Not Compliant' by:

1. Retrieving comprehensive product information from the KQM system
2. Updating ProductSource records with the fetched data
3. Checking if sources become publishable after the updates
4. Updating status to 'Published' when all requirements are met

## How It Works

### 1. Query Processing

- Queries ProductSource records with `Status = NotCompliant` (Status ID: 2)
- Processes records in batches of 20 to manage memory and performance
- Orders by ID to ensure consistent processing

### 2. SKU Extraction

- Extracts unique SKUs from each batch to avoid duplicate API calls
- Skips empty or null SKUs
- Removes duplicates within each batch

### 3. KQM Service Integration

- Uses the existing `GetProductInformationFromKqmService` to fetch product data
- Retrieves comprehensive information including:
    - Product ID
    - Title
    - Long Description
    - Recommended Sell Price
    - Cost Price
    - Total Stock Count across all distributors

### 4. Data Updates

The task updates ProductSource records with the following mappings:

- `ProductTitle` ← KQM Title
- `ProductLongDescription` ← KQM Long Description
- `CostPrice` ← KQM Cost Price (if positive)
- `SellPrice` ← KQM Recommended Sell Price (if positive)
- `QtyAvailable` ← KQM Total Stock Count (if non-negative)

### 5. Status Management

- After updating data, checks if the ProductSource meets publishing requirements
- Updates status to `Published` if all requirements are met:
    - Has Title
    - Has Short Description
    - Has Long Description
    - Has valid Cost Price (> 0)
    - Has Sell Price (either direct or via Fandoogle cache)
    - Has Stock Count (≥ 0)
    - Has Images
- Leaves status as `NotCompliant` if requirements are not met

## Configuration

### Hangfire Attributes

- `[DisableConcurrentExecution(60 * 60 * 6)]`: Prevents concurrent execution for 6 hours
- Follows the same pattern as other tasks in the system

### Batch Processing

- **Batch Size**: 20 records per batch
- **Memory Management**: Processes in small batches to avoid memory issues
- **Performance**: Sends Hangfire heartbeats between batches

## Usage

### Manual Execution

Execute the task manually via Hangfire Dashboard:

```csharp
BackgroundJob.Enqueue(() => UpdateProductInformationFromKqm.Run(null, null, null));
```

### Scheduled Execution

Schedule the task to run periodically:

```csharp
RecurringJob.AddOrUpdate("update-product-info-from-kqm", 
    () => UpdateProductInformationFromKqm.Run(null, null, null), 
    Cron.Daily(2)); // Run daily at 2 AM
```

### Dependency Injection

The task supports dependency injection for testing and flexibility:

```csharp
// With injected dependencies
BackgroundJob.Enqueue(() => UpdateProductInformationFromKqm.Run(context, dbContext, kqmService));
```

## Error Handling

### Service-Level Errors

- KQM service failures are logged and the batch is skipped
- Individual SKU failures don't stop processing of other SKUs
- Database save failures are logged and tracked

### Data Validation

- Validates data before updating (positive prices, non-negative stock)
- Handles null or empty data gracefully
- Skips invalid SKUs

### Logging Strategy

All operations are logged to the `PreflightLog` table with detailed messages:

- Task start/end
- Batch processing progress
- Individual record updates
- Errors and failures
- Final statistics

## Monitoring

### Log Messages

Monitor task execution through the `PreflightLog` table:

```sql
SELECT * FROM PreflightLog 
WHERE Message LIKE '%UpdateProductInformationFromKqm%' 
ORDER BY LogDateTime DESC
```

### Key Metrics

The task logs final statistics including:

- Total records processed
- Total records updated
- Total errors encountered

### Hangfire Dashboard

Monitor execution status, duration, and failures through the Hangfire dashboard at `/hangfire`.

## Integration Points

### Dependencies

- **Database**: Uses `EdunetDatafeedsEntities` for data access
- **KQM Service**: Uses `IGetProductInformationFromKqmService` for product data
- **Logging**: Uses existing `PreflightLog` infrastructure

### Related Tasks

- Works in conjunction with `GetDescriptionsFromKqm` task
- Complements the `PreflightQueueRunner` for product processing
- Integrates with existing product publishing workflow

## Performance Considerations

### Resource Management

- Uses `using` statements for proper resource disposal
- Processes in small batches to limit memory usage
- Includes heartbeat mechanism for long-running operations

### Database Optimization

- Uses Entity Framework Include() for efficient data loading
- Saves changes after each batch to avoid large transactions
- Orders queries by ID for consistent processing

## Testing

### Unit Testing

The task supports dependency injection for easy unit testing:

- Mock `IGetProductInformationFromKqmService` for service testing
- Use in-memory database for data access testing
- Test error handling scenarios

### Integration Testing

- Test with actual KQM service in staging environment
- Verify data updates and status changes
- Test batch processing with large datasets

## Troubleshooting

### Common Issues

1. **No records processed**: Check if there are ProductSources with Status = NotCompliant
2. **KQM service failures**: Verify KQM credentials and service availability
3. **Database errors**: Check database connectivity and permissions

### Debug Information

Enable detailed logging by checking the PreflightLog table for specific error messages and stack traces.

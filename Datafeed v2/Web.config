<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="Datafeed_v2.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
        <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
        <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="Datafeed_v2.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <connectionStrings configSource="ConnStrings.Debug.config">
    </connectionStrings>
    <appSettings>
        <add key="webpages:Version" value="*******" />
        <add key="webpages:Enabled" value="false" />
        <add key="ClientValidationEnabled" value="true" />
        <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    </appSettings>
    <system.web>
        <compilation debug="true" targetFramework="4.8" />
        <httpRuntime targetFramework="4.8" maxRequestLength="6144" executionTimeout="300"/>
        <customErrors mode="Off" />
    </system.web>
    <system.webServer>
        <security>
            <requestFiltering>
                <requestLimits maxAllowedContentLength="6291456"/>
            </requestFiltering>
        </security>
    </system.webServer>
    <runtime>
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <dependentAssembly>
                <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" />
                <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-9.0.0.2" newVersion="9.0.0.2" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="CSharpFunctionalExtensions" publicKeyToken="null" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-3.5.1.0" newVersion="3.5.1.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-9.0.0.2" newVersion="9.0.0.2" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-9.0.0.2" newVersion="9.0.0.2" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-9.0.0.2" newVersion="9.0.0.2" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-9.0.0.2" newVersion="9.0.0.2" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-8.0.0.5" newVersion="8.0.0.5" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.IdentityModel.Protocols" publicKeyToken="31bf3856ad364e35" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-8.6.1.0" newVersion="8.6.1.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.IdentityModel.Protocols.OpenIdConnect" publicKeyToken="31bf3856ad364e35" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-8.6.1.0" newVersion="8.6.1.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.IdentityModel.Tokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-8.6.1.0" newVersion="8.6.1.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.IdentityModel.Tokens.Jwt" publicKeyToken="31bf3856ad364e35" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-8.6.1.0" newVersion="8.6.1.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Memory.Data" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.IdentityModel.Abstractions" publicKeyToken="31bf3856ad364e35" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-8.6.1.0" newVersion="8.6.1.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Identity.Client" publicKeyToken="0a613f4dd989e8ae" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.70.0.0" newVersion="4.70.0.0" />
            </dependentAssembly>
        </assemblyBinding>
    </runtime>
    <system.codedom>
        <compilers>
            <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
            <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
        </compilers>
    </system.codedom>
    <applicationSettings>
        <Datafeed_v2.Properties.Settings>
            <setting name="OAuth_ClientId" serializeAs="String">
                <value>e1d01fd9-932e-41b5-8302-ca56d1e845ef</value>
            </setting>
            <setting name="OAuth_Tenant" serializeAs="String">
                <value>cbfded58-1616-448d-ac8f-1383c66ded21</value>
            </setting>
            <setting name="OAuth_Authority" serializeAs="String">
                <value>https://login.microsoftonline.com/{0}/v2.0</value>
            </setting>
            <setting name="KQMBaseUrl" serializeAs="String">
                <value>https://api.kaseyaquotemanager.com/v1</value>
            </setting>
            <setting name="KQMApiKey" serializeAs="String">
                <value>d6a1fae78f0e4f6da69fb1b6b3566d71</value>
            </setting>
            <setting name="OAuth_RedirectUri" serializeAs="String">
                <value>https://datafeedv2.edunet.com.au</value>
            </setting>
            <setting name="IngramSftpHostname" serializeAs="String">
                <value>sftp://mercury.ingrammicro.com</value>
            </setting>
            <setting name="IngramSftpUsername" serializeAs="String">
                <value>AU_152840_2</value>
            </setting>
            <setting name="IngramSftpPassword" serializeAs="String">
                <value>vpojqw3@</value>
            </setting>
            <setting name="IngramCategorySftpUsername" serializeAs="String">
                <value>au_standard_reports_184417</value>
            </setting>
            <setting name="IngramCategorySftpPassword" serializeAs="String">
                <value>Ingram@1059</value>
            </setting>
            <setting name="GeminiApiKey" serializeAs="String">
                <value>AIzaSyA2GO9fJakCJgwpFgyl2iNQsCyEfN_Du48</value>
            </setting>
            <setting name="PerplexityApiKey" serializeAs="String">
                <value />
            </setting>
            <setting name="KqmEstoreUsername" serializeAs="String">
                <value><EMAIL></value>
            </setting>
            <setting name="KqmEstorePassword" serializeAs="String">
                <value>cHqzD!r1D6FfyaQ%Cdj&amp;bHjP6qT9vH5%#G@6nfD4gN5</value>
            </setting>
            <setting name="IcecatUsername" serializeAs="String">
                <value>mewing_sol1</value>
            </setting>
            <setting name="IcecatPassword" serializeAs="String">
                <value>JJ52kr4N6QFda!HUQ12cNnM0sXxjPrm@2TsQHPR</value>
            </setting>
            <setting name="DatafeedTeamsNotifierClientId" serializeAs="String">
                <value>fff51cf3-48af-473c-aadd-af15e7e79f06</value>
            </setting>
            <setting name="DatafeedTeamsNotifierTenantId" serializeAs="String">
                <value>cbfded58-1616-448d-ac8f-1383c66ded21</value>
            </setting>
            <setting name="DatafeedTeamsNotifierSecret" serializeAs="String">
                <value>****************************************</value>
            </setting>
            <setting name="NopLiveSiteUrl" serializeAs="String">
                <value>https://shop.edunet.com.au</value>
            </setting>
            <setting name="SmtpServer" serializeAs="String">
                <value>saferoute.solution-one.com.au</value>
            </setting>
        </Datafeed_v2.Properties.Settings>
    </applicationSettings>
    <entityFramework>
        <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
            <parameters>
                <parameter value="mssqllocaldb" />
            </parameters>
        </defaultConnectionFactory>
        <providers>
            <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
        </providers>
    </entityFramework>
    <userSettings>
        <Datafeed_v2.Properties.Settings>
            <setting name="SmtpServer" serializeAs="String">
                <value />
            </setting>
        </Datafeed_v2.Properties.Settings>
    </userSettings>
</configuration>
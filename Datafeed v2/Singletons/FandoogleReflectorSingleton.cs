using System;
using FandoogleReflectorService;

namespace Datafeed_v2.Singletons;

/// <summary>
/// Singleton class for FandoogleReflectorService
/// </summary>
public static class FandoogleReflectorSingleton
{
    private static readonly Lazy<Service> LazyInstance =
        new(() => new Service(RateLimiter));

    /// <summary>
    /// Gets the current instance of FandoogleReflectorService
    /// </summary>
    public static Service Instance => LazyInstance.Value;

    /// <summary>
    /// Gets the current instance of RateLimiter
    /// </summary>
    private static RateLimiter RateLimiter { get; set; } = new(10);
}
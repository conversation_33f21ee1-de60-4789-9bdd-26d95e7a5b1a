using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using Datafeed_v2.Properties;
using Hangfire;
using Hangfire.Dashboard;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Owin;
using Microsoft.Owin.Security;
using Microsoft.Owin.Security.Cookies;
using Microsoft.Owin.Security.OpenIdConnect;
using Owin;

namespace Datafeed_v2;

public class MyRestrictiveAuthorizationFilter : IDashboardAuthorizationFilter
{
    public bool Authorize(IDictionary<string, object> owinEnvironment)
    {
        return new OwinContext(owinEnvironment).Authentication.User.Identity.IsAuthenticated;
    }

    public bool Authorize(DashboardContext context)
    {
        return new OwinContext(context.GetOwinEnvironment()).Authentication.User.Identity.IsAuthenticated;
    }
}

public class Startup
{
    private readonly string _clientId = Settings.Default.OAuth_ClientId;

    private readonly string _redirectUri =
        Debugger.IsAttached ? "https://localhost:44323" : Settings.Default.OAuth_RedirectUri;

    private static readonly string Tenant = Settings.Default.OAuth_Tenant;

    private readonly string _authority = string.Format(CultureInfo.InvariantCulture,
        Settings.Default.OAuth_Authority, Tenant);

    public void Configuration(IAppBuilder app)
    {
        #region Configure OAuth

        app.SetDefaultSignInAsAuthenticationType(CookieAuthenticationDefaults.AuthenticationType);
        app.UseCookieAuthentication(new CookieAuthenticationOptions());
        app.UseOpenIdConnectAuthentication(new OpenIdConnectAuthenticationOptions
        {
            ClientId = _clientId,
            Authority = _authority,
            RedirectUri = _redirectUri,
            PostLogoutRedirectUri = _redirectUri,
            Scope = OpenIdConnectScope.OpenIdProfile,
            ResponseType = OpenIdConnectResponseType.CodeIdToken,
            TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = false
            },
            Notifications = new OpenIdConnectAuthenticationNotifications
            {
                // AuthenticationFailed = 
            }
        });

        #endregion

        #region Configure Hangfire

        app.UseHangfireDashboard("/hangfire", new DashboardOptions
        {
            Authorization = new List<IDashboardAuthorizationFilter> { new MyRestrictiveAuthorizationFilter() }
        });
        app.UseHangfireServer();

        #endregion
    }
}
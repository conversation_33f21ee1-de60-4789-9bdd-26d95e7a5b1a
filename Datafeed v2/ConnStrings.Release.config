<?xml version="1.0" encoding="utf-8"?>

<connectionStrings>
    <add name="DefaultConnection"
         connectionString="Data Source=sol1-sql-2022.solution-one.com.au;Initial Catalog=EdunetDatafeeds;Persist Security Info=True;User ID=EdunetMVC;Password=*********;MultipleActiveResultSets=True;Application Name=EntityFramework"
         providerName="System.Data.SqlClient"/>
    <add name="EdunetDatafeedsEntities"
         connectionString="metadata=res://*/Models.DbModels.Datafeed.EdunetDatafeedContext.csdl|res://*/Models.DbModels.Datafeed.EdunetDatafeedContext.ssdl|res://*/Models.DbModels.Datafeed.EdunetDatafeedContext.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sol1-sql-2022;initial catalog=EdunetDatafeeds;user id=EdunetMVC;password=*********;MultipleActiveResultSets=True;App=EntityFramework&quot;"
         providerName="System.Data.EntityClient"/>
    <add name="Jim2AccountingEntities"
         connectionString="metadata=res://*/Models.DbModels.Jim2Accounting.Jim2Accounting.csdl|res://*/Models.DbModels.Jim2Accounting.Jim2Accounting.ssdl|res://*/Models.DbModels.Jim2Accounting.Jim2Accounting.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sol1-sql-2022;initial catalog=Jim2Accounting;persist security info=True;user id=Jim2dbUser;password=************************************;MultipleActiveResultSets=True;App=EntityFramework&quot;"
         providerName="System.Data.EntityClient"/>
    <add name="EdunetMVCEntitiesAdmin"
         connectionString="metadata=res://*/Models.DbModels.Datafeed.EdunetMVCEntitiesAdmin.csdl|res://*/Models.DbModels.Datafeed.EdunetMVCEntitiesAdmin.ssdl|res://*/Models.DbModels.Datafeed.EdunetMVCEntitiesAdmin.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=sol1-sql-2022;initial catalog=EdunetMVC;user id=EdunetMVC;password=*********;MultipleActiveResultSets=True;App=EntityFramework&quot;"
         providerName="System.Data.EntityClient"/>
</connectionStrings>
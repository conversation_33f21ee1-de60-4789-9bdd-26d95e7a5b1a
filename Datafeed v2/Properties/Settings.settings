<?xml version="1.0" encoding="UTF-8"?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)"
              GeneratedClassNamespace="Datafeed_v2.Properties" GeneratedClassName="Settings">
    <Profiles/>
  <Settings>
    <Setting Name="OAuth_ClientId" Type="System.String" Scope="Application">
      <Value Profile="(Default)">e1d01fd9-932e-41b5-8302-ca56d1e845ef</Value>
    </Setting>
    <Setting Name="OAuth_Tenant" Type="System.String" Scope="Application">
      <Value Profile="(Default)">cbfded58-1616-448d-ac8f-1383c66ded21</Value>
    </Setting>
    <Setting Name="OAuth_Authority" Type="System.String" Scope="Application">
      <Value Profile="(Default)">https://login.microsoftonline.com/{0}/v2.0</Value>
    </Setting>
    <Setting Name="KQMBaseUrl" Type="System.String" Scope="Application">
      <Value Profile="(Default)">https://api.kaseyaquotemanager.com/v1</Value>
    </Setting>
    <Setting Name="KQMApiKey" Type="System.String" Scope="Application">
      <Value Profile="(Default)">d6a1fae78f0e4f6da69fb1b6b3566d71</Value>
    </Setting>
    <Setting Name="OAuth_RedirectUri" Type="System.String" Scope="Application">
      <Value Profile="(Default)">https://datafeedv2.edunet.com.au</Value>
    </Setting>
    <Setting Name="IngramSftpHostname" Type="System.String" Scope="Application">
      <Value Profile="(Default)">sftp://mercury.ingrammicro.com</Value>
    </Setting>
    <Setting Name="IngramSftpUsername" Type="System.String" Scope="Application">
      <Value Profile="(Default)">AU_152840_2</Value>
    </Setting>
    <Setting Name="IngramSftpPassword" Type="System.String" Scope="Application">
      <Value Profile="(Default)">vpojqw3@</Value>
    </Setting>
    <Setting Name="IngramCategorySftpUsername" Type="System.String" Scope="Application">
      <Value Profile="(Default)">au_standard_reports_184417</Value>
    </Setting>
    <Setting Name="IngramCategorySftpPassword" Type="System.String" Scope="Application">
      <Value Profile="(Default)">Ingram@1059</Value>
    </Setting>
    <Setting Name="GeminiApiKey" Type="System.String" Scope="Application">
      <Value Profile="(Default)">AIzaSyA2GO9fJakCJgwpFgyl2iNQsCyEfN_Du48</Value>
    </Setting>
    <Setting Name="PerplexityApiKey" Type="System.String" Scope="Application">
        <Value Profile="(Default)"/>
    </Setting>
    <Setting Name="KqmEstoreUsername" Type="System.String" Scope="Application">
      <Value Profile="(Default)"><EMAIL></Value>
    </Setting>
    <Setting Name="KqmEstorePassword" Type="System.String" Scope="Application">
      <Value Profile="(Default)">cHqzD!r1D6FfyaQ%Cdj&amp;bHjP6qT9vH5%#G@6nfD4gN5</Value>
    </Setting>
    <Setting Name="IcecatUsername" Type="System.String" Scope="Application">
      <Value Profile="(Default)">mewing_sol1</Value>
    </Setting>
    <Setting Name="IcecatPassword" Type="System.String" Scope="Application">
      <Value Profile="(Default)">JJ52kr4N6QFda!HUQ12cNnM0sXxjPrm@2TsQHPR</Value>
    </Setting>
    <Setting Name="DatafeedTeamsNotifierClientId" Type="System.String" Scope="Application">
      <Value Profile="(Default)">fff51cf3-48af-473c-aadd-af15e7e79f06</Value>
    </Setting>
    <Setting Name="DatafeedTeamsNotifierTenantId" Type="System.String" Scope="Application">
      <Value Profile="(Default)">cbfded58-1616-448d-ac8f-1383c66ded21</Value>
    </Setting>
    <Setting Name="DatafeedTeamsNotifierSecret" Type="System.String" Scope="Application">
      <Value Profile="(Default)">****************************************</Value>
    </Setting>
    <Setting Name="NopLiveSiteUrl" Type="System.String" Scope="Application">
      <Value Profile="(Default)">https://shop.edunet.com.au</Value>
    </Setting>
      <Setting Name="SmtpServer" Type="System.String" Scope="Application">
          <Value Profile="(Default)">saferoute.solution-one.com.au</Value>
    </Setting>
  </Settings>
</SettingsFile>


using System;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using CSharpFunctionalExtensions;
using Newtonsoft.Json;

namespace Datafeed_v2.Helpers;

public static class HTMXHelpers
{
    public static void RetargetAlert(this HttpResponseBase response)
    {
        response.Headers.Add("HX-Retarget", "this");
        response.Headers.Add("HX-Reswap", "afterbegin");
    }

    public static void RetargetAlertOnProductsAdminPage(this HttpResponseBase response)
    {
        response.Headers.Add("HX-Retarget", "#applyFixDialogContent");
        response.Headers.Add("HX-Reswap", "afterbegin");
    }

    public static void RetargetAlertOnMappedProductsPage(this HttpResponseBase response)
    {
        response.Headers.Add("HX-Retarget", "#mappedProductsForNopCategoryTable");
        response.Headers.Add("HX-Reswap", "beforebegin");
    }

    public static ActionResult DisplayAsAlert(this ActionResult result, string variant)
    {
        string TransformContent(string content)
        {
            return
                $"<sl-alert variant='{variant.Trim()}' open closable><sl-icon slot='icon' name='info-circle'></sl-icon>{content}</sl-alert>";
        }

        if (result is ContentResult contentResult)
        {
            contentResult.Content = TransformContent(contentResult.Content);
            return contentResult;
        }

        return result;
    }

    public static async Task<Result> DisplayHtmxError(this HttpResponseBase response)
    {
        try
        {
            response.Headers.Add("HX-Retarget", "#errorMessage");
            response.Headers.Add("HX-Reswap", "innerHTML");
            response.Headers.Add("HX-Trigger-After-Swap", "error");
        }
        catch (NotSupportedException e)
        {
            return Result.Failure(e.Message);
        }

        return Result.Success();
    }

    public static async Task<Result> DisplayHtmxError(this HttpResponseBase response, string message)
    {
        try
        {
            response.Headers.Add("HX-Trigger-After-Swap", JsonConvert.SerializeObject(new
            {
                error = new
                {
                    message
                }
            }));
        }
        catch (NotSupportedException e)
        {
            return Result.Failure(e.Message);
        }

        return Result.Success();
    }

    public static async Task<Result> DisplayHtmxSuccess(this HttpResponseBase response, string message)
    {
        if (string.IsNullOrEmpty(message))
        {
            return Result.Failure("Success message is null or empty");
        }

        try
        {
            response.Headers.Add("HX-Trigger-After-Swap", JsonConvert.SerializeObject(new
            {
                success = new
                {
                    message
                }
            }));
        }
        catch (NotSupportedException e)
        {
            return Result.Failure(e.Message);
        }

        return Result.Success();
    }
}
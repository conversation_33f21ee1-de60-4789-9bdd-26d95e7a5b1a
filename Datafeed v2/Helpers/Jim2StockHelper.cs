#nullable enable
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Models.Jim2Api;

namespace Datafeed_v2.Helpers
{
    public class Jim2StockHelper
    {
        /// <summary>
        /// Retrieves stock data from Jim2 API and gets the necessary column indices
        /// </summary>
        /// <param name="responseBase">HTTP response for error handling</param>
        /// <returns>Tuple containing success status, stock data, and column indices</returns>
        public static async
            Task<Result<(GetDataModel stockData, int stockCodeColumnIndex, int qtyAvailableColumnIndex)>> GetStockData(
                HttpResponseBase responseBase)
        {
            var jim2Api = new Jim2Api();
            var stockOnHandResult = await jim2Api.GetStockOnHand();
            if (stockOnHandResult.IsFailure)
            {
                await responseBase.DisplayHtmxError(stockOnHandResult.Error);
                return Result.Failure<(GetDataModel, int, int)>(stockOnHandResult.Error);
            }

            var stockOnHand = stockOnHandResult.Value;
            var firstResultSet = stockOnHand.resultSets.First();

            var stockCodeColumnIndexResult = firstResultSet.GetColumnIndex("StockCode");
            if (stockCodeColumnIndexResult.IsFailure)
            {
                await responseBase.DisplayHtmxError(stockCodeColumnIndexResult.Error);
                return Result.Failure<(GetDataModel, int, int)>(stockCodeColumnIndexResult.Error);
            }

            var qtyAvailableColumnIndexResult = firstResultSet.GetColumnIndex("QtyAvailable");
            if (qtyAvailableColumnIndexResult.IsFailure)
            {
                await responseBase.DisplayHtmxError(qtyAvailableColumnIndexResult.Error);
                return Result.Failure<(GetDataModel, int, int)>(qtyAvailableColumnIndexResult.Error);
            }

            return Result.Success((stockOnHand, stockCodeColumnIndexResult.Value, qtyAvailableColumnIndexResult.Value));
        }

        /// <summary>
        /// Updates the QtyAvailable property for KQM products based on Jim2 stock data
        /// </summary>
        /// <param name="kqmProducts">Array of KQM products to update</param>
        /// <param name="firstResultSet">Stock data from Jim2</param>
        /// <param name="stockCodeColumnIndex">Index of the StockCode column</param>
        /// <param name="qtyAvailableColumnIndex">Index of the QtyAvailable column</param>
        public static void UpdateKqmProductQuantities(
            MappedKqmProductsForNopCategory[] kqmProducts,
            ResultSets firstResultSet,
            int stockCodeColumnIndex,
            int qtyAvailableColumnIndex)
        {
            Parallel.ForEach(kqmProducts, kqmProduct =>
            {
                var jim2Stock = firstResultSet.rows.SingleOrDefault(q =>
                    string.Equals(q[stockCodeColumnIndex].ToString().Trim(), kqmProduct.manufacturerPartNumber.Trim(),
                        StringComparison.CurrentCultureIgnoreCase));
                if (jim2Stock == null) return;
                var (_, qtyAvailableFailed, qtyAvailable, _) =
                    Result.Try(() => Convert.ToDecimal(jim2Stock[qtyAvailableColumnIndex].ToString()));
                if (!qtyAvailableFailed)
                {
                    kqmProduct.QtyAvailable = (int)qtyAvailable;
                }
            });
        }

        /// <summary>
        /// Updates the QtyAvailable property for Product Source products based on Jim2 stock data
        /// </summary>
        /// <param name="productSources">Array of Product Source products to update</param>
        /// <param name="firstResultSet">Stock data from Jim2</param>
        /// <param name="stockCodeColumnIndex">Index of the StockCode column</param>
        /// <param name="qtyAvailableColumnIndex">Index of the QtyAvailable column</param>
        public static void UpdateProductSourceQuantities(
            ProductSourcesForNopCategory[] productSources,
            ResultSets firstResultSet,
            int stockCodeColumnIndex,
            int qtyAvailableColumnIndex)
        {
            Parallel.ForEach(productSources, productSource =>
            {
                var jim2Stock = firstResultSet.rows.SingleOrDefault(q =>
                    string.Equals(q[stockCodeColumnIndex].ToString().Trim(), productSource.ProductSku.Trim(),
                        StringComparison.CurrentCultureIgnoreCase));
                if (jim2Stock == null) return;
                var (_, qtyAvailableFailed, qtyAvailable, _) =
                    Result.Try(() => Convert.ToDecimal(jim2Stock[qtyAvailableColumnIndex].ToString()));
                if (!qtyAvailableFailed)
                {
                    productSource.QtyAvailable = (int)qtyAvailable;
                }
            });
        }

        /// <summary>
        /// Updates the QtyAvailable property for Ingram products based on Jim2 stock data
        /// </summary>
        /// <param name="ingramProducts">Array of Ingram products to update</param>
        /// <param name="stockData">Stock data from Jim2</param>
        /// <param name="stockCodeColumnIndex">Index of the StockCode column</param>
        /// <param name="qtyAvailableColumnIndex">Index of the QtyAvailable column</param>
        public static void UpdateIngramProductQuantities(
            MappedIngramProductsForNopCategory[] ingramProducts,
            ResultSets firstResultSet,
            int stockCodeColumnIndex,
            int qtyAvailableColumnIndex)
        {
            Parallel.ForEach(ingramProducts, ingramProduct =>
            {
                var jim2Stock = firstResultSet.rows.SingleOrDefault(q =>
                    string.Equals(q[stockCodeColumnIndex].ToString().Trim(), ingramProduct.VendorPartNumber.Trim(),
                        StringComparison.CurrentCultureIgnoreCase));
                if (jim2Stock == null) return;
                var (_, qtyAvailableFailed, qtyAvailable, _) =
                    Result.Try(() => Convert.ToDecimal(jim2Stock[qtyAvailableColumnIndex].ToString()));
                if (!qtyAvailableFailed)
                {
                    ingramProduct.QtyAvailable = (int)qtyAvailable;
                }
            });
        }

        /// <summary>
        /// Updates the QtyAvailable property for PriceBook items based on Jim2 stock data
        /// </summary>
        /// <param name="priceBookItems">Array of PriceBook items to update</param>
        /// <param name="stockData">Stock data from Jim2</param>
        /// <param name="stockCodeColumnIndex">Index of the StockCode column</param>
        /// <param name="qtyAvailableColumnIndex">Index of the QtyAvailable column</param>
        public static void UpdatePriceBookItemQuantities(
            MappedPriceBookItemsForNopCategory[] priceBookItems,
            ResultSets firstResultSet,
            int stockCodeColumnIndex,
            int qtyAvailableColumnIndex)
        {
            Parallel.ForEach(priceBookItems, priceBookItem =>
            {
                var jim2Stock = firstResultSet.rows.SingleOrDefault(q =>
                    string.Equals(q[stockCodeColumnIndex].ToString().Trim(), priceBookItem.ProductSKU.Trim(),
                        StringComparison.CurrentCultureIgnoreCase));
                if (jim2Stock == null) return;
                var (_, qtyAvailableFailed, qtyAvailable, _) =
                    Result.Try(() => Convert.ToDecimal(jim2Stock[qtyAvailableColumnIndex].ToString()));
                if (!qtyAvailableFailed)
                {
                    priceBookItem.QtyAvailable = (int)qtyAvailable;
                }
            });
        }
    }
}
using System;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Singletons;
using Microsoft.Graph.Models;

namespace Datafeed_v2.Helpers
{
    /// <summary>
    /// Helper class for sending notifications to Microsoft Teams
    /// </summary>
    public static class TeamsNotifier
    {
        /// <summary>
        /// Sends a message to a Teams channel
        /// </summary>
        /// <param name="message">The message content to send</param>
        /// <param name="teamId">The ID of the team</param>
        /// <param name="channelId">The ID of the channel</param>
        /// <returns>A result indicating success or failure</returns>
        public static async Task<Result> SendMessage(string message, string teamId, string channelId)
        {
            try
            {
                var chatMessage = new ChatMessage
                {
                    Body = new ItemBody
                    {
                        Content = message,
                        ContentType = BodyType.Text
                    }
                };

                await GraphClientSingleton.Instance.Teams[teamId]
                    .Channels[channelId]
                    .Messages
                    .PostAsync(chatMessage)
                    .ConfigureAwait(false);

                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Failure($"Failed to send Teams message: {ex.Message}");
            }
        }
    }
}
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Datafeed_v2.Helpers;

public interface IHttpClient
{
    Task<HttpResponseMessage> GetAsync(string requestUri);
    Task<HttpResponseMessage> GetAsync(string requestUri, CancellationToken cancellationToken);
}

public class MockableHttpClient : IHttpClient
{
    private readonly HttpClient _httpClient;

    public MockableHttpClient()
    {
        _httpClient = new HttpClient();
    }

    public virtual Task<HttpResponseMessage> GetAsync(string requestUri)
    {
        return _httpClient.GetAsync(requestUri);
    }

    public virtual Task<HttpResponseMessage> GetAsync(string requestUri, CancellationToken cancellationToken)
    {
        return _httpClient.GetAsync(requestUri, cancellationToken);
    }
}
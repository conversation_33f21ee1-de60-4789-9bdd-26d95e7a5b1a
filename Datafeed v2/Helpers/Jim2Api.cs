#nullable enable
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models.DbModels.Jim2Accounting;
using Datafeed_v2.Models.Jim2Api;
using Newtonsoft.Json.Linq;

namespace Datafeed_v2.Helpers;

public static class Jim2ApiModelExtensions
{
    public static Result<int> GetColumnIndex(this ResultSets resultSet, string columnName)
    {
        if (string.IsNullOrWhiteSpace(columnName))
        {
            return Result.Failure<int>("Column name cannot be empty");
        }

        if (resultSet == null)
        {
            return Result.Failure<int>("No result sets found");
        }

        var columnObjResult = Result.Try(() => resultSet.columns.Single(q =>
            string.Equals(q.Name.Trim(), columnName.Trim(), StringComparison.CurrentCultureIgnoreCase)));
        return columnObjResult.IsFailure
            ? Result.Failure<int>($"Column {columnName} not found")
            : Result.Success(resultSet.columns.ToList().IndexOf(columnObjResult.Value));
    }

    public static Result<object[]> GetColumn(ResultSets resultSet, string columnName)
    {
        if (string.IsNullOrWhiteSpace(columnName))
        {
            return Result.Failure<object[]>("Column name cannot be empty");
        }

        if (resultSet == null)
        {
            return Result.Failure<object[]>("No result sets found");
        }

        var columnIndexResult = resultSet.GetColumnIndex(columnName);
        var columnIndex = columnIndexResult.GetValueOrDefault(-1);
        return columnIndex < 0
            ? Result.Failure<object[]>($"Column {columnName} not found")
            : Result.Success(resultSet.rows.Select(q => q[columnIndex]).ToArray());
    }

    public static Result<object[]> GetColumn(this IJim2ApiModel model, string columnName)
    {
        if (string.IsNullOrWhiteSpace(columnName))
        {
            return Result.Failure<object[]>("Column name cannot be empty");
        }

        if (model.resultSets is not { Length: > 0 })
        {
            return Result.Failure<object[]>("No result sets found");
        }

        var firstResultSet = model.resultSets.First();
        return GetColumn(firstResultSet, columnName);
    }

    public static Result<object[]> GetColumn<T>(this Result<T> modelResult, string columnName) where T : IJim2ApiModel
    {
        return modelResult.IsFailure
            ? Result.Failure<object[]>(modelResult.Error)
            : modelResult.Value.GetColumn(columnName);
    }
}

public static class Jim2ApiEndpoints
{
    public const string TestToken = "api/v1/test";
    public const string GetToken = "token";

    // public const string GetStockOnHand =
    //     "api/v1/custom/GetData?queryname=GetStockMasterOnHand&qp1=2023-11-05&pageno=0&pagesize=20000";
    public const string GetStockOnHand =
        "api/v1/custom/GetData?queryname=GetStockMasterOnHandLocation&qp1=2023-11-05&qp2=ware&pageno=0&pagesize=20000";

    public const string GetCardCodes =
        "api/v1/custom/GetData?queryname=GetCardFiles&qp1=2023-11-05&pageno=0&pagesize=20000";
}

public class Jim2Api
{
    private string _token;

    private static readonly bool?
        UatJim2Override =
            null; // todo: only using these because the GetStockMasterOnHand API endpoint is not available on live environment

    private static readonly string ClientId = UatJim2Override.GetValueOrDefault(Debugger.IsAttached) switch
    {
        true => "B541934F-7F87-453B-9F98-C61512E60F59",
        false => "4C4297F7-204D-4DC2-BA72-FEEEFB2048B6"
    };

    private static readonly string ClientSecret = UatJim2Override.GetValueOrDefault(Debugger.IsAttached) switch
    {
        true => "Dx*2bVemnnVmQwxdvMc@6drfRVAgKB",
        false => "5icromib@e-tlsWl8u3ac"
    };

    private readonly string _baseAddress = UatJim2Override.GetValueOrDefault(Debugger.IsAttached) switch
    {
        true => "https://sol1-jim2.solution-one.com.au/Jim_SolutionOne_UAT/",
        false => "https://sol1-jim2.solution-one.com.au/Jim_Sol1/"
    };

    private static readonly string OAuthDbName = UatJim2Override.GetValueOrDefault(Debugger.IsAttached) switch
    {
        true => "Jim_SolutionOne_UAT",
        false => "Jim_Sol1"
    };

    private readonly string _authString =
        $"Basic {Convert.ToBase64String(Encoding.ASCII.GetBytes($"{ClientId}:{ClientSecret}"))}";

    private Result<HttpClient> CreateHttpClient(string? token = null)
    {
        var httpClient = new HttpClient
        {
            BaseAddress = new Uri(_baseAddress)
        };
        httpClient.DefaultRequestHeaders.Clear();

        var tokenMaybe = token.AsMaybe().GetValueOrDefault(_token);
        var headers = new List<KeyValuePair<string, string>>
        {
            new("Accept", "application/json"),
            new("Content-Type", "application/x-www-form-urlencoded"),
            string.IsNullOrWhiteSpace(tokenMaybe) == false
                ? new KeyValuePair<string, string>("Authorization", $"Bearer {tokenMaybe}")
                : new KeyValuePair<string, string>("Authorization", _authString)
        };

        foreach (var kvp in headers)
        {
            httpClient.DefaultRequestHeaders.TryAddWithoutValidation(kvp.Key, kvp.Value);
        }

        if (string.IsNullOrWhiteSpace(_token) == false)
        {
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _token);
        }

        return Result.Success(httpClient);
    }

    private static async Task<Result<string>> ReadHttpResponse(HttpResponseMessage response)
    {
        if (response == null)
        {
            return Result.Failure<string>("Response is null");
        }

        return Result.Success(await response.Content.ReadAsStringAsync().ConfigureAwait(false));
    }

    private Result<string> UpdateTokenInDatabase(string token, Jim2AccountingEntities? dbJim2Mock = null)
    {
        var dbJim2 = dbJim2Mock.AsMaybe().GetValueOrDefault(new Jim2AccountingEntities());

        var rowResult = Result.Try(() =>
            dbJim2.Jim2OAuthTokens.Single(q => q.jim2_db.Trim().ToLower() == OAuthDbName.Trim().ToLower()));
        if (rowResult.IsFailure)
        {
            return Result.Failure<string>(rowResult.Error);
        }

        rowResult.Value.Token = token;
        rowResult.Value.TimeStamp = DateTime.Now;
        dbJim2.SaveChanges();
        return Result.Success(token);
    }

    private Result<string> RetrieveTokenFromDatabase(Jim2AccountingEntities? dbJim2Mock = null)
    {
        var dbJim2 = dbJim2Mock.AsMaybe().GetValueOrDefault(new Jim2AccountingEntities());

        var tokenResult = Result.Try(() =>
            dbJim2.Jim2OAuthTokens.Where(q => q.jim2_db.Trim().ToLower() == OAuthDbName.Trim().ToLower())
                .Select(q => q.Token).Single());
        return tokenResult.IsFailure ? Result.Failure<string>(tokenResult.Error) : Result.Success(tokenResult.Value);
    }

    private async Task<Result<bool>> IsTokenValid(string? token = null, HttpClient? httpClientMock = null)
    {
        ServicePointManager.ServerCertificateValidationCallback = (_, _, _, _) => true;

        var httpClient = httpClientMock.AsMaybe().GetValueOrDefault(CreateHttpClient(token).GetValueOrDefault());

        var resResult = await Result.Try(async () =>
                await httpClient.GetAsync(Jim2ApiEndpoints.TestToken).ConfigureAwait(false))
            .MapError(e => $"Failed to test Jim2 token: {e}").ConfigureAwait(false);
        if (resResult.IsFailure)
        {
            return Result.Failure<bool>(resResult.Error);
        }

        if (resResult.Value.IsSuccessStatusCode == false)
        {
            return Result.Success(false);
        }

        var responseResult = await ReadHttpResponse(resResult.Value)
            .MapError(e => $"Failed to read Jim2 test token response: {e}");
        if (responseResult.IsFailure)
        {
            return Result.Failure<bool>(responseResult.Error);
        }

        var respJsonResult = Result.Try(() => JObject.Parse(responseResult.Value))
            .MapError(e => $"Failed to parse Jim2 test token response to JObject: {e}");
        if (respJsonResult.IsFailure)
        {
            return Result.Failure<bool>(respJsonResult.Error);
        }

        var loggedOnResult = Result.Try(() => respJsonResult.Value.GetValue("loggedOn").Value<bool>());
        return loggedOnResult.IsFailure
            ? Result.Failure<bool>(loggedOnResult.Error)
            : Result.Success(loggedOnResult.Value);
    }

    private async Task<Result<string>> GetToken(HttpClient? httpClientMock = null,
        Jim2AccountingEntities? dbJim2Mock = null)
    {
        var tokenResult = string.IsNullOrWhiteSpace(_token)
            ? RetrieveTokenFromDatabase(dbJim2Mock)
            : Result.Success(_token);
        if (tokenResult.IsSuccess)
        {
            var tokenIsValidResult = await IsTokenValid(tokenResult.Value, httpClientMock).ConfigureAwait(false);
            if (tokenIsValidResult is { IsSuccess: true } and { Value: true })
            {
                return Result.Success(tokenResult.Value); // no need to get a new token, existing one is still valid
            }
        }
        else
        {
            _token = string.Empty;
        }

        var httpClient = httpClientMock.AsMaybe().GetValueOrDefault(CreateHttpClient().GetValueOrDefault());

        var kvp = new List<KeyValuePair<string, string>>
        {
            new("grant_type", "client_credentials")
        };
        // Only add a scope if getting a token for the live instance
        if (UatJim2Override.GetValueOrDefault(Debugger.IsAttached) == false)
        {
            kvp.Add(new KeyValuePair<string, string>("scope", "api"));
        }

        var req = new FormUrlEncodedContent(kvp);

        var resResult = await Result.Try(async () =>
                await httpClient.PostAsync(Jim2ApiEndpoints.GetToken, req).ConfigureAwait(false))
            .MapError(e => $"Failed to get Jim2 token: {e}").ConfigureAwait(false);
        if (resResult.IsFailure)
        {
            return Result.Failure<string>(resResult.Error);
        }

        var responseResult = await ReadHttpResponse(resResult.Value)
            .MapError(e => $"Failed to read Jim2 token response: {e}");
        if (responseResult.IsFailure)
        {
            return Result.Failure<string>(responseResult.Error);
        }

        var respJsonResult = Result.Try(() => JObject.Parse(responseResult.Value))
            .MapError(e => $"Failed to parse Jim2 token response to JObject: {e}");
        if (respJsonResult.IsFailure)
        {
            return Result.Failure<string>(respJsonResult.Error);
        }

        var errorResult = Result.Try(() => respJsonResult.Value.GetValue("error_description").Value<string>());
        if (errorResult.IsSuccess)
        {
            return Result.Failure<string>(errorResult.Value);
        }

        var accessTokenResult = Result.Try(() => respJsonResult.Value.GetValue("access_token").Value<string>());
        if (accessTokenResult.IsFailure)
        {
            return Result.Failure<string>(accessTokenResult.Error);
        }

        var updateResult = UpdateTokenInDatabase(accessTokenResult.Value, dbJim2Mock);
        if (updateResult.IsFailure)
        {
            return Result.Failure<string>(updateResult.Error);
        }

        return Result.Success(accessTokenResult.Value);
    }

    private async Task<Result<HttpResponseMessage>> GetAsync(string resourceUrl,
        HttpClient? httpClientMock = null)
    {
        ServicePointManager.ServerCertificateValidationCallback = (_, _, _, _) => true;

        var getTokenResult = await GetToken(httpClientMock).ConfigureAwait(false);
        if (getTokenResult.IsFailure)
        {
            return Result.Failure<HttpResponseMessage>(getTokenResult.Error);
        }

        _token = getTokenResult.Value;
        var httpClient = httpClientMock.AsMaybe().GetValueOrDefault(CreateHttpClient().GetValueOrDefault());

        var getResult = await Result.Try(async () => await httpClient.GetAsync(resourceUrl).ConfigureAwait(false));
        return getResult.IsFailure
            ? Result.Failure<HttpResponseMessage>(getResult.Error)
            : Result.Success(getResult.Value);
    }

    public Jim2Api(string? token = null)
    {
        if (token.AsMaybe() is { HasValue: true } tokenMaybe)
        {
            _token = tokenMaybe.Value;
            return;
        }

        var tokenResult = GetToken().ConfigureAwait(false).GetAwaiter().GetResult();
        if (tokenResult.IsFailure)
        {
            throw new Exception(tokenResult.Error);
        }

        _token = tokenResult.Value;
    }

    public string GetBaseAddress()
    {
        return _baseAddress;
    }

    private async Task<Result<GetDataModel>> GetAllStockOnHand(HttpClient? httpClientMock = null)
    {
        var resResult = await GetAsync(Jim2ApiEndpoints.GetStockOnHand, httpClientMock)
            .MapError(e => $"Failed to get all stock on hand from Jim2: {e}");
        if (resResult.IsFailure)
        {
            return Result.Failure<GetDataModel>(resResult.Error);
        }

        if (resResult.Value is { IsSuccessStatusCode: false, StatusCode: HttpStatusCode.Forbidden })
        {
            return Result.Failure<GetDataModel>(
                "Jim2 token is invalid, or otherwise lacks the necessary permissions");
        }

        var responseResult = await ReadHttpResponse(resResult.Value)
            .MapError(e => $"Failed to read Jim2 stock on hand response: {e}");
        if (responseResult.IsFailure)
        {
            return Result.Failure<GetDataModel>(responseResult.Error);
        }

        var respJsonResult = Result.Try(() => JObject.Parse(responseResult.Value))
            .MapError(e =>
                $"Failed to parse Jim2 stock on hand response to JObject: {e}, response: {responseResult.Value}");
        if (respJsonResult.IsFailure)
        {
            return Result.Failure<GetDataModel>(respJsonResult.Error);
        }

        // todo: create a nice interface to deserialise the response into and return that
        var castResult = Result.Try(() => respJsonResult.Value.ToObject<GetDataModel>())
            .MapError(e => $"Failed to cast Jim2 stock on hand response to GetDataModel: {e}");
        if (castResult.IsFailure)
        {
            return Result.Failure<GetDataModel>(castResult.Error);
        }

        return Result.Success(castResult.Value);
    }

    public async Task<Result<GetDataModel>> GetStockOnHand(HttpClient? httpClientMock = null)
    {
        var resResult = await GetAllStockOnHand(httpClientMock).ConfigureAwait(false);
        if (resResult.IsFailure)
        {
            return Result.Failure<GetDataModel>(resResult.Error);
        }

        var firstResultSet = resResult.Value.resultSets.First();
        var qtyAvailableColumnIndexResult = firstResultSet.GetColumnIndex("QtyAvailable");
        if (qtyAvailableColumnIndexResult.IsFailure)
        {
            return Result.Failure<GetDataModel>(qtyAvailableColumnIndexResult.Error);
        }

        var productsWithStockOnHand = firstResultSet.rows.Where(q =>
            Result.Try(() => Convert.ToDecimal(q[qtyAvailableColumnIndexResult.Value].ToString()))
                .GetValueOrDefault(0) >
            0).ToArray();
        firstResultSet.rows = productsWithStockOnHand;
        resResult.Value.resultSets = [firstResultSet];
        return Result.Success(resResult.Value);
    }

    public async Task<Result<ResultSets>> GetCardCodes()
    {
        var resResult = await GetAsync(Jim2ApiEndpoints.GetCardCodes).ConfigureAwait(false);
        if (resResult.IsFailure)
        {
            return Result.Failure<ResultSets>(resResult.Error);
        }

        if (resResult.Value.IsSuccessStatusCode == false)
        {
            if (resResult.Value.StatusCode == HttpStatusCode.Forbidden)
            {
                return Result.Failure<ResultSets>(
                    "Jim2 token is invalid, or otherwise lacks the necessary permissions");
            }
        }

        var responseResult = await ReadHttpResponse(resResult.Value)
            .MapError(e => $"Failed to read Jim2 card codes response: {e}");
        if (responseResult.IsFailure)
        {
            return Result.Failure<ResultSets>(responseResult.Error);
        }

        var respJsonResult = Result.Try(() => JObject.Parse(responseResult.Value))
            .MapError(e => $"Failed to parse Jim2 card codes response to JObject: {e}");
        if (respJsonResult.IsFailure)
        {
            return Result.Failure<ResultSets>(respJsonResult.Error);
        }

        var castResult = Result.Try(() => respJsonResult.Value.ToObject<GetDataModel>())
            .MapError(e => $"Failed to cast Jim2 card codes response to string[]: {e}");
        if (castResult.IsFailure)
        {
            return Result.Failure<ResultSets>(castResult.Error);
        }

        var firstResultSet = castResult.Value.resultSets.First();

        return Result.Success(firstResultSet);
    }
}
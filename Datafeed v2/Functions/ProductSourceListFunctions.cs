using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Datafeed_v2.Models.DbModels.Datafeed;

namespace Datafeed_v2.Functions;

public static class ProductSourceListFunctions
{
    /// <summary>
    /// Creates a new product source type in the database.
    /// </summary>
    /// <param name="name">The name of the source type. Cannot be empty or whitespace.</param>
    /// <param name="fileName">Optional file name associated with the source type.</param>
    /// <param name="diDbDatafeed">Optional external database context. If null, a new context will be created.</param>
    /// <returns>
    /// A Result containing the created ProductSourceList on success, or an error message on failure.
    /// The database context will be disposed if it was created internally.
    /// </returns>
    public static async Task<Result<ProductSourceList>> CreateNewSourceType(string name, string fileName = null,
        EdunetDatafeedsEntities diDbDatafeed = null)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            return Result.Failure<ProductSourceList>("Name cannot be empty or whitespace.");
        }

        var isExternalContext = diDbDatafeed != null;
        var dbDatafeed = diDbDatafeed ?? new EdunetDatafeedsEntities();

        try
        {
            var newSource = new ProductSourceList
            {
                Name = name,
                FileName = fileName
            };

            dbDatafeed.ProductSourceList.Add(newSource);
            await dbDatafeed.SaveChangesAsync();

            return Result.Success(newSource);
        }
        catch (Exception ex)
        {
            return Result.Failure<ProductSourceList>($"Error creating source: {ex.Message}");
        }
        finally
        {
            if (!isExternalContext)
            {
                dbDatafeed.Dispose();
            }
        }
    }

    /// <summary>
    /// Retrieves all product source types from the database.
    /// </summary>
    /// <param name="diDbDatafeed">Optional external database context. If null, a new context will be created.</param>
    /// <returns>
    /// A Result containing a List of ProductSourceList on success, or an error message on failure.
    /// The database context will be disposed if it was created internally.
    /// </returns>
    public static async Task<Result<List<ProductSourceList>>> GetSourceTypes(
        EdunetDatafeedsEntities diDbDatafeed = null)
    {
        var isExternalContext = diDbDatafeed != null;
        var dbDatafeed = diDbDatafeed ?? new EdunetDatafeedsEntities();

        try
        {
            var sources = await dbDatafeed.ProductSourceList.ToListAsync();
            return Result.Success(sources);
        }
        catch (Exception ex)
        {
            return Result.Failure<List<ProductSourceList>>($"Error retrieving source types: {ex.Message}");
        }
        finally
        {
            if (!isExternalContext)
            {
                dbDatafeed.Dispose();
            }
        }
    }

    /// <summary>
    /// Creates a mapping between a product source type and a NopCommerce category.
    /// </summary>
    /// <param name="sourceTypeId">The ID of the product source type. Must be greater than 0.</param>
    /// <param name="nopCategoryId">The ID of the NopCommerce category. Must be greater than 0.</param>
    /// <param name="diDbDatafeed">Optional external database context. If null, a new context will be created and disposed internally.</param>
    /// <returns>
    /// A Result containing the created ProductSourceTypeToNopCategoryMapping on success, or an error message on failure.
    /// </returns>
    /// <remarks>
    /// This method performs the following operations:
    /// 1. Validates that both sourceTypeId and nopCategoryId are greater than zero
    /// 2. Verifies that the specified product source type exists in the database
    /// 3. Verifies that the specified NopCommerce category exists in the database
    /// 4. Creates and saves a new mapping between the source type and category
    /// 5. Returns the created mapping wrapped in a Result object
    /// 
    /// The database context will be disposed if it was created internally (when diDbDatafeed is null).
    /// </remarks>
    public static async Task<Result<ProductSourceTypeToNopCategoryMapping>> MapToNopCategory(
        uint sourceTypeId,
        uint nopCategoryId,
        EdunetDatafeedsEntities diDbDatafeed = null)
    {
        if (sourceTypeId == 0)
        {
            return Result.Failure<ProductSourceTypeToNopCategoryMapping>("Source type ID must be greater than 0");
        }

        if (nopCategoryId == 0)
        {
            return Result.Failure<ProductSourceTypeToNopCategoryMapping>("Category ID must be greater than 0");
        }

        var isExternalContext = diDbDatafeed != null;
        var dbDatafeed = diDbDatafeed ?? new EdunetDatafeedsEntities();

        try
        {
            // Verify source type exists
            var sourceExists = await dbDatafeed.ProductSourceList
                .AnyAsync(s => s.ID == (int)sourceTypeId);

            if (!sourceExists)
            {
                return Result.Failure<ProductSourceTypeToNopCategoryMapping>("Invalid product source type ID");
            }

            // Verify category exists
            var categoryExists = await dbDatafeed.NopCategories
                .AnyAsync(c => c.CategoryId == (int)nopCategoryId);

            if (!categoryExists)
            {
                return Result.Failure<ProductSourceTypeToNopCategoryMapping>("Invalid NopCommerce category ID");
            }

            // Create mapping
            var newMapping = new ProductSourceTypeToNopCategoryMapping
            {
                SourceTypeId = (int)sourceTypeId,
                NopCategoryId = (int)nopCategoryId
            };

            dbDatafeed.ProductSourceTypeToNopCategoryMapping.Add(newMapping);
            await dbDatafeed.SaveChangesAsync();

            return Result.Success(newMapping);
        }
        catch (Exception ex)
        {
            return Result.Failure<ProductSourceTypeToNopCategoryMapping>(
                $"Failed to create mapping: {ex.Message}");
        }
        finally
        {
            if (!isExternalContext)
            {
                dbDatafeed.Dispose();
            }
        }
    }

    /// <summary>
    /// Moves a product source type mapping from one NopCommerce category to another.
    /// </summary>
    /// <param name="sourceTypeId">The ID of the product source type to move. Must be greater than 0.</param>
    /// <param name="currentNopCategoryId">The ID of the current NopCommerce category. Must be greater than 0.</param>
    /// <param name="targetNopCategoryId">The ID of the target NopCommerce category to move to. Must be greater than 0.</param>
    /// <param name="diDbDatafeed">Optional external database context. If null, a new context will be created and disposed internally.</param>
    /// <returns>
    /// A Result containing the updated ProductSourceTypeToNopCategoryMapping on success, or an error message on failure.
    /// </returns>
    /// <remarks>
    /// This method performs the following operations:
    /// 1. Validates that all provided IDs are greater than zero
    /// 2. Verifies that the target NopCommerce category exists
    /// 3. Finds the existing mapping between the source type and current category
    /// 4. Updates the mapping to point to the target category
    /// 
    /// The database context will be disposed if it was created internally (when diDbDatafeed is null).
    /// </remarks>
    public static async Task<Result<ProductSourceTypeToNopCategoryMapping>> MoveSourceTypeMapping(
        uint sourceTypeId,
        uint currentNopCategoryId,
        uint targetNopCategoryId,
        EdunetDatafeedsEntities diDbDatafeed = null)
    {
        if (sourceTypeId == 0)
        {
            return Result.Failure<ProductSourceTypeToNopCategoryMapping>("Source type ID must be greater than 0");
        }

        if (currentNopCategoryId == 0)
        {
            return Result.Failure<ProductSourceTypeToNopCategoryMapping>("Current category ID must be greater than 0");
        }

        if (targetNopCategoryId == 0)
        {
            return Result.Failure<ProductSourceTypeToNopCategoryMapping>("Target category ID must be greater than 0");
        }

        var isExternalContext = diDbDatafeed != null;
        var dbDatafeed = diDbDatafeed ?? new EdunetDatafeedsEntities();

        try
        {
            // Verify target category exists
            var targetCategoryExists = await dbDatafeed.NopCategories
                .AnyAsync(c => c.CategoryId == (int)targetNopCategoryId);

            if (!targetCategoryExists)
            {
                return Result.Failure<ProductSourceTypeToNopCategoryMapping>("Invalid target NopCommerce category ID");
            }

            // Find the existing mapping
            var existingMapping = await dbDatafeed.ProductSourceTypeToNopCategoryMapping
                .FirstOrDefaultAsync(m =>
                    m.SourceTypeId == (int)sourceTypeId && m.NopCategoryId == (int)currentNopCategoryId);

            if (existingMapping == null)
            {
                return Result.Failure<ProductSourceTypeToNopCategoryMapping>(
                    $"No mapping found for source type ID {sourceTypeId} and current category ID {currentNopCategoryId}");
            }

            // Update the mapping to the target category
            existingMapping.NopCategoryId = (int)targetNopCategoryId;

            await dbDatafeed.SaveChangesAsync();

            return Result.Success(existingMapping);
        }
        catch (Exception ex)
        {
            return Result.Failure<ProductSourceTypeToNopCategoryMapping>(
                $"Failed to move mapping: {ex.Message}");
        }
        finally
        {
            if (!isExternalContext)
            {
                dbDatafeed.Dispose();
            }
        }
    }

    /// <summary>
    /// Removes a product source type mapping from a NopCommerce category.
    /// </summary>
    /// <param name="sourceTypeId">The ID of the product source type. Must be greater than 0.</param>
    /// <param name="nopCategoryId">The ID of the NopCommerce category. Must be greater than 0.</param>
    /// <param name="diDbDatafeed">Optional external database context. If null, a new context will be created and disposed internally.</param>
    /// <returns>
    /// A Result indicating success or failure, with an error message on failure.
    /// </returns>
    /// <remarks>
    /// This method performs the following operations:
    /// 1. Validates that both sourceTypeId and nopCategoryId are greater than zero
    /// 2. Finds the existing mapping between the source type and category
    /// 3. Removes the mapping if found
    /// 4. Saves the changes to the database
    /// 
    /// The database context will be disposed if it was created internally (when diDbDatafeed is null).
    /// </remarks>
    public static async Task<Result> RemoveSourceTypeMapping(
        uint sourceTypeId,
        uint nopCategoryId,
        EdunetDatafeedsEntities diDbDatafeed = null)
    {
        if (sourceTypeId == 0)
        {
            return Result.Failure("Source type ID must be greater than 0");
        }

        if (nopCategoryId == 0)
        {
            return Result.Failure("Category ID must be greater than 0");
        }

        var isExternalContext = diDbDatafeed != null;
        var dbDatafeed = diDbDatafeed ?? new EdunetDatafeedsEntities();

        try
        {
            // Find the existing mapping
            var existingMapping = await dbDatafeed.ProductSourceTypeToNopCategoryMapping
                .FirstOrDefaultAsync(m =>
                    m.SourceTypeId == (int)sourceTypeId && m.NopCategoryId == (int)nopCategoryId);

            if (existingMapping == null)
            {
                return Result.Failure(
                    $"No mapping found for source type ID {sourceTypeId} and category ID {nopCategoryId}");
            }

            // Remove the mapping
            dbDatafeed.ProductSourceTypeToNopCategoryMapping.Remove(existingMapping);

            await dbDatafeed.SaveChangesAsync();

            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure(
                $"Failed to remove mapping: {ex.Message}");
        }
        finally
        {
            if (!isExternalContext)
            {
                dbDatafeed.Dispose();
            }
        }
    }
}
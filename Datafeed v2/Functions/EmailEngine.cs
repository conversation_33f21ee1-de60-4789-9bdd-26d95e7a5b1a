using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Hosting;
using Datafeed_v2.Models.DbModels.Datafeed;
using Datafeed_v2.Properties;

namespace Datafeed_v2.Functions;

internal class EmailEngineContentModel
{
    public string TemplateContent { get; set; }
    public string EmailFrom { get; set; }
    public string EmailSubject { get; set; }
    public List<EmailActionTemplateMapEndpoints> RecipientList { get; set; }
    public List<EmailActionTemplateMapEndpoints> RecipientCcList { get; set; }
    public List<EmailActionTemplateMapEndpoints> RecipientBccList { get; set; }
}

public class EmailEngine
{
    private const string TemplateEmailBccAddress = "{EmailBCCAddress}";
    private readonly bool _debuggerAttached;
    private readonly List<EmailEngineContentModel> _emailList = [];
    private readonly List<Attachment> _attachmentList = [];
    private readonly int _emailActionId;
    private readonly EdunetMVCEntitiesAdmin _dbEdunetAdmin;

    public EmailEngine(int emailActionId) : this(emailActionId, new EdunetMVCEntitiesAdmin(), Debugger.IsAttached)
    {
    }

    public EmailEngine(string emailActionName) : this(emailActionName, new EdunetMVCEntitiesAdmin(),
        Debugger.IsAttached)
    {
    }

    public EmailEngine(int emailActionId, EdunetMVCEntitiesAdmin dbEdunetAdmin, bool debuggerAttached)
    {
        _emailActionId = emailActionId;
        _dbEdunetAdmin = dbEdunetAdmin;
        _debuggerAttached = debuggerAttached;
        LoadEmailActions();
    }

    public EmailEngine(string emailActionName, EdunetMVCEntitiesAdmin dbEdunetAdmin, bool debuggerAttached)
    {
        _dbEdunetAdmin = dbEdunetAdmin;
        _debuggerAttached = debuggerAttached;

        var action = _dbEdunetAdmin.EmailActions.SingleOrDefault(q => q.EmailActionName == emailActionName);
        if (action == null)
        {
            throw new Exception("Email Action not found");
        }

        _emailActionId = action.ID;
        LoadEmailActions();
    }

    private void LoadEmailActions()
    {
        var templateMaps = _dbEdunetAdmin.EmailActionTemplateMap.Where(q => q.EmailActionID == _emailActionId).ToList();
        foreach (var action in templateMaps)
        {
            var template =
                _dbEdunetAdmin.EmailTemplates.SingleOrDefault(q => q.ID == action.EmailTemplateID && q.Active);
            if (template == null)
            {
                return;
            }

            var endpoints = _dbEdunetAdmin.EmailActionTemplateMapEndpoints.Where(q => q.ActionMapID == action.ID)
                .ToList();
            _emailList.Add(new EmailEngineContentModel
            {
                EmailFrom = action.EmailFrom,
                EmailSubject = action.EmailSubject,
                TemplateContent = template.TemplateContent,
                RecipientList = endpoints,
                RecipientBccList = endpoints
            });
        }
    }

    private void AddAttachment(Stream content, string fileName, string contentType)
    {
        _attachmentList.Add(new Attachment(content, fileName, contentType));
    }

    public virtual void AddMergeField(string fieldName, string fieldValue)
    {
        foreach (var emailContent in _emailList)
        {
            emailContent.TemplateContent = emailContent.TemplateContent.Replace($"{{{fieldName}}}", fieldValue);
            emailContent.EmailSubject = emailContent.EmailSubject.Replace($"{{{fieldName}}}", fieldValue);
        }
    }

    public virtual void SetRecipient(string emailAddress)
    {
        foreach (var recipient in _emailList.SelectMany(emailContent => emailContent.RecipientList.Where(recipient =>
                     recipient.EmailAddress == "{EmailAddress}")))
        {
            recipient.EmailAddress = emailAddress;
        }
    }

    public virtual void SetCcRecipient(string emailAddress)
    {
        foreach (var recipient in _emailList.SelectMany(emailContent => emailContent.RecipientList.Where(recipient =>
                     recipient.EmailAddress == "{EmailCCAddress}")))
        {
            recipient.EmailAddress = emailAddress;
        }
    }

    public virtual void SendEmail()
    {
        // Iterate through each email configuration that was loaded based on the EmailActionID
        foreach (var emailContent in _emailList)
        {
            // Create a new MailMessage object for the current email content
            var email = new MailMessage
            {
                From = new MailAddress(emailContent.EmailFrom),
                // Initially set the subject with an "ERROR:" prefix. This will be removed if all merge fields are processed.
                Subject = $"ERROR: {emailContent.EmailSubject}",
                // Decode HTML entities in the template content for the email body
                Body = HttpUtility.HtmlDecode(emailContent.TemplateContent) ?? string.Empty,
                IsBodyHtml = true
            };

            // Add any attachments that were previously prepared
            foreach (var attachment in _attachmentList)
            {
                email.Attachments.Add(attachment);
            }

            // Determine the fallback/debug recipient email address
            // Uses the current environment username or "mewing" if the username is not available
            var username = string.IsNullOrWhiteSpace(Environment.UserName) ? "mewing" : Environment.UserName;
            var toAddress = $"{username}@solution-one.com.au";

            // Check if there are any unmerged fields (e.g., "{FieldName}") left in the email body
            if (Regex.IsMatch(emailContent.TemplateContent ?? string.Empty, "{+[A-Za-z\\d]*\\}+"))
            {
                // If unmerged fields exist, send the email to the debug address with the "ERROR:" subject prefix
                email.To.Add(toAddress);
            }
            else
            {
                // If all merge fields are processed, remove the "ERROR:" prefix from the subject
                email.Subject = emailContent.EmailSubject;

                // Check if the application is running in the production environment and the debugger is not attached
                if (!_debuggerAttached && HostingEnvironment.ApplicationHost.GetSiteName() == "Edunet_Datafeedv2")
                {
                    // In production, add the actual recipients from the RecipientList
                    // Exclude any recipients that are marked as BCC addresses (TemplateEmailBccAddress is a placeholder for BCC)
                    foreach (var recipient in emailContent.RecipientList.Where(recipient =>
                                 recipient.EmailAddress != TemplateEmailBccAddress))
                    {
                        email.To.Add(new MailAddress(recipient.EmailAddress));
                    }
                    // Note: BCC recipients are handled by the RecipientBccList during MailMessage construction if that feature is fully implemented.
                    // Currently, RecipientBccList is populated with the same as RecipientList, and this logic directly adds To recipients.
                    // If a separate BCC mechanism via SmtpClient or MailMessage.Bcc is intended, it's not explicitly shown here.
                }
                else
                {
                    // If not in production or the debugger is attached, send the email to the debug address
                    email.To.Add(new MailAddress(toAddress));
                }
            }

            // Configure the SMTP client with server details from settings
            var smtpGateway = new SmtpClient(Settings.Default.SmtpServer, 25);
            // Set a short idle time for the service point to manage resources
            smtpGateway.ServicePoint.MaxIdleTime = 1;
            // Send the email
            smtpGateway.Send(email);
        }
    }
}
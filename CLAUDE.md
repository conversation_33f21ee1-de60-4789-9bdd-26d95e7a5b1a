# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

Datafeed v2 is an ASP.NET MVC application (.NET Framework 4.8) that aggregates product data from multiple sources (Ingram Micro, KQM/Kaseya Quote Manager, and Excel price books) into a unified feed for NopCommerce e-commerce. It includes sophisticated background job processing via Hangfire, comprehensive BDD testing, and integrations with Microsoft Graph, Icecat API, and various external services.

## Build & Development Commands

### Build Solution
```bash
# Restore NuGet packages
nuget restore "Datafeed v2.sln"

# Build entire solution
msbuild "Datafeed v2.sln" /p:Configuration=Debug
msbuild "Datafeed v2.sln" /p:Configuration=Release
```

### Run Tests
```bash
# Run all unit tests
dotnet test DatafeedUnitTests/DatafeedUnitTests.csproj

# Run specific test class
dotnet test DatafeedUnitTests/DatafeedUnitTests.csproj --filter "ClassName~GetProductInformationFromKqmService"

# Run BDD feature tests
dotnet test DatafeedUnitTests/DatafeedUnitTests.csproj --filter "Category=BDD"
```

### Development Setup
- Use Visual Studio 2019+ for development
- Configure connection strings in `Datafeed v2/ConnStrings.Debug.config`
- Run via IIS Express for local development
- Access Hangfire dashboard at `/hangfire` when running locally

## Architecture Overview

### Core Components
- **Controllers**: MVC controllers handling web requests and API endpoints
- **Hangfire/Tasks**: Background job implementations for data processing
- **Functions**: Business logic layer for core operations
- **Services**: External API integrations and service layer
- **Models/DbModels**: Entity Framework database models (Database First approach)
- **PriceBooks**: Excel file parsing and import functionality

### Key Technology Stack
- **Web Framework**: ASP.NET MVC 5.2.7
- **ORM**: Entity Framework 6.2.0 (Database First)
- **Background Jobs**: Hangfire 1.8.14
- **Testing**: NUnit 4.3.2, Reqnroll (BDD), Verify (snapshot testing), Moq
- **Authentication**: Microsoft Owin with OpenID Connect
- **External APIs**: Microsoft Graph, Icecat, Kaseya API, SSH.NET for SFTP

### Data Flow Architecture
1. **Data Ingestion**: Automated feeds from Ingram (SFTP), KQM (API), and Excel imports
2. **Transformation**: Product standardization and enrichment via background jobs
3. **Mapping**: Category and product mapping to NopCommerce structure
4. **Validation**: Preflight quality checks before publishing
5. **Export**: XML/CSV feed generation for NopCommerce consumption

### Background Job System
Hangfire manages scheduled tasks including:
- `GetIngramProducts`: Daily SFTP feed processing
- `UpdateProductInformationFromKqm`: KQM data synchronization
- `GetProductImagesFromIcecat`: Image fetching and processing
- `PreflightQueueRunner`: Quality validation pipeline
- `RequestPricing`: Price updates from various sources

## Database Architecture

### Primary Contexts
- **EdunetDatafeedContext**: Main application database
- **Jim2Accounting**: Integration with Jim2 accounting system

### Key Entity Relationships
- **ProductSource**: Central product entity unified across all sources
- **NopCategories**: E-commerce category hierarchy
- **ProductSourceList**: Product type and source management
- **Mapping Tables**: Various category and product association tables
- **Override Tables**: Custom pricing and product information rules

## Testing Strategy

### Test Types
- **Unit Tests**: Controller and service testing with Moq
- **Integration Tests**: Database and external API testing
- **BDD Tests**: Gherkin scenarios using Reqnroll for business logic
- **Snapshot Tests**: Verify framework for regression detection

### Running Specific Tests
- BDD features are in `DatafeedUnitTests/Features/`
- Unit tests follow namespace structure matching main project
- Snapshot files are in `snapshots/` subdirectories

## Common Development Patterns

### Entity Framework Usage
- Database-first approach with .edmx files
- Multiple contexts for different database sources
- Use existing context patterns when adding new entities

### Background Job Implementation
- Inherit from appropriate base classes in `Hangfire/Tasks/`
- Implement `SkipWhenPreviousJobIsRunningAttribute` for long-running jobs
- Jobs are disabled during debugging via `#if !DEBUG` directives

### Controller Patterns
- Most controllers require `[Authorize]` attribute
- Use HTMX helpers for partial view updates
- Follow existing error handling patterns with alert partials

### Service Integration
- External APIs use singleton pattern (see `Singletons/` folder)
- Implement retry logic using `RetryHelper` for external calls
- Use mockable HTTP clients for testability

## Environment Configuration

### Connection Strings
- Debug: `ConnStrings.Debug.config`
- Release: `ConnStrings.Release.config`
- Transform applied during build process

### Key Settings
- Hangfire connection strings for background job storage
- External API endpoints and authentication
- File paths for SFTP operations and local storage

## Deployment Notes

- Production URL: https://datafeedv2.edunet.com.au
- HTTPS enforcement enabled in production
- Hangfire dashboard secured with authorization
- Azure Identity integration for cloud services